/**********************************************************************
 * Auto-generated by protocol-dts-generator.ts, do not edit manually. *
 **********************************************************************/

import Protocol from './protocol'

/**
 * API generated from Protocol commands and events.
 */
export namespace ProtocolTestsProxyApi {

    export interface ProtocolApi {
        Console: ConsoleApi;

        Debugger: Debugger<PERSON>pi;

        HeapProfiler: HeapProfilerApi;

        Profiler: ProfilerApi;

        Runtime: RuntimeApi;

        Schema: SchemaApi;

        Accessibility: AccessibilityApi;

        Animation: AnimationApi;

        Audits: AuditsApi;

        Extensions: ExtensionsApi;

        Autofill: AutofillApi;

        BackgroundService: BackgroundServiceApi;

        Browser: <PERSON>rowser<PERSON><PERSON>;

        CSS: CSSApi;

        CacheStorage: CacheStorageApi;

        Cast: <PERSON><PERSON><PERSON>;

        DOM: D<PERSON>Api;

        DOMDebugger: <PERSON>OMDebugger<PERSON>pi;

        EventBreakpoints: EventBreakpointsApi;

        DOMSnapshot: DOMSnapshotApi;

        DOMStorage: DOMStorageApi;

        Database: DatabaseApi;

        DeviceOrientation: DeviceOrientationApi;

        Emulation: EmulationApi;

        HeadlessExperimental: HeadlessExperimentalApi;

        IO: IOApi;

        IndexedDB: IndexedDBApi;

        Input: InputApi;

        Inspector: InspectorApi;

        LayerTree: LayerTreeApi;

        Log: LogApi;

        Memory: MemoryApi;

        Network: NetworkApi;

        Overlay: OverlayApi;

        Page: PageApi;

        Performance: PerformanceApi;

        PerformanceTimeline: PerformanceTimelineApi;

        Security: SecurityApi;

        ServiceWorker: ServiceWorkerApi;

        Storage: StorageApi;

        SystemInfo: SystemInfoApi;

        Target: TargetApi;

        Tethering: TetheringApi;

        Tracing: TracingApi;

        Fetch: FetchApi;

        WebAudio: WebAudioApi;

        WebAuthn: WebAuthnApi;

        Media: MediaApi;

        DeviceAccess: DeviceAccessApi;

        Preload: PreloadApi;

        FedCm: FedCmApi;

        PWA: PWAApi;

    }


    export interface ConsoleApi {
        /**
         * Does nothing.
         */
        clearMessages(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables console domain, prevents further console messages from being reported to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables console domain, sends the messages collected so far to the client by means of the
         * `messageAdded` notification.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Issued when new console message is added.
         */
        onMessageAdded(listener: (event: { params: Protocol.Console.MessageAddedEvent }) => void): void;
        offMessageAdded(listener: (event: { params: Protocol.Console.MessageAddedEvent }) => void): void;
        onceMessageAdded(eventMatcher?: (event: { params: Protocol.Console.MessageAddedEvent }) => boolean): Promise<{ params: Protocol.Console.MessageAddedEvent }>;

    }

    export interface DebuggerApi {
        /**
         * Continues execution until specific location is reached.
         */
        continueToLocation(params: Protocol.Debugger.ContinueToLocationRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables debugger for given page.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables debugger for the given page. Clients should not assume that the debugging has been
         * enabled until the result for this command is received.
         */
        enable(params: Protocol.Debugger.EnableRequest): Promise<{id: number, result: Protocol.Debugger.EnableResponse, sessionId: string}>;

        /**
         * Evaluates expression on a given call frame.
         */
        evaluateOnCallFrame(params: Protocol.Debugger.EvaluateOnCallFrameRequest): Promise<{id: number, result: Protocol.Debugger.EvaluateOnCallFrameResponse, sessionId: string}>;

        /**
         * Returns possible locations for breakpoint. scriptId in start and end range locations should be
         * the same.
         */
        getPossibleBreakpoints(params: Protocol.Debugger.GetPossibleBreakpointsRequest): Promise<{id: number, result: Protocol.Debugger.GetPossibleBreakpointsResponse, sessionId: string}>;

        /**
         * Returns source for the script with given id.
         */
        getScriptSource(params: Protocol.Debugger.GetScriptSourceRequest): Promise<{id: number, result: Protocol.Debugger.GetScriptSourceResponse, sessionId: string}>;

        disassembleWasmModule(params: Protocol.Debugger.DisassembleWasmModuleRequest): Promise<{id: number, result: Protocol.Debugger.DisassembleWasmModuleResponse, sessionId: string}>;

        /**
         * Disassemble the next chunk of lines for the module corresponding to the
         * stream. If disassembly is complete, this API will invalidate the streamId
         * and return an empty chunk. Any subsequent calls for the now invalid stream
         * will return errors.
         */
        nextWasmDisassemblyChunk(params: Protocol.Debugger.NextWasmDisassemblyChunkRequest): Promise<{id: number, result: Protocol.Debugger.NextWasmDisassemblyChunkResponse, sessionId: string}>;

        /**
         * This command is deprecated. Use getScriptSource instead.
         */
        getWasmBytecode(params: Protocol.Debugger.GetWasmBytecodeRequest): Promise<{id: number, result: Protocol.Debugger.GetWasmBytecodeResponse, sessionId: string}>;

        /**
         * Returns stack trace with given `stackTraceId`.
         */
        getStackTrace(params: Protocol.Debugger.GetStackTraceRequest): Promise<{id: number, result: Protocol.Debugger.GetStackTraceResponse, sessionId: string}>;

        /**
         * Stops on the next JavaScript statement.
         */
        pause(): Promise<{id: number, result: void, sessionId: string}>;

        pauseOnAsyncCall(params: Protocol.Debugger.PauseOnAsyncCallRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes JavaScript breakpoint.
         */
        removeBreakpoint(params: Protocol.Debugger.RemoveBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Restarts particular call frame from the beginning. The old, deprecated
         * behavior of `restartFrame` is to stay paused and allow further CDP commands
         * after a restart was scheduled. This can cause problems with restarting, so
         * we now continue execution immediatly after it has been scheduled until we
         * reach the beginning of the restarted frame.
         * 
         * To stay back-wards compatible, `restartFrame` now expects a `mode`
         * parameter to be present. If the `mode` parameter is missing, `restartFrame`
         * errors out.
         * 
         * The various return values are deprecated and `callFrames` is always empty.
         * Use the call frames from the `Debugger#paused` events instead, that fires
         * once V8 pauses at the beginning of the restarted function.
         */
        restartFrame(params: Protocol.Debugger.RestartFrameRequest): Promise<{id: number, result: Protocol.Debugger.RestartFrameResponse, sessionId: string}>;

        /**
         * Resumes JavaScript execution.
         */
        resume(params: Protocol.Debugger.ResumeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Searches for given string in script content.
         */
        searchInContent(params: Protocol.Debugger.SearchInContentRequest): Promise<{id: number, result: Protocol.Debugger.SearchInContentResponse, sessionId: string}>;

        /**
         * Enables or disables async call stacks tracking.
         */
        setAsyncCallStackDepth(params: Protocol.Debugger.SetAsyncCallStackDepthRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Replace previous blackbox patterns with passed ones. Forces backend to skip stepping/pausing in
         * scripts with url matching one of the patterns. VM will try to leave blackboxed script by
         * performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
         */
        setBlackboxPatterns(params: Protocol.Debugger.SetBlackboxPatternsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Makes backend skip steps in the script in blackboxed ranges. VM will try leave blacklisted
         * scripts by performing 'step in' several times, finally resorting to 'step out' if unsuccessful.
         * Positions array contains positions where blackbox state is changed. First interval isn't
         * blackboxed. Array should be sorted.
         */
        setBlackboxedRanges(params: Protocol.Debugger.SetBlackboxedRangesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets JavaScript breakpoint at a given location.
         */
        setBreakpoint(params: Protocol.Debugger.SetBreakpointRequest): Promise<{id: number, result: Protocol.Debugger.SetBreakpointResponse, sessionId: string}>;

        /**
         * Sets instrumentation breakpoint.
         */
        setInstrumentationBreakpoint(params: Protocol.Debugger.SetInstrumentationBreakpointRequest): Promise<{id: number, result: Protocol.Debugger.SetInstrumentationBreakpointResponse, sessionId: string}>;

        /**
         * Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this
         * command is issued, all existing parsed scripts will have breakpoints resolved and returned in
         * `locations` property. Further matching script parsing will result in subsequent
         * `breakpointResolved` events issued. This logical breakpoint will survive page reloads.
         */
        setBreakpointByUrl(params: Protocol.Debugger.SetBreakpointByUrlRequest): Promise<{id: number, result: Protocol.Debugger.SetBreakpointByUrlResponse, sessionId: string}>;

        /**
         * Sets JavaScript breakpoint before each call to the given function.
         * If another function was created from the same source as a given one,
         * calling it will also trigger the breakpoint.
         */
        setBreakpointOnFunctionCall(params: Protocol.Debugger.SetBreakpointOnFunctionCallRequest): Promise<{id: number, result: Protocol.Debugger.SetBreakpointOnFunctionCallResponse, sessionId: string}>;

        /**
         * Activates / deactivates all breakpoints on the page.
         */
        setBreakpointsActive(params: Protocol.Debugger.SetBreakpointsActiveRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions,
         * or caught exceptions, no exceptions. Initial pause on exceptions state is `none`.
         */
        setPauseOnExceptions(params: Protocol.Debugger.SetPauseOnExceptionsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Changes return value in top frame. Available only at return break position.
         */
        setReturnValue(params: Protocol.Debugger.SetReturnValueRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Edits JavaScript source live.
         * 
         * In general, functions that are currently on the stack can not be edited with
         * a single exception: If the edited function is the top-most stack frame and
         * that is the only activation of that function on the stack. In this case
         * the live edit will be successful and a `Debugger.restartFrame` for the
         * top-most function is automatically triggered.
         */
        setScriptSource(params: Protocol.Debugger.SetScriptSourceRequest): Promise<{id: number, result: Protocol.Debugger.SetScriptSourceResponse, sessionId: string}>;

        /**
         * Makes page not interrupt on any pauses (breakpoint, exception, dom exception etc).
         */
        setSkipAllPauses(params: Protocol.Debugger.SetSkipAllPausesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Changes value of variable in a callframe. Object-based scopes are not supported and must be
         * mutated manually.
         */
        setVariableValue(params: Protocol.Debugger.SetVariableValueRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Steps into the function call.
         */
        stepInto(params: Protocol.Debugger.StepIntoRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Steps out of the function call.
         */
        stepOut(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Steps over the statement.
         */
        stepOver(params: Protocol.Debugger.StepOverRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fired when breakpoint is resolved to an actual script and location.
         */
        onBreakpointResolved(listener: (event: { params: Protocol.Debugger.BreakpointResolvedEvent }) => void): void;
        offBreakpointResolved(listener: (event: { params: Protocol.Debugger.BreakpointResolvedEvent }) => void): void;
        onceBreakpointResolved(eventMatcher?: (event: { params: Protocol.Debugger.BreakpointResolvedEvent }) => boolean): Promise<{ params: Protocol.Debugger.BreakpointResolvedEvent }>;

        /**
         * Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.
         */
        onPaused(listener: (event: { params: Protocol.Debugger.PausedEvent }) => void): void;
        offPaused(listener: (event: { params: Protocol.Debugger.PausedEvent }) => void): void;
        oncePaused(eventMatcher?: (event: { params: Protocol.Debugger.PausedEvent }) => boolean): Promise<{ params: Protocol.Debugger.PausedEvent }>;

        /**
         * Fired when the virtual machine resumed execution.
         */
        onResumed(listener: (event: ) => void): void;
        offResumed(listener: (event: ) => void): void;
        onceResumed(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when virtual machine fails to parse the script.
         */
        onScriptFailedToParse(listener: (event: { params: Protocol.Debugger.ScriptFailedToParseEvent }) => void): void;
        offScriptFailedToParse(listener: (event: { params: Protocol.Debugger.ScriptFailedToParseEvent }) => void): void;
        onceScriptFailedToParse(eventMatcher?: (event: { params: Protocol.Debugger.ScriptFailedToParseEvent }) => boolean): Promise<{ params: Protocol.Debugger.ScriptFailedToParseEvent }>;

        /**
         * Fired when virtual machine parses script. This event is also fired for all known and uncollected
         * scripts upon enabling debugger.
         */
        onScriptParsed(listener: (event: { params: Protocol.Debugger.ScriptParsedEvent }) => void): void;
        offScriptParsed(listener: (event: { params: Protocol.Debugger.ScriptParsedEvent }) => void): void;
        onceScriptParsed(eventMatcher?: (event: { params: Protocol.Debugger.ScriptParsedEvent }) => boolean): Promise<{ params: Protocol.Debugger.ScriptParsedEvent }>;

    }

    export interface HeapProfilerApi {
        /**
         * Enables console to refer to the node with given id via $x (see Command Line API for more details
         * $x functions).
         */
        addInspectedHeapObject(params: Protocol.HeapProfiler.AddInspectedHeapObjectRequest): Promise<{id: number, result: void, sessionId: string}>;

        collectGarbage(): Promise<{id: number, result: void, sessionId: string}>;

        disable(): Promise<{id: number, result: void, sessionId: string}>;

        enable(): Promise<{id: number, result: void, sessionId: string}>;

        getHeapObjectId(params: Protocol.HeapProfiler.GetHeapObjectIdRequest): Promise<{id: number, result: Protocol.HeapProfiler.GetHeapObjectIdResponse, sessionId: string}>;

        getObjectByHeapObjectId(params: Protocol.HeapProfiler.GetObjectByHeapObjectIdRequest): Promise<{id: number, result: Protocol.HeapProfiler.GetObjectByHeapObjectIdResponse, sessionId: string}>;

        getSamplingProfile(): Promise<{id: number, result: Protocol.HeapProfiler.GetSamplingProfileResponse, sessionId: string}>;

        startSampling(params: Protocol.HeapProfiler.StartSamplingRequest): Promise<{id: number, result: void, sessionId: string}>;

        startTrackingHeapObjects(params: Protocol.HeapProfiler.StartTrackingHeapObjectsRequest): Promise<{id: number, result: void, sessionId: string}>;

        stopSampling(): Promise<{id: number, result: Protocol.HeapProfiler.StopSamplingResponse, sessionId: string}>;

        stopTrackingHeapObjects(params: Protocol.HeapProfiler.StopTrackingHeapObjectsRequest): Promise<{id: number, result: void, sessionId: string}>;

        takeHeapSnapshot(params: Protocol.HeapProfiler.TakeHeapSnapshotRequest): Promise<{id: number, result: void, sessionId: string}>;

        onAddHeapSnapshotChunk(listener: (event: { params: Protocol.HeapProfiler.AddHeapSnapshotChunkEvent }) => void): void;
        offAddHeapSnapshotChunk(listener: (event: { params: Protocol.HeapProfiler.AddHeapSnapshotChunkEvent }) => void): void;
        onceAddHeapSnapshotChunk(eventMatcher?: (event: { params: Protocol.HeapProfiler.AddHeapSnapshotChunkEvent }) => boolean): Promise<{ params: Protocol.HeapProfiler.AddHeapSnapshotChunkEvent }>;

        /**
         * If heap objects tracking has been started then backend may send update for one or more fragments
         */
        onHeapStatsUpdate(listener: (event: { params: Protocol.HeapProfiler.HeapStatsUpdateEvent }) => void): void;
        offHeapStatsUpdate(listener: (event: { params: Protocol.HeapProfiler.HeapStatsUpdateEvent }) => void): void;
        onceHeapStatsUpdate(eventMatcher?: (event: { params: Protocol.HeapProfiler.HeapStatsUpdateEvent }) => boolean): Promise<{ params: Protocol.HeapProfiler.HeapStatsUpdateEvent }>;

        /**
         * If heap objects tracking has been started then backend regularly sends a current value for last
         * seen object id and corresponding timestamp. If the were changes in the heap since last event
         * then one or more heapStatsUpdate events will be sent before a new lastSeenObjectId event.
         */
        onLastSeenObjectId(listener: (event: { params: Protocol.HeapProfiler.LastSeenObjectIdEvent }) => void): void;
        offLastSeenObjectId(listener: (event: { params: Protocol.HeapProfiler.LastSeenObjectIdEvent }) => void): void;
        onceLastSeenObjectId(eventMatcher?: (event: { params: Protocol.HeapProfiler.LastSeenObjectIdEvent }) => boolean): Promise<{ params: Protocol.HeapProfiler.LastSeenObjectIdEvent }>;

        onReportHeapSnapshotProgress(listener: (event: { params: Protocol.HeapProfiler.ReportHeapSnapshotProgressEvent }) => void): void;
        offReportHeapSnapshotProgress(listener: (event: { params: Protocol.HeapProfiler.ReportHeapSnapshotProgressEvent }) => void): void;
        onceReportHeapSnapshotProgress(eventMatcher?: (event: { params: Protocol.HeapProfiler.ReportHeapSnapshotProgressEvent }) => boolean): Promise<{ params: Protocol.HeapProfiler.ReportHeapSnapshotProgressEvent }>;

        onResetProfiles(listener: (event: ) => void): void;
        offResetProfiles(listener: (event: ) => void): void;
        onceResetProfiles(eventMatcher?: (event: ) => boolean): Promise<>;

    }

    export interface ProfilerApi {
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Collect coverage data for the current isolate. The coverage data may be incomplete due to
         * garbage collection.
         */
        getBestEffortCoverage(): Promise<{id: number, result: Protocol.Profiler.GetBestEffortCoverageResponse, sessionId: string}>;

        /**
         * Changes CPU profiler sampling interval. Must be called before CPU profiles recording started.
         */
        setSamplingInterval(params: Protocol.Profiler.SetSamplingIntervalRequest): Promise<{id: number, result: void, sessionId: string}>;

        start(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable precise code coverage. Coverage data for JavaScript executed before enabling precise code
         * coverage may be incomplete. Enabling prevents running optimized code and resets execution
         * counters.
         */
        startPreciseCoverage(params: Protocol.Profiler.StartPreciseCoverageRequest): Promise<{id: number, result: Protocol.Profiler.StartPreciseCoverageResponse, sessionId: string}>;

        stop(): Promise<{id: number, result: Protocol.Profiler.StopResponse, sessionId: string}>;

        /**
         * Disable precise code coverage. Disabling releases unnecessary execution count records and allows
         * executing optimized code.
         */
        stopPreciseCoverage(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Collect coverage data for the current isolate, and resets execution counters. Precise code
         * coverage needs to have started.
         */
        takePreciseCoverage(): Promise<{id: number, result: Protocol.Profiler.TakePreciseCoverageResponse, sessionId: string}>;

        onConsoleProfileFinished(listener: (event: { params: Protocol.Profiler.ConsoleProfileFinishedEvent }) => void): void;
        offConsoleProfileFinished(listener: (event: { params: Protocol.Profiler.ConsoleProfileFinishedEvent }) => void): void;
        onceConsoleProfileFinished(eventMatcher?: (event: { params: Protocol.Profiler.ConsoleProfileFinishedEvent }) => boolean): Promise<{ params: Protocol.Profiler.ConsoleProfileFinishedEvent }>;

        /**
         * Sent when new profile recording is started using console.profile() call.
         */
        onConsoleProfileStarted(listener: (event: { params: Protocol.Profiler.ConsoleProfileStartedEvent }) => void): void;
        offConsoleProfileStarted(listener: (event: { params: Protocol.Profiler.ConsoleProfileStartedEvent }) => void): void;
        onceConsoleProfileStarted(eventMatcher?: (event: { params: Protocol.Profiler.ConsoleProfileStartedEvent }) => boolean): Promise<{ params: Protocol.Profiler.ConsoleProfileStartedEvent }>;

        /**
         * Reports coverage delta since the last poll (either from an event like this, or from
         * `takePreciseCoverage` for the current isolate. May only be sent if precise code
         * coverage has been started. This event can be trigged by the embedder to, for example,
         * trigger collection of coverage data immediately at a certain point in time.
         */
        onPreciseCoverageDeltaUpdate(listener: (event: { params: Protocol.Profiler.PreciseCoverageDeltaUpdateEvent }) => void): void;
        offPreciseCoverageDeltaUpdate(listener: (event: { params: Protocol.Profiler.PreciseCoverageDeltaUpdateEvent }) => void): void;
        oncePreciseCoverageDeltaUpdate(eventMatcher?: (event: { params: Protocol.Profiler.PreciseCoverageDeltaUpdateEvent }) => boolean): Promise<{ params: Protocol.Profiler.PreciseCoverageDeltaUpdateEvent }>;

    }

    export interface RuntimeApi {
        /**
         * Add handler to promise with given promise object id.
         */
        awaitPromise(params: Protocol.Runtime.AwaitPromiseRequest): Promise<{id: number, result: Protocol.Runtime.AwaitPromiseResponse, sessionId: string}>;

        /**
         * Calls function with given declaration on the given object. Object group of the result is
         * inherited from the target object.
         */
        callFunctionOn(params: Protocol.Runtime.CallFunctionOnRequest): Promise<{id: number, result: Protocol.Runtime.CallFunctionOnResponse, sessionId: string}>;

        /**
         * Compiles expression.
         */
        compileScript(params: Protocol.Runtime.CompileScriptRequest): Promise<{id: number, result: Protocol.Runtime.CompileScriptResponse, sessionId: string}>;

        /**
         * Disables reporting of execution contexts creation.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Discards collected exceptions and console API calls.
         */
        discardConsoleEntries(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables reporting of execution contexts creation by means of `executionContextCreated` event.
         * When the reporting gets enabled the event will be sent immediately for each existing execution
         * context.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Evaluates expression on global object.
         */
        evaluate(params: Protocol.Runtime.EvaluateRequest): Promise<{id: number, result: Protocol.Runtime.EvaluateResponse, sessionId: string}>;

        /**
         * Returns the isolate id.
         */
        getIsolateId(): Promise<{id: number, result: Protocol.Runtime.GetIsolateIdResponse, sessionId: string}>;

        /**
         * Returns the JavaScript heap usage.
         * It is the total usage of the corresponding isolate not scoped to a particular Runtime.
         */
        getHeapUsage(): Promise<{id: number, result: Protocol.Runtime.GetHeapUsageResponse, sessionId: string}>;

        /**
         * Returns properties of a given object. Object group of the result is inherited from the target
         * object.
         */
        getProperties(params: Protocol.Runtime.GetPropertiesRequest): Promise<{id: number, result: Protocol.Runtime.GetPropertiesResponse, sessionId: string}>;

        /**
         * Returns all let, const and class variables from global scope.
         */
        globalLexicalScopeNames(params: Protocol.Runtime.GlobalLexicalScopeNamesRequest): Promise<{id: number, result: Protocol.Runtime.GlobalLexicalScopeNamesResponse, sessionId: string}>;

        queryObjects(params: Protocol.Runtime.QueryObjectsRequest): Promise<{id: number, result: Protocol.Runtime.QueryObjectsResponse, sessionId: string}>;

        /**
         * Releases remote object with given id.
         */
        releaseObject(params: Protocol.Runtime.ReleaseObjectRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Releases all remote objects that belong to a given group.
         */
        releaseObjectGroup(params: Protocol.Runtime.ReleaseObjectGroupRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Tells inspected instance to run if it was waiting for debugger to attach.
         */
        runIfWaitingForDebugger(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Runs script with given id in a given context.
         */
        runScript(params: Protocol.Runtime.RunScriptRequest): Promise<{id: number, result: Protocol.Runtime.RunScriptResponse, sessionId: string}>;

        /**
         * Enables or disables async call stacks tracking.
         */
        setAsyncCallStackDepth(params: Protocol.Runtime.SetAsyncCallStackDepthRequest): Promise<{id: number, result: void, sessionId: string}>;

        setCustomObjectFormatterEnabled(params: Protocol.Runtime.SetCustomObjectFormatterEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        setMaxCallStackSizeToCapture(params: Protocol.Runtime.SetMaxCallStackSizeToCaptureRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Terminate current or next JavaScript execution.
         * Will cancel the termination when the outer-most script execution ends.
         */
        terminateExecution(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * If executionContextId is empty, adds binding with the given name on the
         * global objects of all inspected contexts, including those created later,
         * bindings survive reloads.
         * Binding function takes exactly one argument, this argument should be string,
         * in case of any other input, function throws an exception.
         * Each binding function call produces Runtime.bindingCalled notification.
         */
        addBinding(params: Protocol.Runtime.AddBindingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This method does not remove binding function from global object but
         * unsubscribes current runtime agent from Runtime.bindingCalled notifications.
         */
        removeBinding(params: Protocol.Runtime.RemoveBindingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This method tries to lookup and populate exception details for a
         * JavaScript Error object.
         * Note that the stackTrace portion of the resulting exceptionDetails will
         * only be populated if the Runtime domain was enabled at the time when the
         * Error was thrown.
         */
        getExceptionDetails(params: Protocol.Runtime.GetExceptionDetailsRequest): Promise<{id: number, result: Protocol.Runtime.GetExceptionDetailsResponse, sessionId: string}>;

        /**
         * Notification is issued every time when binding is called.
         */
        onBindingCalled(listener: (event: { params: Protocol.Runtime.BindingCalledEvent }) => void): void;
        offBindingCalled(listener: (event: { params: Protocol.Runtime.BindingCalledEvent }) => void): void;
        onceBindingCalled(eventMatcher?: (event: { params: Protocol.Runtime.BindingCalledEvent }) => boolean): Promise<{ params: Protocol.Runtime.BindingCalledEvent }>;

        /**
         * Issued when console API was called.
         */
        onConsoleAPICalled(listener: (event: { params: Protocol.Runtime.ConsoleAPICalledEvent }) => void): void;
        offConsoleAPICalled(listener: (event: { params: Protocol.Runtime.ConsoleAPICalledEvent }) => void): void;
        onceConsoleAPICalled(eventMatcher?: (event: { params: Protocol.Runtime.ConsoleAPICalledEvent }) => boolean): Promise<{ params: Protocol.Runtime.ConsoleAPICalledEvent }>;

        /**
         * Issued when unhandled exception was revoked.
         */
        onExceptionRevoked(listener: (event: { params: Protocol.Runtime.ExceptionRevokedEvent }) => void): void;
        offExceptionRevoked(listener: (event: { params: Protocol.Runtime.ExceptionRevokedEvent }) => void): void;
        onceExceptionRevoked(eventMatcher?: (event: { params: Protocol.Runtime.ExceptionRevokedEvent }) => boolean): Promise<{ params: Protocol.Runtime.ExceptionRevokedEvent }>;

        /**
         * Issued when exception was thrown and unhandled.
         */
        onExceptionThrown(listener: (event: { params: Protocol.Runtime.ExceptionThrownEvent }) => void): void;
        offExceptionThrown(listener: (event: { params: Protocol.Runtime.ExceptionThrownEvent }) => void): void;
        onceExceptionThrown(eventMatcher?: (event: { params: Protocol.Runtime.ExceptionThrownEvent }) => boolean): Promise<{ params: Protocol.Runtime.ExceptionThrownEvent }>;

        /**
         * Issued when new execution context is created.
         */
        onExecutionContextCreated(listener: (event: { params: Protocol.Runtime.ExecutionContextCreatedEvent }) => void): void;
        offExecutionContextCreated(listener: (event: { params: Protocol.Runtime.ExecutionContextCreatedEvent }) => void): void;
        onceExecutionContextCreated(eventMatcher?: (event: { params: Protocol.Runtime.ExecutionContextCreatedEvent }) => boolean): Promise<{ params: Protocol.Runtime.ExecutionContextCreatedEvent }>;

        /**
         * Issued when execution context is destroyed.
         */
        onExecutionContextDestroyed(listener: (event: { params: Protocol.Runtime.ExecutionContextDestroyedEvent }) => void): void;
        offExecutionContextDestroyed(listener: (event: { params: Protocol.Runtime.ExecutionContextDestroyedEvent }) => void): void;
        onceExecutionContextDestroyed(eventMatcher?: (event: { params: Protocol.Runtime.ExecutionContextDestroyedEvent }) => boolean): Promise<{ params: Protocol.Runtime.ExecutionContextDestroyedEvent }>;

        /**
         * Issued when all executionContexts were cleared in browser
         */
        onExecutionContextsCleared(listener: (event: ) => void): void;
        offExecutionContextsCleared(listener: (event: ) => void): void;
        onceExecutionContextsCleared(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Issued when object should be inspected (for example, as a result of inspect() command line API
         * call).
         */
        onInspectRequested(listener: (event: { params: Protocol.Runtime.InspectRequestedEvent }) => void): void;
        offInspectRequested(listener: (event: { params: Protocol.Runtime.InspectRequestedEvent }) => void): void;
        onceInspectRequested(eventMatcher?: (event: { params: Protocol.Runtime.InspectRequestedEvent }) => boolean): Promise<{ params: Protocol.Runtime.InspectRequestedEvent }>;

    }

    export interface SchemaApi {
        /**
         * Returns supported domains.
         */
        getDomains(): Promise<{id: number, result: Protocol.Schema.GetDomainsResponse, sessionId: string}>;

    }

    export interface AccessibilityApi {
        /**
         * Disables the accessibility domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables the accessibility domain which causes `AXNodeId`s to remain consistent between method calls.
         * This turns on accessibility for the page, which can impact performance until accessibility is disabled.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fetches the accessibility node and partial accessibility tree for this DOM node, if it exists.
         */
        getPartialAXTree(params: Protocol.Accessibility.GetPartialAXTreeRequest): Promise<{id: number, result: Protocol.Accessibility.GetPartialAXTreeResponse, sessionId: string}>;

        /**
         * Fetches the entire accessibility tree for the root Document
         */
        getFullAXTree(params: Protocol.Accessibility.GetFullAXTreeRequest): Promise<{id: number, result: Protocol.Accessibility.GetFullAXTreeResponse, sessionId: string}>;

        /**
         * Fetches the root node.
         * Requires `enable()` to have been called previously.
         */
        getRootAXNode(params: Protocol.Accessibility.GetRootAXNodeRequest): Promise<{id: number, result: Protocol.Accessibility.GetRootAXNodeResponse, sessionId: string}>;

        /**
         * Fetches a node and all ancestors up to and including the root.
         * Requires `enable()` to have been called previously.
         */
        getAXNodeAndAncestors(params: Protocol.Accessibility.GetAXNodeAndAncestorsRequest): Promise<{id: number, result: Protocol.Accessibility.GetAXNodeAndAncestorsResponse, sessionId: string}>;

        /**
         * Fetches a particular accessibility node by AXNodeId.
         * Requires `enable()` to have been called previously.
         */
        getChildAXNodes(params: Protocol.Accessibility.GetChildAXNodesRequest): Promise<{id: number, result: Protocol.Accessibility.GetChildAXNodesResponse, sessionId: string}>;

        /**
         * Query a DOM node's accessibility subtree for accessible name and role.
         * This command computes the name and role for all nodes in the subtree, including those that are
         * ignored for accessibility, and returns those that match the specified name and role. If no DOM
         * node is specified, or the DOM node does not exist, the command returns an error. If neither
         * `accessibleName` or `role` is specified, it returns all the accessibility nodes in the subtree.
         */
        queryAXTree(params: Protocol.Accessibility.QueryAXTreeRequest): Promise<{id: number, result: Protocol.Accessibility.QueryAXTreeResponse, sessionId: string}>;

        /**
         * The loadComplete event mirrors the load complete event sent by the browser to assistive
         * technology when the web page has finished loading.
         */
        onLoadComplete(listener: (event: { params: Protocol.Accessibility.LoadCompleteEvent }) => void): void;
        offLoadComplete(listener: (event: { params: Protocol.Accessibility.LoadCompleteEvent }) => void): void;
        onceLoadComplete(eventMatcher?: (event: { params: Protocol.Accessibility.LoadCompleteEvent }) => boolean): Promise<{ params: Protocol.Accessibility.LoadCompleteEvent }>;

        /**
         * The nodesUpdated event is sent every time a previously requested node has changed the in tree.
         */
        onNodesUpdated(listener: (event: { params: Protocol.Accessibility.NodesUpdatedEvent }) => void): void;
        offNodesUpdated(listener: (event: { params: Protocol.Accessibility.NodesUpdatedEvent }) => void): void;
        onceNodesUpdated(eventMatcher?: (event: { params: Protocol.Accessibility.NodesUpdatedEvent }) => boolean): Promise<{ params: Protocol.Accessibility.NodesUpdatedEvent }>;

    }

    export interface AnimationApi {
        /**
         * Disables animation domain notifications.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables animation domain notifications.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns the current time of the an animation.
         */
        getCurrentTime(params: Protocol.Animation.GetCurrentTimeRequest): Promise<{id: number, result: Protocol.Animation.GetCurrentTimeResponse, sessionId: string}>;

        /**
         * Gets the playback rate of the document timeline.
         */
        getPlaybackRate(): Promise<{id: number, result: Protocol.Animation.GetPlaybackRateResponse, sessionId: string}>;

        /**
         * Releases a set of animations to no longer be manipulated.
         */
        releaseAnimations(params: Protocol.Animation.ReleaseAnimationsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Gets the remote object of the Animation.
         */
        resolveAnimation(params: Protocol.Animation.ResolveAnimationRequest): Promise<{id: number, result: Protocol.Animation.ResolveAnimationResponse, sessionId: string}>;

        /**
         * Seek a set of animations to a particular time within each animation.
         */
        seekAnimations(params: Protocol.Animation.SeekAnimationsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets the paused state of a set of animations.
         */
        setPaused(params: Protocol.Animation.SetPausedRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets the playback rate of the document timeline.
         */
        setPlaybackRate(params: Protocol.Animation.SetPlaybackRateRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets the timing of an animation node.
         */
        setTiming(params: Protocol.Animation.SetTimingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Event for when an animation has been cancelled.
         */
        onAnimationCanceled(listener: (event: { params: Protocol.Animation.AnimationCanceledEvent }) => void): void;
        offAnimationCanceled(listener: (event: { params: Protocol.Animation.AnimationCanceledEvent }) => void): void;
        onceAnimationCanceled(eventMatcher?: (event: { params: Protocol.Animation.AnimationCanceledEvent }) => boolean): Promise<{ params: Protocol.Animation.AnimationCanceledEvent }>;

        /**
         * Event for each animation that has been created.
         */
        onAnimationCreated(listener: (event: { params: Protocol.Animation.AnimationCreatedEvent }) => void): void;
        offAnimationCreated(listener: (event: { params: Protocol.Animation.AnimationCreatedEvent }) => void): void;
        onceAnimationCreated(eventMatcher?: (event: { params: Protocol.Animation.AnimationCreatedEvent }) => boolean): Promise<{ params: Protocol.Animation.AnimationCreatedEvent }>;

        /**
         * Event for animation that has been started.
         */
        onAnimationStarted(listener: (event: { params: Protocol.Animation.AnimationStartedEvent }) => void): void;
        offAnimationStarted(listener: (event: { params: Protocol.Animation.AnimationStartedEvent }) => void): void;
        onceAnimationStarted(eventMatcher?: (event: { params: Protocol.Animation.AnimationStartedEvent }) => boolean): Promise<{ params: Protocol.Animation.AnimationStartedEvent }>;

        /**
         * Event for animation that has been updated.
         */
        onAnimationUpdated(listener: (event: { params: Protocol.Animation.AnimationUpdatedEvent }) => void): void;
        offAnimationUpdated(listener: (event: { params: Protocol.Animation.AnimationUpdatedEvent }) => void): void;
        onceAnimationUpdated(eventMatcher?: (event: { params: Protocol.Animation.AnimationUpdatedEvent }) => boolean): Promise<{ params: Protocol.Animation.AnimationUpdatedEvent }>;

    }

    export interface AuditsApi {
        /**
         * Returns the response body and size if it were re-encoded with the specified settings. Only
         * applies to images.
         */
        getEncodedResponse(params: Protocol.Audits.GetEncodedResponseRequest): Promise<{id: number, result: Protocol.Audits.GetEncodedResponseResponse, sessionId: string}>;

        /**
         * Disables issues domain, prevents further issues from being reported to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables issues domain, sends the issues collected so far to the client by means of the
         * `issueAdded` event.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Runs the contrast check for the target page. Found issues are reported
         * using Audits.issueAdded event.
         */
        checkContrast(params: Protocol.Audits.CheckContrastRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Runs the form issues check for the target page. Found issues are reported
         * using Audits.issueAdded event.
         */
        checkFormsIssues(): Promise<{id: number, result: Protocol.Audits.CheckFormsIssuesResponse, sessionId: string}>;

        onIssueAdded(listener: (event: { params: Protocol.Audits.IssueAddedEvent }) => void): void;
        offIssueAdded(listener: (event: { params: Protocol.Audits.IssueAddedEvent }) => void): void;
        onceIssueAdded(eventMatcher?: (event: { params: Protocol.Audits.IssueAddedEvent }) => boolean): Promise<{ params: Protocol.Audits.IssueAddedEvent }>;

    }

    export interface ExtensionsApi {
        /**
         * Installs an unpacked extension from the filesystem similar to
         * --load-extension CLI flags. Returns extension ID once the extension
         * has been installed.
         */
        loadUnpacked(params: Protocol.Extensions.LoadUnpackedRequest): Promise<{id: number, result: Protocol.Extensions.LoadUnpackedResponse, sessionId: string}>;

    }

    export interface AutofillApi {
        /**
         * Trigger autofill on a form identified by the fieldId.
         * If the field and related form cannot be autofilled, returns an error.
         */
        trigger(params: Protocol.Autofill.TriggerRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set addresses so that developers can verify their forms implementation.
         */
        setAddresses(params: Protocol.Autofill.SetAddressesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables autofill domain notifications.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables autofill domain notifications.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Emitted when an address form is filled.
         */
        onAddressFormFilled(listener: (event: { params: Protocol.Autofill.AddressFormFilledEvent }) => void): void;
        offAddressFormFilled(listener: (event: { params: Protocol.Autofill.AddressFormFilledEvent }) => void): void;
        onceAddressFormFilled(eventMatcher?: (event: { params: Protocol.Autofill.AddressFormFilledEvent }) => boolean): Promise<{ params: Protocol.Autofill.AddressFormFilledEvent }>;

    }

    export interface BackgroundServiceApi {
        /**
         * Enables event updates for the service.
         */
        startObserving(params: Protocol.BackgroundService.StartObservingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables event updates for the service.
         */
        stopObserving(params: Protocol.BackgroundService.StopObservingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set the recording state for the service.
         */
        setRecording(params: Protocol.BackgroundService.SetRecordingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears all stored data for the service.
         */
        clearEvents(params: Protocol.BackgroundService.ClearEventsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Called when the recording state for the service has been updated.
         */
        onRecordingStateChanged(listener: (event: { params: Protocol.BackgroundService.RecordingStateChangedEvent }) => void): void;
        offRecordingStateChanged(listener: (event: { params: Protocol.BackgroundService.RecordingStateChangedEvent }) => void): void;
        onceRecordingStateChanged(eventMatcher?: (event: { params: Protocol.BackgroundService.RecordingStateChangedEvent }) => boolean): Promise<{ params: Protocol.BackgroundService.RecordingStateChangedEvent }>;

        /**
         * Called with all existing backgroundServiceEvents when enabled, and all new
         * events afterwards if enabled and recording.
         */
        onBackgroundServiceEventReceived(listener: (event: { params: Protocol.BackgroundService.BackgroundServiceEventReceivedEvent }) => void): void;
        offBackgroundServiceEventReceived(listener: (event: { params: Protocol.BackgroundService.BackgroundServiceEventReceivedEvent }) => void): void;
        onceBackgroundServiceEventReceived(eventMatcher?: (event: { params: Protocol.BackgroundService.BackgroundServiceEventReceivedEvent }) => boolean): Promise<{ params: Protocol.BackgroundService.BackgroundServiceEventReceivedEvent }>;

    }

    export interface BrowserApi {
        /**
         * Set permission settings for given origin.
         */
        setPermission(params: Protocol.Browser.SetPermissionRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Grant specific permissions to the given origin and reject all others.
         */
        grantPermissions(params: Protocol.Browser.GrantPermissionsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Reset all permission management for all origins.
         */
        resetPermissions(params: Protocol.Browser.ResetPermissionsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set the behavior when downloading a file.
         */
        setDownloadBehavior(params: Protocol.Browser.SetDownloadBehaviorRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Cancel a download if in progress
         */
        cancelDownload(params: Protocol.Browser.CancelDownloadRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Close browser gracefully.
         */
        close(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Crashes browser on the main thread.
         */
        crash(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Crashes GPU process.
         */
        crashGpuProcess(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns version information.
         */
        getVersion(): Promise<{id: number, result: Protocol.Browser.GetVersionResponse, sessionId: string}>;

        /**
         * Returns the command line switches for the browser process if, and only if
         * --enable-automation is on the commandline.
         */
        getBrowserCommandLine(): Promise<{id: number, result: Protocol.Browser.GetBrowserCommandLineResponse, sessionId: string}>;

        /**
         * Get Chrome histograms.
         */
        getHistograms(params: Protocol.Browser.GetHistogramsRequest): Promise<{id: number, result: Protocol.Browser.GetHistogramsResponse, sessionId: string}>;

        /**
         * Get a Chrome histogram by name.
         */
        getHistogram(params: Protocol.Browser.GetHistogramRequest): Promise<{id: number, result: Protocol.Browser.GetHistogramResponse, sessionId: string}>;

        /**
         * Get position and size of the browser window.
         */
        getWindowBounds(params: Protocol.Browser.GetWindowBoundsRequest): Promise<{id: number, result: Protocol.Browser.GetWindowBoundsResponse, sessionId: string}>;

        /**
         * Get the browser window that contains the devtools target.
         */
        getWindowForTarget(params: Protocol.Browser.GetWindowForTargetRequest): Promise<{id: number, result: Protocol.Browser.GetWindowForTargetResponse, sessionId: string}>;

        /**
         * Set position and/or size of the browser window.
         */
        setWindowBounds(params: Protocol.Browser.SetWindowBoundsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set dock tile details, platform-specific.
         */
        setDockTile(params: Protocol.Browser.SetDockTileRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Invoke custom browser commands used by telemetry.
         */
        executeBrowserCommand(params: Protocol.Browser.ExecuteBrowserCommandRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Allows a site to use privacy sandbox features that require enrollment
         * without the site actually being enrolled. Only supported on page targets.
         */
        addPrivacySandboxEnrollmentOverride(params: Protocol.Browser.AddPrivacySandboxEnrollmentOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fired when page is about to start a download.
         */
        onDownloadWillBegin(listener: (event: { params: Protocol.Browser.DownloadWillBeginEvent }) => void): void;
        offDownloadWillBegin(listener: (event: { params: Protocol.Browser.DownloadWillBeginEvent }) => void): void;
        onceDownloadWillBegin(eventMatcher?: (event: { params: Protocol.Browser.DownloadWillBeginEvent }) => boolean): Promise<{ params: Protocol.Browser.DownloadWillBeginEvent }>;

        /**
         * Fired when download makes progress. Last call has |done| == true.
         */
        onDownloadProgress(listener: (event: { params: Protocol.Browser.DownloadProgressEvent }) => void): void;
        offDownloadProgress(listener: (event: { params: Protocol.Browser.DownloadProgressEvent }) => void): void;
        onceDownloadProgress(eventMatcher?: (event: { params: Protocol.Browser.DownloadProgressEvent }) => boolean): Promise<{ params: Protocol.Browser.DownloadProgressEvent }>;

    }

    export interface CSSApi {
        /**
         * Inserts a new rule with the given `ruleText` in a stylesheet with given `styleSheetId`, at the
         * position specified by `location`.
         */
        addRule(params: Protocol.CSS.AddRuleRequest): Promise<{id: number, result: Protocol.CSS.AddRuleResponse, sessionId: string}>;

        /**
         * Returns all class names from specified stylesheet.
         */
        collectClassNames(params: Protocol.CSS.CollectClassNamesRequest): Promise<{id: number, result: Protocol.CSS.CollectClassNamesResponse, sessionId: string}>;

        /**
         * Creates a new special "via-inspector" stylesheet in the frame with given `frameId`.
         */
        createStyleSheet(params: Protocol.CSS.CreateStyleSheetRequest): Promise<{id: number, result: Protocol.CSS.CreateStyleSheetResponse, sessionId: string}>;

        /**
         * Disables the CSS agent for the given page.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been
         * enabled until the result of this command is received.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Ensures that the given node will have specified pseudo-classes whenever its style is computed by
         * the browser.
         */
        forcePseudoState(params: Protocol.CSS.ForcePseudoStateRequest): Promise<{id: number, result: void, sessionId: string}>;

        getBackgroundColors(params: Protocol.CSS.GetBackgroundColorsRequest): Promise<{id: number, result: Protocol.CSS.GetBackgroundColorsResponse, sessionId: string}>;

        /**
         * Returns the computed style for a DOM node identified by `nodeId`.
         */
        getComputedStyleForNode(params: Protocol.CSS.GetComputedStyleForNodeRequest): Promise<{id: number, result: Protocol.CSS.GetComputedStyleForNodeResponse, sessionId: string}>;

        /**
         * Returns the styles defined inline (explicitly in the "style" attribute and implicitly, using DOM
         * attributes) for a DOM node identified by `nodeId`.
         */
        getInlineStylesForNode(params: Protocol.CSS.GetInlineStylesForNodeRequest): Promise<{id: number, result: Protocol.CSS.GetInlineStylesForNodeResponse, sessionId: string}>;

        /**
         * Returns requested styles for a DOM node identified by `nodeId`.
         */
        getMatchedStylesForNode(params: Protocol.CSS.GetMatchedStylesForNodeRequest): Promise<{id: number, result: Protocol.CSS.GetMatchedStylesForNodeResponse, sessionId: string}>;

        /**
         * Returns all media queries parsed by the rendering engine.
         */
        getMediaQueries(): Promise<{id: number, result: Protocol.CSS.GetMediaQueriesResponse, sessionId: string}>;

        /**
         * Requests information about platform fonts which we used to render child TextNodes in the given
         * node.
         */
        getPlatformFontsForNode(params: Protocol.CSS.GetPlatformFontsForNodeRequest): Promise<{id: number, result: Protocol.CSS.GetPlatformFontsForNodeResponse, sessionId: string}>;

        /**
         * Returns the current textual content for a stylesheet.
         */
        getStyleSheetText(params: Protocol.CSS.GetStyleSheetTextRequest): Promise<{id: number, result: Protocol.CSS.GetStyleSheetTextResponse, sessionId: string}>;

        /**
         * Returns all layers parsed by the rendering engine for the tree scope of a node.
         * Given a DOM element identified by nodeId, getLayersForNode returns the root
         * layer for the nearest ancestor document or shadow root. The layer root contains
         * the full layer tree for the tree scope and their ordering.
         */
        getLayersForNode(params: Protocol.CSS.GetLayersForNodeRequest): Promise<{id: number, result: Protocol.CSS.GetLayersForNodeResponse, sessionId: string}>;

        /**
         * Given a CSS selector text and a style sheet ID, getLocationForSelector
         * returns an array of locations of the CSS selector in the style sheet.
         */
        getLocationForSelector(params: Protocol.CSS.GetLocationForSelectorRequest): Promise<{id: number, result: Protocol.CSS.GetLocationForSelectorResponse, sessionId: string}>;

        /**
         * Starts tracking the given computed styles for updates. The specified array of properties
         * replaces the one previously specified. Pass empty array to disable tracking.
         * Use takeComputedStyleUpdates to retrieve the list of nodes that had properties modified.
         * The changes to computed style properties are only tracked for nodes pushed to the front-end
         * by the DOM agent. If no changes to the tracked properties occur after the node has been pushed
         * to the front-end, no updates will be issued for the node.
         */
        trackComputedStyleUpdates(params: Protocol.CSS.TrackComputedStyleUpdatesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Polls the next batch of computed style updates.
         */
        takeComputedStyleUpdates(): Promise<{id: number, result: Protocol.CSS.TakeComputedStyleUpdatesResponse, sessionId: string}>;

        /**
         * Find a rule with the given active property for the given node and set the new value for this
         * property
         */
        setEffectivePropertyValueForNode(params: Protocol.CSS.SetEffectivePropertyValueForNodeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Modifies the property rule property name.
         */
        setPropertyRulePropertyName(params: Protocol.CSS.SetPropertyRulePropertyNameRequest): Promise<{id: number, result: Protocol.CSS.SetPropertyRulePropertyNameResponse, sessionId: string}>;

        /**
         * Modifies the keyframe rule key text.
         */
        setKeyframeKey(params: Protocol.CSS.SetKeyframeKeyRequest): Promise<{id: number, result: Protocol.CSS.SetKeyframeKeyResponse, sessionId: string}>;

        /**
         * Modifies the rule selector.
         */
        setMediaText(params: Protocol.CSS.SetMediaTextRequest): Promise<{id: number, result: Protocol.CSS.SetMediaTextResponse, sessionId: string}>;

        /**
         * Modifies the expression of a container query.
         */
        setContainerQueryText(params: Protocol.CSS.SetContainerQueryTextRequest): Promise<{id: number, result: Protocol.CSS.SetContainerQueryTextResponse, sessionId: string}>;

        /**
         * Modifies the expression of a supports at-rule.
         */
        setSupportsText(params: Protocol.CSS.SetSupportsTextRequest): Promise<{id: number, result: Protocol.CSS.SetSupportsTextResponse, sessionId: string}>;

        /**
         * Modifies the expression of a scope at-rule.
         */
        setScopeText(params: Protocol.CSS.SetScopeTextRequest): Promise<{id: number, result: Protocol.CSS.SetScopeTextResponse, sessionId: string}>;

        /**
         * Modifies the rule selector.
         */
        setRuleSelector(params: Protocol.CSS.SetRuleSelectorRequest): Promise<{id: number, result: Protocol.CSS.SetRuleSelectorResponse, sessionId: string}>;

        /**
         * Sets the new stylesheet text.
         */
        setStyleSheetText(params: Protocol.CSS.SetStyleSheetTextRequest): Promise<{id: number, result: Protocol.CSS.SetStyleSheetTextResponse, sessionId: string}>;

        /**
         * Applies specified style edits one after another in the given order.
         */
        setStyleTexts(params: Protocol.CSS.SetStyleTextsRequest): Promise<{id: number, result: Protocol.CSS.SetStyleTextsResponse, sessionId: string}>;

        /**
         * Enables the selector recording.
         */
        startRuleUsageTracking(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stop tracking rule usage and return the list of rules that were used since last call to
         * `takeCoverageDelta` (or since start of coverage instrumentation).
         */
        stopRuleUsageTracking(): Promise<{id: number, result: Protocol.CSS.StopRuleUsageTrackingResponse, sessionId: string}>;

        /**
         * Obtain list of rules that became used since last call to this method (or since start of coverage
         * instrumentation).
         */
        takeCoverageDelta(): Promise<{id: number, result: Protocol.CSS.TakeCoverageDeltaResponse, sessionId: string}>;

        /**
         * Enables/disables rendering of local CSS fonts (enabled by default).
         */
        setLocalFontsEnabled(params: Protocol.CSS.SetLocalFontsEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded
         * web font.
         */
        onFontsUpdated(listener: (event: { params: Protocol.CSS.FontsUpdatedEvent }) => void): void;
        offFontsUpdated(listener: (event: { params: Protocol.CSS.FontsUpdatedEvent }) => void): void;
        onceFontsUpdated(eventMatcher?: (event: { params: Protocol.CSS.FontsUpdatedEvent }) => boolean): Promise<{ params: Protocol.CSS.FontsUpdatedEvent }>;

        /**
         * Fires whenever a MediaQuery result changes (for example, after a browser window has been
         * resized.) The current implementation considers only viewport-dependent media features.
         */
        onMediaQueryResultChanged(listener: (event: ) => void): void;
        offMediaQueryResultChanged(listener: (event: ) => void): void;
        onceMediaQueryResultChanged(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired whenever an active document stylesheet is added.
         */
        onStyleSheetAdded(listener: (event: { params: Protocol.CSS.StyleSheetAddedEvent }) => void): void;
        offStyleSheetAdded(listener: (event: { params: Protocol.CSS.StyleSheetAddedEvent }) => void): void;
        onceStyleSheetAdded(eventMatcher?: (event: { params: Protocol.CSS.StyleSheetAddedEvent }) => boolean): Promise<{ params: Protocol.CSS.StyleSheetAddedEvent }>;

        /**
         * Fired whenever a stylesheet is changed as a result of the client operation.
         */
        onStyleSheetChanged(listener: (event: { params: Protocol.CSS.StyleSheetChangedEvent }) => void): void;
        offStyleSheetChanged(listener: (event: { params: Protocol.CSS.StyleSheetChangedEvent }) => void): void;
        onceStyleSheetChanged(eventMatcher?: (event: { params: Protocol.CSS.StyleSheetChangedEvent }) => boolean): Promise<{ params: Protocol.CSS.StyleSheetChangedEvent }>;

        /**
         * Fired whenever an active document stylesheet is removed.
         */
        onStyleSheetRemoved(listener: (event: { params: Protocol.CSS.StyleSheetRemovedEvent }) => void): void;
        offStyleSheetRemoved(listener: (event: { params: Protocol.CSS.StyleSheetRemovedEvent }) => void): void;
        onceStyleSheetRemoved(eventMatcher?: (event: { params: Protocol.CSS.StyleSheetRemovedEvent }) => boolean): Promise<{ params: Protocol.CSS.StyleSheetRemovedEvent }>;

    }

    export interface CacheStorageApi {
        /**
         * Deletes a cache.
         */
        deleteCache(params: Protocol.CacheStorage.DeleteCacheRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes a cache entry.
         */
        deleteEntry(params: Protocol.CacheStorage.DeleteEntryRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests cache names.
         */
        requestCacheNames(params: Protocol.CacheStorage.RequestCacheNamesRequest): Promise<{id: number, result: Protocol.CacheStorage.RequestCacheNamesResponse, sessionId: string}>;

        /**
         * Fetches cache entry.
         */
        requestCachedResponse(params: Protocol.CacheStorage.RequestCachedResponseRequest): Promise<{id: number, result: Protocol.CacheStorage.RequestCachedResponseResponse, sessionId: string}>;

        /**
         * Requests data from cache.
         */
        requestEntries(params: Protocol.CacheStorage.RequestEntriesRequest): Promise<{id: number, result: Protocol.CacheStorage.RequestEntriesResponse, sessionId: string}>;

    }

    export interface CastApi {
        /**
         * Starts observing for sinks that can be used for tab mirroring, and if set,
         * sinks compatible with |presentationUrl| as well. When sinks are found, a
         * |sinksUpdated| event is fired.
         * Also starts observing for issue messages. When an issue is added or removed,
         * an |issueUpdated| event is fired.
         */
        enable(params: Protocol.Cast.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stops observing for sinks and issues.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets a sink to be used when the web page requests the browser to choose a
         * sink via Presentation API, Remote Playback API, or Cast SDK.
         */
        setSinkToUse(params: Protocol.Cast.SetSinkToUseRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Starts mirroring the desktop to the sink.
         */
        startDesktopMirroring(params: Protocol.Cast.StartDesktopMirroringRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Starts mirroring the tab to the sink.
         */
        startTabMirroring(params: Protocol.Cast.StartTabMirroringRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stops the active Cast session on the sink.
         */
        stopCasting(params: Protocol.Cast.StopCastingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This is fired whenever the list of available sinks changes. A sink is a
         * device or a software surface that you can cast to.
         */
        onSinksUpdated(listener: (event: { params: Protocol.Cast.SinksUpdatedEvent }) => void): void;
        offSinksUpdated(listener: (event: { params: Protocol.Cast.SinksUpdatedEvent }) => void): void;
        onceSinksUpdated(eventMatcher?: (event: { params: Protocol.Cast.SinksUpdatedEvent }) => boolean): Promise<{ params: Protocol.Cast.SinksUpdatedEvent }>;

        /**
         * This is fired whenever the outstanding issue/error message changes.
         * |issueMessage| is empty if there is no issue.
         */
        onIssueUpdated(listener: (event: { params: Protocol.Cast.IssueUpdatedEvent }) => void): void;
        offIssueUpdated(listener: (event: { params: Protocol.Cast.IssueUpdatedEvent }) => void): void;
        onceIssueUpdated(eventMatcher?: (event: { params: Protocol.Cast.IssueUpdatedEvent }) => boolean): Promise<{ params: Protocol.Cast.IssueUpdatedEvent }>;

    }

    export interface DOMApi {
        /**
         * Collects class names for the node with given id and all of it's child nodes.
         */
        collectClassNamesFromSubtree(params: Protocol.DOM.CollectClassNamesFromSubtreeRequest): Promise<{id: number, result: Protocol.DOM.CollectClassNamesFromSubtreeResponse, sessionId: string}>;

        /**
         * Creates a deep copy of the specified node and places it into the target container before the
         * given anchor.
         */
        copyTo(params: Protocol.DOM.CopyToRequest): Promise<{id: number, result: Protocol.DOM.CopyToResponse, sessionId: string}>;

        /**
         * Describes node given its id, does not require domain to be enabled. Does not start tracking any
         * objects, can be used for automation.
         */
        describeNode(params: Protocol.DOM.DescribeNodeRequest): Promise<{id: number, result: Protocol.DOM.DescribeNodeResponse, sessionId: string}>;

        /**
         * Scrolls the specified rect of the given node into view if not already visible.
         * Note: exactly one between nodeId, backendNodeId and objectId should be passed
         * to identify the node.
         */
        scrollIntoViewIfNeeded(params: Protocol.DOM.ScrollIntoViewIfNeededRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables DOM agent for the given page.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Discards search results from the session with the given id. `getSearchResults` should no longer
         * be called for that search.
         */
        discardSearchResults(params: Protocol.DOM.DiscardSearchResultsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables DOM agent for the given page.
         */
        enable(params: Protocol.DOM.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Focuses the given element.
         */
        focus(params: Protocol.DOM.FocusRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns attributes for the specified node.
         */
        getAttributes(params: Protocol.DOM.GetAttributesRequest): Promise<{id: number, result: Protocol.DOM.GetAttributesResponse, sessionId: string}>;

        /**
         * Returns boxes for the given node.
         */
        getBoxModel(params: Protocol.DOM.GetBoxModelRequest): Promise<{id: number, result: Protocol.DOM.GetBoxModelResponse, sessionId: string}>;

        /**
         * Returns quads that describe node position on the page. This method
         * might return multiple quads for inline nodes.
         */
        getContentQuads(params: Protocol.DOM.GetContentQuadsRequest): Promise<{id: number, result: Protocol.DOM.GetContentQuadsResponse, sessionId: string}>;

        /**
         * Returns the root DOM node (and optionally the subtree) to the caller.
         * Implicitly enables the DOM domain events for the current target.
         */
        getDocument(params: Protocol.DOM.GetDocumentRequest): Promise<{id: number, result: Protocol.DOM.GetDocumentResponse, sessionId: string}>;

        /**
         * Returns the root DOM node (and optionally the subtree) to the caller.
         * Deprecated, as it is not designed to work well with the rest of the DOM agent.
         * Use DOMSnapshot.captureSnapshot instead.
         */
        getFlattenedDocument(params: Protocol.DOM.GetFlattenedDocumentRequest): Promise<{id: number, result: Protocol.DOM.GetFlattenedDocumentResponse, sessionId: string}>;

        /**
         * Finds nodes with a given computed style in a subtree.
         */
        getNodesForSubtreeByStyle(params: Protocol.DOM.GetNodesForSubtreeByStyleRequest): Promise<{id: number, result: Protocol.DOM.GetNodesForSubtreeByStyleResponse, sessionId: string}>;

        /**
         * Returns node id at given location. Depending on whether DOM domain is enabled, nodeId is
         * either returned or not.
         */
        getNodeForLocation(params: Protocol.DOM.GetNodeForLocationRequest): Promise<{id: number, result: Protocol.DOM.GetNodeForLocationResponse, sessionId: string}>;

        /**
         * Returns node's HTML markup.
         */
        getOuterHTML(params: Protocol.DOM.GetOuterHTMLRequest): Promise<{id: number, result: Protocol.DOM.GetOuterHTMLResponse, sessionId: string}>;

        /**
         * Returns the id of the nearest ancestor that is a relayout boundary.
         */
        getRelayoutBoundary(params: Protocol.DOM.GetRelayoutBoundaryRequest): Promise<{id: number, result: Protocol.DOM.GetRelayoutBoundaryResponse, sessionId: string}>;

        /**
         * Returns search results from given `fromIndex` to given `toIndex` from the search with the given
         * identifier.
         */
        getSearchResults(params: Protocol.DOM.GetSearchResultsRequest): Promise<{id: number, result: Protocol.DOM.GetSearchResultsResponse, sessionId: string}>;

        /**
         * Hides any highlight.
         */
        hideHighlight(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights DOM node.
         */
        highlightNode(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights given rectangle.
         */
        highlightRect(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Marks last undoable state.
         */
        markUndoableState(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Moves node into the new container, places it before the given anchor.
         */
        moveTo(params: Protocol.DOM.MoveToRequest): Promise<{id: number, result: Protocol.DOM.MoveToResponse, sessionId: string}>;

        /**
         * Searches for a given string in the DOM tree. Use `getSearchResults` to access search results or
         * `cancelSearch` to end this search session.
         */
        performSearch(params: Protocol.DOM.PerformSearchRequest): Promise<{id: number, result: Protocol.DOM.PerformSearchResponse, sessionId: string}>;

        /**
         * Requests that the node is sent to the caller given its path. // FIXME, use XPath
         */
        pushNodeByPathToFrontend(params: Protocol.DOM.PushNodeByPathToFrontendRequest): Promise<{id: number, result: Protocol.DOM.PushNodeByPathToFrontendResponse, sessionId: string}>;

        /**
         * Requests that a batch of nodes is sent to the caller given their backend node ids.
         */
        pushNodesByBackendIdsToFrontend(params: Protocol.DOM.PushNodesByBackendIdsToFrontendRequest): Promise<{id: number, result: Protocol.DOM.PushNodesByBackendIdsToFrontendResponse, sessionId: string}>;

        /**
         * Executes `querySelector` on a given node.
         */
        querySelector(params: Protocol.DOM.QuerySelectorRequest): Promise<{id: number, result: Protocol.DOM.QuerySelectorResponse, sessionId: string}>;

        /**
         * Executes `querySelectorAll` on a given node.
         */
        querySelectorAll(params: Protocol.DOM.QuerySelectorAllRequest): Promise<{id: number, result: Protocol.DOM.QuerySelectorAllResponse, sessionId: string}>;

        /**
         * Returns NodeIds of current top layer elements.
         * Top layer is rendered closest to the user within a viewport, therefore its elements always
         * appear on top of all other content.
         */
        getTopLayerElements(): Promise<{id: number, result: Protocol.DOM.GetTopLayerElementsResponse, sessionId: string}>;

        /**
         * Returns the NodeId of the matched element according to certain relations.
         */
        getElementByRelation(params: Protocol.DOM.GetElementByRelationRequest): Promise<{id: number, result: Protocol.DOM.GetElementByRelationResponse, sessionId: string}>;

        /**
         * Re-does the last undone action.
         */
        redo(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes attribute with given name from an element with given id.
         */
        removeAttribute(params: Protocol.DOM.RemoveAttributeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes node with given id.
         */
        removeNode(params: Protocol.DOM.RemoveNodeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that children of the node with given id are returned to the caller in form of
         * `setChildNodes` events where not only immediate children are retrieved, but all children down to
         * the specified depth.
         */
        requestChildNodes(params: Protocol.DOM.RequestChildNodesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that the node is sent to the caller given the JavaScript node object reference. All
         * nodes that form the path from the node to the root are also sent to the client as a series of
         * `setChildNodes` notifications.
         */
        requestNode(params: Protocol.DOM.RequestNodeRequest): Promise<{id: number, result: Protocol.DOM.RequestNodeResponse, sessionId: string}>;

        /**
         * Resolves the JavaScript node object for a given NodeId or BackendNodeId.
         */
        resolveNode(params: Protocol.DOM.ResolveNodeRequest): Promise<{id: number, result: Protocol.DOM.ResolveNodeResponse, sessionId: string}>;

        /**
         * Sets attribute for an element with given id.
         */
        setAttributeValue(params: Protocol.DOM.SetAttributeValueRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets attributes on element with given id. This method is useful when user edits some existing
         * attribute value and types in several attribute name/value pairs.
         */
        setAttributesAsText(params: Protocol.DOM.SetAttributesAsTextRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets files for the given file input element.
         */
        setFileInputFiles(params: Protocol.DOM.SetFileInputFilesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets if stack traces should be captured for Nodes. See `Node.getNodeStackTraces`. Default is disabled.
         */
        setNodeStackTracesEnabled(params: Protocol.DOM.SetNodeStackTracesEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Gets stack traces associated with a Node. As of now, only provides stack trace for Node creation.
         */
        getNodeStackTraces(params: Protocol.DOM.GetNodeStackTracesRequest): Promise<{id: number, result: Protocol.DOM.GetNodeStackTracesResponse, sessionId: string}>;

        /**
         * Returns file information for the given
         * File wrapper.
         */
        getFileInfo(params: Protocol.DOM.GetFileInfoRequest): Promise<{id: number, result: Protocol.DOM.GetFileInfoResponse, sessionId: string}>;

        /**
         * Enables console to refer to the node with given id via $x (see Command Line API for more details
         * $x functions).
         */
        setInspectedNode(params: Protocol.DOM.SetInspectedNodeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets node name for a node with given id.
         */
        setNodeName(params: Protocol.DOM.SetNodeNameRequest): Promise<{id: number, result: Protocol.DOM.SetNodeNameResponse, sessionId: string}>;

        /**
         * Sets node value for a node with given id.
         */
        setNodeValue(params: Protocol.DOM.SetNodeValueRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets node HTML markup, returns new node id.
         */
        setOuterHTML(params: Protocol.DOM.SetOuterHTMLRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Undoes the last performed action.
         */
        undo(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns iframe node that owns iframe with the given domain.
         */
        getFrameOwner(params: Protocol.DOM.GetFrameOwnerRequest): Promise<{id: number, result: Protocol.DOM.GetFrameOwnerResponse, sessionId: string}>;

        /**
         * Returns the query container of the given node based on container query
         * conditions: containerName, physical, and logical axes. If no axes are
         * provided, the style container is returned, which is the direct parent or the
         * closest element with a matching container-name.
         */
        getContainerForNode(params: Protocol.DOM.GetContainerForNodeRequest): Promise<{id: number, result: Protocol.DOM.GetContainerForNodeResponse, sessionId: string}>;

        /**
         * Returns the descendants of a container query container that have
         * container queries against this container.
         */
        getQueryingDescendantsForContainer(params: Protocol.DOM.GetQueryingDescendantsForContainerRequest): Promise<{id: number, result: Protocol.DOM.GetQueryingDescendantsForContainerResponse, sessionId: string}>;

        /**
         * Returns the target anchor element of the given anchor query according to
         * https://www.w3.org/TR/css-anchor-position-1/#target.
         */
        getAnchorElement(params: Protocol.DOM.GetAnchorElementRequest): Promise<{id: number, result: Protocol.DOM.GetAnchorElementResponse, sessionId: string}>;

        /**
         * Fired when `Element`'s attribute is modified.
         */
        onAttributeModified(listener: (event: { params: Protocol.DOM.AttributeModifiedEvent }) => void): void;
        offAttributeModified(listener: (event: { params: Protocol.DOM.AttributeModifiedEvent }) => void): void;
        onceAttributeModified(eventMatcher?: (event: { params: Protocol.DOM.AttributeModifiedEvent }) => boolean): Promise<{ params: Protocol.DOM.AttributeModifiedEvent }>;

        /**
         * Fired when `Element`'s attribute is removed.
         */
        onAttributeRemoved(listener: (event: { params: Protocol.DOM.AttributeRemovedEvent }) => void): void;
        offAttributeRemoved(listener: (event: { params: Protocol.DOM.AttributeRemovedEvent }) => void): void;
        onceAttributeRemoved(eventMatcher?: (event: { params: Protocol.DOM.AttributeRemovedEvent }) => boolean): Promise<{ params: Protocol.DOM.AttributeRemovedEvent }>;

        /**
         * Mirrors `DOMCharacterDataModified` event.
         */
        onCharacterDataModified(listener: (event: { params: Protocol.DOM.CharacterDataModifiedEvent }) => void): void;
        offCharacterDataModified(listener: (event: { params: Protocol.DOM.CharacterDataModifiedEvent }) => void): void;
        onceCharacterDataModified(eventMatcher?: (event: { params: Protocol.DOM.CharacterDataModifiedEvent }) => boolean): Promise<{ params: Protocol.DOM.CharacterDataModifiedEvent }>;

        /**
         * Fired when `Container`'s child node count has changed.
         */
        onChildNodeCountUpdated(listener: (event: { params: Protocol.DOM.ChildNodeCountUpdatedEvent }) => void): void;
        offChildNodeCountUpdated(listener: (event: { params: Protocol.DOM.ChildNodeCountUpdatedEvent }) => void): void;
        onceChildNodeCountUpdated(eventMatcher?: (event: { params: Protocol.DOM.ChildNodeCountUpdatedEvent }) => boolean): Promise<{ params: Protocol.DOM.ChildNodeCountUpdatedEvent }>;

        /**
         * Mirrors `DOMNodeInserted` event.
         */
        onChildNodeInserted(listener: (event: { params: Protocol.DOM.ChildNodeInsertedEvent }) => void): void;
        offChildNodeInserted(listener: (event: { params: Protocol.DOM.ChildNodeInsertedEvent }) => void): void;
        onceChildNodeInserted(eventMatcher?: (event: { params: Protocol.DOM.ChildNodeInsertedEvent }) => boolean): Promise<{ params: Protocol.DOM.ChildNodeInsertedEvent }>;

        /**
         * Mirrors `DOMNodeRemoved` event.
         */
        onChildNodeRemoved(listener: (event: { params: Protocol.DOM.ChildNodeRemovedEvent }) => void): void;
        offChildNodeRemoved(listener: (event: { params: Protocol.DOM.ChildNodeRemovedEvent }) => void): void;
        onceChildNodeRemoved(eventMatcher?: (event: { params: Protocol.DOM.ChildNodeRemovedEvent }) => boolean): Promise<{ params: Protocol.DOM.ChildNodeRemovedEvent }>;

        /**
         * Called when distribution is changed.
         */
        onDistributedNodesUpdated(listener: (event: { params: Protocol.DOM.DistributedNodesUpdatedEvent }) => void): void;
        offDistributedNodesUpdated(listener: (event: { params: Protocol.DOM.DistributedNodesUpdatedEvent }) => void): void;
        onceDistributedNodesUpdated(eventMatcher?: (event: { params: Protocol.DOM.DistributedNodesUpdatedEvent }) => boolean): Promise<{ params: Protocol.DOM.DistributedNodesUpdatedEvent }>;

        /**
         * Fired when `Document` has been totally updated. Node ids are no longer valid.
         */
        onDocumentUpdated(listener: (event: ) => void): void;
        offDocumentUpdated(listener: (event: ) => void): void;
        onceDocumentUpdated(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when `Element`'s inline style is modified via a CSS property modification.
         */
        onInlineStyleInvalidated(listener: (event: { params: Protocol.DOM.InlineStyleInvalidatedEvent }) => void): void;
        offInlineStyleInvalidated(listener: (event: { params: Protocol.DOM.InlineStyleInvalidatedEvent }) => void): void;
        onceInlineStyleInvalidated(eventMatcher?: (event: { params: Protocol.DOM.InlineStyleInvalidatedEvent }) => boolean): Promise<{ params: Protocol.DOM.InlineStyleInvalidatedEvent }>;

        /**
         * Called when a pseudo element is added to an element.
         */
        onPseudoElementAdded(listener: (event: { params: Protocol.DOM.PseudoElementAddedEvent }) => void): void;
        offPseudoElementAdded(listener: (event: { params: Protocol.DOM.PseudoElementAddedEvent }) => void): void;
        oncePseudoElementAdded(eventMatcher?: (event: { params: Protocol.DOM.PseudoElementAddedEvent }) => boolean): Promise<{ params: Protocol.DOM.PseudoElementAddedEvent }>;

        /**
         * Called when top layer elements are changed.
         */
        onTopLayerElementsUpdated(listener: (event: ) => void): void;
        offTopLayerElementsUpdated(listener: (event: ) => void): void;
        onceTopLayerElementsUpdated(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Called when a pseudo element is removed from an element.
         */
        onPseudoElementRemoved(listener: (event: { params: Protocol.DOM.PseudoElementRemovedEvent }) => void): void;
        offPseudoElementRemoved(listener: (event: { params: Protocol.DOM.PseudoElementRemovedEvent }) => void): void;
        oncePseudoElementRemoved(eventMatcher?: (event: { params: Protocol.DOM.PseudoElementRemovedEvent }) => boolean): Promise<{ params: Protocol.DOM.PseudoElementRemovedEvent }>;

        /**
         * Fired when backend wants to provide client with the missing DOM structure. This happens upon
         * most of the calls requesting node ids.
         */
        onSetChildNodes(listener: (event: { params: Protocol.DOM.SetChildNodesEvent }) => void): void;
        offSetChildNodes(listener: (event: { params: Protocol.DOM.SetChildNodesEvent }) => void): void;
        onceSetChildNodes(eventMatcher?: (event: { params: Protocol.DOM.SetChildNodesEvent }) => boolean): Promise<{ params: Protocol.DOM.SetChildNodesEvent }>;

        /**
         * Called when shadow root is popped from the element.
         */
        onShadowRootPopped(listener: (event: { params: Protocol.DOM.ShadowRootPoppedEvent }) => void): void;
        offShadowRootPopped(listener: (event: { params: Protocol.DOM.ShadowRootPoppedEvent }) => void): void;
        onceShadowRootPopped(eventMatcher?: (event: { params: Protocol.DOM.ShadowRootPoppedEvent }) => boolean): Promise<{ params: Protocol.DOM.ShadowRootPoppedEvent }>;

        /**
         * Called when shadow root is pushed into the element.
         */
        onShadowRootPushed(listener: (event: { params: Protocol.DOM.ShadowRootPushedEvent }) => void): void;
        offShadowRootPushed(listener: (event: { params: Protocol.DOM.ShadowRootPushedEvent }) => void): void;
        onceShadowRootPushed(eventMatcher?: (event: { params: Protocol.DOM.ShadowRootPushedEvent }) => boolean): Promise<{ params: Protocol.DOM.ShadowRootPushedEvent }>;

    }

    export interface DOMDebuggerApi {
        /**
         * Returns event listeners of the given object.
         */
        getEventListeners(params: Protocol.DOMDebugger.GetEventListenersRequest): Promise<{id: number, result: Protocol.DOMDebugger.GetEventListenersResponse, sessionId: string}>;

        /**
         * Removes DOM breakpoint that was set using `setDOMBreakpoint`.
         */
        removeDOMBreakpoint(params: Protocol.DOMDebugger.RemoveDOMBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes breakpoint on particular DOM event.
         */
        removeEventListenerBreakpoint(params: Protocol.DOMDebugger.RemoveEventListenerBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes breakpoint on particular native event.
         */
        removeInstrumentationBreakpoint(params: Protocol.DOMDebugger.RemoveInstrumentationBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes breakpoint from XMLHttpRequest.
         */
        removeXHRBreakpoint(params: Protocol.DOMDebugger.RemoveXHRBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets breakpoint on particular CSP violations.
         */
        setBreakOnCSPViolation(params: Protocol.DOMDebugger.SetBreakOnCSPViolationRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets breakpoint on particular operation with DOM.
         */
        setDOMBreakpoint(params: Protocol.DOMDebugger.SetDOMBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets breakpoint on particular DOM event.
         */
        setEventListenerBreakpoint(params: Protocol.DOMDebugger.SetEventListenerBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets breakpoint on particular native event.
         */
        setInstrumentationBreakpoint(params: Protocol.DOMDebugger.SetInstrumentationBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets breakpoint on XMLHttpRequest.
         */
        setXHRBreakpoint(params: Protocol.DOMDebugger.SetXHRBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

    }

    export interface EventBreakpointsApi {
        /**
         * Sets breakpoint on particular native event.
         */
        setInstrumentationBreakpoint(params: Protocol.EventBreakpoints.SetInstrumentationBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes breakpoint on particular native event.
         */
        removeInstrumentationBreakpoint(params: Protocol.EventBreakpoints.RemoveInstrumentationBreakpointRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes all breakpoints
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

    }

    export interface DOMSnapshotApi {
        /**
         * Disables DOM snapshot agent for the given page.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables DOM snapshot agent for the given page.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns a document snapshot, including the full DOM tree of the root node (including iframes,
         * template contents, and imported documents) in a flattened array, as well as layout and
         * white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
         * flattened.
         */
        getSnapshot(params: Protocol.DOMSnapshot.GetSnapshotRequest): Promise<{id: number, result: Protocol.DOMSnapshot.GetSnapshotResponse, sessionId: string}>;

        /**
         * Returns a document snapshot, including the full DOM tree of the root node (including iframes,
         * template contents, and imported documents) in a flattened array, as well as layout and
         * white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
         * flattened.
         */
        captureSnapshot(params: Protocol.DOMSnapshot.CaptureSnapshotRequest): Promise<{id: number, result: Protocol.DOMSnapshot.CaptureSnapshotResponse, sessionId: string}>;

    }

    export interface DOMStorageApi {
        clear(params: Protocol.DOMStorage.ClearRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables storage tracking, prevents storage events from being sent to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables storage tracking, storage events will now be delivered to the client.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        getDOMStorageItems(params: Protocol.DOMStorage.GetDOMStorageItemsRequest): Promise<{id: number, result: Protocol.DOMStorage.GetDOMStorageItemsResponse, sessionId: string}>;

        removeDOMStorageItem(params: Protocol.DOMStorage.RemoveDOMStorageItemRequest): Promise<{id: number, result: void, sessionId: string}>;

        setDOMStorageItem(params: Protocol.DOMStorage.SetDOMStorageItemRequest): Promise<{id: number, result: void, sessionId: string}>;

        onDomStorageItemAdded(listener: (event: { params: Protocol.DOMStorage.DomStorageItemAddedEvent }) => void): void;
        offDomStorageItemAdded(listener: (event: { params: Protocol.DOMStorage.DomStorageItemAddedEvent }) => void): void;
        onceDomStorageItemAdded(eventMatcher?: (event: { params: Protocol.DOMStorage.DomStorageItemAddedEvent }) => boolean): Promise<{ params: Protocol.DOMStorage.DomStorageItemAddedEvent }>;

        onDomStorageItemRemoved(listener: (event: { params: Protocol.DOMStorage.DomStorageItemRemovedEvent }) => void): void;
        offDomStorageItemRemoved(listener: (event: { params: Protocol.DOMStorage.DomStorageItemRemovedEvent }) => void): void;
        onceDomStorageItemRemoved(eventMatcher?: (event: { params: Protocol.DOMStorage.DomStorageItemRemovedEvent }) => boolean): Promise<{ params: Protocol.DOMStorage.DomStorageItemRemovedEvent }>;

        onDomStorageItemUpdated(listener: (event: { params: Protocol.DOMStorage.DomStorageItemUpdatedEvent }) => void): void;
        offDomStorageItemUpdated(listener: (event: { params: Protocol.DOMStorage.DomStorageItemUpdatedEvent }) => void): void;
        onceDomStorageItemUpdated(eventMatcher?: (event: { params: Protocol.DOMStorage.DomStorageItemUpdatedEvent }) => boolean): Promise<{ params: Protocol.DOMStorage.DomStorageItemUpdatedEvent }>;

        onDomStorageItemsCleared(listener: (event: { params: Protocol.DOMStorage.DomStorageItemsClearedEvent }) => void): void;
        offDomStorageItemsCleared(listener: (event: { params: Protocol.DOMStorage.DomStorageItemsClearedEvent }) => void): void;
        onceDomStorageItemsCleared(eventMatcher?: (event: { params: Protocol.DOMStorage.DomStorageItemsClearedEvent }) => boolean): Promise<{ params: Protocol.DOMStorage.DomStorageItemsClearedEvent }>;

    }

    export interface DatabaseApi {
        /**
         * Disables database tracking, prevents database events from being sent to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables database tracking, database events will now be delivered to the client.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        executeSQL(params: Protocol.Database.ExecuteSQLRequest): Promise<{id: number, result: Protocol.Database.ExecuteSQLResponse, sessionId: string}>;

        getDatabaseTableNames(params: Protocol.Database.GetDatabaseTableNamesRequest): Promise<{id: number, result: Protocol.Database.GetDatabaseTableNamesResponse, sessionId: string}>;

        onAddDatabase(listener: (event: { params: Protocol.Database.AddDatabaseEvent }) => void): void;
        offAddDatabase(listener: (event: { params: Protocol.Database.AddDatabaseEvent }) => void): void;
        onceAddDatabase(eventMatcher?: (event: { params: Protocol.Database.AddDatabaseEvent }) => boolean): Promise<{ params: Protocol.Database.AddDatabaseEvent }>;

    }

    export interface DeviceOrientationApi {
        /**
         * Clears the overridden Device Orientation.
         */
        clearDeviceOrientationOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the Device Orientation.
         */
        setDeviceOrientationOverride(params: Protocol.DeviceOrientation.SetDeviceOrientationOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

    }

    export interface EmulationApi {
        /**
         * Tells whether emulation is supported.
         */
        canEmulate(): Promise<{id: number, result: Protocol.Emulation.CanEmulateResponse, sessionId: string}>;

        /**
         * Clears the overridden device metrics.
         */
        clearDeviceMetricsOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears the overridden Geolocation Position and Error.
         */
        clearGeolocationOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that page scale factor is reset to initial values.
         */
        resetPageScaleFactor(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables or disables simulating a focused and active page.
         */
        setFocusEmulationEnabled(params: Protocol.Emulation.SetFocusEmulationEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Automatically render all web contents using a dark theme.
         */
        setAutoDarkModeOverride(params: Protocol.Emulation.SetAutoDarkModeOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables CPU throttling to emulate slow CPUs.
         */
        setCPUThrottlingRate(params: Protocol.Emulation.SetCPUThrottlingRateRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets or clears an override of the default background color of the frame. This override is used
         * if the content does not specify one.
         */
        setDefaultBackgroundColorOverride(params: Protocol.Emulation.SetDefaultBackgroundColorOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
         * window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
         * query results).
         */
        setDeviceMetricsOverride(params: Protocol.Emulation.SetDeviceMetricsOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Start reporting the given posture value to the Device Posture API.
         * This override can also be set in setDeviceMetricsOverride().
         */
        setDevicePostureOverride(params: Protocol.Emulation.SetDevicePostureOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears a device posture override set with either setDeviceMetricsOverride()
         * or setDevicePostureOverride() and starts using posture information from the
         * platform again.
         * Does nothing if no override is set.
         */
        clearDevicePostureOverride(): Promise<{id: number, result: void, sessionId: string}>;

        setScrollbarsHidden(params: Protocol.Emulation.SetScrollbarsHiddenRequest): Promise<{id: number, result: void, sessionId: string}>;

        setDocumentCookieDisabled(params: Protocol.Emulation.SetDocumentCookieDisabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        setEmitTouchEventsForMouse(params: Protocol.Emulation.SetEmitTouchEventsForMouseRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Emulates the given media type or media feature for CSS media queries.
         */
        setEmulatedMedia(params: Protocol.Emulation.SetEmulatedMediaRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Emulates the given vision deficiency.
         */
        setEmulatedVisionDeficiency(params: Protocol.Emulation.SetEmulatedVisionDeficiencyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position
         * unavailable.
         */
        setGeolocationOverride(params: Protocol.Emulation.SetGeolocationOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        getOverriddenSensorInformation(params: Protocol.Emulation.GetOverriddenSensorInformationRequest): Promise<{id: number, result: Protocol.Emulation.GetOverriddenSensorInformationResponse, sessionId: string}>;

        /**
         * Overrides a platform sensor of a given type. If |enabled| is true, calls to
         * Sensor.start() will use a virtual sensor as backend rather than fetching
         * data from a real hardware sensor. Otherwise, existing virtual
         * sensor-backend Sensor objects will fire an error event and new calls to
         * Sensor.start() will attempt to use a real sensor instead.
         */
        setSensorOverrideEnabled(params: Protocol.Emulation.SetSensorOverrideEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Updates the sensor readings reported by a sensor type previously overridden
         * by setSensorOverrideEnabled.
         */
        setSensorOverrideReadings(params: Protocol.Emulation.SetSensorOverrideReadingsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the Idle state.
         */
        setIdleOverride(params: Protocol.Emulation.SetIdleOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears Idle state overrides.
         */
        clearIdleOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides value returned by the javascript navigator object.
         */
        setNavigatorOverrides(params: Protocol.Emulation.SetNavigatorOverridesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets a specified page scale factor.
         */
        setPageScaleFactor(params: Protocol.Emulation.SetPageScaleFactorRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Switches script execution in the page.
         */
        setScriptExecutionDisabled(params: Protocol.Emulation.SetScriptExecutionDisabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables touch on platforms which do not support them.
         */
        setTouchEmulationEnabled(params: Protocol.Emulation.SetTouchEmulationEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Turns on virtual time for all frames (replacing real-time with a synthetic time source) and sets
         * the current virtual time policy.  Note this supersedes any previous time budget.
         */
        setVirtualTimePolicy(params: Protocol.Emulation.SetVirtualTimePolicyRequest): Promise<{id: number, result: Protocol.Emulation.SetVirtualTimePolicyResponse, sessionId: string}>;

        /**
         * Overrides default host system locale with the specified one.
         */
        setLocaleOverride(params: Protocol.Emulation.SetLocaleOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides default host system timezone with the specified one.
         */
        setTimezoneOverride(params: Protocol.Emulation.SetTimezoneOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Resizes the frame/viewport of the page. Note that this does not affect the frame's container
         * (e.g. browser window). Can be used to produce screenshots of the specified size. Not supported
         * on Android.
         */
        setVisibleSize(params: Protocol.Emulation.SetVisibleSizeRequest): Promise<{id: number, result: void, sessionId: string}>;

        setDisabledImageTypes(params: Protocol.Emulation.SetDisabledImageTypesRequest): Promise<{id: number, result: void, sessionId: string}>;

        setHardwareConcurrencyOverride(params: Protocol.Emulation.SetHardwareConcurrencyOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Allows overriding user agent with the given string.
         * `userAgentMetadata` must be set for Client Hint headers to be sent.
         */
        setUserAgentOverride(params: Protocol.Emulation.SetUserAgentOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Allows overriding the automation flag.
         */
        setAutomationOverride(params: Protocol.Emulation.SetAutomationOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Notification sent after the virtual time budget for the current VirtualTimePolicy has run out.
         */
        onVirtualTimeBudgetExpired(listener: (event: ) => void): void;
        offVirtualTimeBudgetExpired(listener: (event: ) => void): void;
        onceVirtualTimeBudgetExpired(eventMatcher?: (event: ) => boolean): Promise<>;

    }

    export interface HeadlessExperimentalApi {
        /**
         * Sends a BeginFrame to the target and returns when the frame was completed. Optionally captures a
         * screenshot from the resulting frame. Requires that the target was created with enabled
         * BeginFrameControl. Designed for use with --run-all-compositor-stages-before-draw, see also
         * https://goo.gle/chrome-headless-rendering for more background.
         */
        beginFrame(params: Protocol.HeadlessExperimental.BeginFrameRequest): Promise<{id: number, result: Protocol.HeadlessExperimental.BeginFrameResponse, sessionId: string}>;

        /**
         * Disables headless events for the target.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables headless events for the target.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

    }

    export interface IOApi {
        /**
         * Close the stream, discard any temporary backing storage.
         */
        close(params: Protocol.IO.CloseRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Read a chunk of the stream
         */
        read(params: Protocol.IO.ReadRequest): Promise<{id: number, result: Protocol.IO.ReadResponse, sessionId: string}>;

        /**
         * Return UUID of Blob object specified by a remote object id.
         */
        resolveBlob(params: Protocol.IO.ResolveBlobRequest): Promise<{id: number, result: Protocol.IO.ResolveBlobResponse, sessionId: string}>;

    }

    export interface IndexedDBApi {
        /**
         * Clears all entries from an object store.
         */
        clearObjectStore(params: Protocol.IndexedDB.ClearObjectStoreRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes a database.
         */
        deleteDatabase(params: Protocol.IndexedDB.DeleteDatabaseRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Delete a range of entries from an object store
         */
        deleteObjectStoreEntries(params: Protocol.IndexedDB.DeleteObjectStoreEntriesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables events from backend.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables events from backend.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests data from object store or index.
         */
        requestData(params: Protocol.IndexedDB.RequestDataRequest): Promise<{id: number, result: Protocol.IndexedDB.RequestDataResponse, sessionId: string}>;

        /**
         * Gets metadata of an object store.
         */
        getMetadata(params: Protocol.IndexedDB.GetMetadataRequest): Promise<{id: number, result: Protocol.IndexedDB.GetMetadataResponse, sessionId: string}>;

        /**
         * Requests database with given name in given frame.
         */
        requestDatabase(params: Protocol.IndexedDB.RequestDatabaseRequest): Promise<{id: number, result: Protocol.IndexedDB.RequestDatabaseResponse, sessionId: string}>;

        /**
         * Requests database names for given security origin.
         */
        requestDatabaseNames(params: Protocol.IndexedDB.RequestDatabaseNamesRequest): Promise<{id: number, result: Protocol.IndexedDB.RequestDatabaseNamesResponse, sessionId: string}>;

    }

    export interface InputApi {
        /**
         * Dispatches a drag event into the page.
         */
        dispatchDragEvent(params: Protocol.Input.DispatchDragEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Dispatches a key event to the page.
         */
        dispatchKeyEvent(params: Protocol.Input.DispatchKeyEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This method emulates inserting text that doesn't come from a key press,
         * for example an emoji keyboard or an IME.
         */
        insertText(params: Protocol.Input.InsertTextRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This method sets the current candidate text for IME.
         * Use imeCommitComposition to commit the final text.
         * Use imeSetComposition with empty string as text to cancel composition.
         */
        imeSetComposition(params: Protocol.Input.ImeSetCompositionRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Dispatches a mouse event to the page.
         */
        dispatchMouseEvent(params: Protocol.Input.DispatchMouseEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Dispatches a touch event to the page.
         */
        dispatchTouchEvent(params: Protocol.Input.DispatchTouchEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Cancels any active dragging in the page.
         */
        cancelDragging(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Emulates touch event from the mouse event parameters.
         */
        emulateTouchFromMouseEvent(params: Protocol.Input.EmulateTouchFromMouseEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Ignores input events (useful while auditing page).
         */
        setIgnoreInputEvents(params: Protocol.Input.SetIgnoreInputEventsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Prevents default drag and drop behavior and instead emits `Input.dragIntercepted` events.
         * Drag and drop behavior can be directly controlled via `Input.dispatchDragEvent`.
         */
        setInterceptDrags(params: Protocol.Input.SetInterceptDragsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Synthesizes a pinch gesture over a time period by issuing appropriate touch events.
         */
        synthesizePinchGesture(params: Protocol.Input.SynthesizePinchGestureRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Synthesizes a scroll gesture over a time period by issuing appropriate touch events.
         */
        synthesizeScrollGesture(params: Protocol.Input.SynthesizeScrollGestureRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Synthesizes a tap gesture over a time period by issuing appropriate touch events.
         */
        synthesizeTapGesture(params: Protocol.Input.SynthesizeTapGestureRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Emitted only when `Input.setInterceptDrags` is enabled. Use this data with `Input.dispatchDragEvent` to
         * restore normal drag and drop behavior.
         */
        onDragIntercepted(listener: (event: { params: Protocol.Input.DragInterceptedEvent }) => void): void;
        offDragIntercepted(listener: (event: { params: Protocol.Input.DragInterceptedEvent }) => void): void;
        onceDragIntercepted(eventMatcher?: (event: { params: Protocol.Input.DragInterceptedEvent }) => boolean): Promise<{ params: Protocol.Input.DragInterceptedEvent }>;

    }

    export interface InspectorApi {
        /**
         * Disables inspector domain notifications.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables inspector domain notifications.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fired when remote debugging connection is about to be terminated. Contains detach reason.
         */
        onDetached(listener: (event: { params: Protocol.Inspector.DetachedEvent }) => void): void;
        offDetached(listener: (event: { params: Protocol.Inspector.DetachedEvent }) => void): void;
        onceDetached(eventMatcher?: (event: { params: Protocol.Inspector.DetachedEvent }) => boolean): Promise<{ params: Protocol.Inspector.DetachedEvent }>;

        /**
         * Fired when debugging target has crashed
         */
        onTargetCrashed(listener: (event: ) => void): void;
        offTargetCrashed(listener: (event: ) => void): void;
        onceTargetCrashed(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when debugging target has reloaded after crash
         */
        onTargetReloadedAfterCrash(listener: (event: ) => void): void;
        offTargetReloadedAfterCrash(listener: (event: ) => void): void;
        onceTargetReloadedAfterCrash(eventMatcher?: (event: ) => boolean): Promise<>;

    }

    export interface LayerTreeApi {
        /**
         * Provides the reasons why the given layer was composited.
         */
        compositingReasons(params: Protocol.LayerTree.CompositingReasonsRequest): Promise<{id: number, result: Protocol.LayerTree.CompositingReasonsResponse, sessionId: string}>;

        /**
         * Disables compositing tree inspection.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables compositing tree inspection.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns the snapshot identifier.
         */
        loadSnapshot(params: Protocol.LayerTree.LoadSnapshotRequest): Promise<{id: number, result: Protocol.LayerTree.LoadSnapshotResponse, sessionId: string}>;

        /**
         * Returns the layer snapshot identifier.
         */
        makeSnapshot(params: Protocol.LayerTree.MakeSnapshotRequest): Promise<{id: number, result: Protocol.LayerTree.MakeSnapshotResponse, sessionId: string}>;

        profileSnapshot(params: Protocol.LayerTree.ProfileSnapshotRequest): Promise<{id: number, result: Protocol.LayerTree.ProfileSnapshotResponse, sessionId: string}>;

        /**
         * Releases layer snapshot captured by the back-end.
         */
        releaseSnapshot(params: Protocol.LayerTree.ReleaseSnapshotRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Replays the layer snapshot and returns the resulting bitmap.
         */
        replaySnapshot(params: Protocol.LayerTree.ReplaySnapshotRequest): Promise<{id: number, result: Protocol.LayerTree.ReplaySnapshotResponse, sessionId: string}>;

        /**
         * Replays the layer snapshot and returns canvas log.
         */
        snapshotCommandLog(params: Protocol.LayerTree.SnapshotCommandLogRequest): Promise<{id: number, result: Protocol.LayerTree.SnapshotCommandLogResponse, sessionId: string}>;

        onLayerPainted(listener: (event: { params: Protocol.LayerTree.LayerPaintedEvent }) => void): void;
        offLayerPainted(listener: (event: { params: Protocol.LayerTree.LayerPaintedEvent }) => void): void;
        onceLayerPainted(eventMatcher?: (event: { params: Protocol.LayerTree.LayerPaintedEvent }) => boolean): Promise<{ params: Protocol.LayerTree.LayerPaintedEvent }>;

        onLayerTreeDidChange(listener: (event: { params: Protocol.LayerTree.LayerTreeDidChangeEvent }) => void): void;
        offLayerTreeDidChange(listener: (event: { params: Protocol.LayerTree.LayerTreeDidChangeEvent }) => void): void;
        onceLayerTreeDidChange(eventMatcher?: (event: { params: Protocol.LayerTree.LayerTreeDidChangeEvent }) => boolean): Promise<{ params: Protocol.LayerTree.LayerTreeDidChangeEvent }>;

    }

    export interface LogApi {
        /**
         * Clears the log.
         */
        clear(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables log domain, prevents further log entries from being reported to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables log domain, sends the entries collected so far to the client by means of the
         * `entryAdded` notification.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * start violation reporting.
         */
        startViolationsReport(params: Protocol.Log.StartViolationsReportRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stop violation reporting.
         */
        stopViolationsReport(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Issued when new message was logged.
         */
        onEntryAdded(listener: (event: { params: Protocol.Log.EntryAddedEvent }) => void): void;
        offEntryAdded(listener: (event: { params: Protocol.Log.EntryAddedEvent }) => void): void;
        onceEntryAdded(eventMatcher?: (event: { params: Protocol.Log.EntryAddedEvent }) => boolean): Promise<{ params: Protocol.Log.EntryAddedEvent }>;

    }

    export interface MemoryApi {
        getDOMCounters(): Promise<{id: number, result: Protocol.Memory.GetDOMCountersResponse, sessionId: string}>;

        prepareForLeakDetection(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Simulate OomIntervention by purging V8 memory.
         */
        forciblyPurgeJavaScriptMemory(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable/disable suppressing memory pressure notifications in all processes.
         */
        setPressureNotificationsSuppressed(params: Protocol.Memory.SetPressureNotificationsSuppressedRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Simulate a memory pressure notification in all processes.
         */
        simulatePressureNotification(params: Protocol.Memory.SimulatePressureNotificationRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Start collecting native memory profile.
         */
        startSampling(params: Protocol.Memory.StartSamplingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stop collecting native memory profile.
         */
        stopSampling(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Retrieve native memory allocations profile
         * collected since renderer process startup.
         */
        getAllTimeSamplingProfile(): Promise<{id: number, result: Protocol.Memory.GetAllTimeSamplingProfileResponse, sessionId: string}>;

        /**
         * Retrieve native memory allocations profile
         * collected since browser process startup.
         */
        getBrowserSamplingProfile(): Promise<{id: number, result: Protocol.Memory.GetBrowserSamplingProfileResponse, sessionId: string}>;

        /**
         * Retrieve native memory allocations profile collected since last
         * `startSampling` call.
         */
        getSamplingProfile(): Promise<{id: number, result: Protocol.Memory.GetSamplingProfileResponse, sessionId: string}>;

    }

    export interface NetworkApi {
        /**
         * Sets a list of content encodings that will be accepted. Empty list means no encoding is accepted.
         */
        setAcceptedEncodings(params: Protocol.Network.SetAcceptedEncodingsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears accepted encodings set by setAcceptedEncodings
         */
        clearAcceptedEncodingsOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Tells whether clearing browser cache is supported.
         */
        canClearBrowserCache(): Promise<{id: number, result: Protocol.Network.CanClearBrowserCacheResponse, sessionId: string}>;

        /**
         * Tells whether clearing browser cookies is supported.
         */
        canClearBrowserCookies(): Promise<{id: number, result: Protocol.Network.CanClearBrowserCookiesResponse, sessionId: string}>;

        /**
         * Tells whether emulation of network conditions is supported.
         */
        canEmulateNetworkConditions(): Promise<{id: number, result: Protocol.Network.CanEmulateNetworkConditionsResponse, sessionId: string}>;

        /**
         * Clears browser cache.
         */
        clearBrowserCache(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears browser cookies.
         */
        clearBrowserCookies(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Response to Network.requestIntercepted which either modifies the request to continue with any
         * modifications, or blocks it, or completes it with the provided response bytes. If a network
         * fetch occurs as a result which encounters a redirect an additional Network.requestIntercepted
         * event will be sent with the same InterceptionId.
         * Deprecated, use Fetch.continueRequest, Fetch.fulfillRequest and Fetch.failRequest instead.
         */
        continueInterceptedRequest(params: Protocol.Network.ContinueInterceptedRequestRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes browser cookies with matching name and url or domain/path/partitionKey pair.
         */
        deleteCookies(params: Protocol.Network.DeleteCookiesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables network tracking, prevents network events from being sent to the client.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Activates emulation of network conditions.
         */
        emulateNetworkConditions(params: Protocol.Network.EmulateNetworkConditionsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables network tracking, network events will now be delivered to the client.
         */
        enable(params: Protocol.Network.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns all browser cookies. Depending on the backend support, will return detailed cookie
         * information in the `cookies` field.
         * Deprecated. Use Storage.getCookies instead.
         */
        getAllCookies(): Promise<{id: number, result: Protocol.Network.GetAllCookiesResponse, sessionId: string}>;

        /**
         * Returns the DER-encoded certificate.
         */
        getCertificate(params: Protocol.Network.GetCertificateRequest): Promise<{id: number, result: Protocol.Network.GetCertificateResponse, sessionId: string}>;

        /**
         * Returns all browser cookies for the current URL. Depending on the backend support, will return
         * detailed cookie information in the `cookies` field.
         */
        getCookies(params: Protocol.Network.GetCookiesRequest): Promise<{id: number, result: Protocol.Network.GetCookiesResponse, sessionId: string}>;

        /**
         * Returns content served for the given request.
         */
        getResponseBody(params: Protocol.Network.GetResponseBodyRequest): Promise<{id: number, result: Protocol.Network.GetResponseBodyResponse, sessionId: string}>;

        /**
         * Returns post data sent with the request. Returns an error when no data was sent with the request.
         */
        getRequestPostData(params: Protocol.Network.GetRequestPostDataRequest): Promise<{id: number, result: Protocol.Network.GetRequestPostDataResponse, sessionId: string}>;

        /**
         * Returns content served for the given currently intercepted request.
         */
        getResponseBodyForInterception(params: Protocol.Network.GetResponseBodyForInterceptionRequest): Promise<{id: number, result: Protocol.Network.GetResponseBodyForInterceptionResponse, sessionId: string}>;

        /**
         * Returns a handle to the stream representing the response body. Note that after this command,
         * the intercepted request can't be continued as is -- you either need to cancel it or to provide
         * the response body. The stream only supports sequential read, IO.read will fail if the position
         * is specified.
         */
        takeResponseBodyForInterceptionAsStream(params: Protocol.Network.TakeResponseBodyForInterceptionAsStreamRequest): Promise<{id: number, result: Protocol.Network.TakeResponseBodyForInterceptionAsStreamResponse, sessionId: string}>;

        /**
         * This method sends a new XMLHttpRequest which is identical to the original one. The following
         * parameters should be identical: method, url, async, request body, extra headers, withCredentials
         * attribute, user, password.
         */
        replayXHR(params: Protocol.Network.ReplayXHRRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Searches for given string in response content.
         */
        searchInResponseBody(params: Protocol.Network.SearchInResponseBodyRequest): Promise<{id: number, result: Protocol.Network.SearchInResponseBodyResponse, sessionId: string}>;

        /**
         * Blocks URLs from loading.
         */
        setBlockedURLs(params: Protocol.Network.SetBlockedURLsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Toggles ignoring of service worker for each request.
         */
        setBypassServiceWorker(params: Protocol.Network.SetBypassServiceWorkerRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Toggles ignoring cache for each request. If `true`, cache will not be used.
         */
        setCacheDisabled(params: Protocol.Network.SetCacheDisabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets a cookie with the given cookie data; may overwrite equivalent cookies if they exist.
         */
        setCookie(params: Protocol.Network.SetCookieRequest): Promise<{id: number, result: Protocol.Network.SetCookieResponse, sessionId: string}>;

        /**
         * Sets given cookies.
         */
        setCookies(params: Protocol.Network.SetCookiesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Specifies whether to always send extra HTTP headers with the requests from this page.
         */
        setExtraHTTPHeaders(params: Protocol.Network.SetExtraHTTPHeadersRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Specifies whether to attach a page script stack id in requests
         */
        setAttachDebugStack(params: Protocol.Network.SetAttachDebugStackRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets the requests to intercept that match the provided patterns and optionally resource types.
         * Deprecated, please use Fetch.enable instead.
         */
        setRequestInterception(params: Protocol.Network.SetRequestInterceptionRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Allows overriding user agent with the given string.
         */
        setUserAgentOverride(params: Protocol.Network.SetUserAgentOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables streaming of the response for the given requestId.
         * If enabled, the dataReceived event contains the data that was received during streaming.
         */
        streamResourceContent(params: Protocol.Network.StreamResourceContentRequest): Promise<{id: number, result: Protocol.Network.StreamResourceContentResponse, sessionId: string}>;

        /**
         * Returns information about the COEP/COOP isolation status.
         */
        getSecurityIsolationStatus(params: Protocol.Network.GetSecurityIsolationStatusRequest): Promise<{id: number, result: Protocol.Network.GetSecurityIsolationStatusResponse, sessionId: string}>;

        /**
         * Enables tracking for the Reporting API, events generated by the Reporting API will now be delivered to the client.
         * Enabling triggers 'reportingApiReportAdded' for all existing reports.
         */
        enableReportingApi(params: Protocol.Network.EnableReportingApiRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fetches the resource and returns the content.
         */
        loadNetworkResource(params: Protocol.Network.LoadNetworkResourceRequest): Promise<{id: number, result: Protocol.Network.LoadNetworkResourceResponse, sessionId: string}>;

        /**
         * Fired when data chunk was received over the network.
         */
        onDataReceived(listener: (event: { params: Protocol.Network.DataReceivedEvent }) => void): void;
        offDataReceived(listener: (event: { params: Protocol.Network.DataReceivedEvent }) => void): void;
        onceDataReceived(eventMatcher?: (event: { params: Protocol.Network.DataReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.DataReceivedEvent }>;

        /**
         * Fired when EventSource message is received.
         */
        onEventSourceMessageReceived(listener: (event: { params: Protocol.Network.EventSourceMessageReceivedEvent }) => void): void;
        offEventSourceMessageReceived(listener: (event: { params: Protocol.Network.EventSourceMessageReceivedEvent }) => void): void;
        onceEventSourceMessageReceived(eventMatcher?: (event: { params: Protocol.Network.EventSourceMessageReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.EventSourceMessageReceivedEvent }>;

        /**
         * Fired when HTTP request has failed to load.
         */
        onLoadingFailed(listener: (event: { params: Protocol.Network.LoadingFailedEvent }) => void): void;
        offLoadingFailed(listener: (event: { params: Protocol.Network.LoadingFailedEvent }) => void): void;
        onceLoadingFailed(eventMatcher?: (event: { params: Protocol.Network.LoadingFailedEvent }) => boolean): Promise<{ params: Protocol.Network.LoadingFailedEvent }>;

        /**
         * Fired when HTTP request has finished loading.
         */
        onLoadingFinished(listener: (event: { params: Protocol.Network.LoadingFinishedEvent }) => void): void;
        offLoadingFinished(listener: (event: { params: Protocol.Network.LoadingFinishedEvent }) => void): void;
        onceLoadingFinished(eventMatcher?: (event: { params: Protocol.Network.LoadingFinishedEvent }) => boolean): Promise<{ params: Protocol.Network.LoadingFinishedEvent }>;

        /**
         * Details of an intercepted HTTP request, which must be either allowed, blocked, modified or
         * mocked.
         * Deprecated, use Fetch.requestPaused instead.
         */
        onRequestIntercepted(listener: (event: { params: Protocol.Network.RequestInterceptedEvent }) => void): void;
        offRequestIntercepted(listener: (event: { params: Protocol.Network.RequestInterceptedEvent }) => void): void;
        onceRequestIntercepted(eventMatcher?: (event: { params: Protocol.Network.RequestInterceptedEvent }) => boolean): Promise<{ params: Protocol.Network.RequestInterceptedEvent }>;

        /**
         * Fired if request ended up loading from cache.
         */
        onRequestServedFromCache(listener: (event: { params: Protocol.Network.RequestServedFromCacheEvent }) => void): void;
        offRequestServedFromCache(listener: (event: { params: Protocol.Network.RequestServedFromCacheEvent }) => void): void;
        onceRequestServedFromCache(eventMatcher?: (event: { params: Protocol.Network.RequestServedFromCacheEvent }) => boolean): Promise<{ params: Protocol.Network.RequestServedFromCacheEvent }>;

        /**
         * Fired when page is about to send HTTP request.
         */
        onRequestWillBeSent(listener: (event: { params: Protocol.Network.RequestWillBeSentEvent }) => void): void;
        offRequestWillBeSent(listener: (event: { params: Protocol.Network.RequestWillBeSentEvent }) => void): void;
        onceRequestWillBeSent(eventMatcher?: (event: { params: Protocol.Network.RequestWillBeSentEvent }) => boolean): Promise<{ params: Protocol.Network.RequestWillBeSentEvent }>;

        /**
         * Fired when resource loading priority is changed
         */
        onResourceChangedPriority(listener: (event: { params: Protocol.Network.ResourceChangedPriorityEvent }) => void): void;
        offResourceChangedPriority(listener: (event: { params: Protocol.Network.ResourceChangedPriorityEvent }) => void): void;
        onceResourceChangedPriority(eventMatcher?: (event: { params: Protocol.Network.ResourceChangedPriorityEvent }) => boolean): Promise<{ params: Protocol.Network.ResourceChangedPriorityEvent }>;

        /**
         * Fired when a signed exchange was received over the network
         */
        onSignedExchangeReceived(listener: (event: { params: Protocol.Network.SignedExchangeReceivedEvent }) => void): void;
        offSignedExchangeReceived(listener: (event: { params: Protocol.Network.SignedExchangeReceivedEvent }) => void): void;
        onceSignedExchangeReceived(eventMatcher?: (event: { params: Protocol.Network.SignedExchangeReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.SignedExchangeReceivedEvent }>;

        /**
         * Fired when HTTP response is available.
         */
        onResponseReceived(listener: (event: { params: Protocol.Network.ResponseReceivedEvent }) => void): void;
        offResponseReceived(listener: (event: { params: Protocol.Network.ResponseReceivedEvent }) => void): void;
        onceResponseReceived(eventMatcher?: (event: { params: Protocol.Network.ResponseReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.ResponseReceivedEvent }>;

        /**
         * Fired when WebSocket is closed.
         */
        onWebSocketClosed(listener: (event: { params: Protocol.Network.WebSocketClosedEvent }) => void): void;
        offWebSocketClosed(listener: (event: { params: Protocol.Network.WebSocketClosedEvent }) => void): void;
        onceWebSocketClosed(eventMatcher?: (event: { params: Protocol.Network.WebSocketClosedEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketClosedEvent }>;

        /**
         * Fired upon WebSocket creation.
         */
        onWebSocketCreated(listener: (event: { params: Protocol.Network.WebSocketCreatedEvent }) => void): void;
        offWebSocketCreated(listener: (event: { params: Protocol.Network.WebSocketCreatedEvent }) => void): void;
        onceWebSocketCreated(eventMatcher?: (event: { params: Protocol.Network.WebSocketCreatedEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketCreatedEvent }>;

        /**
         * Fired when WebSocket message error occurs.
         */
        onWebSocketFrameError(listener: (event: { params: Protocol.Network.WebSocketFrameErrorEvent }) => void): void;
        offWebSocketFrameError(listener: (event: { params: Protocol.Network.WebSocketFrameErrorEvent }) => void): void;
        onceWebSocketFrameError(eventMatcher?: (event: { params: Protocol.Network.WebSocketFrameErrorEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketFrameErrorEvent }>;

        /**
         * Fired when WebSocket message is received.
         */
        onWebSocketFrameReceived(listener: (event: { params: Protocol.Network.WebSocketFrameReceivedEvent }) => void): void;
        offWebSocketFrameReceived(listener: (event: { params: Protocol.Network.WebSocketFrameReceivedEvent }) => void): void;
        onceWebSocketFrameReceived(eventMatcher?: (event: { params: Protocol.Network.WebSocketFrameReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketFrameReceivedEvent }>;

        /**
         * Fired when WebSocket message is sent.
         */
        onWebSocketFrameSent(listener: (event: { params: Protocol.Network.WebSocketFrameSentEvent }) => void): void;
        offWebSocketFrameSent(listener: (event: { params: Protocol.Network.WebSocketFrameSentEvent }) => void): void;
        onceWebSocketFrameSent(eventMatcher?: (event: { params: Protocol.Network.WebSocketFrameSentEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketFrameSentEvent }>;

        /**
         * Fired when WebSocket handshake response becomes available.
         */
        onWebSocketHandshakeResponseReceived(listener: (event: { params: Protocol.Network.WebSocketHandshakeResponseReceivedEvent }) => void): void;
        offWebSocketHandshakeResponseReceived(listener: (event: { params: Protocol.Network.WebSocketHandshakeResponseReceivedEvent }) => void): void;
        onceWebSocketHandshakeResponseReceived(eventMatcher?: (event: { params: Protocol.Network.WebSocketHandshakeResponseReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketHandshakeResponseReceivedEvent }>;

        /**
         * Fired when WebSocket is about to initiate handshake.
         */
        onWebSocketWillSendHandshakeRequest(listener: (event: { params: Protocol.Network.WebSocketWillSendHandshakeRequestEvent }) => void): void;
        offWebSocketWillSendHandshakeRequest(listener: (event: { params: Protocol.Network.WebSocketWillSendHandshakeRequestEvent }) => void): void;
        onceWebSocketWillSendHandshakeRequest(eventMatcher?: (event: { params: Protocol.Network.WebSocketWillSendHandshakeRequestEvent }) => boolean): Promise<{ params: Protocol.Network.WebSocketWillSendHandshakeRequestEvent }>;

        /**
         * Fired upon WebTransport creation.
         */
        onWebTransportCreated(listener: (event: { params: Protocol.Network.WebTransportCreatedEvent }) => void): void;
        offWebTransportCreated(listener: (event: { params: Protocol.Network.WebTransportCreatedEvent }) => void): void;
        onceWebTransportCreated(eventMatcher?: (event: { params: Protocol.Network.WebTransportCreatedEvent }) => boolean): Promise<{ params: Protocol.Network.WebTransportCreatedEvent }>;

        /**
         * Fired when WebTransport handshake is finished.
         */
        onWebTransportConnectionEstablished(listener: (event: { params: Protocol.Network.WebTransportConnectionEstablishedEvent }) => void): void;
        offWebTransportConnectionEstablished(listener: (event: { params: Protocol.Network.WebTransportConnectionEstablishedEvent }) => void): void;
        onceWebTransportConnectionEstablished(eventMatcher?: (event: { params: Protocol.Network.WebTransportConnectionEstablishedEvent }) => boolean): Promise<{ params: Protocol.Network.WebTransportConnectionEstablishedEvent }>;

        /**
         * Fired when WebTransport is disposed.
         */
        onWebTransportClosed(listener: (event: { params: Protocol.Network.WebTransportClosedEvent }) => void): void;
        offWebTransportClosed(listener: (event: { params: Protocol.Network.WebTransportClosedEvent }) => void): void;
        onceWebTransportClosed(eventMatcher?: (event: { params: Protocol.Network.WebTransportClosedEvent }) => boolean): Promise<{ params: Protocol.Network.WebTransportClosedEvent }>;

        /**
         * Fired when additional information about a requestWillBeSent event is available from the
         * network stack. Not every requestWillBeSent event will have an additional
         * requestWillBeSentExtraInfo fired for it, and there is no guarantee whether requestWillBeSent
         * or requestWillBeSentExtraInfo will be fired first for the same request.
         */
        onRequestWillBeSentExtraInfo(listener: (event: { params: Protocol.Network.RequestWillBeSentExtraInfoEvent }) => void): void;
        offRequestWillBeSentExtraInfo(listener: (event: { params: Protocol.Network.RequestWillBeSentExtraInfoEvent }) => void): void;
        onceRequestWillBeSentExtraInfo(eventMatcher?: (event: { params: Protocol.Network.RequestWillBeSentExtraInfoEvent }) => boolean): Promise<{ params: Protocol.Network.RequestWillBeSentExtraInfoEvent }>;

        /**
         * Fired when additional information about a responseReceived event is available from the network
         * stack. Not every responseReceived event will have an additional responseReceivedExtraInfo for
         * it, and responseReceivedExtraInfo may be fired before or after responseReceived.
         */
        onResponseReceivedExtraInfo(listener: (event: { params: Protocol.Network.ResponseReceivedExtraInfoEvent }) => void): void;
        offResponseReceivedExtraInfo(listener: (event: { params: Protocol.Network.ResponseReceivedExtraInfoEvent }) => void): void;
        onceResponseReceivedExtraInfo(eventMatcher?: (event: { params: Protocol.Network.ResponseReceivedExtraInfoEvent }) => boolean): Promise<{ params: Protocol.Network.ResponseReceivedExtraInfoEvent }>;

        /**
         * Fired when 103 Early Hints headers is received in addition to the common response.
         * Not every responseReceived event will have an responseReceivedEarlyHints fired.
         * Only one responseReceivedEarlyHints may be fired for eached responseReceived event.
         */
        onResponseReceivedEarlyHints(listener: (event: { params: Protocol.Network.ResponseReceivedEarlyHintsEvent }) => void): void;
        offResponseReceivedEarlyHints(listener: (event: { params: Protocol.Network.ResponseReceivedEarlyHintsEvent }) => void): void;
        onceResponseReceivedEarlyHints(eventMatcher?: (event: { params: Protocol.Network.ResponseReceivedEarlyHintsEvent }) => boolean): Promise<{ params: Protocol.Network.ResponseReceivedEarlyHintsEvent }>;

        /**
         * Fired exactly once for each Trust Token operation. Depending on
         * the type of the operation and whether the operation succeeded or
         * failed, the event is fired before the corresponding request was sent
         * or after the response was received.
         */
        onTrustTokenOperationDone(listener: (event: { params: Protocol.Network.TrustTokenOperationDoneEvent }) => void): void;
        offTrustTokenOperationDone(listener: (event: { params: Protocol.Network.TrustTokenOperationDoneEvent }) => void): void;
        onceTrustTokenOperationDone(eventMatcher?: (event: { params: Protocol.Network.TrustTokenOperationDoneEvent }) => boolean): Promise<{ params: Protocol.Network.TrustTokenOperationDoneEvent }>;

        /**
         * Fired once when parsing the .wbn file has succeeded.
         * The event contains the information about the web bundle contents.
         */
        onSubresourceWebBundleMetadataReceived(listener: (event: { params: Protocol.Network.SubresourceWebBundleMetadataReceivedEvent }) => void): void;
        offSubresourceWebBundleMetadataReceived(listener: (event: { params: Protocol.Network.SubresourceWebBundleMetadataReceivedEvent }) => void): void;
        onceSubresourceWebBundleMetadataReceived(eventMatcher?: (event: { params: Protocol.Network.SubresourceWebBundleMetadataReceivedEvent }) => boolean): Promise<{ params: Protocol.Network.SubresourceWebBundleMetadataReceivedEvent }>;

        /**
         * Fired once when parsing the .wbn file has failed.
         */
        onSubresourceWebBundleMetadataError(listener: (event: { params: Protocol.Network.SubresourceWebBundleMetadataErrorEvent }) => void): void;
        offSubresourceWebBundleMetadataError(listener: (event: { params: Protocol.Network.SubresourceWebBundleMetadataErrorEvent }) => void): void;
        onceSubresourceWebBundleMetadataError(eventMatcher?: (event: { params: Protocol.Network.SubresourceWebBundleMetadataErrorEvent }) => boolean): Promise<{ params: Protocol.Network.SubresourceWebBundleMetadataErrorEvent }>;

        /**
         * Fired when handling requests for resources within a .wbn file.
         * Note: this will only be fired for resources that are requested by the webpage.
         */
        onSubresourceWebBundleInnerResponseParsed(listener: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseParsedEvent }) => void): void;
        offSubresourceWebBundleInnerResponseParsed(listener: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseParsedEvent }) => void): void;
        onceSubresourceWebBundleInnerResponseParsed(eventMatcher?: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseParsedEvent }) => boolean): Promise<{ params: Protocol.Network.SubresourceWebBundleInnerResponseParsedEvent }>;

        /**
         * Fired when request for resources within a .wbn file failed.
         */
        onSubresourceWebBundleInnerResponseError(listener: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseErrorEvent }) => void): void;
        offSubresourceWebBundleInnerResponseError(listener: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseErrorEvent }) => void): void;
        onceSubresourceWebBundleInnerResponseError(eventMatcher?: (event: { params: Protocol.Network.SubresourceWebBundleInnerResponseErrorEvent }) => boolean): Promise<{ params: Protocol.Network.SubresourceWebBundleInnerResponseErrorEvent }>;

        /**
         * Is sent whenever a new report is added.
         * And after 'enableReportingApi' for all existing reports.
         */
        onReportingApiReportAdded(listener: (event: { params: Protocol.Network.ReportingApiReportAddedEvent }) => void): void;
        offReportingApiReportAdded(listener: (event: { params: Protocol.Network.ReportingApiReportAddedEvent }) => void): void;
        onceReportingApiReportAdded(eventMatcher?: (event: { params: Protocol.Network.ReportingApiReportAddedEvent }) => boolean): Promise<{ params: Protocol.Network.ReportingApiReportAddedEvent }>;

        onReportingApiReportUpdated(listener: (event: { params: Protocol.Network.ReportingApiReportUpdatedEvent }) => void): void;
        offReportingApiReportUpdated(listener: (event: { params: Protocol.Network.ReportingApiReportUpdatedEvent }) => void): void;
        onceReportingApiReportUpdated(eventMatcher?: (event: { params: Protocol.Network.ReportingApiReportUpdatedEvent }) => boolean): Promise<{ params: Protocol.Network.ReportingApiReportUpdatedEvent }>;

        onReportingApiEndpointsChangedForOrigin(listener: (event: { params: Protocol.Network.ReportingApiEndpointsChangedForOriginEvent }) => void): void;
        offReportingApiEndpointsChangedForOrigin(listener: (event: { params: Protocol.Network.ReportingApiEndpointsChangedForOriginEvent }) => void): void;
        onceReportingApiEndpointsChangedForOrigin(eventMatcher?: (event: { params: Protocol.Network.ReportingApiEndpointsChangedForOriginEvent }) => boolean): Promise<{ params: Protocol.Network.ReportingApiEndpointsChangedForOriginEvent }>;

    }

    export interface OverlayApi {
        /**
         * Disables domain notifications.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables domain notifications.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * For testing.
         */
        getHighlightObjectForTest(params: Protocol.Overlay.GetHighlightObjectForTestRequest): Promise<{id: number, result: Protocol.Overlay.GetHighlightObjectForTestResponse, sessionId: string}>;

        /**
         * For Persistent Grid testing.
         */
        getGridHighlightObjectsForTest(params: Protocol.Overlay.GetGridHighlightObjectsForTestRequest): Promise<{id: number, result: Protocol.Overlay.GetGridHighlightObjectsForTestResponse, sessionId: string}>;

        /**
         * For Source Order Viewer testing.
         */
        getSourceOrderHighlightObjectForTest(params: Protocol.Overlay.GetSourceOrderHighlightObjectForTestRequest): Promise<{id: number, result: Protocol.Overlay.GetSourceOrderHighlightObjectForTestResponse, sessionId: string}>;

        /**
         * Hides any highlight.
         */
        hideHighlight(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights owner element of the frame with given id.
         * Deprecated: Doesn't work reliably and cannot be fixed due to process
         * separation (the owner node might be in a different process). Determine
         * the owner node in the client and use highlightNode.
         */
        highlightFrame(params: Protocol.Overlay.HighlightFrameRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or
         * objectId must be specified.
         */
        highlightNode(params: Protocol.Overlay.HighlightNodeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights given quad. Coordinates are absolute with respect to the main frame viewport.
         */
        highlightQuad(params: Protocol.Overlay.HighlightQuadRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.
         */
        highlightRect(params: Protocol.Overlay.HighlightRectRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights the source order of the children of the DOM node with given id or with the given
         * JavaScript object wrapper. Either nodeId or objectId must be specified.
         */
        highlightSourceOrder(params: Protocol.Overlay.HighlightSourceOrderRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted.
         * Backend then generates 'inspectNodeRequested' event upon element selection.
         */
        setInspectMode(params: Protocol.Overlay.SetInspectModeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlights owner element of all frames detected to be ads.
         */
        setShowAdHighlights(params: Protocol.Overlay.SetShowAdHighlightsRequest): Promise<{id: number, result: void, sessionId: string}>;

        setPausedInDebuggerMessage(params: Protocol.Overlay.SetPausedInDebuggerMessageRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that backend shows debug borders on layers
         */
        setShowDebugBorders(params: Protocol.Overlay.SetShowDebugBordersRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that backend shows the FPS counter
         */
        setShowFPSCounter(params: Protocol.Overlay.SetShowFPSCounterRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Highlight multiple elements with the CSS Grid overlay.
         */
        setShowGridOverlays(params: Protocol.Overlay.SetShowGridOverlaysRequest): Promise<{id: number, result: void, sessionId: string}>;

        setShowFlexOverlays(params: Protocol.Overlay.SetShowFlexOverlaysRequest): Promise<{id: number, result: void, sessionId: string}>;

        setShowScrollSnapOverlays(params: Protocol.Overlay.SetShowScrollSnapOverlaysRequest): Promise<{id: number, result: void, sessionId: string}>;

        setShowContainerQueryOverlays(params: Protocol.Overlay.SetShowContainerQueryOverlaysRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that backend shows paint rectangles
         */
        setShowPaintRects(params: Protocol.Overlay.SetShowPaintRectsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that backend shows layout shift regions
         */
        setShowLayoutShiftRegions(params: Protocol.Overlay.SetShowLayoutShiftRegionsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests that backend shows scroll bottleneck rects
         */
        setShowScrollBottleneckRects(params: Protocol.Overlay.SetShowScrollBottleneckRectsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deprecated, no longer has any effect.
         */
        setShowHitTestBorders(params: Protocol.Overlay.SetShowHitTestBordersRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Request that backend shows an overlay with web vital metrics.
         */
        setShowWebVitals(params: Protocol.Overlay.SetShowWebVitalsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Paints viewport size upon main frame resize.
         */
        setShowViewportSizeOnResize(params: Protocol.Overlay.SetShowViewportSizeOnResizeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Add a dual screen device hinge
         */
        setShowHinge(params: Protocol.Overlay.SetShowHingeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Show elements in isolation mode with overlays.
         */
        setShowIsolatedElements(params: Protocol.Overlay.SetShowIsolatedElementsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Show Window Controls Overlay for PWA
         */
        setShowWindowControlsOverlay(params: Protocol.Overlay.SetShowWindowControlsOverlayRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fired when the node should be inspected. This happens after call to `setInspectMode` or when
         * user manually inspects an element.
         */
        onInspectNodeRequested(listener: (event: { params: Protocol.Overlay.InspectNodeRequestedEvent }) => void): void;
        offInspectNodeRequested(listener: (event: { params: Protocol.Overlay.InspectNodeRequestedEvent }) => void): void;
        onceInspectNodeRequested(eventMatcher?: (event: { params: Protocol.Overlay.InspectNodeRequestedEvent }) => boolean): Promise<{ params: Protocol.Overlay.InspectNodeRequestedEvent }>;

        /**
         * Fired when the node should be highlighted. This happens after call to `setInspectMode`.
         */
        onNodeHighlightRequested(listener: (event: { params: Protocol.Overlay.NodeHighlightRequestedEvent }) => void): void;
        offNodeHighlightRequested(listener: (event: { params: Protocol.Overlay.NodeHighlightRequestedEvent }) => void): void;
        onceNodeHighlightRequested(eventMatcher?: (event: { params: Protocol.Overlay.NodeHighlightRequestedEvent }) => boolean): Promise<{ params: Protocol.Overlay.NodeHighlightRequestedEvent }>;

        /**
         * Fired when user asks to capture screenshot of some area on the page.
         */
        onScreenshotRequested(listener: (event: { params: Protocol.Overlay.ScreenshotRequestedEvent }) => void): void;
        offScreenshotRequested(listener: (event: { params: Protocol.Overlay.ScreenshotRequestedEvent }) => void): void;
        onceScreenshotRequested(eventMatcher?: (event: { params: Protocol.Overlay.ScreenshotRequestedEvent }) => boolean): Promise<{ params: Protocol.Overlay.ScreenshotRequestedEvent }>;

        /**
         * Fired when user cancels the inspect mode.
         */
        onInspectModeCanceled(listener: (event: ) => void): void;
        offInspectModeCanceled(listener: (event: ) => void): void;
        onceInspectModeCanceled(eventMatcher?: (event: ) => boolean): Promise<>;

    }

    export interface PageApi {
        /**
         * Deprecated, please use addScriptToEvaluateOnNewDocument instead.
         */
        addScriptToEvaluateOnLoad(params: Protocol.Page.AddScriptToEvaluateOnLoadRequest): Promise<{id: number, result: Protocol.Page.AddScriptToEvaluateOnLoadResponse, sessionId: string}>;

        /**
         * Evaluates given script in every frame upon creation (before loading frame's scripts).
         */
        addScriptToEvaluateOnNewDocument(params: Protocol.Page.AddScriptToEvaluateOnNewDocumentRequest): Promise<{id: number, result: Protocol.Page.AddScriptToEvaluateOnNewDocumentResponse, sessionId: string}>;

        /**
         * Brings page to front (activates tab).
         */
        bringToFront(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Capture page screenshot.
         */
        captureScreenshot(params: Protocol.Page.CaptureScreenshotRequest): Promise<{id: number, result: Protocol.Page.CaptureScreenshotResponse, sessionId: string}>;

        /**
         * Returns a snapshot of the page as a string. For MHTML format, the serialization includes
         * iframes, shadow DOM, external resources, and element-inline styles.
         */
        captureSnapshot(params: Protocol.Page.CaptureSnapshotRequest): Promise<{id: number, result: Protocol.Page.CaptureSnapshotResponse, sessionId: string}>;

        /**
         * Clears the overridden device metrics.
         */
        clearDeviceMetricsOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears the overridden Device Orientation.
         */
        clearDeviceOrientationOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears the overridden Geolocation Position and Error.
         */
        clearGeolocationOverride(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Creates an isolated world for the given frame.
         */
        createIsolatedWorld(params: Protocol.Page.CreateIsolatedWorldRequest): Promise<{id: number, result: Protocol.Page.CreateIsolatedWorldResponse, sessionId: string}>;

        /**
         * Deletes browser cookie with given name, domain and path.
         */
        deleteCookie(params: Protocol.Page.DeleteCookieRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables page domain notifications.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables page domain notifications.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Gets the processed manifest for this current document.
         *   This API always waits for the manifest to be loaded.
         *   If manifestId is provided, and it does not match the manifest of the
         *     current document, this API errors out.
         *   If there is not a loaded page, this API errors out immediately.
         */
        getAppManifest(params: Protocol.Page.GetAppManifestRequest): Promise<{id: number, result: Protocol.Page.GetAppManifestResponse, sessionId: string}>;

        getInstallabilityErrors(): Promise<{id: number, result: Protocol.Page.GetInstallabilityErrorsResponse, sessionId: string}>;

        /**
         * Deprecated because it's not guaranteed that the returned icon is in fact the one used for PWA installation.
         */
        getManifestIcons(): Promise<{id: number, result: Protocol.Page.GetManifestIconsResponse, sessionId: string}>;

        /**
         * Returns the unique (PWA) app id.
         * Only returns values if the feature flag 'WebAppEnableManifestId' is enabled
         */
        getAppId(): Promise<{id: number, result: Protocol.Page.GetAppIdResponse, sessionId: string}>;

        getAdScriptId(params: Protocol.Page.GetAdScriptIdRequest): Promise<{id: number, result: Protocol.Page.GetAdScriptIdResponse, sessionId: string}>;

        /**
         * Returns present frame tree structure.
         */
        getFrameTree(): Promise<{id: number, result: Protocol.Page.GetFrameTreeResponse, sessionId: string}>;

        /**
         * Returns metrics relating to the layouting of the page, such as viewport bounds/scale.
         */
        getLayoutMetrics(): Promise<{id: number, result: Protocol.Page.GetLayoutMetricsResponse, sessionId: string}>;

        /**
         * Returns navigation history for the current page.
         */
        getNavigationHistory(): Promise<{id: number, result: Protocol.Page.GetNavigationHistoryResponse, sessionId: string}>;

        /**
         * Resets navigation history for the current page.
         */
        resetNavigationHistory(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns content of the given resource.
         */
        getResourceContent(params: Protocol.Page.GetResourceContentRequest): Promise<{id: number, result: Protocol.Page.GetResourceContentResponse, sessionId: string}>;

        /**
         * Returns present frame / resource tree structure.
         */
        getResourceTree(): Promise<{id: number, result: Protocol.Page.GetResourceTreeResponse, sessionId: string}>;

        /**
         * Accepts or dismisses a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload).
         */
        handleJavaScriptDialog(params: Protocol.Page.HandleJavaScriptDialogRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Navigates current page to the given URL.
         */
        navigate(params: Protocol.Page.NavigateRequest): Promise<{id: number, result: Protocol.Page.NavigateResponse, sessionId: string}>;

        /**
         * Navigates current page to the given history entry.
         */
        navigateToHistoryEntry(params: Protocol.Page.NavigateToHistoryEntryRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Print page as PDF.
         */
        printToPDF(params: Protocol.Page.PrintToPDFRequest): Promise<{id: number, result: Protocol.Page.PrintToPDFResponse, sessionId: string}>;

        /**
         * Reloads given page optionally ignoring the cache.
         */
        reload(params: Protocol.Page.ReloadRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deprecated, please use removeScriptToEvaluateOnNewDocument instead.
         */
        removeScriptToEvaluateOnLoad(params: Protocol.Page.RemoveScriptToEvaluateOnLoadRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes given script from the list.
         */
        removeScriptToEvaluateOnNewDocument(params: Protocol.Page.RemoveScriptToEvaluateOnNewDocumentRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Acknowledges that a screencast frame has been received by the frontend.
         */
        screencastFrameAck(params: Protocol.Page.ScreencastFrameAckRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Searches for given string in resource content.
         */
        searchInResource(params: Protocol.Page.SearchInResourceRequest): Promise<{id: number, result: Protocol.Page.SearchInResourceResponse, sessionId: string}>;

        /**
         * Enable Chrome's experimental ad filter on all sites.
         */
        setAdBlockingEnabled(params: Protocol.Page.SetAdBlockingEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable page Content Security Policy by-passing.
         */
        setBypassCSP(params: Protocol.Page.SetBypassCSPRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Get Permissions Policy state on given frame.
         */
        getPermissionsPolicyState(params: Protocol.Page.GetPermissionsPolicyStateRequest): Promise<{id: number, result: Protocol.Page.GetPermissionsPolicyStateResponse, sessionId: string}>;

        /**
         * Get Origin Trials on given frame.
         */
        getOriginTrials(params: Protocol.Page.GetOriginTrialsRequest): Promise<{id: number, result: Protocol.Page.GetOriginTrialsResponse, sessionId: string}>;

        /**
         * Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
         * window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
         * query results).
         */
        setDeviceMetricsOverride(params: Protocol.Page.SetDeviceMetricsOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the Device Orientation.
         */
        setDeviceOrientationOverride(params: Protocol.Page.SetDeviceOrientationOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set generic font families.
         */
        setFontFamilies(params: Protocol.Page.SetFontFamiliesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set default font sizes.
         */
        setFontSizes(params: Protocol.Page.SetFontSizesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets given markup as the document's HTML.
         */
        setDocumentContent(params: Protocol.Page.SetDocumentContentRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set the behavior when downloading a file.
         */
        setDownloadBehavior(params: Protocol.Page.SetDownloadBehaviorRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position
         * unavailable.
         */
        setGeolocationOverride(params: Protocol.Page.SetGeolocationOverrideRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Controls whether page will emit lifecycle events.
         */
        setLifecycleEventsEnabled(params: Protocol.Page.SetLifecycleEventsEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Toggles mouse event-based touch event emulation.
         */
        setTouchEmulationEnabled(params: Protocol.Page.SetTouchEmulationEnabledRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Starts sending each frame using the `screencastFrame` event.
         */
        startScreencast(params: Protocol.Page.StartScreencastRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Force the page stop all navigations and pending resource fetches.
         */
        stopLoading(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Crashes renderer on the IO thread, generates minidumps.
         */
        crash(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Tries to close page, running its beforeunload hooks, if any.
         */
        close(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Tries to update the web lifecycle state of the page.
         * It will transition the page to the given state according to:
         * https://github.com/WICG/web-lifecycle/
         */
        setWebLifecycleState(params: Protocol.Page.SetWebLifecycleStateRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Stops sending each frame in the `screencastFrame`.
         */
        stopScreencast(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Requests backend to produce compilation cache for the specified scripts.
         * `scripts` are appended to the list of scripts for which the cache
         * would be produced. The list may be reset during page navigation.
         * When script with a matching URL is encountered, the cache is optionally
         * produced upon backend discretion, based on internal heuristics.
         * See also: `Page.compilationCacheProduced`.
         */
        produceCompilationCache(params: Protocol.Page.ProduceCompilationCacheRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Seeds compilation cache for given url. Compilation cache does not survive
         * cross-process navigation.
         */
        addCompilationCache(params: Protocol.Page.AddCompilationCacheRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears seeded compilation cache.
         */
        clearCompilationCache(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets the Secure Payment Confirmation transaction mode.
         * https://w3c.github.io/secure-payment-confirmation/#sctn-automation-set-spc-transaction-mode
         */
        setSPCTransactionMode(params: Protocol.Page.SetSPCTransactionModeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Extensions for Custom Handlers API:
         * https://html.spec.whatwg.org/multipage/system-state.html#rph-automation
         */
        setRPHRegistrationMode(params: Protocol.Page.SetRPHRegistrationModeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Generates a report for testing.
         */
        generateTestReport(params: Protocol.Page.GenerateTestReportRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Pauses page execution. Can be resumed using generic Runtime.runIfWaitingForDebugger.
         */
        waitForDebugger(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Intercept file chooser requests and transfer control to protocol clients.
         * When file chooser interception is enabled, native file chooser dialog is not shown.
         * Instead, a protocol event `Page.fileChooserOpened` is emitted.
         */
        setInterceptFileChooserDialog(params: Protocol.Page.SetInterceptFileChooserDialogRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable/disable prerendering manually.
         * 
         * This command is a short-term solution for https://crbug.com/1440085.
         * See https://docs.google.com/document/d/12HVmFxYj5Jc-eJr5OmWsa2bqTJsbgGLKI6ZIyx0_wpA
         * for more details.
         * 
         * TODO(https://crbug.com/1440085): Remove this once Puppeteer supports tab targets.
         */
        setPrerenderingAllowed(params: Protocol.Page.SetPrerenderingAllowedRequest): Promise<{id: number, result: void, sessionId: string}>;

        onDomContentEventFired(listener: (event: { params: Protocol.Page.DomContentEventFiredEvent }) => void): void;
        offDomContentEventFired(listener: (event: { params: Protocol.Page.DomContentEventFiredEvent }) => void): void;
        onceDomContentEventFired(eventMatcher?: (event: { params: Protocol.Page.DomContentEventFiredEvent }) => boolean): Promise<{ params: Protocol.Page.DomContentEventFiredEvent }>;

        /**
         * Emitted only when `page.interceptFileChooser` is enabled.
         */
        onFileChooserOpened(listener: (event: { params: Protocol.Page.FileChooserOpenedEvent }) => void): void;
        offFileChooserOpened(listener: (event: { params: Protocol.Page.FileChooserOpenedEvent }) => void): void;
        onceFileChooserOpened(eventMatcher?: (event: { params: Protocol.Page.FileChooserOpenedEvent }) => boolean): Promise<{ params: Protocol.Page.FileChooserOpenedEvent }>;

        /**
         * Fired when frame has been attached to its parent.
         */
        onFrameAttached(listener: (event: { params: Protocol.Page.FrameAttachedEvent }) => void): void;
        offFrameAttached(listener: (event: { params: Protocol.Page.FrameAttachedEvent }) => void): void;
        onceFrameAttached(eventMatcher?: (event: { params: Protocol.Page.FrameAttachedEvent }) => boolean): Promise<{ params: Protocol.Page.FrameAttachedEvent }>;

        /**
         * Fired when frame no longer has a scheduled navigation.
         */
        onFrameClearedScheduledNavigation(listener: (event: { params: Protocol.Page.FrameClearedScheduledNavigationEvent }) => void): void;
        offFrameClearedScheduledNavigation(listener: (event: { params: Protocol.Page.FrameClearedScheduledNavigationEvent }) => void): void;
        onceFrameClearedScheduledNavigation(eventMatcher?: (event: { params: Protocol.Page.FrameClearedScheduledNavigationEvent }) => boolean): Promise<{ params: Protocol.Page.FrameClearedScheduledNavigationEvent }>;

        /**
         * Fired when frame has been detached from its parent.
         */
        onFrameDetached(listener: (event: { params: Protocol.Page.FrameDetachedEvent }) => void): void;
        offFrameDetached(listener: (event: { params: Protocol.Page.FrameDetachedEvent }) => void): void;
        onceFrameDetached(eventMatcher?: (event: { params: Protocol.Page.FrameDetachedEvent }) => boolean): Promise<{ params: Protocol.Page.FrameDetachedEvent }>;

        /**
         * Fired once navigation of the frame has completed. Frame is now associated with the new loader.
         */
        onFrameNavigated(listener: (event: { params: Protocol.Page.FrameNavigatedEvent }) => void): void;
        offFrameNavigated(listener: (event: { params: Protocol.Page.FrameNavigatedEvent }) => void): void;
        onceFrameNavigated(eventMatcher?: (event: { params: Protocol.Page.FrameNavigatedEvent }) => boolean): Promise<{ params: Protocol.Page.FrameNavigatedEvent }>;

        /**
         * Fired when opening document to write to.
         */
        onDocumentOpened(listener: (event: { params: Protocol.Page.DocumentOpenedEvent }) => void): void;
        offDocumentOpened(listener: (event: { params: Protocol.Page.DocumentOpenedEvent }) => void): void;
        onceDocumentOpened(eventMatcher?: (event: { params: Protocol.Page.DocumentOpenedEvent }) => boolean): Promise<{ params: Protocol.Page.DocumentOpenedEvent }>;

        onFrameResized(listener: (event: ) => void): void;
        offFrameResized(listener: (event: ) => void): void;
        onceFrameResized(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when a renderer-initiated navigation is requested.
         * Navigation may still be cancelled after the event is issued.
         */
        onFrameRequestedNavigation(listener: (event: { params: Protocol.Page.FrameRequestedNavigationEvent }) => void): void;
        offFrameRequestedNavigation(listener: (event: { params: Protocol.Page.FrameRequestedNavigationEvent }) => void): void;
        onceFrameRequestedNavigation(eventMatcher?: (event: { params: Protocol.Page.FrameRequestedNavigationEvent }) => boolean): Promise<{ params: Protocol.Page.FrameRequestedNavigationEvent }>;

        /**
         * Fired when frame schedules a potential navigation.
         */
        onFrameScheduledNavigation(listener: (event: { params: Protocol.Page.FrameScheduledNavigationEvent }) => void): void;
        offFrameScheduledNavigation(listener: (event: { params: Protocol.Page.FrameScheduledNavigationEvent }) => void): void;
        onceFrameScheduledNavigation(eventMatcher?: (event: { params: Protocol.Page.FrameScheduledNavigationEvent }) => boolean): Promise<{ params: Protocol.Page.FrameScheduledNavigationEvent }>;

        /**
         * Fired when frame has started loading.
         */
        onFrameStartedLoading(listener: (event: { params: Protocol.Page.FrameStartedLoadingEvent }) => void): void;
        offFrameStartedLoading(listener: (event: { params: Protocol.Page.FrameStartedLoadingEvent }) => void): void;
        onceFrameStartedLoading(eventMatcher?: (event: { params: Protocol.Page.FrameStartedLoadingEvent }) => boolean): Promise<{ params: Protocol.Page.FrameStartedLoadingEvent }>;

        /**
         * Fired when frame has stopped loading.
         */
        onFrameStoppedLoading(listener: (event: { params: Protocol.Page.FrameStoppedLoadingEvent }) => void): void;
        offFrameStoppedLoading(listener: (event: { params: Protocol.Page.FrameStoppedLoadingEvent }) => void): void;
        onceFrameStoppedLoading(eventMatcher?: (event: { params: Protocol.Page.FrameStoppedLoadingEvent }) => boolean): Promise<{ params: Protocol.Page.FrameStoppedLoadingEvent }>;

        /**
         * Fired when page is about to start a download.
         * Deprecated. Use Browser.downloadWillBegin instead.
         */
        onDownloadWillBegin(listener: (event: { params: Protocol.Page.DownloadWillBeginEvent }) => void): void;
        offDownloadWillBegin(listener: (event: { params: Protocol.Page.DownloadWillBeginEvent }) => void): void;
        onceDownloadWillBegin(eventMatcher?: (event: { params: Protocol.Page.DownloadWillBeginEvent }) => boolean): Promise<{ params: Protocol.Page.DownloadWillBeginEvent }>;

        /**
         * Fired when download makes progress. Last call has |done| == true.
         * Deprecated. Use Browser.downloadProgress instead.
         */
        onDownloadProgress(listener: (event: { params: Protocol.Page.DownloadProgressEvent }) => void): void;
        offDownloadProgress(listener: (event: { params: Protocol.Page.DownloadProgressEvent }) => void): void;
        onceDownloadProgress(eventMatcher?: (event: { params: Protocol.Page.DownloadProgressEvent }) => boolean): Promise<{ params: Protocol.Page.DownloadProgressEvent }>;

        /**
         * Fired when interstitial page was hidden
         */
        onInterstitialHidden(listener: (event: ) => void): void;
        offInterstitialHidden(listener: (event: ) => void): void;
        onceInterstitialHidden(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when interstitial page was shown
         */
        onInterstitialShown(listener: (event: ) => void): void;
        offInterstitialShown(listener: (event: ) => void): void;
        onceInterstitialShown(eventMatcher?: (event: ) => boolean): Promise<>;

        /**
         * Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) has been
         * closed.
         */
        onJavascriptDialogClosed(listener: (event: { params: Protocol.Page.JavascriptDialogClosedEvent }) => void): void;
        offJavascriptDialogClosed(listener: (event: { params: Protocol.Page.JavascriptDialogClosedEvent }) => void): void;
        onceJavascriptDialogClosed(eventMatcher?: (event: { params: Protocol.Page.JavascriptDialogClosedEvent }) => boolean): Promise<{ params: Protocol.Page.JavascriptDialogClosedEvent }>;

        /**
         * Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) is about to
         * open.
         */
        onJavascriptDialogOpening(listener: (event: { params: Protocol.Page.JavascriptDialogOpeningEvent }) => void): void;
        offJavascriptDialogOpening(listener: (event: { params: Protocol.Page.JavascriptDialogOpeningEvent }) => void): void;
        onceJavascriptDialogOpening(eventMatcher?: (event: { params: Protocol.Page.JavascriptDialogOpeningEvent }) => boolean): Promise<{ params: Protocol.Page.JavascriptDialogOpeningEvent }>;

        /**
         * Fired for top level page lifecycle events such as navigation, load, paint, etc.
         */
        onLifecycleEvent(listener: (event: { params: Protocol.Page.LifecycleEventEvent }) => void): void;
        offLifecycleEvent(listener: (event: { params: Protocol.Page.LifecycleEventEvent }) => void): void;
        onceLifecycleEvent(eventMatcher?: (event: { params: Protocol.Page.LifecycleEventEvent }) => boolean): Promise<{ params: Protocol.Page.LifecycleEventEvent }>;

        /**
         * Fired for failed bfcache history navigations if BackForwardCache feature is enabled. Do
         * not assume any ordering with the Page.frameNavigated event. This event is fired only for
         * main-frame history navigation where the document changes (non-same-document navigations),
         * when bfcache navigation fails.
         */
        onBackForwardCacheNotUsed(listener: (event: { params: Protocol.Page.BackForwardCacheNotUsedEvent }) => void): void;
        offBackForwardCacheNotUsed(listener: (event: { params: Protocol.Page.BackForwardCacheNotUsedEvent }) => void): void;
        onceBackForwardCacheNotUsed(eventMatcher?: (event: { params: Protocol.Page.BackForwardCacheNotUsedEvent }) => boolean): Promise<{ params: Protocol.Page.BackForwardCacheNotUsedEvent }>;

        onLoadEventFired(listener: (event: { params: Protocol.Page.LoadEventFiredEvent }) => void): void;
        offLoadEventFired(listener: (event: { params: Protocol.Page.LoadEventFiredEvent }) => void): void;
        onceLoadEventFired(eventMatcher?: (event: { params: Protocol.Page.LoadEventFiredEvent }) => boolean): Promise<{ params: Protocol.Page.LoadEventFiredEvent }>;

        /**
         * Fired when same-document navigation happens, e.g. due to history API usage or anchor navigation.
         */
        onNavigatedWithinDocument(listener: (event: { params: Protocol.Page.NavigatedWithinDocumentEvent }) => void): void;
        offNavigatedWithinDocument(listener: (event: { params: Protocol.Page.NavigatedWithinDocumentEvent }) => void): void;
        onceNavigatedWithinDocument(eventMatcher?: (event: { params: Protocol.Page.NavigatedWithinDocumentEvent }) => boolean): Promise<{ params: Protocol.Page.NavigatedWithinDocumentEvent }>;

        /**
         * Compressed image data requested by the `startScreencast`.
         */
        onScreencastFrame(listener: (event: { params: Protocol.Page.ScreencastFrameEvent }) => void): void;
        offScreencastFrame(listener: (event: { params: Protocol.Page.ScreencastFrameEvent }) => void): void;
        onceScreencastFrame(eventMatcher?: (event: { params: Protocol.Page.ScreencastFrameEvent }) => boolean): Promise<{ params: Protocol.Page.ScreencastFrameEvent }>;

        /**
         * Fired when the page with currently enabled screencast was shown or hidden `.
         */
        onScreencastVisibilityChanged(listener: (event: { params: Protocol.Page.ScreencastVisibilityChangedEvent }) => void): void;
        offScreencastVisibilityChanged(listener: (event: { params: Protocol.Page.ScreencastVisibilityChangedEvent }) => void): void;
        onceScreencastVisibilityChanged(eventMatcher?: (event: { params: Protocol.Page.ScreencastVisibilityChangedEvent }) => boolean): Promise<{ params: Protocol.Page.ScreencastVisibilityChangedEvent }>;

        /**
         * Fired when a new window is going to be opened, via window.open(), link click, form submission,
         * etc.
         */
        onWindowOpen(listener: (event: { params: Protocol.Page.WindowOpenEvent }) => void): void;
        offWindowOpen(listener: (event: { params: Protocol.Page.WindowOpenEvent }) => void): void;
        onceWindowOpen(eventMatcher?: (event: { params: Protocol.Page.WindowOpenEvent }) => boolean): Promise<{ params: Protocol.Page.WindowOpenEvent }>;

        /**
         * Issued for every compilation cache generated. Is only available
         * if Page.setGenerateCompilationCache is enabled.
         */
        onCompilationCacheProduced(listener: (event: { params: Protocol.Page.CompilationCacheProducedEvent }) => void): void;
        offCompilationCacheProduced(listener: (event: { params: Protocol.Page.CompilationCacheProducedEvent }) => void): void;
        onceCompilationCacheProduced(eventMatcher?: (event: { params: Protocol.Page.CompilationCacheProducedEvent }) => boolean): Promise<{ params: Protocol.Page.CompilationCacheProducedEvent }>;

    }

    export interface PerformanceApi {
        /**
         * Disable collecting and reporting metrics.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable collecting and reporting metrics.
         */
        enable(params: Protocol.Performance.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets time domain to use for collecting and reporting duration metrics.
         * Note that this must be called before enabling metrics collection. Calling
         * this method while metrics collection is enabled returns an error.
         */
        setTimeDomain(params: Protocol.Performance.SetTimeDomainRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Retrieve current values of run-time metrics.
         */
        getMetrics(): Promise<{id: number, result: Protocol.Performance.GetMetricsResponse, sessionId: string}>;

        /**
         * Current values of the metrics.
         */
        onMetrics(listener: (event: { params: Protocol.Performance.MetricsEvent }) => void): void;
        offMetrics(listener: (event: { params: Protocol.Performance.MetricsEvent }) => void): void;
        onceMetrics(eventMatcher?: (event: { params: Protocol.Performance.MetricsEvent }) => boolean): Promise<{ params: Protocol.Performance.MetricsEvent }>;

    }

    export interface PerformanceTimelineApi {
        /**
         * Previously buffered events would be reported before method returns.
         * See also: timelineEventAdded
         */
        enable(params: Protocol.PerformanceTimeline.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sent when a performance timeline event is added. See reportPerformanceTimeline method.
         */
        onTimelineEventAdded(listener: (event: { params: Protocol.PerformanceTimeline.TimelineEventAddedEvent }) => void): void;
        offTimelineEventAdded(listener: (event: { params: Protocol.PerformanceTimeline.TimelineEventAddedEvent }) => void): void;
        onceTimelineEventAdded(eventMatcher?: (event: { params: Protocol.PerformanceTimeline.TimelineEventAddedEvent }) => boolean): Promise<{ params: Protocol.PerformanceTimeline.TimelineEventAddedEvent }>;

    }

    export interface SecurityApi {
        /**
         * Disables tracking security state changes.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables tracking security state changes.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable/disable whether all certificate errors should be ignored.
         */
        setIgnoreCertificateErrors(params: Protocol.Security.SetIgnoreCertificateErrorsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Handles a certificate error that fired a certificateError event.
         */
        handleCertificateError(params: Protocol.Security.HandleCertificateErrorRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enable/disable overriding certificate errors. If enabled, all certificate error events need to
         * be handled by the DevTools client and should be answered with `handleCertificateError` commands.
         */
        setOverrideCertificateErrors(params: Protocol.Security.SetOverrideCertificateErrorsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * There is a certificate error. If overriding certificate errors is enabled, then it should be
         * handled with the `handleCertificateError` command. Note: this event does not fire if the
         * certificate error has been allowed internally. Only one client per target should override
         * certificate errors at the same time.
         */
        onCertificateError(listener: (event: { params: Protocol.Security.CertificateErrorEvent }) => void): void;
        offCertificateError(listener: (event: { params: Protocol.Security.CertificateErrorEvent }) => void): void;
        onceCertificateError(eventMatcher?: (event: { params: Protocol.Security.CertificateErrorEvent }) => boolean): Promise<{ params: Protocol.Security.CertificateErrorEvent }>;

        /**
         * The security state of the page changed.
         */
        onVisibleSecurityStateChanged(listener: (event: { params: Protocol.Security.VisibleSecurityStateChangedEvent }) => void): void;
        offVisibleSecurityStateChanged(listener: (event: { params: Protocol.Security.VisibleSecurityStateChangedEvent }) => void): void;
        onceVisibleSecurityStateChanged(eventMatcher?: (event: { params: Protocol.Security.VisibleSecurityStateChangedEvent }) => boolean): Promise<{ params: Protocol.Security.VisibleSecurityStateChangedEvent }>;

        /**
         * The security state of the page changed. No longer being sent.
         */
        onSecurityStateChanged(listener: (event: { params: Protocol.Security.SecurityStateChangedEvent }) => void): void;
        offSecurityStateChanged(listener: (event: { params: Protocol.Security.SecurityStateChangedEvent }) => void): void;
        onceSecurityStateChanged(eventMatcher?: (event: { params: Protocol.Security.SecurityStateChangedEvent }) => boolean): Promise<{ params: Protocol.Security.SecurityStateChangedEvent }>;

    }

    export interface ServiceWorkerApi {
        deliverPushMessage(params: Protocol.ServiceWorker.DeliverPushMessageRequest): Promise<{id: number, result: void, sessionId: string}>;

        disable(): Promise<{id: number, result: void, sessionId: string}>;

        dispatchSyncEvent(params: Protocol.ServiceWorker.DispatchSyncEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        dispatchPeriodicSyncEvent(params: Protocol.ServiceWorker.DispatchPeriodicSyncEventRequest): Promise<{id: number, result: void, sessionId: string}>;

        enable(): Promise<{id: number, result: void, sessionId: string}>;

        inspectWorker(params: Protocol.ServiceWorker.InspectWorkerRequest): Promise<{id: number, result: void, sessionId: string}>;

        setForceUpdateOnPageLoad(params: Protocol.ServiceWorker.SetForceUpdateOnPageLoadRequest): Promise<{id: number, result: void, sessionId: string}>;

        skipWaiting(params: Protocol.ServiceWorker.SkipWaitingRequest): Promise<{id: number, result: void, sessionId: string}>;

        startWorker(params: Protocol.ServiceWorker.StartWorkerRequest): Promise<{id: number, result: void, sessionId: string}>;

        stopAllWorkers(): Promise<{id: number, result: void, sessionId: string}>;

        stopWorker(params: Protocol.ServiceWorker.StopWorkerRequest): Promise<{id: number, result: void, sessionId: string}>;

        unregister(params: Protocol.ServiceWorker.UnregisterRequest): Promise<{id: number, result: void, sessionId: string}>;

        updateRegistration(params: Protocol.ServiceWorker.UpdateRegistrationRequest): Promise<{id: number, result: void, sessionId: string}>;

        onWorkerErrorReported(listener: (event: { params: Protocol.ServiceWorker.WorkerErrorReportedEvent }) => void): void;
        offWorkerErrorReported(listener: (event: { params: Protocol.ServiceWorker.WorkerErrorReportedEvent }) => void): void;
        onceWorkerErrorReported(eventMatcher?: (event: { params: Protocol.ServiceWorker.WorkerErrorReportedEvent }) => boolean): Promise<{ params: Protocol.ServiceWorker.WorkerErrorReportedEvent }>;

        onWorkerRegistrationUpdated(listener: (event: { params: Protocol.ServiceWorker.WorkerRegistrationUpdatedEvent }) => void): void;
        offWorkerRegistrationUpdated(listener: (event: { params: Protocol.ServiceWorker.WorkerRegistrationUpdatedEvent }) => void): void;
        onceWorkerRegistrationUpdated(eventMatcher?: (event: { params: Protocol.ServiceWorker.WorkerRegistrationUpdatedEvent }) => boolean): Promise<{ params: Protocol.ServiceWorker.WorkerRegistrationUpdatedEvent }>;

        onWorkerVersionUpdated(listener: (event: { params: Protocol.ServiceWorker.WorkerVersionUpdatedEvent }) => void): void;
        offWorkerVersionUpdated(listener: (event: { params: Protocol.ServiceWorker.WorkerVersionUpdatedEvent }) => void): void;
        onceWorkerVersionUpdated(eventMatcher?: (event: { params: Protocol.ServiceWorker.WorkerVersionUpdatedEvent }) => boolean): Promise<{ params: Protocol.ServiceWorker.WorkerVersionUpdatedEvent }>;

    }

    export interface StorageApi {
        /**
         * Returns a storage key given a frame id.
         */
        getStorageKeyForFrame(params: Protocol.Storage.GetStorageKeyForFrameRequest): Promise<{id: number, result: Protocol.Storage.GetStorageKeyForFrameResponse, sessionId: string}>;

        /**
         * Clears storage for origin.
         */
        clearDataForOrigin(params: Protocol.Storage.ClearDataForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears storage for storage key.
         */
        clearDataForStorageKey(params: Protocol.Storage.ClearDataForStorageKeyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns all browser cookies.
         */
        getCookies(params: Protocol.Storage.GetCookiesRequest): Promise<{id: number, result: Protocol.Storage.GetCookiesResponse, sessionId: string}>;

        /**
         * Sets given cookies.
         */
        setCookies(params: Protocol.Storage.SetCookiesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears cookies.
         */
        clearCookies(params: Protocol.Storage.ClearCookiesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns usage and quota in bytes.
         */
        getUsageAndQuota(params: Protocol.Storage.GetUsageAndQuotaRequest): Promise<{id: number, result: Protocol.Storage.GetUsageAndQuotaResponse, sessionId: string}>;

        /**
         * Override quota for the specified origin
         */
        overrideQuotaForOrigin(params: Protocol.Storage.OverrideQuotaForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Registers origin to be notified when an update occurs to its cache storage list.
         */
        trackCacheStorageForOrigin(params: Protocol.Storage.TrackCacheStorageForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Registers storage key to be notified when an update occurs to its cache storage list.
         */
        trackCacheStorageForStorageKey(params: Protocol.Storage.TrackCacheStorageForStorageKeyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Registers origin to be notified when an update occurs to its IndexedDB.
         */
        trackIndexedDBForOrigin(params: Protocol.Storage.TrackIndexedDBForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Registers storage key to be notified when an update occurs to its IndexedDB.
         */
        trackIndexedDBForStorageKey(params: Protocol.Storage.TrackIndexedDBForStorageKeyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Unregisters origin from receiving notifications for cache storage.
         */
        untrackCacheStorageForOrigin(params: Protocol.Storage.UntrackCacheStorageForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Unregisters storage key from receiving notifications for cache storage.
         */
        untrackCacheStorageForStorageKey(params: Protocol.Storage.UntrackCacheStorageForStorageKeyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Unregisters origin from receiving notifications for IndexedDB.
         */
        untrackIndexedDBForOrigin(params: Protocol.Storage.UntrackIndexedDBForOriginRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Unregisters storage key from receiving notifications for IndexedDB.
         */
        untrackIndexedDBForStorageKey(params: Protocol.Storage.UntrackIndexedDBForStorageKeyRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns the number of stored Trust Tokens per issuer for the
         * current browsing context.
         */
        getTrustTokens(): Promise<{id: number, result: Protocol.Storage.GetTrustTokensResponse, sessionId: string}>;

        /**
         * Removes all Trust Tokens issued by the provided issuerOrigin.
         * Leaves other stored data, including the issuer's Redemption Records, intact.
         */
        clearTrustTokens(params: Protocol.Storage.ClearTrustTokensRequest): Promise<{id: number, result: Protocol.Storage.ClearTrustTokensResponse, sessionId: string}>;

        /**
         * Gets details for a named interest group.
         */
        getInterestGroupDetails(params: Protocol.Storage.GetInterestGroupDetailsRequest): Promise<{id: number, result: Protocol.Storage.GetInterestGroupDetailsResponse, sessionId: string}>;

        /**
         * Enables/Disables issuing of interestGroupAccessed events.
         */
        setInterestGroupTracking(params: Protocol.Storage.SetInterestGroupTrackingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables/Disables issuing of interestGroupAuctionEventOccurred and
         * interestGroupAuctionNetworkRequestCreated.
         */
        setInterestGroupAuctionTracking(params: Protocol.Storage.SetInterestGroupAuctionTrackingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Gets metadata for an origin's shared storage.
         */
        getSharedStorageMetadata(params: Protocol.Storage.GetSharedStorageMetadataRequest): Promise<{id: number, result: Protocol.Storage.GetSharedStorageMetadataResponse, sessionId: string}>;

        /**
         * Gets the entries in an given origin's shared storage.
         */
        getSharedStorageEntries(params: Protocol.Storage.GetSharedStorageEntriesRequest): Promise<{id: number, result: Protocol.Storage.GetSharedStorageEntriesResponse, sessionId: string}>;

        /**
         * Sets entry with `key` and `value` for a given origin's shared storage.
         */
        setSharedStorageEntry(params: Protocol.Storage.SetSharedStorageEntryRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes entry for `key` (if it exists) for a given origin's shared storage.
         */
        deleteSharedStorageEntry(params: Protocol.Storage.DeleteSharedStorageEntryRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears all entries for a given origin's shared storage.
         */
        clearSharedStorageEntries(params: Protocol.Storage.ClearSharedStorageEntriesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Resets the budget for `ownerOrigin` by clearing all budget withdrawals.
         */
        resetSharedStorageBudget(params: Protocol.Storage.ResetSharedStorageBudgetRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables/disables issuing of sharedStorageAccessed events.
         */
        setSharedStorageTracking(params: Protocol.Storage.SetSharedStorageTrackingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Set tracking for a storage key's buckets.
         */
        setStorageBucketTracking(params: Protocol.Storage.SetStorageBucketTrackingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes the Storage Bucket with the given storage key and bucket name.
         */
        deleteStorageBucket(params: Protocol.Storage.DeleteStorageBucketRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes state for sites identified as potential bounce trackers, immediately.
         */
        runBounceTrackingMitigations(): Promise<{id: number, result: Protocol.Storage.RunBounceTrackingMitigationsResponse, sessionId: string}>;

        /**
         * https://wicg.github.io/attribution-reporting-api/
         */
        setAttributionReportingLocalTestingMode(params: Protocol.Storage.SetAttributionReportingLocalTestingModeRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables/disables issuing of Attribution Reporting events.
         */
        setAttributionReportingTracking(params: Protocol.Storage.SetAttributionReportingTrackingRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sends all pending Attribution Reports immediately, regardless of their
         * scheduled report time.
         */
        sendPendingAttributionReports(): Promise<{id: number, result: Protocol.Storage.SendPendingAttributionReportsResponse, sessionId: string}>;

        /**
         * Returns the effective Related Website Sets in use by this profile for the browser
         * session. The effective Related Website Sets will not change during a browser session.
         */
        getRelatedWebsiteSets(): Promise<{id: number, result: Protocol.Storage.GetRelatedWebsiteSetsResponse, sessionId: string}>;

        /**
         * A cache's contents have been modified.
         */
        onCacheStorageContentUpdated(listener: (event: { params: Protocol.Storage.CacheStorageContentUpdatedEvent }) => void): void;
        offCacheStorageContentUpdated(listener: (event: { params: Protocol.Storage.CacheStorageContentUpdatedEvent }) => void): void;
        onceCacheStorageContentUpdated(eventMatcher?: (event: { params: Protocol.Storage.CacheStorageContentUpdatedEvent }) => boolean): Promise<{ params: Protocol.Storage.CacheStorageContentUpdatedEvent }>;

        /**
         * A cache has been added/deleted.
         */
        onCacheStorageListUpdated(listener: (event: { params: Protocol.Storage.CacheStorageListUpdatedEvent }) => void): void;
        offCacheStorageListUpdated(listener: (event: { params: Protocol.Storage.CacheStorageListUpdatedEvent }) => void): void;
        onceCacheStorageListUpdated(eventMatcher?: (event: { params: Protocol.Storage.CacheStorageListUpdatedEvent }) => boolean): Promise<{ params: Protocol.Storage.CacheStorageListUpdatedEvent }>;

        /**
         * The origin's IndexedDB object store has been modified.
         */
        onIndexedDBContentUpdated(listener: (event: { params: Protocol.Storage.IndexedDBContentUpdatedEvent }) => void): void;
        offIndexedDBContentUpdated(listener: (event: { params: Protocol.Storage.IndexedDBContentUpdatedEvent }) => void): void;
        onceIndexedDBContentUpdated(eventMatcher?: (event: { params: Protocol.Storage.IndexedDBContentUpdatedEvent }) => boolean): Promise<{ params: Protocol.Storage.IndexedDBContentUpdatedEvent }>;

        /**
         * The origin's IndexedDB database list has been modified.
         */
        onIndexedDBListUpdated(listener: (event: { params: Protocol.Storage.IndexedDBListUpdatedEvent }) => void): void;
        offIndexedDBListUpdated(listener: (event: { params: Protocol.Storage.IndexedDBListUpdatedEvent }) => void): void;
        onceIndexedDBListUpdated(eventMatcher?: (event: { params: Protocol.Storage.IndexedDBListUpdatedEvent }) => boolean): Promise<{ params: Protocol.Storage.IndexedDBListUpdatedEvent }>;

        /**
         * One of the interest groups was accessed. Note that these events are global
         * to all targets sharing an interest group store.
         */
        onInterestGroupAccessed(listener: (event: { params: Protocol.Storage.InterestGroupAccessedEvent }) => void): void;
        offInterestGroupAccessed(listener: (event: { params: Protocol.Storage.InterestGroupAccessedEvent }) => void): void;
        onceInterestGroupAccessed(eventMatcher?: (event: { params: Protocol.Storage.InterestGroupAccessedEvent }) => boolean): Promise<{ params: Protocol.Storage.InterestGroupAccessedEvent }>;

        /**
         * An auction involving interest groups is taking place. These events are
         * target-specific.
         */
        onInterestGroupAuctionEventOccurred(listener: (event: { params: Protocol.Storage.InterestGroupAuctionEventOccurredEvent }) => void): void;
        offInterestGroupAuctionEventOccurred(listener: (event: { params: Protocol.Storage.InterestGroupAuctionEventOccurredEvent }) => void): void;
        onceInterestGroupAuctionEventOccurred(eventMatcher?: (event: { params: Protocol.Storage.InterestGroupAuctionEventOccurredEvent }) => boolean): Promise<{ params: Protocol.Storage.InterestGroupAuctionEventOccurredEvent }>;

        /**
         * Specifies which auctions a particular network fetch may be related to, and
         * in what role. Note that it is not ordered with respect to
         * Network.requestWillBeSent (but will happen before loadingFinished
         * loadingFailed).
         */
        onInterestGroupAuctionNetworkRequestCreated(listener: (event: { params: Protocol.Storage.InterestGroupAuctionNetworkRequestCreatedEvent }) => void): void;
        offInterestGroupAuctionNetworkRequestCreated(listener: (event: { params: Protocol.Storage.InterestGroupAuctionNetworkRequestCreatedEvent }) => void): void;
        onceInterestGroupAuctionNetworkRequestCreated(eventMatcher?: (event: { params: Protocol.Storage.InterestGroupAuctionNetworkRequestCreatedEvent }) => boolean): Promise<{ params: Protocol.Storage.InterestGroupAuctionNetworkRequestCreatedEvent }>;

        /**
         * Shared storage was accessed by the associated page.
         * The following parameters are included in all events.
         */
        onSharedStorageAccessed(listener: (event: { params: Protocol.Storage.SharedStorageAccessedEvent }) => void): void;
        offSharedStorageAccessed(listener: (event: { params: Protocol.Storage.SharedStorageAccessedEvent }) => void): void;
        onceSharedStorageAccessed(eventMatcher?: (event: { params: Protocol.Storage.SharedStorageAccessedEvent }) => boolean): Promise<{ params: Protocol.Storage.SharedStorageAccessedEvent }>;

        onStorageBucketCreatedOrUpdated(listener: (event: { params: Protocol.Storage.StorageBucketCreatedOrUpdatedEvent }) => void): void;
        offStorageBucketCreatedOrUpdated(listener: (event: { params: Protocol.Storage.StorageBucketCreatedOrUpdatedEvent }) => void): void;
        onceStorageBucketCreatedOrUpdated(eventMatcher?: (event: { params: Protocol.Storage.StorageBucketCreatedOrUpdatedEvent }) => boolean): Promise<{ params: Protocol.Storage.StorageBucketCreatedOrUpdatedEvent }>;

        onStorageBucketDeleted(listener: (event: { params: Protocol.Storage.StorageBucketDeletedEvent }) => void): void;
        offStorageBucketDeleted(listener: (event: { params: Protocol.Storage.StorageBucketDeletedEvent }) => void): void;
        onceStorageBucketDeleted(eventMatcher?: (event: { params: Protocol.Storage.StorageBucketDeletedEvent }) => boolean): Promise<{ params: Protocol.Storage.StorageBucketDeletedEvent }>;

        onAttributionReportingSourceRegistered(listener: (event: { params: Protocol.Storage.AttributionReportingSourceRegisteredEvent }) => void): void;
        offAttributionReportingSourceRegistered(listener: (event: { params: Protocol.Storage.AttributionReportingSourceRegisteredEvent }) => void): void;
        onceAttributionReportingSourceRegistered(eventMatcher?: (event: { params: Protocol.Storage.AttributionReportingSourceRegisteredEvent }) => boolean): Promise<{ params: Protocol.Storage.AttributionReportingSourceRegisteredEvent }>;

        onAttributionReportingTriggerRegistered(listener: (event: { params: Protocol.Storage.AttributionReportingTriggerRegisteredEvent }) => void): void;
        offAttributionReportingTriggerRegistered(listener: (event: { params: Protocol.Storage.AttributionReportingTriggerRegisteredEvent }) => void): void;
        onceAttributionReportingTriggerRegistered(eventMatcher?: (event: { params: Protocol.Storage.AttributionReportingTriggerRegisteredEvent }) => boolean): Promise<{ params: Protocol.Storage.AttributionReportingTriggerRegisteredEvent }>;

    }

    export interface SystemInfoApi {
        /**
         * Returns information about the system.
         */
        getInfo(): Promise<{id: number, result: Protocol.SystemInfo.GetInfoResponse, sessionId: string}>;

        /**
         * Returns information about the feature state.
         */
        getFeatureState(params: Protocol.SystemInfo.GetFeatureStateRequest): Promise<{id: number, result: Protocol.SystemInfo.GetFeatureStateResponse, sessionId: string}>;

        /**
         * Returns information about all running processes.
         */
        getProcessInfo(): Promise<{id: number, result: Protocol.SystemInfo.GetProcessInfoResponse, sessionId: string}>;

    }

    export interface TargetApi {
        /**
         * Activates (focuses) the target.
         */
        activateTarget(params: Protocol.Target.ActivateTargetRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Attaches to the target with given id.
         */
        attachToTarget(params: Protocol.Target.AttachToTargetRequest): Promise<{id: number, result: Protocol.Target.AttachToTargetResponse, sessionId: string}>;

        /**
         * Attaches to the browser target, only uses flat sessionId mode.
         */
        attachToBrowserTarget(): Promise<{id: number, result: Protocol.Target.AttachToBrowserTargetResponse, sessionId: string}>;

        /**
         * Closes the target. If the target is a page that gets closed too.
         */
        closeTarget(params: Protocol.Target.CloseTargetRequest): Promise<{id: number, result: Protocol.Target.CloseTargetResponse, sessionId: string}>;

        /**
         * Inject object to the target's main frame that provides a communication
         * channel with browser target.
         * 
         * Injected object will be available as `window[bindingName]`.
         * 
         * The object has the following API:
         * - `binding.send(json)` - a method to send messages over the remote debugging protocol
         * - `binding.onmessage = json => handleMessage(json)` - a callback that will be called for the protocol notifications and command responses.
         */
        exposeDevToolsProtocol(params: Protocol.Target.ExposeDevToolsProtocolRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Creates a new empty BrowserContext. Similar to an incognito profile but you can have more than
         * one.
         */
        createBrowserContext(params: Protocol.Target.CreateBrowserContextRequest): Promise<{id: number, result: Protocol.Target.CreateBrowserContextResponse, sessionId: string}>;

        /**
         * Returns all browser contexts created with `Target.createBrowserContext` method.
         */
        getBrowserContexts(): Promise<{id: number, result: Protocol.Target.GetBrowserContextsResponse, sessionId: string}>;

        /**
         * Creates a new page.
         */
        createTarget(params: Protocol.Target.CreateTargetRequest): Promise<{id: number, result: Protocol.Target.CreateTargetResponse, sessionId: string}>;

        /**
         * Detaches session with given id.
         */
        detachFromTarget(params: Protocol.Target.DetachFromTargetRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Deletes a BrowserContext. All the belonging pages will be closed without calling their
         * beforeunload hooks.
         */
        disposeBrowserContext(params: Protocol.Target.DisposeBrowserContextRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns information about a target.
         */
        getTargetInfo(params: Protocol.Target.GetTargetInfoRequest): Promise<{id: number, result: Protocol.Target.GetTargetInfoResponse, sessionId: string}>;

        /**
         * Retrieves a list of available targets.
         */
        getTargets(params: Protocol.Target.GetTargetsRequest): Promise<{id: number, result: Protocol.Target.GetTargetsResponse, sessionId: string}>;

        /**
         * Sends protocol message over session with given id.
         * Consider using flat mode instead; see commands attachToTarget, setAutoAttach,
         * and crbug.com/991325.
         */
        sendMessageToTarget(params: Protocol.Target.SendMessageToTargetRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Controls whether to automatically attach to new targets which are considered to be related to
         * this one. When turned on, attaches to all existing related targets as well. When turned off,
         * automatically detaches from all currently attached targets.
         * This also clears all targets added by `autoAttachRelated` from the list of targets to watch
         * for creation of related targets.
         */
        setAutoAttach(params: Protocol.Target.SetAutoAttachRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Adds the specified target to the list of targets that will be monitored for any related target
         * creation (such as child frames, child workers and new versions of service worker) and reported
         * through `attachedToTarget`. The specified target is also auto-attached.
         * This cancels the effect of any previous `setAutoAttach` and is also cancelled by subsequent
         * `setAutoAttach`. Only available at the Browser target.
         */
        autoAttachRelated(params: Protocol.Target.AutoAttachRelatedRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Controls whether to discover available targets and notify via
         * `targetCreated/targetInfoChanged/targetDestroyed` events.
         */
        setDiscoverTargets(params: Protocol.Target.SetDiscoverTargetsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables target discovery for the specified locations, when `setDiscoverTargets` was set to
         * `true`.
         */
        setRemoteLocations(params: Protocol.Target.SetRemoteLocationsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Issued when attached to target because of auto-attach or `attachToTarget` command.
         */
        onAttachedToTarget(listener: (event: { params: Protocol.Target.AttachedToTargetEvent }) => void): void;
        offAttachedToTarget(listener: (event: { params: Protocol.Target.AttachedToTargetEvent }) => void): void;
        onceAttachedToTarget(eventMatcher?: (event: { params: Protocol.Target.AttachedToTargetEvent }) => boolean): Promise<{ params: Protocol.Target.AttachedToTargetEvent }>;

        /**
         * Issued when detached from target for any reason (including `detachFromTarget` command). Can be
         * issued multiple times per target if multiple sessions have been attached to it.
         */
        onDetachedFromTarget(listener: (event: { params: Protocol.Target.DetachedFromTargetEvent }) => void): void;
        offDetachedFromTarget(listener: (event: { params: Protocol.Target.DetachedFromTargetEvent }) => void): void;
        onceDetachedFromTarget(eventMatcher?: (event: { params: Protocol.Target.DetachedFromTargetEvent }) => boolean): Promise<{ params: Protocol.Target.DetachedFromTargetEvent }>;

        /**
         * Notifies about a new protocol message received from the session (as reported in
         * `attachedToTarget` event).
         */
        onReceivedMessageFromTarget(listener: (event: { params: Protocol.Target.ReceivedMessageFromTargetEvent }) => void): void;
        offReceivedMessageFromTarget(listener: (event: { params: Protocol.Target.ReceivedMessageFromTargetEvent }) => void): void;
        onceReceivedMessageFromTarget(eventMatcher?: (event: { params: Protocol.Target.ReceivedMessageFromTargetEvent }) => boolean): Promise<{ params: Protocol.Target.ReceivedMessageFromTargetEvent }>;

        /**
         * Issued when a possible inspection target is created.
         */
        onTargetCreated(listener: (event: { params: Protocol.Target.TargetCreatedEvent }) => void): void;
        offTargetCreated(listener: (event: { params: Protocol.Target.TargetCreatedEvent }) => void): void;
        onceTargetCreated(eventMatcher?: (event: { params: Protocol.Target.TargetCreatedEvent }) => boolean): Promise<{ params: Protocol.Target.TargetCreatedEvent }>;

        /**
         * Issued when a target is destroyed.
         */
        onTargetDestroyed(listener: (event: { params: Protocol.Target.TargetDestroyedEvent }) => void): void;
        offTargetDestroyed(listener: (event: { params: Protocol.Target.TargetDestroyedEvent }) => void): void;
        onceTargetDestroyed(eventMatcher?: (event: { params: Protocol.Target.TargetDestroyedEvent }) => boolean): Promise<{ params: Protocol.Target.TargetDestroyedEvent }>;

        /**
         * Issued when a target has crashed.
         */
        onTargetCrashed(listener: (event: { params: Protocol.Target.TargetCrashedEvent }) => void): void;
        offTargetCrashed(listener: (event: { params: Protocol.Target.TargetCrashedEvent }) => void): void;
        onceTargetCrashed(eventMatcher?: (event: { params: Protocol.Target.TargetCrashedEvent }) => boolean): Promise<{ params: Protocol.Target.TargetCrashedEvent }>;

        /**
         * Issued when some information about a target has changed. This only happens between
         * `targetCreated` and `targetDestroyed`.
         */
        onTargetInfoChanged(listener: (event: { params: Protocol.Target.TargetInfoChangedEvent }) => void): void;
        offTargetInfoChanged(listener: (event: { params: Protocol.Target.TargetInfoChangedEvent }) => void): void;
        onceTargetInfoChanged(eventMatcher?: (event: { params: Protocol.Target.TargetInfoChangedEvent }) => boolean): Promise<{ params: Protocol.Target.TargetInfoChangedEvent }>;

    }

    export interface TetheringApi {
        /**
         * Request browser port binding.
         */
        bind(params: Protocol.Tethering.BindRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Request browser port unbinding.
         */
        unbind(params: Protocol.Tethering.UnbindRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Informs that port was successfully bound and got a specified connection id.
         */
        onAccepted(listener: (event: { params: Protocol.Tethering.AcceptedEvent }) => void): void;
        offAccepted(listener: (event: { params: Protocol.Tethering.AcceptedEvent }) => void): void;
        onceAccepted(eventMatcher?: (event: { params: Protocol.Tethering.AcceptedEvent }) => boolean): Promise<{ params: Protocol.Tethering.AcceptedEvent }>;

    }

    export interface TracingApi {
        /**
         * Stop trace events collection.
         */
        end(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Gets supported tracing categories.
         */
        getCategories(): Promise<{id: number, result: Protocol.Tracing.GetCategoriesResponse, sessionId: string}>;

        /**
         * Record a clock sync marker in the trace.
         */
        recordClockSyncMarker(params: Protocol.Tracing.RecordClockSyncMarkerRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Request a global memory dump.
         */
        requestMemoryDump(params: Protocol.Tracing.RequestMemoryDumpRequest): Promise<{id: number, result: Protocol.Tracing.RequestMemoryDumpResponse, sessionId: string}>;

        /**
         * Start trace events collection.
         */
        start(params: Protocol.Tracing.StartRequest): Promise<{id: number, result: void, sessionId: string}>;

        onBufferUsage(listener: (event: { params: Protocol.Tracing.BufferUsageEvent }) => void): void;
        offBufferUsage(listener: (event: { params: Protocol.Tracing.BufferUsageEvent }) => void): void;
        onceBufferUsage(eventMatcher?: (event: { params: Protocol.Tracing.BufferUsageEvent }) => boolean): Promise<{ params: Protocol.Tracing.BufferUsageEvent }>;

        /**
         * Contains a bucket of collected trace events. When tracing is stopped collected events will be
         * sent as a sequence of dataCollected events followed by tracingComplete event.
         */
        onDataCollected(listener: (event: { params: Protocol.Tracing.DataCollectedEvent }) => void): void;
        offDataCollected(listener: (event: { params: Protocol.Tracing.DataCollectedEvent }) => void): void;
        onceDataCollected(eventMatcher?: (event: { params: Protocol.Tracing.DataCollectedEvent }) => boolean): Promise<{ params: Protocol.Tracing.DataCollectedEvent }>;

        /**
         * Signals that tracing is stopped and there is no trace buffers pending flush, all data were
         * delivered via dataCollected events.
         */
        onTracingComplete(listener: (event: { params: Protocol.Tracing.TracingCompleteEvent }) => void): void;
        offTracingComplete(listener: (event: { params: Protocol.Tracing.TracingCompleteEvent }) => void): void;
        onceTracingComplete(eventMatcher?: (event: { params: Protocol.Tracing.TracingCompleteEvent }) => boolean): Promise<{ params: Protocol.Tracing.TracingCompleteEvent }>;

    }

    export interface FetchApi {
        /**
         * Disables the fetch domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Enables issuing of requestPaused events. A request will be paused until client
         * calls one of failRequest, fulfillRequest or continueRequest/continueWithAuth.
         */
        enable(params: Protocol.Fetch.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Causes the request to fail with specified reason.
         */
        failRequest(params: Protocol.Fetch.FailRequestRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Provides response to the request.
         */
        fulfillRequest(params: Protocol.Fetch.FulfillRequestRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Continues the request, optionally modifying some of its parameters.
         */
        continueRequest(params: Protocol.Fetch.ContinueRequestRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Continues a request supplying authChallengeResponse following authRequired event.
         */
        continueWithAuth(params: Protocol.Fetch.ContinueWithAuthRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Continues loading of the paused response, optionally modifying the
         * response headers. If either responseCode or headers are modified, all of them
         * must be present.
         */
        continueResponse(params: Protocol.Fetch.ContinueResponseRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Causes the body of the response to be received from the server and
         * returned as a single string. May only be issued for a request that
         * is paused in the Response stage and is mutually exclusive with
         * takeResponseBodyForInterceptionAsStream. Calling other methods that
         * affect the request or disabling fetch domain before body is received
         * results in an undefined behavior.
         * Note that the response body is not available for redirects. Requests
         * paused in the _redirect received_ state may be differentiated by
         * `responseCode` and presence of `location` response header, see
         * comments to `requestPaused` for details.
         */
        getResponseBody(params: Protocol.Fetch.GetResponseBodyRequest): Promise<{id: number, result: Protocol.Fetch.GetResponseBodyResponse, sessionId: string}>;

        /**
         * Returns a handle to the stream representing the response body.
         * The request must be paused in the HeadersReceived stage.
         * Note that after this command the request can't be continued
         * as is -- client either needs to cancel it or to provide the
         * response body.
         * The stream only supports sequential read, IO.read will fail if the position
         * is specified.
         * This method is mutually exclusive with getResponseBody.
         * Calling other methods that affect the request or disabling fetch
         * domain before body is received results in an undefined behavior.
         */
        takeResponseBodyAsStream(params: Protocol.Fetch.TakeResponseBodyAsStreamRequest): Promise<{id: number, result: Protocol.Fetch.TakeResponseBodyAsStreamResponse, sessionId: string}>;

        /**
         * Issued when the domain is enabled and the request URL matches the
         * specified filter. The request is paused until the client responds
         * with one of continueRequest, failRequest or fulfillRequest.
         * The stage of the request can be determined by presence of responseErrorReason
         * and responseStatusCode -- the request is at the response stage if either
         * of these fields is present and in the request stage otherwise.
         * Redirect responses and subsequent requests are reported similarly to regular
         * responses and requests. Redirect responses may be distinguished by the value
         * of `responseStatusCode` (which is one of 301, 302, 303, 307, 308) along with
         * presence of the `location` header. Requests resulting from a redirect will
         * have `redirectedRequestId` field set.
         */
        onRequestPaused(listener: (event: { params: Protocol.Fetch.RequestPausedEvent }) => void): void;
        offRequestPaused(listener: (event: { params: Protocol.Fetch.RequestPausedEvent }) => void): void;
        onceRequestPaused(eventMatcher?: (event: { params: Protocol.Fetch.RequestPausedEvent }) => boolean): Promise<{ params: Protocol.Fetch.RequestPausedEvent }>;

        /**
         * Issued when the domain is enabled with handleAuthRequests set to true.
         * The request is paused until client responds with continueWithAuth.
         */
        onAuthRequired(listener: (event: { params: Protocol.Fetch.AuthRequiredEvent }) => void): void;
        offAuthRequired(listener: (event: { params: Protocol.Fetch.AuthRequiredEvent }) => void): void;
        onceAuthRequired(eventMatcher?: (event: { params: Protocol.Fetch.AuthRequiredEvent }) => boolean): Promise<{ params: Protocol.Fetch.AuthRequiredEvent }>;

    }

    export interface WebAudioApi {
        /**
         * Enables the WebAudio domain and starts sending context lifetime events.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables the WebAudio domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Fetch the realtime data from the registered contexts.
         */
        getRealtimeData(params: Protocol.WebAudio.GetRealtimeDataRequest): Promise<{id: number, result: Protocol.WebAudio.GetRealtimeDataResponse, sessionId: string}>;

        /**
         * Notifies that a new BaseAudioContext has been created.
         */
        onContextCreated(listener: (event: { params: Protocol.WebAudio.ContextCreatedEvent }) => void): void;
        offContextCreated(listener: (event: { params: Protocol.WebAudio.ContextCreatedEvent }) => void): void;
        onceContextCreated(eventMatcher?: (event: { params: Protocol.WebAudio.ContextCreatedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.ContextCreatedEvent }>;

        /**
         * Notifies that an existing BaseAudioContext will be destroyed.
         */
        onContextWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.ContextWillBeDestroyedEvent }) => void): void;
        offContextWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.ContextWillBeDestroyedEvent }) => void): void;
        onceContextWillBeDestroyed(eventMatcher?: (event: { params: Protocol.WebAudio.ContextWillBeDestroyedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.ContextWillBeDestroyedEvent }>;

        /**
         * Notifies that existing BaseAudioContext has changed some properties (id stays the same)..
         */
        onContextChanged(listener: (event: { params: Protocol.WebAudio.ContextChangedEvent }) => void): void;
        offContextChanged(listener: (event: { params: Protocol.WebAudio.ContextChangedEvent }) => void): void;
        onceContextChanged(eventMatcher?: (event: { params: Protocol.WebAudio.ContextChangedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.ContextChangedEvent }>;

        /**
         * Notifies that the construction of an AudioListener has finished.
         */
        onAudioListenerCreated(listener: (event: { params: Protocol.WebAudio.AudioListenerCreatedEvent }) => void): void;
        offAudioListenerCreated(listener: (event: { params: Protocol.WebAudio.AudioListenerCreatedEvent }) => void): void;
        onceAudioListenerCreated(eventMatcher?: (event: { params: Protocol.WebAudio.AudioListenerCreatedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioListenerCreatedEvent }>;

        /**
         * Notifies that a new AudioListener has been created.
         */
        onAudioListenerWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioListenerWillBeDestroyedEvent }) => void): void;
        offAudioListenerWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioListenerWillBeDestroyedEvent }) => void): void;
        onceAudioListenerWillBeDestroyed(eventMatcher?: (event: { params: Protocol.WebAudio.AudioListenerWillBeDestroyedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioListenerWillBeDestroyedEvent }>;

        /**
         * Notifies that a new AudioNode has been created.
         */
        onAudioNodeCreated(listener: (event: { params: Protocol.WebAudio.AudioNodeCreatedEvent }) => void): void;
        offAudioNodeCreated(listener: (event: { params: Protocol.WebAudio.AudioNodeCreatedEvent }) => void): void;
        onceAudioNodeCreated(eventMatcher?: (event: { params: Protocol.WebAudio.AudioNodeCreatedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioNodeCreatedEvent }>;

        /**
         * Notifies that an existing AudioNode has been destroyed.
         */
        onAudioNodeWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioNodeWillBeDestroyedEvent }) => void): void;
        offAudioNodeWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioNodeWillBeDestroyedEvent }) => void): void;
        onceAudioNodeWillBeDestroyed(eventMatcher?: (event: { params: Protocol.WebAudio.AudioNodeWillBeDestroyedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioNodeWillBeDestroyedEvent }>;

        /**
         * Notifies that a new AudioParam has been created.
         */
        onAudioParamCreated(listener: (event: { params: Protocol.WebAudio.AudioParamCreatedEvent }) => void): void;
        offAudioParamCreated(listener: (event: { params: Protocol.WebAudio.AudioParamCreatedEvent }) => void): void;
        onceAudioParamCreated(eventMatcher?: (event: { params: Protocol.WebAudio.AudioParamCreatedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioParamCreatedEvent }>;

        /**
         * Notifies that an existing AudioParam has been destroyed.
         */
        onAudioParamWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioParamWillBeDestroyedEvent }) => void): void;
        offAudioParamWillBeDestroyed(listener: (event: { params: Protocol.WebAudio.AudioParamWillBeDestroyedEvent }) => void): void;
        onceAudioParamWillBeDestroyed(eventMatcher?: (event: { params: Protocol.WebAudio.AudioParamWillBeDestroyedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.AudioParamWillBeDestroyedEvent }>;

        /**
         * Notifies that two AudioNodes are connected.
         */
        onNodesConnected(listener: (event: { params: Protocol.WebAudio.NodesConnectedEvent }) => void): void;
        offNodesConnected(listener: (event: { params: Protocol.WebAudio.NodesConnectedEvent }) => void): void;
        onceNodesConnected(eventMatcher?: (event: { params: Protocol.WebAudio.NodesConnectedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.NodesConnectedEvent }>;

        /**
         * Notifies that AudioNodes are disconnected. The destination can be null, and it means all the outgoing connections from the source are disconnected.
         */
        onNodesDisconnected(listener: (event: { params: Protocol.WebAudio.NodesDisconnectedEvent }) => void): void;
        offNodesDisconnected(listener: (event: { params: Protocol.WebAudio.NodesDisconnectedEvent }) => void): void;
        onceNodesDisconnected(eventMatcher?: (event: { params: Protocol.WebAudio.NodesDisconnectedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.NodesDisconnectedEvent }>;

        /**
         * Notifies that an AudioNode is connected to an AudioParam.
         */
        onNodeParamConnected(listener: (event: { params: Protocol.WebAudio.NodeParamConnectedEvent }) => void): void;
        offNodeParamConnected(listener: (event: { params: Protocol.WebAudio.NodeParamConnectedEvent }) => void): void;
        onceNodeParamConnected(eventMatcher?: (event: { params: Protocol.WebAudio.NodeParamConnectedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.NodeParamConnectedEvent }>;

        /**
         * Notifies that an AudioNode is disconnected to an AudioParam.
         */
        onNodeParamDisconnected(listener: (event: { params: Protocol.WebAudio.NodeParamDisconnectedEvent }) => void): void;
        offNodeParamDisconnected(listener: (event: { params: Protocol.WebAudio.NodeParamDisconnectedEvent }) => void): void;
        onceNodeParamDisconnected(eventMatcher?: (event: { params: Protocol.WebAudio.NodeParamDisconnectedEvent }) => boolean): Promise<{ params: Protocol.WebAudio.NodeParamDisconnectedEvent }>;

    }

    export interface WebAuthnApi {
        /**
         * Enable the WebAuthn domain and start intercepting credential storage and
         * retrieval with a virtual authenticator.
         */
        enable(params: Protocol.WebAuthn.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disable the WebAuthn domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Creates and adds a virtual authenticator.
         */
        addVirtualAuthenticator(params: Protocol.WebAuthn.AddVirtualAuthenticatorRequest): Promise<{id: number, result: Protocol.WebAuthn.AddVirtualAuthenticatorResponse, sessionId: string}>;

        /**
         * Resets parameters isBogusSignature, isBadUV, isBadUP to false if they are not present.
         */
        setResponseOverrideBits(params: Protocol.WebAuthn.SetResponseOverrideBitsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Removes the given authenticator.
         */
        removeVirtualAuthenticator(params: Protocol.WebAuthn.RemoveVirtualAuthenticatorRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Adds the credential to the specified authenticator.
         */
        addCredential(params: Protocol.WebAuthn.AddCredentialRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Returns a single credential stored in the given virtual authenticator that
         * matches the credential ID.
         */
        getCredential(params: Protocol.WebAuthn.GetCredentialRequest): Promise<{id: number, result: Protocol.WebAuthn.GetCredentialResponse, sessionId: string}>;

        /**
         * Returns all the credentials stored in the given virtual authenticator.
         */
        getCredentials(params: Protocol.WebAuthn.GetCredentialsRequest): Promise<{id: number, result: Protocol.WebAuthn.GetCredentialsResponse, sessionId: string}>;

        /**
         * Removes a credential from the authenticator.
         */
        removeCredential(params: Protocol.WebAuthn.RemoveCredentialRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Clears all the credentials from the specified device.
         */
        clearCredentials(params: Protocol.WebAuthn.ClearCredentialsRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets whether User Verification succeeds or fails for an authenticator.
         * The default is true.
         */
        setUserVerified(params: Protocol.WebAuthn.SetUserVerifiedRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Sets whether tests of user presence will succeed immediately (if true) or fail to resolve (if false) for an authenticator.
         * The default is true.
         */
        setAutomaticPresenceSimulation(params: Protocol.WebAuthn.SetAutomaticPresenceSimulationRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Allows setting credential properties.
         * https://w3c.github.io/webauthn/#sctn-automation-set-credential-properties
         */
        setCredentialProperties(params: Protocol.WebAuthn.SetCredentialPropertiesRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Triggered when a credential is added to an authenticator.
         */
        onCredentialAdded(listener: (event: { params: Protocol.WebAuthn.CredentialAddedEvent }) => void): void;
        offCredentialAdded(listener: (event: { params: Protocol.WebAuthn.CredentialAddedEvent }) => void): void;
        onceCredentialAdded(eventMatcher?: (event: { params: Protocol.WebAuthn.CredentialAddedEvent }) => boolean): Promise<{ params: Protocol.WebAuthn.CredentialAddedEvent }>;

        /**
         * Triggered when a credential is used in a webauthn assertion.
         */
        onCredentialAsserted(listener: (event: { params: Protocol.WebAuthn.CredentialAssertedEvent }) => void): void;
        offCredentialAsserted(listener: (event: { params: Protocol.WebAuthn.CredentialAssertedEvent }) => void): void;
        onceCredentialAsserted(eventMatcher?: (event: { params: Protocol.WebAuthn.CredentialAssertedEvent }) => boolean): Promise<{ params: Protocol.WebAuthn.CredentialAssertedEvent }>;

    }

    export interface MediaApi {
        /**
         * Enables the Media domain
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disables the Media domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * This can be called multiple times, and can be used to set / override /
         * remove player properties. A null propValue indicates removal.
         */
        onPlayerPropertiesChanged(listener: (event: { params: Protocol.Media.PlayerPropertiesChangedEvent }) => void): void;
        offPlayerPropertiesChanged(listener: (event: { params: Protocol.Media.PlayerPropertiesChangedEvent }) => void): void;
        oncePlayerPropertiesChanged(eventMatcher?: (event: { params: Protocol.Media.PlayerPropertiesChangedEvent }) => boolean): Promise<{ params: Protocol.Media.PlayerPropertiesChangedEvent }>;

        /**
         * Send events as a list, allowing them to be batched on the browser for less
         * congestion. If batched, events must ALWAYS be in chronological order.
         */
        onPlayerEventsAdded(listener: (event: { params: Protocol.Media.PlayerEventsAddedEvent }) => void): void;
        offPlayerEventsAdded(listener: (event: { params: Protocol.Media.PlayerEventsAddedEvent }) => void): void;
        oncePlayerEventsAdded(eventMatcher?: (event: { params: Protocol.Media.PlayerEventsAddedEvent }) => boolean): Promise<{ params: Protocol.Media.PlayerEventsAddedEvent }>;

        /**
         * Send a list of any messages that need to be delivered.
         */
        onPlayerMessagesLogged(listener: (event: { params: Protocol.Media.PlayerMessagesLoggedEvent }) => void): void;
        offPlayerMessagesLogged(listener: (event: { params: Protocol.Media.PlayerMessagesLoggedEvent }) => void): void;
        oncePlayerMessagesLogged(eventMatcher?: (event: { params: Protocol.Media.PlayerMessagesLoggedEvent }) => boolean): Promise<{ params: Protocol.Media.PlayerMessagesLoggedEvent }>;

        /**
         * Send a list of any errors that need to be delivered.
         */
        onPlayerErrorsRaised(listener: (event: { params: Protocol.Media.PlayerErrorsRaisedEvent }) => void): void;
        offPlayerErrorsRaised(listener: (event: { params: Protocol.Media.PlayerErrorsRaisedEvent }) => void): void;
        oncePlayerErrorsRaised(eventMatcher?: (event: { params: Protocol.Media.PlayerErrorsRaisedEvent }) => boolean): Promise<{ params: Protocol.Media.PlayerErrorsRaisedEvent }>;

        /**
         * Called whenever a player is created, or when a new agent joins and receives
         * a list of active players. If an agent is restored, it will receive the full
         * list of player ids and all events again.
         */
        onPlayersCreated(listener: (event: { params: Protocol.Media.PlayersCreatedEvent }) => void): void;
        offPlayersCreated(listener: (event: { params: Protocol.Media.PlayersCreatedEvent }) => void): void;
        oncePlayersCreated(eventMatcher?: (event: { params: Protocol.Media.PlayersCreatedEvent }) => boolean): Promise<{ params: Protocol.Media.PlayersCreatedEvent }>;

    }

    export interface DeviceAccessApi {
        /**
         * Enable events in this domain.
         */
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Disable events in this domain.
         */
        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Select a device in response to a DeviceAccess.deviceRequestPrompted event.
         */
        selectPrompt(params: Protocol.DeviceAccess.SelectPromptRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Cancel a prompt in response to a DeviceAccess.deviceRequestPrompted event.
         */
        cancelPrompt(params: Protocol.DeviceAccess.CancelPromptRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * A device request opened a user prompt to select a device. Respond with the
         * selectPrompt or cancelPrompt command.
         */
        onDeviceRequestPrompted(listener: (event: { params: Protocol.DeviceAccess.DeviceRequestPromptedEvent }) => void): void;
        offDeviceRequestPrompted(listener: (event: { params: Protocol.DeviceAccess.DeviceRequestPromptedEvent }) => void): void;
        onceDeviceRequestPrompted(eventMatcher?: (event: { params: Protocol.DeviceAccess.DeviceRequestPromptedEvent }) => boolean): Promise<{ params: Protocol.DeviceAccess.DeviceRequestPromptedEvent }>;

    }

    export interface PreloadApi {
        enable(): Promise<{id: number, result: void, sessionId: string}>;

        disable(): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Upsert. Currently, it is only emitted when a rule set added.
         */
        onRuleSetUpdated(listener: (event: { params: Protocol.Preload.RuleSetUpdatedEvent }) => void): void;
        offRuleSetUpdated(listener: (event: { params: Protocol.Preload.RuleSetUpdatedEvent }) => void): void;
        onceRuleSetUpdated(eventMatcher?: (event: { params: Protocol.Preload.RuleSetUpdatedEvent }) => boolean): Promise<{ params: Protocol.Preload.RuleSetUpdatedEvent }>;

        onRuleSetRemoved(listener: (event: { params: Protocol.Preload.RuleSetRemovedEvent }) => void): void;
        offRuleSetRemoved(listener: (event: { params: Protocol.Preload.RuleSetRemovedEvent }) => void): void;
        onceRuleSetRemoved(eventMatcher?: (event: { params: Protocol.Preload.RuleSetRemovedEvent }) => boolean): Promise<{ params: Protocol.Preload.RuleSetRemovedEvent }>;

        /**
         * Fired when a preload enabled state is updated.
         */
        onPreloadEnabledStateUpdated(listener: (event: { params: Protocol.Preload.PreloadEnabledStateUpdatedEvent }) => void): void;
        offPreloadEnabledStateUpdated(listener: (event: { params: Protocol.Preload.PreloadEnabledStateUpdatedEvent }) => void): void;
        oncePreloadEnabledStateUpdated(eventMatcher?: (event: { params: Protocol.Preload.PreloadEnabledStateUpdatedEvent }) => boolean): Promise<{ params: Protocol.Preload.PreloadEnabledStateUpdatedEvent }>;

        /**
         * Fired when a prefetch attempt is updated.
         */
        onPrefetchStatusUpdated(listener: (event: { params: Protocol.Preload.PrefetchStatusUpdatedEvent }) => void): void;
        offPrefetchStatusUpdated(listener: (event: { params: Protocol.Preload.PrefetchStatusUpdatedEvent }) => void): void;
        oncePrefetchStatusUpdated(eventMatcher?: (event: { params: Protocol.Preload.PrefetchStatusUpdatedEvent }) => boolean): Promise<{ params: Protocol.Preload.PrefetchStatusUpdatedEvent }>;

        /**
         * Fired when a prerender attempt is updated.
         */
        onPrerenderStatusUpdated(listener: (event: { params: Protocol.Preload.PrerenderStatusUpdatedEvent }) => void): void;
        offPrerenderStatusUpdated(listener: (event: { params: Protocol.Preload.PrerenderStatusUpdatedEvent }) => void): void;
        oncePrerenderStatusUpdated(eventMatcher?: (event: { params: Protocol.Preload.PrerenderStatusUpdatedEvent }) => boolean): Promise<{ params: Protocol.Preload.PrerenderStatusUpdatedEvent }>;

        /**
         * Send a list of sources for all preloading attempts in a document.
         */
        onPreloadingAttemptSourcesUpdated(listener: (event: { params: Protocol.Preload.PreloadingAttemptSourcesUpdatedEvent }) => void): void;
        offPreloadingAttemptSourcesUpdated(listener: (event: { params: Protocol.Preload.PreloadingAttemptSourcesUpdatedEvent }) => void): void;
        oncePreloadingAttemptSourcesUpdated(eventMatcher?: (event: { params: Protocol.Preload.PreloadingAttemptSourcesUpdatedEvent }) => boolean): Promise<{ params: Protocol.Preload.PreloadingAttemptSourcesUpdatedEvent }>;

    }

    export interface FedCmApi {
        enable(params: Protocol.FedCm.EnableRequest): Promise<{id: number, result: void, sessionId: string}>;

        disable(): Promise<{id: number, result: void, sessionId: string}>;

        selectAccount(params: Protocol.FedCm.SelectAccountRequest): Promise<{id: number, result: void, sessionId: string}>;

        clickDialogButton(params: Protocol.FedCm.ClickDialogButtonRequest): Promise<{id: number, result: void, sessionId: string}>;

        openUrl(params: Protocol.FedCm.OpenUrlRequest): Promise<{id: number, result: void, sessionId: string}>;

        dismissDialog(params: Protocol.FedCm.DismissDialogRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Resets the cooldown time, if any, to allow the next FedCM call to show
         * a dialog even if one was recently dismissed by the user.
         */
        resetCooldown(): Promise<{id: number, result: void, sessionId: string}>;

        onDialogShown(listener: (event: { params: Protocol.FedCm.DialogShownEvent }) => void): void;
        offDialogShown(listener: (event: { params: Protocol.FedCm.DialogShownEvent }) => void): void;
        onceDialogShown(eventMatcher?: (event: { params: Protocol.FedCm.DialogShownEvent }) => boolean): Promise<{ params: Protocol.FedCm.DialogShownEvent }>;

        /**
         * Triggered when a dialog is closed, either by user action, JS abort,
         * or a command below.
         */
        onDialogClosed(listener: (event: { params: Protocol.FedCm.DialogClosedEvent }) => void): void;
        offDialogClosed(listener: (event: { params: Protocol.FedCm.DialogClosedEvent }) => void): void;
        onceDialogClosed(eventMatcher?: (event: { params: Protocol.FedCm.DialogClosedEvent }) => boolean): Promise<{ params: Protocol.FedCm.DialogClosedEvent }>;

    }

    export interface PWAApi {
        /**
         * Returns the following OS state for the given manifest id.
         */
        getOsAppState(params: Protocol.PWA.GetOsAppStateRequest): Promise<{id: number, result: Protocol.PWA.GetOsAppStateResponse, sessionId: string}>;

        /**
         * Installs the given manifest identity, optionally using the given install_url
         * or IWA bundle location.
         * 
         * TODO(crbug.com/337872319) Support IWA to meet the following specific
         * requirement.
         * IWA-specific install description: If the manifest_id is isolated-app://,
         * install_url_or_bundle_url is required, and can be either an http(s) URL or
         * file:// URL pointing to a signed web bundle (.swbn). The .swbn file's
         * signing key must correspond to manifest_id. If Chrome is not in IWA dev
         * mode, the installation will fail, regardless of the state of the allowlist.
         */
        install(params: Protocol.PWA.InstallRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Uninstals the given manifest_id and closes any opened app windows.
         */
        uninstall(params: Protocol.PWA.UninstallRequest): Promise<{id: number, result: void, sessionId: string}>;

        /**
         * Launches the installed web app, or an url in the same web app instead of the
         * default start url if it is provided. Returns a page Target.TargetID which
         * can be used to attach to via Target.attachToTarget or similar APIs.
         */
        launch(params: Protocol.PWA.LaunchRequest): Promise<{id: number, result: Protocol.PWA.LaunchResponse, sessionId: string}>;

        /**
         * Opens one or more local files from an installed web app identified by its
         * manifestId. The web app needs to have file handlers registered to process
         * the files. The API returns one or more page Target.TargetIDs which can be
         * used to attach to via Target.attachToTarget or similar APIs.
         * If some files in the parameters cannot be handled by the web app, they will
         * be ignored. If none of the files can be handled, this API returns an error.
         * If no files provided as the parameter, this API also returns an error.
         * 
         * According to the definition of the file handlers in the manifest file, one
         * Target.TargetID may represent a page handling one or more files. The order
         * of the returned Target.TargetIDs is not guaranteed.
         * 
         * TODO(crbug.com/339454034): Check the existences of the input files.
         */
        launchFilesInApp(params: Protocol.PWA.LaunchFilesInAppRequest): Promise<{id: number, result: Protocol.PWA.LaunchFilesInAppResponse, sessionId: string}>;

        /**
         * Opens the current page in its web app identified by the manifest id, needs
         * to be called on a page target. This function returns immediately without
         * waiting for the app finishing loading.
         */
        openCurrentPageInApp(params: Protocol.PWA.OpenCurrentPageInAppRequest): Promise<{id: number, result: void, sessionId: string}>;

    }
}

export default ProtocolTestsProxyApi;
