import { PlanType, PlanDuration, Plan } from '@/types/plan'

export const PLAN_LIMITS = {
  [PlanType.FREE]: {
    dailyScans: 10,
    monthlyScans: 300,
    fileUploadSize: 5, // MB
    apiCallsPerDay: 50,
    apiCallsPerMonth: 1500,
    osintQueries: 5,
    concurrentScans: 1,
    botAccess: false,
    prioritySupport: false,
    customIntegrations: false,
    advancedFeatures: false,
  },
  [PlanType.STUDENT]: {
    dailyScans: 100,
    monthlyScans: 3000,
    fileUploadSize: 25, // MB
    apiCallsPerDay: 500,
    apiCallsPerMonth: 15000,
    osintQueries: 50,
    concurrentScans: 3,
    botAccess: true,
    prioritySupport: true,
    customIntegrations: false,
    advancedFeatures: false,
  },
  [PlanType.HOBBY]: {
    dailyScans: 500,
    monthlyScans: 15000,
    fileUploadSize: 50, // MB
    apiCallsPerDay: 2000,
    apiCallsPerMonth: 60000,
    osintQueries: 200,
    concurrentScans: 5,
    botAccess: true,
    prioritySupport: true,
    customIntegrations: true,
    advancedFeatures: false,
  },
  [PlanType.BUGHUNTER]: {
    dailyScans: 2000,
    monthlyScans: 60000,
    fileUploadSize: 100, // MB
    apiCallsPerDay: 10000,
    apiCallsPerMonth: 300000,
    osintQueries: 1000,
    concurrentScans: 10,
    botAccess: true,
    prioritySupport: true,
    customIntegrations: true,
    advancedFeatures: true,
  },
  [PlanType.CYBERSECURITY]: {
    dailyScans: -1, // Unlimited
    monthlyScans: -1, // Unlimited
    fileUploadSize: 500, // MB
    apiCallsPerDay: -1, // Unlimited
    apiCallsPerMonth: -1, // Unlimited
    osintQueries: -1, // Unlimited
    concurrentScans: 20,
    botAccess: true,
    prioritySupport: true,
    customIntegrations: true,
    advancedFeatures: true,
  },
}

export const PLAN_PRICES = {
  [PlanType.FREE]: {
    [PlanDuration.MONTHLY]: 0,
    [PlanDuration.YEARLY]: 0,
  },
  [PlanType.STUDENT]: {
    [PlanDuration.DAILY]: 2000,
    [PlanDuration.WEEKLY]: 12000,
    [PlanDuration.MONTHLY]: 25000,
    [PlanDuration.YEARLY]: 250000,
  },
  [PlanType.HOBBY]: {
    [PlanDuration.DAILY]: 5000,
    [PlanDuration.WEEKLY]: 30000,
    [PlanDuration.MONTHLY]: 75000,
    [PlanDuration.YEARLY]: 750000,
  },
  [PlanType.BUGHUNTER]: {
    [PlanDuration.DAILY]: 10000,
    [PlanDuration.WEEKLY]: 60000,
    [PlanDuration.MONTHLY]: 125000,
    [PlanDuration.YEARLY]: 1250000,
  },
  [PlanType.CYBERSECURITY]: {
    [PlanDuration.DAILY]: 20000,
    [PlanDuration.WEEKLY]: 120000,
    [PlanDuration.MONTHLY]: 250000,
    [PlanDuration.YEARLY]: 2500000,
  },
}

export const PLAN_FEATURES = {
  [PlanType.FREE]: [
    'Basic OSINT lookup',
    'CVE database access',
    'Community support',
    'Basic vulnerability scanner',
    'File hash checker',
  ],
  [PlanType.STUDENT]: [
    'Full OSINT investigator',
    'Advanced vulnerability scanner',
    'File analyzer',
    'Bot access (WhatsApp/Telegram)',
    'Priority support',
    'CVE intelligence',
    'Google dorking presets',
  ],
  [PlanType.HOBBY]: [
    'All Student features',
    'Custom integrations',
    'Advanced file analysis',
    'Payload generator',
    'Custom dork creation',
    'API access',
    'Export reports',
  ],
  [PlanType.BUGHUNTER]: [
    'All Hobby features',
    'Advanced OSINT sources',
    'Exploit database',
    'Automated scanning',
    'Custom payloads',
    'Advanced API features',
    'Priority bot access',
    'Custom webhooks',
  ],
  [PlanType.CYBERSECURITY]: [
    'All features included',
    'Unlimited everything',
    'Enterprise support',
    'Custom development',
    'Dedicated resources',
    'SLA guarantee',
    'White-label options',
    'On-premise deployment',
  ],
}

export const DEFAULT_PLANS: Omit<Plan, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Gratis',
    type: PlanType.FREE,
    price: 0,
    currency: 'IDR',
    duration: PlanDuration.MONTHLY,
    features: PLAN_FEATURES[PlanType.FREE].map((name, index) => ({
      id: `free-${index}`,
      name,
      description: name,
      isIncluded: true,
    })),
    limits: PLAN_LIMITS[PlanType.FREE],
    isActive: true,
    isPopular: false,
    description: 'Perfect untuk pemula yang ingin mencoba fitur dasar cybersecurity',
  },
  {
    name: 'Pelajar',
    type: PlanType.STUDENT,
    price: 25000,
    currency: 'IDR',
    duration: PlanDuration.MONTHLY,
    features: PLAN_FEATURES[PlanType.STUDENT].map((name, index) => ({
      id: `student-${index}`,
      name,
      description: name,
      isIncluded: true,
    })),
    limits: PLAN_LIMITS[PlanType.STUDENT],
    isActive: true,
    isPopular: true,
    description: 'Ideal untuk pelajar dan mahasiswa yang belajar cybersecurity',
  },
  {
    name: 'Hobby',
    type: PlanType.HOBBY,
    price: 75000,
    currency: 'IDR',
    duration: PlanDuration.MONTHLY,
    features: PLAN_FEATURES[PlanType.HOBBY].map((name, index) => ({
      id: `hobby-${index}`,
      name,
      description: name,
      isIncluded: true,
    })),
    limits: PLAN_LIMITS[PlanType.HOBBY],
    isActive: true,
    isPopular: false,
    description: 'Untuk enthusiast yang ingin mengeksplorasi lebih dalam',
  },
  {
    name: 'Bug Hunter',
    type: PlanType.BUGHUNTER,
    price: 125000,
    currency: 'IDR',
    duration: PlanDuration.MONTHLY,
    features: PLAN_FEATURES[PlanType.BUGHUNTER].map((name, index) => ({
      id: `bughunter-${index}`,
      name,
      description: name,
      isIncluded: true,
    })),
    limits: PLAN_LIMITS[PlanType.BUGHUNTER],
    isActive: true,
    isPopular: false,
    description: 'Untuk bug hunter profesional dengan kebutuhan tinggi',
  },
  {
    name: 'Cybersecurity',
    type: PlanType.CYBERSECURITY,
    price: 250000,
    currency: 'IDR',
    duration: PlanDuration.MONTHLY,
    features: PLAN_FEATURES[PlanType.CYBERSECURITY].map((name, index) => ({
      id: `cybersecurity-${index}`,
      name,
      description: name,
      isIncluded: true,
    })),
    limits: PLAN_LIMITS[PlanType.CYBERSECURITY],
    isActive: true,
    isPopular: false,
    description: 'Enterprise solution untuk tim cybersecurity profesional',
  },
]
