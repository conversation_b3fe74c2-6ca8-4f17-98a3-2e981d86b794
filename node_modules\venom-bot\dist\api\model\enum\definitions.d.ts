declare const defs: {
    MAX_GROUP_SIZE: number;
    MAX_SUBJECT_LENGTH: number;
    IMG_MAX_EDGE: number;
    IMG_MAX_BYTES: number;
    IMG_THUMB_MAX_EDGE: number;
    DOC_THUMB_MAX_EDGE: number;
    MAX_MEDIA_UPLOAD_SIZE: number;
    MAX_FILE_SIZE: number;
    MAX_FILES: number;
    SHOW_GIF_SEARCH: boolean;
    USE_NOTIFICATION_QUERY: boolean;
    FWD_UI_START_TS: number;
    GOOGLE_MAPS_DO_NOT_AUTH: boolean;
    GOOGLE_MAPS_KEYLESS: boolean;
    SUSPICIOUS_LINKS: boolean;
    FINAL_LIVE_LOCATION: boolean;
    STATUS_RANKING: boolean;
    FREQUENTLY_FORWARDED_MESSAGES: boolean;
    FREQUENTLY_FORWARDED_THRESHOLD: number;
    FREQUENTLY_FORWARDED_MAX: number;
    FREQUENTLY_FORWARDED_GROUP_SETTING: boolean;
    QUICK_MESSAGE_SEARCH: boolean;
    EPHEMERAL_MESSAGES: boolean;
    PRELOAD_STICKERS: boolean;
    PRODUCT_CATALOG_DEEPLINK: boolean;
    PRODUCT_CATALOG_OPEN_DEEPLINK: boolean;
    PRODUCT_MEDIA_ATTACHMENTS: boolean;
    WEB_CLEAN_INCOMING_FILENAME: boolean;
    WEB_VOIP_INTERNAL_TESTER: boolean;
    WEB_ENABLE_MODEL_STORAGE: boolean;
    WS_CAN_CACHE_REQUESTS: boolean;
    MAX_FORWARD_COUNT_GLOBAL: number;
    FREQUENTLY_FORWARDED_SENTINEL: number;
    MAX_SMB_LABEL_COUNT: number;
    DEFAULT_SMB__NEW_LABEL_COLOR: string;
    FB_CLB_TOKEN: string;
    FB_CLB_CHECK_URL: string;
    FB_CLB_URL: string;
    G_MAPS_DIR_URL: string;
    G_MAPS_IMG_URL: string;
    G_MAPS_SEARCH_URL: string;
    G_MAPS_URL: string;
    NOTIFICATION_PROMPT_DELAY: number;
    PTT_PLAYBACK_DELAY: number;
    NOTIFICATION_TIMEOUT: number;
    CALL_NOTIFICATION_TIMEOUT: number;
    IDLE_TIMEOUT: number;
    IDLE_TIMEOUT_WAIT: number;
    SEARCH_ZOOM: number;
    SEND_UNAVAILABLE_WAIT: number;
    SEND_PAUSED_WAIT: number;
    CLEAR_CHAT_DIRTY_WAIT: number;
    LOG_UPLOAD_INTERVAL: number;
    REVOKE_WINDOW: number;
    WAM_ROTATE_INTERVAL: number;
    ALBUM_DIFF_INTERVAL: number;
    MAX_TXT_MSG_SIZE: number;
    INITIAL_PAGE_SIZE: number;
    FREQUENTLY_FORWARDED_INITIAL_PAGE_SIZE: number;
    SUBSEQUENT_PAGE_SIZE: number;
    OVERFLOWING_PAGE_THRESHOLD: number;
    GROUP_DESCRIPTION_INFO_PANEL_TRUNC_LENGTH: number;
    GROUP_DESCRIPTION_LENGTH: number;
    GROUPS_V3_RESTRICT_GROUPS: boolean;
    GROUPS_V3_ANNOUNCE_GROUPS: boolean;
    GROUPS_V3: boolean;
    INFO_DRAWER_MAX_ROWS: number;
    NUM_COLORS: number;
    FTS_MIN_CHARS: number;
    FTS_TTL: number;
    FTS_TYPING_DELAY: number;
    FTS_NUM_RESULTS: number;
    STICKERS: boolean;
    HSM_ASPECT_RATIO: number;
    TEMPLATE_DOC_MIME_TYPES: number;
    TEMPLATE_URL_START: number;
    TEMPLATE_URL_END: number;
    MMS_MEDIA_KEY_TTL: number;
    KEY_STORAGE_TEST: string;
    KEY_CLIENT_TOKEN: string;
    KEY_SERVER_TOKEN: string;
    KEY_SECRET: string;
    KEY_SECRET_BUNDLE: string;
    KEY_SECURITY_NOTIFICATIONS: string;
    KEY_BROWSER_ID: string;
    KEY_GEOCODER_LOCATION: string;
    KEY_GROUP_ASSIGNED_COLOR: string;
    KEY_GMAPS_OVER_LIMIT: string;
    KEY_GLOBAL_MUTE_SOUNDS: string;
    KEY_GLOBAL_MUTE_NOTIFICATIONS: string;
    KEY_GLOBAL_MUTE_IN_APP_NOTIFICATIONS: string;
    KEY_GLOBAL_MUTE_PREVIEWS: string;
    KEY_GLOBAL_COLLAPSE_MUTED: string;
    KEY_NOTIFICATION_SOUND: string;
    KEY_LANG: string;
    KEY_LAST_ACTIVE_EMOJI_TAB: string;
    KEY_LAST_SELECTED_COMPOSE_BOX_PANEL: string;
    KEY_LAST_CHAT_MUTE_DURATION: string;
    KEY_UNKNOWN_ID: string;
    KEY_VERSION: string;
    KEY_LOAD_RETRY_GENERATION: string;
    KEY_WHATSAPP_MUTEX: string;
    KEY_LAST_WID: string;
    KEY_LAST_WID_MD: string;
    KEY_SAVE_TO_CAMERA_ROLL: string;
    KEY_SMB_LABEL_COLOR_PALETTE: string;
    KEY_LAST_PUSHNAME: string;
    KEY_PROTO_VERSION: string;
    KEY_MOBILE_PLATFORM: string;
    KEY_REMEMBER_ME: string;
    KEY_LOGOUT_TOKEN: string;
    KEY_OLD_LOGOUT_CREDS: string;
    KEY_NO_TAKEOVER: string;
    KEY_WHATSAPP_LS_VERSION: string;
    KEY_WAM_BUFFER: string;
    KEY_WAM_INFO: string;
    KEY_TIME_SPENT_EVENT: string;
    KEY_VIDEO_VOLUME: string;
    KEY_VIDEO_MUTE: string;
    KEY_CONTACT_CHECKSUM: string;
    KEY_COMPOSE_CONTENTS_PREFIX: string;
    COOKIE_REF: string;
    COOKIE_TOK: string;
    PAGE_SIZE: number;
    MSG_PRELOAD_THRESHOLD: number;
    MEDIA_QUERY_LIMIT: number;
    MIN_PIC_SIDE: number;
    MAX_PIC_SIDE: number;
    PROF_PIC_THUMB_SIDE: number;
    MAX_CAPTION_LENGTH: number;
    MAX_PRODUCT_SUBTITLE_LENGTH: number;
    MAX_REPLY_PRODUCT_TITLE_LENGTH: number;
    MAX_REPLY_PRODUCT_DESC_LENGTH: number;
    ALBUM_MIN_SIZE: number;
    ALBUM_MAX_SIZE: number;
    ALBUM_MAX_HEIGHT: number;
    ALBUM_PADDING: number;
    PRESENCE_COMPOSING_TIMEOUT: number;
    PRESENCE_RESEND_WAIT: number;
    MIMETYPE_OGG: string;
    IMAGE_MIMES: string;
    WEBP_MIMES: string;
    VIDEO_MIMES: string;
    KEY_LOG_CURSOR: string;
    MAX_STATUS_LENGTH: number;
    MAX_PUSHNAME_LENGTH: number;
    DISP_TYPE: {
        CONVERSATION: string;
        MSG_INFO: string;
        STARRED_MSGS: string;
        GALLERY: string;
        REPLY_STAGE: string;
        QUOTED_MSG: string;
        CONTACT_CARD: string;
    };
    SEND_LOGS_MAX_EMAIL_LENGTH: number;
    SEND_LOGS_MAX_SUBJECT_LENGTH: number;
    SEND_LOGS_MIN_DESC_LENGTH: number;
    SEND_LOGS_MAX_DESC_LENGTH: number;
    SEND_LOGS_MAX_SCREENSHOTS: number;
    SEND_LOGS_MAX_SCREENSHOT_SIZE: number;
    ACK: {
        MD_DOWNGRADE: number;
        INACTIVE: number;
        CONTENT_UNUPLOADABLE: number;
        CONTENT_TOO_BIG: number;
        CONTENT_GONE: number;
        EXPIRED: number;
        FAILED: number;
        CLOCK: number;
        SENT: number;
        RECEIVED: number;
        READ: number;
        PLAYED: number;
    };
    ACK_STRING: {
        SENDER: string;
        DELIVERY: string;
        READ: string;
        PLAYED: string;
        INACTIVE: string;
    };
    RETRY: {
        VALIDATE_OLD_SESSION: number;
        MAX_RETRY: number;
    };
    KEY_BUNDLE_TYPE: string;
    EDIT_ATTR: {
        REVOKE: number;
    };
    DEVICE: {
        PRIMARY_DEVICE: number;
        PRIMARY_VERSION: number;
    };
    BATTERY_LOW_THRESHOLD_1: number;
    BATTERY_LOW_THRESHOLD_2: number;
    BATTERY_DELAY: number;
    WAM_MAX_BUFFER_SIZE: number;
    SOCKET_STATE: {
        OPENING: string;
        PAIRING: string;
        UNPAIRED: string;
        UNPAIRED_IDLE: string;
        CONNECTED: string;
        WITHOUT_INTERNET: string;
        CONFLICT: string;
        UNLAUNCHED: string;
        PROXYBLOCK: string;
        TOS_BLOCK: string;
        SMB_TOS_BLOCK: string;
        DEPRECATED_VERSION: string;
    };
    SOCKET_STREAM: {
        DISCONNECTED: string;
        SYNCING: string;
        RESUMING: string;
        CONNECTED: string;
    };
    COLLECTION_HAS_SYNCED: string;
    NEW_MSG_SENT: string;
    DIAGNOSTIC_DELAY: number;
    ONE_BY_ONE_TRANS_GIF: string;
    WALLPAPER_COLOR: string[];
    DEFAULT_CHAT_WALLPAPER: string;
    INVERT_TRANSPARENT: {
        '#ede9e4': boolean;
        '#ccebdc': boolean;
        '#aed8c7': boolean;
        '#7acba5': boolean;
        '#c7e9eb': boolean;
        '#a9dbd8': boolean;
        '#68d5d9': boolean;
        '#6ec3d4': boolean;
        '#f2dad5': boolean;
        '#f2d5e1': boolean;
        '#fbcad2': boolean;
        '#ffa7a8': boolean;
        '#cbdaec': boolean;
        '#d7d3eb': boolean;
        '#e5c0eb': boolean;
        '#d0deb1': boolean;
        '#dee0b4': boolean;
        '#e6dfa8': boolean;
        '#f7e9a8': boolean;
        '#ffd1a4': boolean;
        '#ff8a8c': boolean;
        '#ff5978': boolean;
        '#f56056': boolean;
        '#dc6e4f': boolean;
        '#e6e365': boolean;
        '#73c780': boolean;
        '#2293a4': boolean;
        '#219ed9': boolean;
        '#2b5aa6': boolean;
        '#74676a': boolean;
        '#48324d': boolean;
        '#dee3e9': boolean;
        '#d9dade': boolean;
        '#c0c1c4': boolean;
        '#7e90a3': boolean;
        '#55626f': boolean;
        '#243640': boolean;
        '#162127': boolean;
    };
    L10N_PRIORITY: {
        SAVED: number;
        PHONE: number;
        PREVIOUS: number;
        URL: number;
        BROWSER: number;
        DEFAULT: number;
    };
    RENDER_CURSOR: {
        RECENT_AT_TOP: string;
        RECENT_AT_BOTTOM: string;
        CONVERSATION: string;
        GROUP_CONVERSATION: string;
        STARRED_DRAWER: string;
    };
    SECURITY_LINK: string;
    SMB_TOS_LEARN_MORE_LINK: string;
    SERVER_WID: string;
    PSA_WID: string;
    STATUS_WID: string;
    OFFICIAL_BIZ_WID: string;
    VISIBILITY: {
        ABOVE: string;
        VISIBLE: string;
        BELOW: string;
    };
    VIDEO_STREAM_URL: string;
    SPELL_CHECK_SKIP_WORDS: {
        en_us: Set<string>;
        en_gb: Set<string>;
        en: Set<string>;
    };
    GROUP_INVITE_LINK_URL: string;
    GROUP_SETTING_TYPE: {
        ANNOUNCEMENT: string;
        RESTRICT: string;
        NO_FREQUENTLY_FORWARDED: string;
        EPHEMERAL: string;
    };
    GROUP_SETTING_TO_METADATA: {
        announcement: string;
        restrict: string;
        no_frequently_forwarded: string;
        ephemeral: string;
    };
    L10N: {
        DEFAULT: string;
    };
    EMOJI: {
        BUCKET_SIZE: number;
        CATEGORIES: {
            SMILEYS_PEOPLE: string;
            ANIMALS_NATURE: string;
            FOOD_DRINK: string;
            ACTIVITY: string;
            TRAVEL_PLACES: string;
            OBJECTS: string;
            SYMBOLS: string;
            FLAGS: string;
        };
        CATEGORY_MAPPING: {
            'Smileys & People': string;
            'Animals & Nature': string;
            'Food & Drink': string;
            Activity: string;
            'Travel & Places': string;
            Objects: string;
            Symbols: string;
            Flags: string;
        };
        ORDERED_CATEGORY_IDS: string[];
        EMOJI_TYPE: {
            APPLE: string;
            WHATSAPP: string;
        };
        LARGE_EMOJI_BASE_PATH: string;
        LARGE_EMOJI_ELECTRON_BASE_PATH: string;
        EMOJI_SPRITES_BASE_PATH: string;
        EMOJI_SPRITES_ELECTRON_BASE_PATH: string;
    };
    MSG_TYPE: {
        NOTIFICATION: string;
        NOTIFICATION_TEMPLATE: string;
        GROUP_NOTIFICATION: string;
        GP2: string;
        BROADCAST_NOTIFICATION: string;
        E2E_NOTIFICATION: string;
        CALL_LOG: string;
        PROTOCOL: string;
        CHAT: string;
        LOCATION: string;
        PAYMENT: string;
        VCARD: string;
        CIPHERTEXT: string;
        MULTI_VCARD: string;
        REVOKED: string;
        OVERSIZED: string;
        GROUPS_V4_INVITE: string;
        TEMPLATE: string;
        HSM: string;
        TEMPLATE_BUTTON_REPLY: string;
        IMAGE: string;
        VIDEO: string;
        AUDIO: string;
        PTT: string;
        STICKER: string;
        DOCUMENT: string;
        PRODUCT: string;
        UNKNOWN: string;
    };
    TEMPLATE_SUBTYPE: {
        IMAGE: string;
        VIDEO: string;
        LOCATION: string;
        DOCUMENT: string;
        TEXT: string;
    };
    TEMPLATE_BUTTON_SUBTYPE: {
        QUICK_REPLY: string;
        CALL: string;
        URL: string;
    };
    NATIVE_PREF: {
        LAST_SAVED_LOCATION: string;
        CONTENT_SETTINGS: string;
    };
    TOUCHBAR_MAX_EMOJIS: number;
    VERIFIED_LEVEL: {
        UNKNOWN: number;
        LOW: number;
        HIGH: number;
    };
    HOSTNAME: {
        YOUTUBE: string;
        YOUTUBE_SHORTENED: string;
        INSTAGRAM: string;
        STREAMABLE: string;
        FACEBOOK: string;
        FBWATCH: string;
        LASSOVIDEOS: string;
    };
    WHATSAPP_ORIGIN: string;
    SMB_SEARCH_FILTERS: {
        UNREAD: string;
        GROUP: string;
        BROADCAST: string;
    };
    SMB_LABELS: {
        MAX_LABEL_LENGTH: number;
    };
    PRODUCT_INQUIRY_TYPE: string;
    PRODUCT_LIST_ITEM_HEIGHT: number;
    LOADABLE_DELAY: number;
    MAX_EPHEMERAL_DURATION: number;
    EPHEMERAL_SETTINGS: {
        OFF: number;
        ONE_HOUR: number;
        ONE_DAY: number;
        ONE_WEEK: number;
        ONE_MONTH: number;
        ONE_YEAR: number;
    };
    TAB_ORDERS: {
        COMPOSE_BOX_INPUT: number;
        MESSAGE_LIST: number;
        CHAT_STARRED_DRAWER: number;
        CHAT_LIST_SEARCH: number;
        CHAT_LIST: number;
        CHAT_CONTACT_LIST: number;
        CHAT_IMAGE_GALLERY: number;
        CHAT_SEARCH_MSG_LIST: number;
        PANEL_SEARCH_INPUT: number;
        COMPOSE_BOX_MENU_BUTTON: number;
    };
    SPEEDY_RESUME_MAX_CHATS: number;
    MEDIA_VIEWER: {
        ANIMATION_DURATION: number;
        CLOSE_ANIMATION_DURATION: number;
        ZOOM_IN_FACTOR: number;
    };
};
