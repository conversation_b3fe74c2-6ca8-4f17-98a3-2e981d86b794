import { NextRequest, NextResponse } from 'next/server'
import { advancedAPI } from '@/lib/api/advanced-api'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  let apiKey: any = null

  try {
    // Authenticate API request
    const authResult = await advancedAPI.authenticate(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }

    apiKey = authResult.apiKey!

    // Check rate limit
    const rateLimit = await advancedAPI.checkRateLimit(apiKey, '/api/v1/status')
    if (!rateLimit.allowed) {
      const response = NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
      
      response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
      response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
      response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())
      
      return response
    }

    // Handle status request
    const response = await advancedAPI.handleStatusRequest(request, apiKey)
    
    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())

    return response

  } catch (error) {
    console.error('API status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  } finally {
    // Log API usage
    if (apiKey) {
      const responseTime = Date.now() - startTime
      const ipAddress = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       'unknown'
      const userAgent = request.headers.get('user-agent') || 'unknown'

      await advancedAPI.logAPIUsage(
        apiKey.id,
        '/api/v1/status',
        'GET',
        200,
        responseTime,
        0,
        0,
        ipAddress,
        userAgent
      )
    }
  }
}
