{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../src/bidi/BrowserContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,OAAO,EAAC,qCAAqC,EAAC,MAAM,mBAAmB,CAAC;AAExE,OAAO,EAAC,cAAc,EAAsB,MAAM,0BAA0B,CAAC;AAG7E,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAC,MAAM,EAAC,MAAM,uBAAuB,CAAC;AAI7C,OAAO,EAAC,WAAW,EAAC,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAC,QAAQ,EAAC,MAAM,WAAW,CAAC;AACnC,OAAO,EAAC,gBAAgB,EAAC,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAC,eAAe,EAAE,cAAc,EAAC,MAAM,aAAa,CAAC;AAU5D;;GAEG;IACU,kBAAkB;sBAAS,cAAc;;;;iBAAzC,kBAAmB,SAAQ,WAAc;;;0CAWnD,MAAM,EAAE;YACT,+LAAS,cAAc,6BAAd,cAAc,uGAA4C;;;QAXnE,MAAM,CAAC,IAAI,CACT,OAAoB,EACpB,WAAwB,EACxB,OAAkC;YAElC,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YACtE,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACjB,CAAC;QAGD,yFAA0B,IAAI,YAAY,EAAwB,EAAC;QAAnE,IAAS,cAAc,oDAA4C;QAAnE,IAAS,cAAc,0DAA4C;QAE1D,QAAQ,8DAAc;QACtB,gBAAgB,CAAkB;QAC3C,qCAAqC;QAC5B,WAAW,CAAc;QACzB,MAAM,GAAG,IAAI,OAAO,EAA6B,CAAC;QAClD,QAAQ,GAAG,IAAI,GAAG,EAMxB,CAAC;QAEJ,UAAU,GAAoD,EAAE,CAAC;QAEjE,YACE,OAAoB,EACpB,WAAwB,EACxB,OAAkC;YAElC,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;QAClD,CAAC;QAED,WAAW;YACT,iDAAiD;YACjD,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;gBAChE,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,EAAC,eAAe,EAAC,EAAE,EAAE;gBAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAE/C,kDAAkD;gBAClD,+DAA+D;gBAC/D,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE;oBAC5C,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;wBACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;4BACxD,IAAI,OAAO,CAAC,EAAE,KAAK,eAAe,CAAC,cAAc,EAAE,CAAC;gCAClD,IAAI,CAAC,MAAM;qCACR,GAAG,CAAC,OAAO,CAAE;qCACb,cAAc,CAAC,IAAI,gCAAkB,IAAI,CAAC,CAAC;4BAChD,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACjC,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC3C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,WAAW,CAAC,eAAgC;YAC1C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,EAAE,gCAAkB,GAAG,EAAE;gBAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YAEH,iCAAiC;YACjC,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;YAEnD,IAAI,CAAC,cAAc,CAAC,EAAE,gDAA0B,KAAK,CAAC,EAAE;gBACtD,MAAM,SAAS,GAAG,KAAkB,CAAC;gBACrC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;gBAC9C,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACnC,IAAI,CAAC,cAAc,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,EAAE,kDAA2B,KAAK,CAAC,EAAE;gBACvD,MAAM,SAAS,GAAG,KAAkB,CAAC;gBACrC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAC1C,wDAAwD;gBACxD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,IAAI,CAAC,cAAc,CAAC,IAAI,0DAAoC,UAAU,CAAC,CAAC;gBAC1E,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,cAAc,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,EAAE,gDAA0B,KAAK,CAAC,EAAE;gBACtD,MAAM,SAAS,GAAG,KAAkB,CAAC;gBACrC,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAC1C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,OAAO;gBACT,CAAC;gBACD,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,8DAAsC,MAAM,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,EAAE,gDAA0B,MAAM,CAAC,EAAE;gBACvD,MAAM,UAAU,GAAG,MAAuB,CAAC;gBAC3C,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAChD,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACpC,IAAI,CAAC,cAAc,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,EAAE,oDAA4B,MAAM,CAAC,EAAE;gBACzD,MAAM,UAAU,GAAG,MAAuB,CAAC;gBAC3C,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC3C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,OAAO;gBACT,CAAC;gBACD,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,8DAAsC,MAAM,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,EAAE,gCAAkB,GAAG,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,8DAAsC,UAAU,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,IAAI,0DAAoC,UAAU,CAAC,CAAC;YACxE,+BAA+B;YAE/B,OAAO,IAAI,CAAC;QACd,CAAC;QAEQ,OAAO;YACd,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE;gBAC9D,OAAO,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QACL,CAAC;QAEQ,KAAK,CAAC,OAAO;;;gBACpB,MAAM,MAAM,kCAAG,MAAM,IAAI,CAAC,2BAA2B,EAAE,QAAA,CAAC;gBAExD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,iDAE3D,CAAC;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;gBACvC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC1B,IAAI,CAAC;wBACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAChD,CAAC;oBAAC,MAAM,CAAC;wBACP,yCAAyC;oBAC3C,CAAC;gBACH,CAAC;gBAED,OAAO,IAAI,CAAC;;;;;;;;;SACb;QAEQ,KAAK,CAAC,KAAK;YAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAClC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAEQ,OAAO;YACd,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAEQ,KAAK,CAAC,KAAK;YAClB,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC1D,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC;QACrD,CAAC;QAEQ,KAAK,CAAC,mBAAmB,CAChC,MAAc,EACd,WAAyB;YAEzB,MAAM,cAAc,GAAG,IAAI,GAAG,CAC5B,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBAC3B,MAAM,kBAAkB,GACtB,qCAAqC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACxD,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBACxB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,CAAC;gBACvD,CAAC;gBACD,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC,CACH,CAAC;YACF,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAC1D,UAAU,CAAC,EAAE;gBACX,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAC5C,MAAM,EACN;oBACE,IAAI,EAAE,UAAU;iBACjB,EACD,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC5B,CAAC;oBACD,CAAC,uDAAwC,CAC5C,CAAC;gBACF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC;gBAC3C,sEAAsE;gBACtE,YAAY;gBACZ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;oBACpC,OAAO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBAClC,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC,CACF,CACF,CAAC;QACJ,CAAC;QAEQ,KAAK,CAAC,wBAAwB;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAC,UAAU,EAAE,MAAM,EAAC,EAAE,EAAE;gBAC5D,OAAO,IAAI,CAAC,WAAW;qBACpB,cAAc,CACb,MAAM,EACN;oBACE,IAAI,EAAE,UAAU;iBACjB,yDAEF;qBACA,KAAK,CAAC,UAAU,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAED,IAAa,EAAE;YACb,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;gBAChD,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7B,CAAC;;;SAtPU,kBAAkB"}