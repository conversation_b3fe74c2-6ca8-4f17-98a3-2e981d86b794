{"name": "prompts-ncu", "version": "3.0.2", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": "https://github.com/raineorshine/prompts/tree/ncu", "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "index.js"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec", "test-types": "tsc --noEmit test/type-declarations.ts"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^4.0.1", "sisteransi": "^1.0.5"}, "devDependencies": {"@types/node": "^18.11.17", "tap-spec": "^5.0.0", "tape": "^4.13.3", "typescript": "^4.9.4"}, "engines": {"node": ">= 14"}}