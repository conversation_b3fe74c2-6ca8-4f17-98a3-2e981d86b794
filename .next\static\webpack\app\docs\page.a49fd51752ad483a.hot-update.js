"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DocsPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    const categories = [\n        {\n            id: \"getting-started\",\n            name: \"Getting Started\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"Quick start guide and basic setup\"\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"Open Source Intelligence gathering\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Security scanning and assessment\"\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Malware and file analysis tools\"\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Database\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Vulnerability database and search\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Advanced search techniques\"\n        },\n        {\n            id: \"api\",\n            name: \"API Reference\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"REST API documentation\"\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Additional security utilities\"\n        }\n    ];\n    const quickStartGuides = [\n        {\n            title: \"Platform Overview\",\n            description: \"Learn about KodeXGuard features and capabilities\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            time: \"5 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"First OSINT Investigation\",\n            description: \"Step-by-step guide to your first investigation\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            time: \"15 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"Running Vulnerability Scans\",\n            description: \"How to perform comprehensive security scans\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            time: \"20 min read\",\n            difficulty: \"Intermediate\"\n        },\n        {\n            title: \"API Integration\",\n            description: \"Integrate KodeXGuard into your workflow\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            time: \"30 min read\",\n            difficulty: \"Advanced\"\n        }\n    ];\n    const popularDocs = [\n        {\n            title: \"OSINT Methodology\",\n            category: \"OSINT\",\n            views: \"15.2K\",\n            updated: \"2 days ago\"\n        },\n        {\n            title: \"CVE Search Techniques\",\n            category: \"CVE Database\",\n            views: \"12.8K\",\n            updated: \"1 week ago\"\n        },\n        {\n            title: \"Advanced Google Dorking\",\n            category: \"Dorking\",\n            views: \"9.5K\",\n            updated: \"3 days ago\"\n        },\n        {\n            title: \"API Authentication\",\n            category: \"API\",\n            views: \"8.1K\",\n            updated: \"5 days ago\"\n        }\n    ];\n    const tutorials = [\n        {\n            title: \"Bug Bounty Reconnaissance\",\n            description: \"Complete guide to reconnaissance for bug bounty hunting\",\n            duration: \"45 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OSINT\",\n                \"Subdomain Enumeration\",\n                \"Port Scanning\"\n            ]\n        },\n        {\n            title: \"Malware Analysis Basics\",\n            description: \"Introduction to static and dynamic malware analysis\",\n            duration: \"60 min\",\n            level: \"Advanced\",\n            topics: [\n                \"File Analysis\",\n                \"Reverse Engineering\",\n                \"Sandboxing\"\n            ]\n        },\n        {\n            title: \"Web Application Security Testing\",\n            description: \"Comprehensive web app security assessment\",\n            duration: \"90 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OWASP Top 10\",\n                \"SQL Injection\",\n                \"XSS\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-glow\",\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-pink\",\n                                        children: \"Center\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Comprehensive guides, tutorials, and API documentation to help you master cybersecurity tools and techniques\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 \".concat(themeClasses.textMuted)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search documentation...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 rounded-lg \".concat(themeClasses.input, \" text-lg\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\",\n                        children: [\n                            {\n                                label: \"Documentation Pages\",\n                                value: \"150+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                label: \"Video Tutorials\",\n                                value: \"45+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                label: \"Code Examples\",\n                                value: \"200+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                label: \"API Endpoints\",\n                                value: \"80+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                            }\n                        ].map((stat, index)=>{\n                            const Icon = stat.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(themeClasses.card, \" text-center\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 \".concat(themeClasses.isDark ? \"animate-cyber-pulse\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: themeClasses.card,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: categories.map((category)=>{\n                                                const Icon = category.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedCategory(category.id),\n                                                    className: \"w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 \".concat(selectedCategory === category.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary\" : \"bg-blue-100 text-blue-600 border border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Quick Start Guides\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: quickStartGuides.map((guide, index)=>{\n                                                    const Icon = guide.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-100\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-6 w-6 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: guide.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3 text-sm\"),\n                                                                            children: guide.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(guide.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : guide.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: guide.difficulty\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs \".concat(themeClasses.textMuted),\n                                                                                    children: guide.time\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Popular Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: popularDocs.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                                                            children: doc.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-secondary/20 text-cyber-secondary\" : \"bg-pink-100 text-pink-600\"),\n                                                                                    children: doc.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        doc.views,\n                                                                                        \" views\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        \"Updated \",\n                                                                                        doc.updated\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 \".concat(themeClasses.textMuted)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Video Tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-6\",\n                                                children: tutorials.map((tutorial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full md:w-48 h-32 rounded-lg \".concat(themeClasses.isDark ? \"bg-gradient-to-br from-cyber-primary/20 to-cyber-secondary/20\" : \"bg-gradient-to-br from-blue-100 to-pink-100\", \" flex items-center justify-center\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"h-12 w-12 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: tutorial.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                            children: tutorial.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap items-center gap-2 mb-3\",\n                                                                            children: tutorial.topics.map((topic, topicIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-accent/20 text-cyber-accent\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                    children: topic\n                                                                                }, topicIndex, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: tutorial.duration\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(tutorial.level === \"Beginner\" ? \"bg-green-100 text-green-800\" : tutorial.level === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: tutorial.level\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                                        children: \"Watch Now\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold \".concat(themeClasses.textPrimary),\n                                                        children: \"API Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Integrate KodeXGuard's powerful cybersecurity tools into your applications with our comprehensive REST API.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Authentication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"API key and OAuth 2.0 authentication methods\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Rate Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Request limits and best practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"SDKs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Python, JavaScript, and Go libraries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                        children: \"View API Docs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Download SDK\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(DocsPage, \"bErKlB3njoaufpGnSTOHHJBBUlQ=\");\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/page.tsx\n"));

/***/ })

});