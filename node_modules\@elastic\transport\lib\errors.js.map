{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAKH,yCAA6C;AAM7C,MAAa,wBAAyB,SAAQ,KAAK;IAEjD,YAAa,OAAe,EAAE,OAAsB;QAClD,KAAK,CAAC,OAAO,CAAC,CAAA;QAFhB;;;;;WAAqB;QAGnB,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAA;QAEtC,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE;gBACT,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE,EAAE;aACnB;SACF,CAAA;QAED,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC,SAAS,EAAE,CAAA;QAC9E,CAAC;IACH,CAAC;CACF;AAjBD,4DAiBC;AAED,MAAa,YAAa,SAAQ,wBAAwB;IAExD,YAAa,OAAe,EAAE,IAAuB,EAAE,OAAsB;QAC3E,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAFzB;;;;;WAAuB;QAGrB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;QAC3C,IAAI,CAAC,IAAI,GAAG,cAAc,CAAA;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,eAAe,CAAA;QAEzC,IAAI,QAAQ,CAAC,IAAI,CAAC;YAAE,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAXD,oCAWC;AAED,MAAa,eAAgB,SAAQ,wBAAwB;IAE3D,YAAa,OAAe,EAAE,IAAuB,EAAE,OAAsB;QAC3E,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAFzB;;;;;WAAuB;QAGrB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;QAC9C,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAA;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,kBAAkB,CAAA;QAE5C,IAAI,QAAQ,CAAC,IAAI,CAAC;YAAE,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAXD,0CAWC;AAED,MAAa,wBAAyB,SAAQ,wBAAwB;IAEpE,YAAa,OAAe,EAAE,IAAsB,EAAE,OAAsB;QAC1E,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAFzB;;;;;WAAsB;QAGpB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,wBAAwB,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAA;QACtC,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,wGAAwG,CAAA;QAElI,IAAI,CAAC,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC5D,CAAC;CACF;AAVD,4DAUC;AAED,MAAa,kBAAmB,SAAQ,wBAAwB;IAE9D,YAAa,OAAe,EAAE,IAAyB;QACrD,KAAK,CAAC,OAAO,CAAC,CAAA;QAFhB;;;;;WAAyB;QAGvB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;QACjD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAA;QAChC,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,qBAAqB,CAAA;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AATD,gDASC;AAED,MAAa,oBAAqB,SAAQ,wBAAwB;IAEhE,YAAa,OAAe,EAAE,IAAY;QACxC,KAAK,CAAC,OAAO,CAAC,CAAA;QAFhB;;;;;WAAY;QAGV,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAA;QAClC,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,uBAAuB,CAAA;QACjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AATD,oDASC;AAED,MAAa,kBAAmB,SAAQ,wBAAwB;IAC9D,YAAa,OAAe;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;QACjD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAA;QAChC,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,qBAAqB,CAAA;IACjD,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,aAAc,SAAQ,wBAAwB;IAEzD,YAAa,IAAsB,EAAE,OAAsB;;QACzD,KAAK,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;QAFlC;;;;;WAAsB;QAGpB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;QAC5C,IAAI,CAAC,IAAI,GAAG,eAAe,CAAA;QAE3B,IAAI,IAAI,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,GAAG,wEAAwE,CAAA;QACzF,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YAC1F,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;YAEnC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA;gBAClD,MAAM,QAAQ,GAAG;oBACf,cAAc;oBACd,OAAO,IAAc,KAAK,MAAgB,EAAE;iBAC7C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACZ,IAAI,CAAC,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAA;YACjC,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzF,MAAM,eAAe,GAAG,CAAC,KAA6B,EAAU,EAAE,CAChE,OAAO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,CAAA;gBAEtC,MAAM,UAAU,GAAG;oBACjB,gBAAgB;oBAChB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,CAAC;iBACnD,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEZ,IAAI,CAAC,OAAO,IAAI,KAAK,UAAU,EAAE,CAAA;YACnC,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YAC9D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,IAAc,mCAAI,gBAAgB,CAAA;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC5D,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAED,IAAI,UAAU;QACZ,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1E,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAA;IAC7B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAA;IAC1B,CAAC;CACF;AAvDD,sCAuDC;AAED,MAAa,mBAAoB,SAAQ,wBAAwB;IAE/D,YAAa,OAAe,EAAE,IAAuB,EAAE,OAAsB;QAC3E,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAFzB;;;;;WAAuB;QAGrB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAA;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,iBAAiB,CAAA;QAE3C,IAAI,QAAQ,CAAC,IAAI,CAAC;YAAE,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAXD,kDAWC;AAED,MAAa,wBAAyB,SAAQ,wBAAwB;IAEpE,YAAa,OAAe,EAAE,IAAuB,EAAE,OAAsB;QAC3E,KAAK,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAA;QAF/C;;;;;WAAuB;QAGrB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,wBAAwB,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAA;QACtC,IAAI,CAAC,OAAO,GAAG,6CAA6C,OAAO,8CAA8C,CAAA;QAEjH,IAAI,QAAQ,CAAC,IAAI,CAAC;YAAE,IAAI,GAAG,IAAA,2BAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAXD,4DAWC;AAED,SAAS,QAAQ,CAAE,GAAQ;IACzB,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,CAAA;AAChD,CAAC"}