import { UserRole } from '@/types/user'

export const ROLE_PERMISSIONS = {
  [UserRole.SUPER_ADMIN]: [
    'system:manage',
    'users:manage',
    'plans:manage',
    'bots:manage',
    'api:unlimited',
    'scans:unlimited',
    'files:unlimited',
    'osint:unlimited',
    'cve:manage',
    'dorking:manage',
    'admin:access',
    'audit:view',
    'payments:manage',
    'maintenance:toggle',
    'backup:manage',
  ],
  [UserRole.ADMIN]: [
    'users:view',
    'users:edit',
    'plans:view',
    'bots:view',
    'api:extended',
    'scans:extended',
    'files:extended',
    'osint:extended',
    'cve:view',
    'dorking:view',
    'admin:access',
    'audit:view',
    'support:manage',
  ],
  [UserRole.USER]: [
    'api:basic',
    'scans:basic',
    'files:basic',
    'osint:basic',
    'cve:view',
    'dorking:basic',
    'profile:edit',
    'apikeys:manage',
  ],
}

export const ROLE_DESCRIPTIONS = {
  [UserRole.SUPER_ADMIN]: 'Full system access with all permissions',
  [UserRole.ADMIN]: 'Administrative access with user and content management',
  [UserRole.USER]: 'Standard user with basic platform features',
}

export const ROLE_LABELS = {
  [UserRole.SUPER_ADMIN]: 'Super Admin',
  [UserRole.ADMIN]: 'Admin',
  [UserRole.USER]: 'User',
}

export function hasPermission(userRole: UserRole, permission: string): boolean {
  const permissions = ROLE_PERMISSIONS[userRole] || []
  return permissions.includes(permission)
}

export function canAccessAdminPanel(userRole: UserRole): boolean {
  return hasPermission(userRole, 'admin:access')
}

export function canManageUsers(userRole: UserRole): boolean {
  return hasPermission(userRole, 'users:manage')
}

export function canManageBots(userRole: UserRole): boolean {
  return hasPermission(userRole, 'bots:manage')
}

export function canManageSystem(userRole: UserRole): boolean {
  return hasPermission(userRole, 'system:manage')
}
