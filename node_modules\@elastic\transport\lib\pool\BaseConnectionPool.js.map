{"version": 3, "file": "BaseConnectionPool.js", "sourceRoot": "", "sources": ["../../src/pool/BaseConnectionPool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAgDH,8CAWC;;AAzDD,uCAA8B;AAE9B,0DAAyB;AACzB,uEAAsC;AACtC,wCAA2C;AAC3C,8CAIsB;AAWtB,sCAA8C;AAE9C,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAA;AAwBpC,SAAgB,iBAAiB,CAAE,IAAgB;IACjD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACvB;QACE,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,MAAM;YACjB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;YAChB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;YAClB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACd,OAAO,KAAK,CAAA;IAChB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;;;;GAKG;AACH,MAAqB,kBAAkB;IAWrC,YAAa,IAA2B;;QAVxC;;;;;WAAyB;QACzB;;;;;WAAY;QACZ;;;;;WAAiC;QACjC;;;;;WAAsB;QACtB;;;;;WAA0C;QAC1C;;;;;WAAgE;QAChE;;;;;WAAqB;QACrB;;;;;WAA2B;QAC3B;;;;;WAAyB;QAGvB,4BAA4B;QAC5B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACrB,0CAA0C;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;QACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QACjC,IAAI,CAAC,UAAU,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,IAAI,oBAAU,EAAE,CAAA;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,wBAAc,CAAC,GAAG,IAAI,CAAC,aAAa,CAAA;IAC3C,CAAC;IAED,SAAS,CAAE,UAAsB;QAC/B,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,KAAK,CAAA;QACjD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,QAAQ,CAAE,UAAsB;QAC9B,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,IAAI,CAAA;QAChD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,aAAa,CAAE,IAA0B;QACvC,MAAM,IAAI,2BAAkB,CAAC,oEAAoE,CAAC,CAAA;IACpG,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAE,IAAgC;QAChD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACvB,CAAC;aAAM,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC,IAAI,GAAG;gBACV,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC/C,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;aAChD,CAAA;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI;YAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QAC1C,0BAA0B;QAC1B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;QAChD,0BAA0B;QAC1B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;QAChD,0BAA0B;QAC1B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI;YAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAC9D,0BAA0B;QAC1B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI;YAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAc,CAAC,CAAA;QAEzE,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAE5C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,CAAC,EAAE,sBAAsB,CAAC,CAAA;YAC7E,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAE,UAAyD;QACtE,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAiB,EAAE,CAAA;YACpC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAA;YAC/C,CAAC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAC9E,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAE,UAAsB;QACtC,KAAK,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAA;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACpC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QACb,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,UAAU,CAAC,KAAK,EAAE,CAAA;QAC1B,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAE,KAA4C;QAClD,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACrC,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,MAAM,cAAc,GAAG,EAAE,CAAA;QAEzB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,oDAAoD;YACpD,yDAAyD;YACzD,yBAAyB;YACzB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;YACnE,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC1E,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;gBAC3B,KAAK,CAAC,2BAA2B,IAAI,CAAC,EAAY,sBAAsB,CAAC,CAAA;gBACzE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAA;gBAC9B,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;gBACrC,kEAAkE;gBAClE,8EAA8E;gBAC9E,mEAAmE;gBACnE,uEAAuE;YACvE,CAAC;iBAAM,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;gBACnC,eAAe,CAAC,EAAE,GAAG,IAAI,CAAC,EAAY,CAAA;gBACtC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;gBAC/B,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACtC,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,YAAY,2BAAc,EAAE,CAAC;oBACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC3B,CAAC;qBAAM,CAAC;oBACN,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAA;gBAClD,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAChC,sDAAsD;QACtD,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE,CAAC;YACxC,UAAU,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,0BAA0B,CAAA,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;QAC9D,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,cAAc,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;QAEnC,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAE,KAA0B,EAAE,QAAgB;QACvD,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC9B,MAAM,KAAK,GAAG,EAAE,CAAA;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1B,2DAA2D;YAC3D,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS;gBAAE,SAAQ;YAErC,6BAA6B;YAC7B,2CAA2C;YAC3C,0CAA0C;YAC1C,cAAc;YACd,uBAAuB;YACvB,6CAA6C;YAC7C,qCAAqC;YACrC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAyB,CAAA;YACjD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAChC,6CAA6C;YAC7C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;gBACzB,MAAM,IAAI,GAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAC5E,OAAO,GAAG,GAAG,QAAQ,IAAI,IAAI,EAAE,CAAA;YACjC,CAAC;YAED,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM;gBACtC,0BAA0B;gBAC1B,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,GAAG,QAAQ,KAAK,OAAO,EAAE,CAAA;YAE7B,KAAK,CAAC,IAAI,CAAC;gBACT,GAAG,EAAE,IAAI,cAAG,CAAC,OAAO,CAAC;gBACrB,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;aACX,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAE,GAAW;QACpB,OAAO;YACL,GAAG,EAAE,IAAI,cAAG,CAAC,GAAG,CAAC;SAClB,CAAA;IACH,CAAC;CACF;KAlOE,wBAAc;kBATI,kBAAkB"}