# [5.1.0](https://github.com/orkestral/venom/compare/v5.0.22...v5.1.0) (2024-04-03)



## [5.0.23](https://github.com/orkestral/venom/compare/v5.0.22...v5.0.23) (2024-01-18)



## [5.0.22](https://github.com/orkestral/venom/compare/v5.0.21...v5.0.22) (2023-10-05)


### Features

* version ([4a6114e](https://github.com/orkestral/venom/commit/4a6114e56cdcc7ee36940126b001dfe7a97c9156))



## [5.0.21](https://github.com/orkestral/venom/compare/v5.0.20...v5.0.21) (2023-09-14)


### Bug Fixes

* lint ([a8e755d](https://github.com/orkestral/venom/commit/a8e755d549fca8cc831a583cdee85794d990c7ad))
* url donate ([f8568da](https://github.com/orkestral/venom/commit/f8568da1f0ebfa25cf2766312656c75b880c41a8))
* url donate ([78f5979](https://github.com/orkestral/venom/commit/78f5979b74d57fa202a0664dce896e9537503414))


### Features

* donate info ([bf69543](https://github.com/orkestral/venom/commit/bf69543880d4fce0df1d069e29a5449f2d31f126))
* img donate ([3675e55](https://github.com/orkestral/venom/commit/3675e551ccbf59e93e4a59cf7086633687598171))
* lint setup-node@v2 ([e346488](https://github.com/orkestral/venom/commit/e346488dcde8861a5773524c498c85390b7153db))
* node version 16 ([2d2e010](https://github.com/orkestral/venom/commit/2d2e010418bce657ffa98bd89c50af3edf5559f5))
* setup-node@v2 ([e96afb2](https://github.com/orkestral/venom/commit/e96afb2a9d6a62d0a2de918840de5a1cd47f1dc5))
* Update README.md ([dab3d17](https://github.com/orkestral/venom/commit/dab3d1770363d4427879621b7c17169912322db9))
* Update README.md donate ([994e9c7](https://github.com/orkestral/venom/commit/994e9c705b67317eddb55349d69dd5266e72cda5))



## [5.0.20](https://github.com/orkestral/venom/compare/v5.0.19...v5.0.20) (2023-09-06)


### Bug Fixes

* deleteMessage ([2608239](https://github.com/orkestral/venom/commit/26082393b1fa95cb2e2463ede56a1fbf08716798))


### Features

* add types ts ([05d6b72](https://github.com/orkestral/venom/commit/05d6b72a654c72529d38dfb8b5bd4903937e6be0))
* messageSendResult ([873b0b6](https://github.com/orkestral/venom/commit/873b0b61db16b841916905fdbb3a1d280b1cee79))
* writeFile SingletonLock ([25c56d4](https://github.com/orkestral/venom/commit/25c56d4cf22e8fc06fa8a2aeb15682dae5585e42))



## [5.0.19](https://github.com/orkestral/venom/compare/v5.0.18...v5.0.19) (2023-08-23)


### Features

* add headless: 'old' ([bc919e8](https://github.com/orkestral/venom/commit/bc919e8f740496a1d34078916f8e8df860d26daa))



## [5.0.18](https://github.com/orkestral/venom/compare/v5.0.17...v5.0.18) (2023-08-23)


### Bug Fixes

* message Send Result - (reading 'error') ([7d14c87](https://github.com/orkestral/venom/commit/7d14c87bffe76a5d98060ce6ccd43f0a7da2aa52))
* remove  Create Release ([a6f10f6](https://github.com/orkestral/venom/commit/a6f10f63cdf2e2ae03e3cb85883995dd03b5a613))


### Features

* add spinnies info ([bbc4c38](https://github.com/orkestral/venom/commit/bbc4c3869927e0e6e8af5538ae6237cc2b286fc2))



## [5.0.17](https://github.com/orkestral/venom/compare/v5.0.16...v5.0.17) (2023-08-11)


### Bug Fixes

* chromium version ([e692b48](https://github.com/orkestral/venom/commit/e692b4804e53604bdf33d8e2a86efc378c66f36d))
* error r.findImpl is not function ([9c7d5f3](https://github.com/orkestral/venom/commit/9c7d5f35f15849c11a5c732b678f238e4b801fee))
* QRCode Scan terminal ([33d8d50](https://github.com/orkestral/venom/commit/33d8d50e2366e17e64347f472c995d99a5bb0909))


### Features

* remove npm publish ([ce117a7](https://github.com/orkestral/venom/commit/ce117a7104a71b5dd2c63e1169bf7ce43677a8b5))
* set timeout page ([817945f](https://github.com/orkestral/venom/commit/817945f256effca3cf831a75f757aa2865f307e5))



## [5.0.16](https://github.com/orkestral/venom/compare/v5.0.15...v5.0.16) (2023-08-03)


### Bug Fixes

* npm install ([e6eccd6](https://github.com/orkestral/venom/commit/e6eccd6817fc9c3b7a8fffeacc33e7d3918d4656))
* tsconfig.json ([1a98b89](https://github.com/orkestral/venom/commit/1a98b89e8b336f103c5dbd4fb27113ce72f2ff5b))



## [5.0.15](https://github.com/orkestral/venom/compare/v5.0.14...v5.0.15) (2023-08-03)


### Bug Fixes

* is-admin ([3d47967](https://github.com/orkestral/venom/commit/3d479674a0bb0301f4d3673ce1a0a98cda57e6a9))



## [5.0.14](https://github.com/orkestral/venom/compare/v5.0.13...v5.0.14) (2023-08-01)


### Bug Fixes

* add `npm-check-updates` as dependencies ([3ba733f](https://github.com/orkestral/venom/commit/3ba733f9da96c6a1ed6600a3fa2d3c20d018e37c))
* add argsRoot ([089db66](https://github.com/orkestral/venom/commit/089db66d4f9a4d8638dc071571d4c451aa2b8c72))
* remove npm ci ([8db4b93](https://github.com/orkestral/venom/commit/8db4b934ed83b1c9602e7915e7e2af035f32db0c))



## [5.0.13](https://github.com/orkestral/venom/compare/v5.0.12...v5.0.13) (2023-07-23)


### Bug Fixes

* Error removing SingletonLock ([fd6b316](https://github.com/orkestral/venom/commit/fd6b316e1e1dd7462cbbba9eb4080875d6613759))
* in NPM ([dc52d4a](https://github.com/orkestral/venom/commit/dc52d4a28cbd74d312cd43aa0d96bba8d9038c81))
* npm check updates ([68aa57c](https://github.com/orkestral/venom/commit/68aa57c85aad56773e9ff2769e8362241a58ee9a))



## [5.0.12](https://github.com/orkestral/venom/compare/v5.0.11...v5.0.12) (2023-07-21)


### Bug Fixes

* browser does not open when there are token files ([77909a5](https://github.com/orkestral/venom/commit/77909a54ca5d07a8e9b5d19ed416d8dd493d85ed))
* Check if version exists ([8348640](https://github.com/orkestral/venom/commit/8348640973417df3056755c5ec8821f30f288fcc))
* Chrome path darwin ([2b434c2](https://github.com/orkestral/venom/commit/2b434c24227dc95a54521d585e20849c92a35c0a))
* sendLinkPreview interim ([d66b4f8](https://github.com/orkestral/venom/commit/d66b4f80cc374da7316200830a7e91478b3d5f45))


### Features

* add errors and promise to SingletonLock ([25b3fa8](https://github.com/orkestral/venom/commit/25b3fa8e6b40e7841e8381f2ec65f4aaa45a7688))
* Apply Bug Fixes and Features ([32a36a5](https://github.com/orkestral/venom/commit/32a36a540caab689ed3304c278c08d58aedf486d))
* browser executable path ([451dbfa](https://github.com/orkestral/venom/commit/451dbfab5f3bc74a8ce4393cbf4349b20ea56713))
* chrome Version bash ([bb76bb2](https://github.com/orkestral/venom/commit/bb76bb22892844d5091f8827f7d5d5fde458681d))
* downloadBash linux and darwin ([d4914b4](https://github.com/orkestral/venom/commit/d4914b4c8387ba400c6e649aa6aaf59ebcf8ea74))
* get version: headless ([e04f2d4](https://github.com/orkestral/venom/commit/e04f2d4b492c42f59abf0024040a3946e2c5ffce))
* release headless: true ([dc90954](https://github.com/orkestral/venom/commit/dc90954773a3b248d2e4064feffe8d6c3a53cef7))



## [5.0.11](https://github.com/orkestral/venom/compare/v5.0.7...v5.0.11) (2023-07-03)


### Bug Fixes

* getAllContacts ([4f98590](https://github.com/orkestral/venom/commit/4f9859066add6dbd5ebc9ba39fcde0da2f078225))
* Group Link ([98ade84](https://github.com/orkestral/venom/commit/98ade8416b326b467ff50c3ee5f874ac03d0fab6))
* Query Group Store ([5a44a29](https://github.com/orkestral/venom/commit/5a44a29bedf2ce97d23e642ae1e4a533891c231b))
* remove createWid ([5e8da45](https://github.com/orkestral/venom/commit/5e8da457315497b258c3cccc38560e8105b1c3ac))


### Features

* dowload ([e1ef845](https://github.com/orkestral/venom/commit/e1ef8456d56fa7372f524ba4f35d9c28450f11e4))
* download Chrome ([777cd44](https://github.com/orkestral/venom/commit/777cd4459c7f861955873a3727b9e2e8a1a06d42))
* getChrome ([8b98eba](https://github.com/orkestral/venom/commit/8b98eba3e4018297cb2d50ff574580cba7ff8c9f))
* refactoring chrome download ([4623eed](https://github.com/orkestral/venom/commit/4623eedbdb39a9e30aeb75324d3eae245b8111a8))



## [5.0.7](https://github.com/orkestral/venom/compare/v5.0.6...v5.0.7) (2023-06-19)


### Bug Fixes

* session name ([5db179c](https://github.com/orkestral/venom/commit/5db179c2ef601f440e6519c99d1d619d5638ab2c))


### Features

* Create Release versions ([bf14b48](https://github.com/orkestral/venom/commit/bf14b481e2f9b2f69703773c2f6b845123087551))
* headless =  "new" |  false https://developer.chrome.com/articles/new-headless/ ([b2d0610](https://github.com/orkestral/venom/commit/b2d0610762ce4f4373ca7ae64a034e1f31064805))



## [5.0.6](https://github.com/orkestral/venom/compare/v5.0.5...v5.0.6) (2023-06-19)


### Bug Fixes

* Environment Files ([08f9812](https://github.com/orkestral/venom/commit/08f98127c04a243e6e1d241bef9b19392af1f2b9))
* latest-version ([e77e790](https://github.com/orkestral/venom/commit/e77e7900aed827f9342ed19ebd80dd7f1089636e))
* required Node Version ([4751ac4](https://github.com/orkestral/venom/commit/4751ac4c56357bf4a6c67bb68f4e1c731317fa14))



## [5.0.5](https://github.com/orkestral/venom/compare/v5.0.4...v5.0.5) (2023-06-16)


### Bug Fixes

* main and types ([ded9fd6](https://github.com/orkestral/venom/commit/ded9fd69c3c8918946819488fa70d10af84ce6da))


### Features

* add routine Update Package Version ([f3160b1](https://github.com/orkestral/venom/commit/f3160b1914c98663c998d3407068454691ab71f3))



## [5.0.4](https://github.com/orkestral/venom/compare/v5.0.3...v5.0.4) (2023-06-16)


### Bug Fixes

* ci dependencies ([6902d7d](https://github.com/orkestral/venom/commit/6902d7d6691927176e3cab0c4ff812d5e2750a58))
* wait For Objects webpack ([6c22540](https://github.com/orkestral/venom/commit/6c225408ceb43beef43bfa4cae64754edf5bad68))



## [5.0.1](https://github.com/orkestral/venom/compare/v5.0.0...v5.0.1) (2023-05-05)


### Bug Fixes

* eslintrc.cjs ([59ee984](https://github.com/orkestral/venom/commit/59ee98427c99cab569c394b1475f274e753955d0))
* sendListMenu for Bussines contacts ([262eaaa](https://github.com/orkestral/venom/commit/262eaaa6ba5f50e8e335a1a0af1f50d2dd703c5b))
* temporary sendLinkPreview ([059df92](https://github.com/orkestral/venom/commit/059df928b9013ae0d7c56bff56f0da673c6a9d42))


### Features

* remove useChrome ([a1850b4](https://github.com/orkestral/venom/commit/a1850b4a9dc4c1a22e593af47986eac916f7cc9f))
* removing multi device option ([4564c24](https://github.com/orkestral/venom/commit/4564c246347074bd1f625b2f6bede62d7ac89d19))
* update puppeteer ([459a2ea](https://github.com/orkestral/venom/commit/459a2eaa5746133d06314d3c67b1245b05d963f7))



# [5.0.0](https://github.com/orkestral/venom/compare/v4.3.7...v5.0.0) (2023-04-12)


### Bug Fixes

* all functions ([dbbee0e](https://github.com/orkestral/venom/commit/dbbee0ebc7f89eece8ed2953768e2ec4b6bd0e28))
* lint! ([f25bc41](https://github.com/orkestral/venom/commit/f25bc41994f72e933bd72dfc588bd972f91f1542))
* lint? ([46c6cae](https://github.com/orkestral/venom/commit/46c6cae046d503bf52dcc95f69c5a850a738c665))
* yml ([c167d83](https://github.com/orkestral/venom/commit/c167d832232e1412cc0a4f12bf8efa1b4e214545))


### Features

* checkUpdates ([c90a155](https://github.com/orkestral/venom/commit/c90a1557370121fbd8ae68cc09799ef3d13d7309))
* ECMAScript ([05b1854](https://github.com/orkestral/venom/commit/05b1854fd66d428d329cc79892887443a3bda588))
* workflows ([4ac92fd](https://github.com/orkestral/venom/commit/4ac92fdd0896764137bc8936444c194fe5c92484))



## [4.3.7](https://github.com/orkestral/venom/compare/v4.3.6...v4.3.7) (2022-12-01)



## [4.3.6](https://github.com/orkestral/venom/compare/v4.3.4...v4.3.6) (2022-11-24)



## [4.3.4](https://github.com/orkestral/venom/compare/v4.3.3...v4.3.4) (2022-11-18)



## [4.3.3](https://github.com/orkestral/venom/compare/v4.3.2...v4.3.3) (2022-11-08)



## [4.3.2](https://github.com/orkestral/venom/compare/v4.3.1...v4.3.2) (2022-11-08)



## [4.3.1](https://github.com/orkestral/venom/compare/v4.3.0...v4.3.1) (2022-11-07)


### Bug Fixes

* alterado ordem de inicialização ([f782610](https://github.com/orkestral/venom/commit/f7826106b05b361123fe6c8f724408dab68d22df))
* getAllUnreadMessages ack incorreto ([c39c3a0](https://github.com/orkestral/venom/commit/c39c3a09d8e41413512ea35f6f958b80e9cd4d30))
* loadAllEarlierMessages e retorno da loadAndGetAllMessagesInChat ([4e2a198](https://github.com/orkestral/venom/commit/4e2a198109677df4be10a82ed99cfca8d4230f10))



# [4.3.0](https://github.com/orkestral/venom/compare/v4.2.3...v4.3.0) (2022-08-15)



## [4.2.3](https://github.com/orkestral/venom/compare/v4.2.2...v4.2.3) (2022-08-08)



## [4.2.2](https://github.com/orkestral/venom/compare/v4.2.1...v4.2.2) (2022-08-08)



## [4.2.1](https://github.com/orkestral/venom/compare/v4.2.0...v4.2.1) (2022-08-08)


### Bug Fixes

* e.isLid is not a function ([88209cf](https://github.com/orkestral/venom/commit/88209cf86d706935bb5f59884097959bd32385e0))
* queryExists ([267372a](https://github.com/orkestral/venom/commit/267372a7fa6183298965644983c3978521bb0fb9))



# [4.2.0](https://github.com/orkestral/venom/compare/v4.1.0...v4.2.0) (2022-06-09)


### Bug Fixes

* **wapi:** fix sendFile function issue [#1718](https://github.com/orkestral/venom/issues/1718) ([ed89eb7](https://github.com/orkestral/venom/commit/ed89eb797b78c884898a49d23d33691ee3cb6eb2))
* Bug Fix send message with media


# [4.1.0](https://github.com/orkestral/venom/compare/v4.0.11...v4.1.0) (2022-06-02)

### Features

* Bug Fix e.isUser
* Change target TS

## [4.0.11](https://github.com/orkestral/venom/compare/v4.0.10...v4.0.11) (2022-04-27)


### Features

* addBrowserArgs ([6541669](https://github.com/orkestral/venom/commit/654166943f8d256a07fc1163c49681c16a783a65))
* statusFind ([7ee608d](https://github.com/orkestral/venom/commit/7ee608d20595146e4287635f3844db403a08b17f))



## [4.0.10](https://github.com/orkestral/venom/compare/v4.0.9...v4.0.10) (2022-04-18)


### Bug Fixes

* lint ([890d7b3](https://github.com/orkestral/venom/commit/890d7b37a8a571794d46c0c7b1a857ca48f045c0))
* module Cmd is now a class, no longer exported in the `default` key ([b21b11f](https://github.com/orkestral/venom/commit/b21b11f53261d50735782d3d73cea1d639dfaedd))
* sendLinkPreview ([d00634a](https://github.com/orkestral/venom/commit/d00634a5b44adc47517708bc22c220576b269ec8))
* sendLocation ([921e2f0](https://github.com/orkestral/venom/commit/921e2f016f55522ba49e51563a21f09dfa13ec52))
* type definition and code expect a promise from isBeta ([b7b9ccb](https://github.com/orkestral/venom/commit/b7b9ccb5156a83027ab028a71e4f0a81996d8ec3))
* **wapi:** sendMessage function throw exception on invalid object ([4332fce](https://github.com/orkestral/venom/commit/4332fce89820a287921708e361990caf4ea903fb))


### Features

* device ([21d4173](https://github.com/orkestral/venom/commit/21d4173e785cb2228dbd40854561485491926819))
* remove mandatory descrition sendList ([0098594](https://github.com/orkestral/venom/commit/0098594df2695596cd2ec30c5cf8611b848129ab))



## [4.0.9](https://github.com/orkestral/venom/compare/v4.0.8...v4.0.9) (2022-03-10)


### Bug Fixes

* add queryExists ([bdea56c](https://github.com/orkestral/venom/commit/bdea56c2b2cb7e246e2a8614eaad3fd260868bad))
* lint ([9bfbead](https://github.com/orkestral/venom/commit/9bfbead2009d617600261f804f0939bb8d7dd921))



## [4.0.8](https://github.com/orkestral/venom/compare/v4.0.7...v4.0.8) (2022-03-10)


### Bug Fixes

* checkNumber ([d0515e7](https://github.com/orkestral/venom/commit/d0515e79db9da39f881c6fc7ef8561187524cd9d))



## [4.0.7](https://github.com/orkestral/venom/compare/v4.0.6...v4.0.7) (2022-03-01)


### Bug Fixes

* Socket ([bca8283](https://github.com/orkestral/venom/commit/bca8283323fdd92274637a92ddb8c94748e21c69))
* Stream and State ([b71e032](https://github.com/orkestral/venom/commit/b71e032610d0e2935d1b5f8d0a926538b3c97ab6))


### Features

* forcing injectApi ([f5404ba](https://github.com/orkestral/venom/commit/f5404ba93ab258664b88ca46dc3702bd9e32fef6))
* typedoc ([9ca17ec](https://github.com/orkestral/venom/commit/9ca17ec34780ed1811217f1ffde70f4d2a3c2889))
* webpack sleep ([ec89f4e](https://github.com/orkestral/venom/commit/ec89f4e9114aff587153f5e170cecc5bb801e267))



## [4.0.6](https://github.com/orkestral/venom/compare/v4.0.5...v4.0.6) (2022-02-23)


### Bug Fixes

* checkNumber ([0a20479](https://github.com/orkestral/venom/commit/0a204796998d547da7a3395dcf1985a05edf0935))
* Product ([d17d3a3](https://github.com/orkestral/venom/commit/d17d3a3ee1597c44749e0cb3173f7f977302782f))
* sendSticker ([557d1ae](https://github.com/orkestral/venom/commit/557d1ae8b07597d16a77a1a3c27de24240c85685))


### Features

* typedoc ([977eb8d](https://github.com/orkestral/venom/commit/977eb8d954956b46c43149de7c6ce1ee4ab16d0b))



## [4.0.5](https://github.com/orkestral/venom/compare/v4.0.4...v4.0.5) (2022-01-15)


### Bug Fixes

* logout ([ee6b6c3](https://github.com/orkestral/venom/commit/ee6b6c33ed2a1bccda588af5f08b156139c9dd41))
* MessageObj ([deae0e9](https://github.com/orkestral/venom/commit/deae0e9b15b5385572ec32c56d5252f54f3b5ad8))


### Features

* addProxy ([22f9f01](https://github.com/orkestral/venom/commit/22f9f01866dc90d0d682500f97116e4e148efe5f))
* readme ([ab0ff66](https://github.com/orkestral/venom/commit/ab0ff6667b7c127a2e39246c822e65c006118235))



## [4.0.4](https://github.com/orkestral/venom/compare/v4.0.3...v4.0.4) (2021-12-19)


### Bug Fixes

* changed _quotedMsgObj to quotedMsg ([288b65a](https://github.com/orkestral/venom/commit/288b65a6064ef4d0f047004b7fe89e7348244213))
* logout ([8f08abb](https://github.com/orkestral/venom/commit/8f08abbc2ed9414c9c7d71862d0bc0666eab52eb))
* sendContactVcard ([d4a4700](https://github.com/orkestral/venom/commit/d4a4700557f1bbf632ba5f70a86477f146462fc6))
* sendLinkPreview ([de3b0a7](https://github.com/orkestral/venom/commit/de3b0a71fc2a0ac0786c935c7599b8881a993430))
* setProfilePic ([ddc8184](https://github.com/orkestral/venom/commit/ddc81844c5fb76be3ece25d715bd032ed07fa36e))
* sticker ([6506d40](https://github.com/orkestral/venom/commit/6506d408863f740afc03e5325fb79345c7803960))
* Store.State ([54dcd8e](https://github.com/orkestral/venom/commit/54dcd8e14c1895d7a45f8fd257118ddce03eccc1))


### Features

* checkNumber ([a2708e5](https://github.com/orkestral/venom/commit/a2708e5c559366a9395bff7b7ce76d9d3f0fc850))
* checkNumberStatus ([15e8224](https://github.com/orkestral/venom/commit/15e822496b96f12187446db615ed9502b0b2bbaf))
* multidevice and chromiumVersion ([4f8fe94](https://github.com/orkestral/venom/commit/4f8fe94b9ade9e6e291c5864ddbb97a9347a2531))
* SerializeMeObj ([5d72a61](https://github.com/orkestral/venom/commit/5d72a61c4782009f19dd841ac5191a0e3a001721))
* test typedoc ([5e4007f](https://github.com/orkestral/venom/commit/5e4007fd338c306d51d07e5b7c9028ef5610ff1c))
* typedoc ([bb58502](https://github.com/orkestral/venom/commit/bb58502f747a023eb884fabdf3c1a6a54c0484e9))
* update puppeteer ([b8ca6b6](https://github.com/orkestral/venom/commit/b8ca6b633a77c4e14535083586a46a58bfa6d777))



## [4.0.3](https://github.com/orkestral/venom/compare/v4.0.2...v4.0.3) (2021-11-24)


### Bug Fixes

* get host ([0e055ae](https://github.com/orkestral/venom/commit/0e055aeda0c55279ca9278a8b618be44b672282a))
* get me and State ([c1fac1b](https://github.com/orkestral/venom/commit/c1fac1b000f7277a680bcebfc04aed457e718305))
* getNumberProfile ([cd62707](https://github.com/orkestral/venom/commit/cd62707d2bfe55c45e164fba1e1852198f2b7200))


### Features

* readme ([6d48b4e](https://github.com/orkestral/venom/commit/6d48b4e8f522acf4ce22682d7db15b61fc6059e2))



## [4.0.2](https://github.com/orkestral/venom/compare/v4.0.1...v4.0.2) (2021-11-17)


### Bug Fixes

* checkNumber ([42e631e](https://github.com/orkestral/venom/commit/42e631ebcbcd5baccedec31f37512773c77b186e))
* eslint ([7a91719](https://github.com/orkestral/venom/commit/7a91719ec6b0a414c33cc176063778fe0597bab8))
* eslint ([5779d62](https://github.com/orkestral/venom/commit/5779d62a44fe90be72417a3037e14a7d24ac242a))
* send ([f1a6289](https://github.com/orkestral/venom/commit/f1a6289af68449ea2d0fca476dab8c2cb12057b0))
* Send with more digits ([14e2fea](https://github.com/orkestral/venom/commit/14e2fea6fe6b066ce6a793b7894c1d8955b15792))
* webpack ([dc6e5db](https://github.com/orkestral/venom/commit/dc6e5db8380a0bd0b2f56d4f1e4a9c88d6f7ab66))



## [4.0.1](https://github.com/orkestral/venom/compare/v4.0.0...v4.0.1) (2021-11-08)


### Bug Fixes

* corretion for not multidevice version ([782fea4](https://github.com/orkestral/venom/commit/782fea4b1c664e4d96c664545eba2cdfe700c9c1))
* corretion for version not multidevice ([e4d9c37](https://github.com/orkestral/venom/commit/e4d9c37cc5ee4b42db575a9145180f4134c49245))
* eslint ([77a670b](https://github.com/orkestral/venom/commit/77a670b123fad100644705394c34750530a3d192))
* readme param MD ([96c3163](https://github.com/orkestral/venom/commit/96c31630485c789a59d3d5e93314280b8f77ebcc))



# [4.0.0](https://github.com/orkestral/venom/compare/v3.1.12...v4.0.0) (2021-11-08)



## [3.1.12](https://github.com/orkestral/venom/compare/v3.1.11...v3.1.12) (2021-10-28)


### Bug Fixes

* Error connect ([e15a018](https://github.com/orkestral/venom/commit/e15a018486a36edc510b57ca5206f38c639bf053))



## [3.1.11](https://github.com/orkestral/venom/compare/v3.1.10...v3.1.11) (2021-10-24)



## [3.1.10](https://github.com/orkestral/venom/compare/v3.1.9...v3.1.10) (2021-10-24)


### Bug Fixes

* body ([242df0b](https://github.com/orkestral/venom/commit/242df0be835708c772758fe8edae28cd74ee04e9))


### Features

* Add text option to the message with thumb method ([edddc3c](https://github.com/orkestral/venom/commit/edddc3cd85995c9c8ad734e3b0be2a422eb1ae17))



## [3.1.9](https://github.com/orkestral/venom/compare/v3.1.8...v3.1.9) (2021-10-23)


### Bug Fixes

* deleteMessage ([1ae12b1](https://github.com/orkestral/venom/commit/1ae12b134aec9620c83859db94389c778a6886d3))
* get profile ([642aedc](https://github.com/orkestral/venom/commit/642aedcf3ccc0517df1e12d0f27cbe8eb1a09121))
* onAnyMessage ([ba96f80](https://github.com/orkestral/venom/commit/ba96f80790c3eb23d115ed3277f0bf1bcdcd3d4a))
* send group ([58b46d6](https://github.com/orkestral/venom/commit/58b46d60aa8f058cdee4484e5e1a7a030bce464f))



## [3.1.8](https://github.com/orkestral/venom/compare/v3.1.7...v3.1.8) (2021-10-06)


### Bug Fixes

* lint ([7a0f465](https://github.com/orkestral/venom/commit/7a0f465094b037e530b4628b01657d45bb90bdbf))
* lint ([83a8df9](https://github.com/orkestral/venom/commit/83a8df985fa79c03b9cc6bc9ff6c5586cd53ec58))
* lint -  fix-reply ([423c844](https://github.com/orkestral/venom/commit/423c84440dea92f631ec3079dd21a8970773a3b4))


### Features

*  checkNumber - state ([d617640](https://github.com/orkestral/venom/commit/d6176405aead74a2d734f0a7f7a21d17e49bb7a5))
* get profile connection ([b57455e](https://github.com/orkestral/venom/commit/b57455e2b80882f4735571d5ff69a89c80fac8b3))
* getHostDevice ([b342e23](https://github.com/orkestral/venom/commit/b342e2384f67aa0266a97ab137967efa11de582c))
* sendCheckType ([dd10fea](https://github.com/orkestral/venom/commit/dd10feab37247b351ce5fcf6ee14335eceb9df97))



## [3.1.7](https://github.com/orkestral/venom/compare/v3.1.6...v3.1.7) (2021-09-24)


### Bug Fixes

* Chat find ([00918f0](https://github.com/orkestral/venom/commit/00918f0c89e0226555df4eeefb9e16f1b36b3f70))
* lint ([35a1c4a](https://github.com/orkestral/venom/commit/35a1c4ad38b1326cd1951689d4a22d530017ce0a))
* statusFind ([80b5a77](https://github.com/orkestral/venom/commit/80b5a774d9f77002b405555071d162076af91849))


### Features

* checkChat ([ca4c1ba](https://github.com/orkestral/venom/commit/ca4c1bab17f15eeb33bca22f84867607352ff03c))
* checkNumberStatus - Promise ([457eccb](https://github.com/orkestral/venom/commit/457eccb352f55044140ceba812af4938ce8d653a))
* lint check-send-exist ([0390f9a](https://github.com/orkestral/venom/commit/0390f9a13e5105dc610207ec7c0d14dbaedcc01a))
* select chat group ([170ab45](https://github.com/orkestral/venom/commit/170ab454c2f4bda1c7b99bc7c5b117ce4ca44281))



## [3.1.6](https://github.com/orkestral/venom/compare/v3.1.4...v3.1.6) (2021-09-16)


### Bug Fixes

* chat not exist ([18281d3](https://github.com/orkestral/venom/commit/18281d37bd26197b0df7621cad0512fc845fc3a4))
* check lint ([6f24f22](https://github.com/orkestral/venom/commit/6f24f226ef997ca2c0222a7155b66296e34eeb1f))
* check lint - Process completed ([535cd99](https://github.com/orkestral/venom/commit/535cd997ae364d0af1660c7344f4fb43849c0cc5))
* close browser ([c791d43](https://github.com/orkestral/venom/commit/c791d4348bae5d383888b93ce2a6dcc57e1e8a12))
* codeql ([1ab1b55](https://github.com/orkestral/venom/commit/1ab1b55d149344bd252457171aa06a98ef7eadf6))
* codeql ([fe7400d](https://github.com/orkestral/venom/commit/fe7400d8dc036c01edb296ca2206582ee321764c))
* getGroupInviteLink ([bfbbde1](https://github.com/orkestral/venom/commit/bfbbde152e3ed3cdeb03d9366ce65044f929bfb4))
* lint ([81bdabb](https://github.com/orkestral/venom/commit/81bdabb942bc1d83eadcc80d8f8e43e424cc9a67))
* lint code concert ([a340eaa](https://github.com/orkestral/venom/commit/a340eaa69efa6f458cd0cc841b3759c526efac9d))
* lint code concert ([ab1e82d](https://github.com/orkestral/venom/commit/ab1e82d2c3ff91c07faf373a13e90b521ebb78fd))
* lint code concert ([9876b1b](https://github.com/orkestral/venom/commit/9876b1b0bf33a342491416d2a9475bbff28076ed))
* new version ([daeb1cc](https://github.com/orkestral/venom/commit/daeb1cc0fce81bfe0d592c48b11d5333be56d266))
* not  Organizations ([4aeef14](https://github.com/orkestral/venom/commit/4aeef1437d667ae5ebea5eaf0003d6ee48e87e42))
* queryGroupInviteInfo ([ba55f4a](https://github.com/orkestral/venom/commit/ba55f4a3a86e7b24169b788e3c0b29954a29ddc1))
* remove HEAD^2 ([e3d318e](https://github.com/orkestral/venom/commit/e3d318e4be64db80df21bde6e3b1b6d557d3fa35))
* send-loacation ([261920b](https://github.com/orkestral/venom/commit/261920ba41b495c424efb180253c7f4098cbe581))
* sendButtons - (reformulated) ([28d7898](https://github.com/orkestral/venom/commit/28d7898ff5330b0b59cb4f393c19abf848fcc848))
* sendContactVcard ([e9ecd0c](https://github.com/orkestral/venom/commit/e9ecd0c1c6fad27783d8658c402dfe0e5c377266))
* sendContactVcardList ([fb0417f](https://github.com/orkestral/venom/commit/fb0417f8c7076ae3680e01fe47e440b1fa6fac41))
* sendExist and checkNumberStatus ([8f3c954](https://github.com/orkestral/venom/commit/8f3c954a944b22371d0e29537b135874d6783347))
* typedoc-default-themes ([9d75b0e](https://github.com/orkestral/venom/commit/9d75b0e739a48dc76ed922bf63eaa5c0aa84b6e8))
* webpack config ([da8c190](https://github.com/orkestral/venom/commit/da8c190df83ae0dce2c3d2f70c5796afb3ebbd60))


### Features

* Change group description ([3278bff](https://github.com/orkestral/venom/commit/3278bff7f394c5c2b47a26c20688878192f34a48))
* lint fix - package ([226b490](https://github.com/orkestral/venom/commit/226b4902b13d1783ac56c6446b6b2d81265a3895))
* mew ([06915eb](https://github.com/orkestral/venom/commit/06915eba4b8ba85c2d5d28c24be77c4d7c4ba6c5))
* new functions ([6721144](https://github.com/orkestral/venom/commit/6721144b770f55a3195326c7f301e56cb062ac3b))
* project ([8fc9aaa](https://github.com/orkestral/venom/commit/8fc9aaa96611ded0cbc927392c83d17828298d77))
* sendListMenu ([fd8792d](https://github.com/orkestral/venom/commit/fd8792dcc2f82cf981f151a1ffb2592c94e1af47))
* typescript formatter ([6e79c52](https://github.com/orkestral/venom/commit/6e79c5290faf166ec149983db7a3fe7d9cf8aa00))



## [3.1.5](https://github.com/orkestral/venom/compare/v3.1.4...v3.1.5) (2021-09-16)


### Bug Fixes

* chat not exist ([18281d3](https://github.com/orkestral/venom/commit/18281d37bd26197b0df7621cad0512fc845fc3a4))
* check lint ([6f24f22](https://github.com/orkestral/venom/commit/6f24f226ef997ca2c0222a7155b66296e34eeb1f))
* check lint - Process completed ([535cd99](https://github.com/orkestral/venom/commit/535cd997ae364d0af1660c7344f4fb43849c0cc5))
* close browser ([c791d43](https://github.com/orkestral/venom/commit/c791d4348bae5d383888b93ce2a6dcc57e1e8a12))
* codeql ([1ab1b55](https://github.com/orkestral/venom/commit/1ab1b55d149344bd252457171aa06a98ef7eadf6))
* codeql ([fe7400d](https://github.com/orkestral/venom/commit/fe7400d8dc036c01edb296ca2206582ee321764c))
* getGroupInviteLink ([bfbbde1](https://github.com/orkestral/venom/commit/bfbbde152e3ed3cdeb03d9366ce65044f929bfb4))
* lint ([81bdabb](https://github.com/orkestral/venom/commit/81bdabb942bc1d83eadcc80d8f8e43e424cc9a67))
* lint code concert ([a340eaa](https://github.com/orkestral/venom/commit/a340eaa69efa6f458cd0cc841b3759c526efac9d))
* lint code concert ([ab1e82d](https://github.com/orkestral/venom/commit/ab1e82d2c3ff91c07faf373a13e90b521ebb78fd))
* lint code concert ([9876b1b](https://github.com/orkestral/venom/commit/9876b1b0bf33a342491416d2a9475bbff28076ed))
* new version ([daeb1cc](https://github.com/orkestral/venom/commit/daeb1cc0fce81bfe0d592c48b11d5333be56d266))
* not  Organizations ([4aeef14](https://github.com/orkestral/venom/commit/4aeef1437d667ae5ebea5eaf0003d6ee48e87e42))
* queryGroupInviteInfo ([ba55f4a](https://github.com/orkestral/venom/commit/ba55f4a3a86e7b24169b788e3c0b29954a29ddc1))
* remove HEAD^2 ([e3d318e](https://github.com/orkestral/venom/commit/e3d318e4be64db80df21bde6e3b1b6d557d3fa35))
* send-loacation ([261920b](https://github.com/orkestral/venom/commit/261920ba41b495c424efb180253c7f4098cbe581))
* sendButtons - (reformulated) ([28d7898](https://github.com/orkestral/venom/commit/28d7898ff5330b0b59cb4f393c19abf848fcc848))
* sendContactVcard ([e9ecd0c](https://github.com/orkestral/venom/commit/e9ecd0c1c6fad27783d8658c402dfe0e5c377266))
* sendContactVcardList ([fb0417f](https://github.com/orkestral/venom/commit/fb0417f8c7076ae3680e01fe47e440b1fa6fac41))
* sendExist and checkNumberStatus ([8f3c954](https://github.com/orkestral/venom/commit/8f3c954a944b22371d0e29537b135874d6783347))
* typedoc-default-themes ([9d75b0e](https://github.com/orkestral/venom/commit/9d75b0e739a48dc76ed922bf63eaa5c0aa84b6e8))
* webpack config ([da8c190](https://github.com/orkestral/venom/commit/da8c190df83ae0dce2c3d2f70c5796afb3ebbd60))


### Features

* Change group description ([3278bff](https://github.com/orkestral/venom/commit/3278bff7f394c5c2b47a26c20688878192f34a48))
* lint fix - package ([226b490](https://github.com/orkestral/venom/commit/226b4902b13d1783ac56c6446b6b2d81265a3895))
* mew ([06915eb](https://github.com/orkestral/venom/commit/06915eba4b8ba85c2d5d28c24be77c4d7c4ba6c5))
* new functions ([6721144](https://github.com/orkestral/venom/commit/6721144b770f55a3195326c7f301e56cb062ac3b))
* project ([8fc9aaa](https://github.com/orkestral/venom/commit/8fc9aaa96611ded0cbc927392c83d17828298d77))
* sendListMenu ([fd8792d](https://github.com/orkestral/venom/commit/fd8792dcc2f82cf981f151a1ffb2592c94e1af47))
* typescript formatter ([6e79c52](https://github.com/orkestral/venom/commit/6e79c5290faf166ec149983db7a3fe7d9cf8aa00))



## [3.1.4](https://github.com/orkestral/venom/compare/v3.1.3...v3.1.4) (2021-09-02)

## [3.1.3](https://github.com/orkestral/venom/compare/v3.1.2...v3.1.3) (2021-09-02)

### Features

- api ([72b9648](https://github.com/orkestral/venom/commit/72b9648ef7638c3f769b0210f76828f1612d3387))

## [3.1.2](https://github.com/orkestral/venom/compare/v3.1.1...v3.1.2) (2021-08-18)

## [3.1.1](https://github.com/orkestral/venom/compare/v3.1.0...v3.1.1) (2021-08-17)

# [3.1.0](https://github.com/orkestral/venom/compare/v3.0.21...v3.1.0) (2021-08-17)

### Bug Fixes

- lint ([81568ed](https://github.com/orkestral/venom/commit/81568ed57ea2b35cd28ae9de2225a51b245a6ca1))
- lint dependencies ([e86e6b2](https://github.com/orkestral/venom/commit/e86e6b275f38cd27af1b2194ddeb42b9fb0054a4))

## [3.0.21](https://github.com/orkestral/venom/compare/v3.0.20...v3.0.21) (2021-07-09)

### Bug Fixes

- chat not exist ([082a80a](https://github.com/orkestral/venom/commit/082a80aecb493972b198186fa802c328573a6932))
- Parcial fix webpack ([4dce474](https://github.com/orkestral/venom/commit/4dce4748308c7f5a0b4d2dfed88ddf856382f082))

## [3.0.20](https://github.com/orkestral/venom/compare/v3.0.19...v3.0.20) (2021-06-24)

### Bug Fixes

- send-loacation ([92446ea](https://github.com/orkestral/venom/commit/92446ea447deafe88b363f5e6294b9ee95274512))
- sendContactVcardList ([e6518cc](https://github.com/orkestral/venom/commit/e6518cc7e98031a8a62fe0606b08b8a26245dd70))

## [3.0.19](https://github.com/orkestral/venom/compare/v3.0.18...v3.0.19) (2021-06-22)

### Bug Fixes

- getGroupInviteLink (await) ([7040168](https://github.com/orkestral/venom/commit/70401688b0104af74610ac5f40d5ef123a5888b1))
- queryGroupInviteInfo ([48e1086](https://github.com/orkestral/venom/commit/48e10863eb2fecf4b405ade38c4bb1a00e2bb3ed))
- revokeGroupInviteLink (export) ([07947c3](https://github.com/orkestral/venom/commit/07947c326fc42c457b21cdbd74cab8f76726bc1b))
- sendContactVcard ([e47ff16](https://github.com/orkestral/venom/commit/e47ff16c44e0b0025a653b347df667d8bcabca82))
- typescript ([5e345a9](https://github.com/orkestral/venom/commit/5e345a941ece883d9875578035da5aabd9faadee))

### Features

- Change group description ([c635644](https://github.com/orkestral/venom/commit/c6356445b12eeae77306efc000202f6f353e0839))
- Support ([190be33](https://github.com/orkestral/venom/commit/190be33cecca2b7a528c991bd879c5b6269b7faf))

## [3.0.18](https://github.com/orkestral/venom/compare/v3.0.17...v3.0.18) (2021-06-04)

### Bug Fixes

- Button Reload ([ae20c9f](https://github.com/orkestral/venom/commit/ae20c9f5b13a113e56e3201b9e830bd7775a1c20))
- deleteMessages ([da56acb](https://github.com/orkestral/venom/commit/da56acb08e3b966176059418a8eeb24034997a26))
- queryExist ([1e5e2a6](https://github.com/orkestral/venom/commit/1e5e2a67f5f4b92537946da2aeb58f208dce59f6))
- RevokeGroup and getGroupInviteLink ([55e1310](https://github.com/orkestral/venom/commit/55e1310ab1cc3b202b0b30222c27d925bdae1913))
- statusFind ([ef9dc78](https://github.com/orkestral/venom/commit/ef9dc78af8592ac75089ad43d0098020218990af))
- WidFactory ([c2a51e7](https://github.com/orkestral/venom/commit/c2a51e7aa0c00dc499bb5aeeba318d0c70e73232))

## [3.0.16](https://github.com/orkestral/venom/compare/v3.0.15...v3.0.16) (2021-05-04)

### Bug Fixes

- browserInstance callback ([8132418](https://github.com/orkestral/venom/commit/8132418c208e7efd2006cd76be9028e8a2031aaa))
- Close error multi sessions ([68505e0](https://github.com/orkestral/venom/commit/68505e0af2c152531ebb8989129564359e360ae9))
- delete file ([dca4152](https://github.com/orkestral/venom/commit/dca415282fac3a8fa35bff067d45e2c2a8b1c921))
- directory permissions ([08724ea](https://github.com/orkestral/venom/commit/08724ea49228a3ad37ee4742afa988a0bc2924d1))
- lint ([f922428](https://github.com/orkestral/venom/commit/f922428ebc58432c71ec8fb7800fe7c69ceca309))
- lint ([d89aa5a](https://github.com/orkestral/venom/commit/d89aa5a0c29e0cfd9d5c2e9585b205f860453d7f))

### Features

- add args defaultOptions ([cc75244](https://github.com/orkestral/venom/commit/cc752442d03afd67cee02b7d50c6796081e98df9))

## [3.0.15](https://github.com/orkestral/venom/compare/v3.0.14...v3.0.15) (2021-04-25)

### Bug Fixes

- id double ([bf5181f](https://github.com/orkestral/venom/commit/bf5181f611701465022449c735ec2c092e07f2c5))

### Features

- files ([de02822](https://github.com/orkestral/venom/commit/de02822582716c0a7438e8a267abe3110e66864c))

## [3.0.14](https://github.com/orkestral/venom/compare/v3.0.13...v3.0.14) (2021-04-11)

### Bug Fixes

- close and inject ([20e2f74](https://github.com/orkestral/venom/commit/20e2f74f5438e9305a27420dbd56ad16fbdda953))
- error checkIdMessag not function ([31c3041](https://github.com/orkestral/venom/commit/31c304104fe1707b2dfbae72827690a40b7a5a70))
- lint ([e39d709](https://github.com/orkestral/venom/commit/e39d7098a888ee8ccfe076eb50a789cf0da4e719))
- object returns correct deleteMessages ([e99edfd](https://github.com/orkestral/venom/commit/e99edfda9083e21e1c77f7bf12aa985054c90f56))
- remove delay checkIdMessage ([e63ca37](https://github.com/orkestral/venom/commit/e63ca37f6e08e7b6cf2fbe99c6e14a1ff6f08acf))
- statusFind deviceNotConnected ([46d39f4](https://github.com/orkestral/venom/commit/46d39f4c0d3395503f102cbd9992b6ae17def396))
- update Agent ([60458e5](https://github.com/orkestral/venom/commit/60458e559d999eebe4ab4794d91fbaf9e3f01c06))

### Features

- add Args ([01f4a53](https://github.com/orkestral/venom/commit/01f4a53223ec51ab1b473540566211f0d6c0aaa2))
- Check npmjs page ([2591f52](https://github.com/orkestral/venom/commit/2591f52c47821a2747f77035c69707e80e7f0557))
- check site access ([b822fce](https://github.com/orkestral/venom/commit/b822fce7f7150786376e2b64af20df51408b625c))

## [3.0.13](https://github.com/orkestral/venom/compare/v3.0.12...v3.0.13) (2021-04-04)

### Features

- addOnStreamChange ([7ab8d10](https://github.com/orkestral/venom/commit/7ab8d1067b058889c3d38a4fa1b58fe2d6409efb))
- checkFileJson ([9602b78](https://github.com/orkestral/venom/commit/9602b7881bbe3f268659787829307412be504307))
- injectParasite intro ([9f7fe6e](https://github.com/orkestral/venom/commit/9f7fe6e81597bf5102a241de052b1da5e00fc320))
- listener onStreamChange ([5a5a819](https://github.com/orkestral/venom/commit/5a5a8192e57e7b81aadd87cb808d6af01f759525))
- new return statusFind ([d8b6cee](https://github.com/orkestral/venom/commit/d8b6ceea1817c1c16127300123a3b8c59e86bc2b))
- SocketStream ([0683a20](https://github.com/orkestral/venom/commit/0683a20c2661d95b03bbf791733dbcd2447873f5))
- WAPI logout ([b309586](https://github.com/orkestral/venom/commit/b30958609520bd049558a2dc62c7e0107c35217f))

## [3.0.12](https://github.com/orkestral/venom/compare/v3.0.11...v3.0.12) (2021-03-31)

### Bug Fixes

- error undefined ([eb2b9af](https://github.com/orkestral/venom/commit/eb2b9afa0a867a48dda30caae8120b52dbe194fb))
- token ([b75c51a](https://github.com/orkestral/venom/commit/b75c51ad6f8d0f40a2cb31e11b45bfab308bb7c9))

### Features

- add BrowserInstance callback on create function ([fe96807](https://github.com/orkestral/venom/commit/fe96807b2d69e25032839f05a2814fd892338c0f))
- check deleteMessage ([c525c71](https://github.com/orkestral/venom/commit/c525c71de5cacff1bcf4974c4243a1b1e86d4713))
- checkIdMessage and returnReply function ([e3a307d](https://github.com/orkestral/venom/commit/e3a307d451a62719aa20dd139761b97f0374b71f))
- ERROR 429 ([074dc28](https://github.com/orkestral/venom/commit/074dc281838a5ddb41d84740a7b8fae52bd93370))
- refactored the reply function ([d74f96b](https://github.com/orkestral/venom/commit/d74f96b4b0654d29503d6edaa6bca507db50ebf0))
- retrunReply ([6ae385e](https://github.com/orkestral/venom/commit/6ae385e63d44d82f63ca512937d212d42cf399ac))

## [3.0.11](https://github.com/orkestral/venom/compare/v3.0.10...v3.0.11) (2021-03-17)

### Bug Fixes

- lint ([39a1b61](https://github.com/orkestral/venom/commit/39a1b611d6604511c8d4703c1e05672d8bceff0c))
- Return the correct number! ([7ecc5b0](https://github.com/orkestral/venom/commit/7ecc5b0d307edb3107a3bc1b129dd3b152a4d909))
- send group and transmission ([80c4cd2](https://github.com/orkestral/venom/commit/80c4cd2abb555130dc7aec6e848ca03181486af6))

### Features

- check values sendImageFromBase64 - ts ([f44dc08](https://github.com/orkestral/venom/commit/f44dc083bdf6cdc11f45430ed4e0463b98841a01))

## [3.0.10](https://github.com/orkestral/venom/compare/v3.0.9...v3.0.10) (2021-03-15)

### Bug Fixes

- Error when checking number! ([39d0f17](https://github.com/orkestral/venom/commit/39d0f1711a98b949ef70a79d8c682a0d2c81f39a))

### Features

- check values sendLinkPreview - ts ([55e7ac6](https://github.com/orkestral/venom/commit/55e7ac699be51271cb3c61197032d37a6e6bab47))
- check values sendText - ts ([dd3eb0c](https://github.com/orkestral/venom/commit/dd3eb0c40071100871345f7b325e0e021d8c5521))
- Create checkValues ([319230f](https://github.com/orkestral/venom/commit/319230fd248ebbc957a0309eed1649c577366065))

## [3.0.9](https://github.com/orkestral/venom/compare/v3.0.8...v3.0.9) (2021-03-10)

### Bug Fixes

- Cannot read property '\_\_x_id' of undefined ([bc83c78](https://github.com/orkestral/venom/commit/bc83c78e9b5401c1e5eebbc6749ef78960e5c493))
- forwardMessages ([0c0e14d](https://github.com/orkestral/venom/commit/0c0e14d8f3d329b0ee4a600041f9b07dd5cfbfe3))
- return parameters sendVoice ([a7b6813](https://github.com/orkestral/venom/commit/a7b681311e7fe52df69e650c285c1e0c53873116))
- sendLinkPreview ([d5d1145](https://github.com/orkestral/venom/commit/d5d11458bda0256f9d090d7acc012c381a90a368))

### Features

- sendVoiceBase64 ([677c36e](https://github.com/orkestral/venom/commit/677c36e3d74e7ba80ad392c71c9d29246e4f9324))

## [3.0.8](https://github.com/orkestral/venom/compare/v3.0.7...v3.0.8) (2021-03-02)

### Bug Fixes

- qrcode ([691c3af](https://github.com/orkestral/venom/commit/691c3af5def47cbd0dc9cc3ff3f504c38fe6f789))
- sendContactVcard ([448b686](https://github.com/orkestral/venom/commit/448b686384cc212a0d153165be31156de11ae568))

### Features

- return sendLinkPreview ([11f3c34](https://github.com/orkestral/venom/commit/11f3c34205dcdf8a4267d0e963d651a9f8170913))

## [3.0.7](https://github.com/orkestral/venom/compare/v3.0.6...v3.0.7) (2021-03-01)

### Bug Fixes

- spinnies ([e10be38](https://github.com/orkestral/venom/commit/e10be389b3f1b578446f0d07d2ee050a721f4600))
- Undefined or null to object sendMessage ([1b34977](https://github.com/orkestral/venom/commit/1b3497789c3853c2254a85ce2f1c4c483e2ba54c))
- wapi ([003a5b2](https://github.com/orkestral/venom/commit/003a5b2a85064b000081a0ac8f2e9534933de476))

### Features

- Error results sendExist () ([488a5e1](https://github.com/orkestral/venom/commit/488a5e193b4069a47e1658e071163415f03c3924))

## [3.0.6](https://github.com/orkestral/venom/compare/v3.0.5...v3.0.6) (2021-02-28)

### Bug Fixes

- clearChatMessages ([4c10248](https://github.com/orkestral/venom/commit/4c102488fdd9a3e85ca93ef7f9b04baeaaa973ab))
- forwardMessages ([3570b9e](https://github.com/orkestral/venom/commit/3570b9ebf0edd8ed404fb96ac74c7ae65d2a8b08))
- forwardMessages return ([4b3511b](https://github.com/orkestral/venom/commit/4b3511bf4dfba7b15d38424cbc6de58307662259))
- getAllChatsGroups ([06e641c](https://github.com/orkestral/venom/commit/06e641ca37160f6d2c1a7816af38caa27f5b7394))
- getAllContacts ([9cd653e](https://github.com/orkestral/venom/commit/9cd653e01db2fad54201c0f1c611749d23286b99))
- lint ([29aa079](https://github.com/orkestral/venom/commit/29aa07959ad32402366f97e4867532d057304f3c))
- sendText ([dbea75d](https://github.com/orkestral/venom/commit/dbea75de8a9a7c1f4baadc12bf8caa57e05ae48c))

### Features

- getAllChatsNewMsg ([b703462](https://github.com/orkestral/venom/commit/b703462225e680707d1a976c80c250af33510b04))
- getAllChatsTransmission ([6461f1f](https://github.com/orkestral/venom/commit/6461f1fff18b765a53f36e4d6e16c57c886974ce))
- getChatGroupNewMsg ([04f2200](https://github.com/orkestral/venom/commit/04f2200af27fb1f0fab4487b6b017c6937b9bc3d))
- return object forwardMessages and sendText ([ee40497](https://github.com/orkestral/venom/commit/ee404970ecad12f704e1b21363c87f84292b69b8))

## [3.0.5](https://github.com/orkestral/venom/compare/3.0.4...3.0.5) (2021-02-27)

### Bug Fixes

- getAllGroups return ([7673f1e](https://github.com/orkestral/venom/commit/7673f1e7ed4c11e6622fd53c2bbce7bf55c08d7c))

## [3.0.1](https://github.com/orkestral/venom/compare/v2.2.6...v3.0.1) (2021-02-26)

### Bug Fixes

- sendText, onMessage, onck ([e04586f](https://github.com/orkestral/venom/commit/e04586f7f43d5d3dd5426709a1469eb2c41398a4))

# [3.0.0](https://github.com/orkestral/venom/compare/v2.2.6...v3.0.0) (2021-02-25)

### Bug Fixes

### Fix Whatsapp update

### New Initializer

### Add SendVoice

### Speed Improvements

### Memory loss improvements

### Resource Balancing

## [2.2.6](https://github.com/orkestral/venom/compare/v2.2.5...v2.2.6) (2021-02-21)

### Bug Fixes

- lint ([538306f](https://github.com/orkestral/venom/commit/538306fd9565c90fe2670a087cf92fa3f72c76ec))

## [2.2.5](https://github.com/orkestral/venom/compare/v2.2.4...v2.2.5) (2021-02-21)

### Bug Fixes

- file upload functions ([e9cee11](https://github.com/orkestral/venom/commit/e9cee118fac64f25d133e3f62ca0e9ee2451c1b7))
- function sticker ([c1e40b2](https://github.com/orkestral/venom/commit/c1e40b225f40d22302551d7f673f3818c687ad85))
- getchats ([c4fade7](https://github.com/orkestral/venom/commit/c4fade7a8b95537a1921c091475465cef4ba519e))
- incorrect class object -onAck ([1c18017](https://github.com/orkestral/venom/commit/1c18017c9b72e88b3e509b1fec5814929bee58fa))
- Logged out correctly - logout() ([64daabc](https://github.com/orkestral/venom/commit/64daabc560c7e9d6254936ec023a0130a3bea221))
- Multiple equal returns in the onack function ([3628734](https://github.com/orkestral/venom/commit/36287341dae4c43468063e19e9bd8c3800852d4f))
- onMessage return sender and contact ([8902a53](https://github.com/orkestral/venom/commit/8902a53f7357661eec3ba4e0ce4caf43af53a6f4))
- onStreamChange ([44eab67](https://github.com/orkestral/venom/commit/44eab67d22ee988bb589be2984a2a951a3caa280))

## [2.2.4](https://github.com/orkestral/venom/compare/v2.2.3...v2.2.4) (2021-02-13)

### Bug Fixes

- client url undefined ([6d1818c](https://github.com/orkestral/venom/commit/6d1818c26b65d9a322372ecc28f9c67d056f2bd6))
- client url undefined ([cc288b9](https://github.com/orkestral/venom/commit/cc288b942430a876ffa889c523feeecaeeea6c57))
- Fixed contact thumbmail (close [#523](https://github.com/orkestral/venom/issues/523)) ([2e6e54f](https://github.com/orkestral/venom/commit/2e6e54f759790dc8ea24e5a4ff23d7f1fd9bde03))
- Fixed sendLocation error: 'includes' of undefined ([cce314a](https://github.com/orkestral/venom/commit/cce314ab87f8c42e9fc8154f9bedce0f100012ae))
- issues [#585](https://github.com/orkestral/venom/issues/585) ([1794097](https://github.com/orkestral/venom/commit/1794097ac40e8d56f2ee5271eaad4083272b5ada))

### Features

- Added onStreamChange method ([1236dbb](https://github.com/orkestral/venom/commit/1236dbb61395b404229d64915aa8edfb3330f177))
- callback decryptFile interface ([8e6c0db](https://github.com/orkestral/venom/commit/8e6c0db86d5f8030d5d62920e8e868164ca33748))
- Get list chats ([acf172d](https://github.com/orkestral/venom/commit/acf172ddc749ca6529f8cb32de29bb4594d802e0))
- Get new messages chat ([674b1c8](https://github.com/orkestral/venom/commit/674b1c83cd798c0e7c4c2cc9795f478573ae173b))
- Picthumb ([3d1a42b](https://github.com/orkestral/venom/commit/3d1a42bc7811b04285b4fc1e4bb4ebd3026c68d3))
- Send Status ([66af91d](https://github.com/orkestral/venom/commit/66af91db3452b93580336d8117bcd30a839bdb81))
- Send Status: Text, Video, img ([f310e77](https://github.com/orkestral/venom/commit/f310e77f16ebd65a4fd739f2c91c2425df673586))

## [2.2.3](https://github.com/orkestral/venom/compare/v2.2.2...v2.2.3) (2020-12-30)

### Bug Fixes

- Fixed downloadMedia for videos on Chromium (close [#456](https://github.com/orkestral/venom/issues/456)) ([8834957](https://github.com/orkestral/venom/commit/8834957b7629976a81a7782f6a0575545ece7c52))

### Features

- append optional 'stale' property to ContactStatus (close [#461](https://github.com/orkestral/venom/issues/461)) ([56cbb68](https://github.com/orkestral/venom/commit/56cbb685b15d0f4d731cd7d4d40faa070585f0fa))

## [2.2.2](https://github.com/orkestral/venom/compare/v2.2.1...v2.2.2) (2020-12-17)

### Bug Fixes

- Fixed browser error output ([b7da3a5](https://github.com/orkestral/venom/commit/b7da3a5ecaf9ea62870f3c6eb18711c359308f06))
- Fixed set profile picture (fix [#359](https://github.com/orkestral/venom/issues/359), close [#426](https://github.com/orkestral/venom/issues/426)) ([034c603](https://github.com/orkestral/venom/commit/034c603a76d5fb0c16f47fa075dca28ef16a37cb))
- Reset autoclose interval after QRCode read ([fb9d5bd](https://github.com/orkestral/venom/commit/fb9d5bdcd57f22acbb2e17b29f4656033bb2b2ce))

## [2.2.1](https://github.com/orkestral/venom/compare/v2.2.0...v2.2.1) (2020-12-15)

### Bug Fixes

- Fixed sendSeen call ([e2c0026](https://github.com/orkestral/venom/commit/e2c0026763f6255c903095c02c484d763b8de65d))

# [2.2.0](https://github.com/orkestral/venom/compare/v2.1.2...v2.2.0) (2020-12-15)

### Bug Fixes

- Fixed getMessageById from non loaded chat ([600ace1](https://github.com/orkestral/venom/commit/600ace18eb87807d07f813e06e6758dba2f79ad1))
- Fixed group functions (close [#416](https://github.com/orkestral/venom/issues/416)) ([e595f94](https://github.com/orkestral/venom/commit/e595f94a0768af41e2a89e6fbabe058deb2af637))
- Fixed undefined from sendLocation and sendContactVcard (close [#410](https://github.com/orkestral/venom/issues/410)) ([b46fc39](https://github.com/orkestral/venom/commit/b46fc39133f44e60b5833211b2eb700e6662a5e1))
- Improved script injection ([ba3b109](https://github.com/orkestral/venom/commit/ba3b109820c4dce62d02568b88b6174c5f9ea3b3))

### Features

- Added onInterfaceChange listener ([61e5fdf](https://github.com/orkestral/venom/commit/61e5fdfa43256b1eb4b91a1928fdf3ed6dfbf544))
- Added option to create from object ([6e9d557](https://github.com/orkestral/venom/commit/6e9d55757a76e969399e27f4a1798192e8663397))
- Improved logger using winston package ([dffb38c](https://github.com/orkestral/venom/commit/dffb38cc8330cec53aa70f66cff1b105e9c027d1))

## [2.1.2](https://github.com/orkestral/venom/compare/v2.1.1...v2.1.2) (2020-12-04)

### Bug Fixes

- Added error throw for sendSticker ([cdd43ef](https://github.com/orkestral/venom/commit/cdd43ef6b3a031a3820b92aba79f5f6da5c94d5c))

### Features

- Added downloadMedia method (close [#383](https://github.com/orkestral/venom/issues/383), close [#378](https://github.com/orkestral/venom/issues/378)) ([f3e3906](https://github.com/orkestral/venom/commit/f3e3906836c0b9458a8244995c7c32292eacb065))

## [2.1.1](https://github.com/orkestral/venom/compare/v2.1.0...v2.1.1) (2020-12-02)

### Bug Fixes

- Fixed ninth digit problem (close [#370](https://github.com/orkestral/venom/issues/370)) ([ee970dd](https://github.com/orkestral/venom/commit/ee970ddb38ed0a5c048cff093dc337f109de34fb))
- Fixed ninth digit problem for groups (close [#372](https://github.com/orkestral/venom/issues/372)) ([7017dea](https://github.com/orkestral/venom/commit/7017dea8ab66298a267e7cdcf9aa810b82d7313a))
- Fixed ninth digit problem group operation (close [#374](https://github.com/orkestral/venom/issues/374)) ([df477e7](https://github.com/orkestral/venom/commit/df477e7973b620c1f80850d95842ae09936985bc))

# [2.1.0](https://github.com/orkestral/venom/compare/v2.0.27...v2.1.0) (2020-12-01)

### Bug Fixes

- Fixed CPU and memory usage ([b439abf](https://github.com/orkestral/venom/commit/b439abf53372fbe935bbedba8e6967cd78194693))

### Features

- Added logout method ([6950e40](https://github.com/orkestral/venom/commit/6950e4053ab80976064ee353f011cb5eb5be8426))
- Improved client, allow page refresh, better event listener ([fec67ca](https://github.com/orkestral/venom/commit/fec67caada07443a26c6b9bf66a976a16285df48))
- Improved the quality of wapi.js ([5ea77e3](https://github.com/orkestral/venom/commit/5ea77e3d269edc959d7730c7fa59b60b7d68a486))
- new example ([ea45fa5](https://github.com/orkestral/venom/commit/ea45fa50b720e96a4f7389b093ca0013e816dc7e))

## [2.0.27](https://github.com/orkestral/venom/compare/v2.0.26...v2.0.27) (2020-11-28)

### Bug Fixes

- Fixed error log for send files from local ([e4a6f06](https://github.com/orkestral/venom/commit/e4a6f069d3a8c1d8f1d3478fb3cd43b3c50a0aa7))

## [2.0.26](https://github.com/orkestral/venom/compare/v2.0.25...v2.0.26) (2020-11-25)

### Features

- Improved QRCode reader and added url code in callback ([0bdcc05](https://github.com/orkestral/venom/commit/0bdcc056508396ebefd7c6b91a44258602f00250))

## [2.0.25](https://github.com/orkestral/venom/compare/v2.0.24...v2.0.25) (2020-11-23)

### Bug Fixes

- Fixed download GIF image format ([78def2e](https://github.com/orkestral/venom/commit/78def2e1e55de40f63c7b2374f4285676d10424f))

## [2.0.24](https://github.com/orkestral/venom/compare/v2.0.23...v2.0.24) (2020-11-23)

### Bug Fixes

- Fixed download JPEG image format ([c88590f](https://github.com/orkestral/venom/commit/c88590f16c1976a60372a3af8dd1d4868a9ef1a8))

## [2.0.23](https://github.com/orkestral/venom/compare/v2.0.22...v2.0.23) (2020-11-21)

### Bug Fixes

- Added timeout for browserWS connection ([3b31c1f](https://github.com/orkestral/venom/commit/3b31c1f2412559d87ee4097d7ab4cd027445b5e6))
- Added timeout for browserWS connection ([1b28832](https://github.com/orkestral/venom/commit/1b288322d5e4a4ef7d52402be28d638aaf478d6a))
- Fixed autoclose is holding promise ([35a5898](https://github.com/orkestral/venom/commit/35a58989d6314a82dfc3a0c4f8bb81c58b98808a))
- Fixed return of sendText for new non contact ([4147e75](https://github.com/orkestral/venom/commit/4147e75b53ca22fecd855356dbaaf80dd3710f94))
- Fixed send content from URL ([325538b](https://github.com/orkestral/venom/commit/325538b80f45a2792ff1119841bf03849723a416))
- Fixed sendImageAsStickerGif for big images (close [#251](https://github.com/orkestral/venom/issues/251)) ([48b1268](https://github.com/orkestral/venom/commit/48b12683bf8b249bf1088d2a8dd8071140109014))

## [2.0.22](https://github.com/orkestral/venom/compare/v2.0.21...v2.0.22) (2020-11-19)

### Bug Fixes

- Fixed QRCode reader ([247fe17](https://github.com/orkestral/venom/commit/247fe178aec25c504afd6142464ea7420a489a7c))

## [2.0.21](https://github.com/orkestral/venom/compare/v2.0.20...v2.0.21) (2020-11-17)

### Bug Fixes

- Added lint and fixed javascript issues (close [#326](https://github.com/orkestral/venom/issues/326)) ([#328](https://github.com/orkestral/venom/issues/328)) ([31509d6](https://github.com/orkestral/venom/commit/31509d64de7ce228653f8b521986c4c29ff1ac80))

### Features

- Added method getLastSeen (close [#303](https://github.com/orkestral/venom/issues/303)) ([#330](https://github.com/orkestral/venom/issues/330)) ([430e2c3](https://github.com/orkestral/venom/commit/430e2c32d398240991b50c8984a508dc0a2952a9))

## [2.0.20](https://github.com/orkestral/venom/compare/v2.0.19...v2.0.20) (2020-11-16)

### Bug Fixes

- Fixed error on replying a invalid message (close [#320](https://github.com/orkestral/venom/issues/320)) ([917db7a](https://github.com/orkestral/venom/commit/917db7a8db960d914d207a967bdd3603dbf040e0))

### Features

- Send image from base64 ([#319](https://github.com/orkestral/venom/issues/319)) ([9085132](https://github.com/orkestral/venom/commit/90851322e9cedddb16299dbc93c675af084033c0))

## [2.0.19](https://github.com/orkestral/venom/compare/v2.0.18...v2.0.19) (2020-11-14)

### Bug Fixes

- Add StickerGif compatibility with Webp format ([#278](https://github.com/orkestral/venom/issues/278)) ([9104a9b](https://github.com/orkestral/venom/commit/9104a9b4713b1c5a194a8f9f955240318a5d4b2d)), closes [/github.com/orkestral/venom/issues/277#issuecomment-719068476](https://github.com//github.com/orkestral/venom/issues/277/issues/issuecomment-719068476)
- git ignore ([2c1fc1d](https://github.com/orkestral/venom/commit/2c1fc1d108bb907f83fe6ab1d00439c0ad6ce0ae))
- remove package-lock.json ([2c2e134](https://github.com/orkestral/venom/commit/2c2e134f40d97b6583797aa0c6f20b728e87b713))
- sendSticker 'modelClass' of undefined ([9166c68](https://github.com/orkestral/venom/commit/9166c6821e1c91c446478e4584c9ef0304f081f1))
- **293:** group property 'find' of undefined ([a661e1c](https://github.com/orkestral/venom/commit/a661e1c0d3505469af1ce8f2af198ad016b7ba85))

### Features

- fix some things and create space for examples ([ed964d8](https://github.com/orkestral/venom/commit/ed964d8829d26ae2740442c27bf7ab333d28b8ac))
- **conteex:** fix sticker ([f8feaae](https://github.com/orkestral/venom/commit/f8feaae74b71328ac0ab541cdea15c939a4308a2))
- **context:** file dist.zip ([3134cdf](https://github.com/orkestral/venom/commit/3134cdfeb24422874b1a996b176183c93b4d4cba))
- **context:** readme whatsapp ([6682e01](https://github.com/orkestral/venom/commit/6682e01779f13f967fc16544763fb02f88c4ba57))
- dist teste ([68d63de](https://github.com/orkestral/venom/commit/68d63ded50b98e7968dff32320c8c1f999229ef1))
- new function and fixed some things ([#295](https://github.com/orkestral/venom/issues/295)) ([f1cea5d](https://github.com/orkestral/venom/commit/f1cea5d5ec1e3610087363c5d4df7545033f60d9))
- new function and some corrections ([a4be96f](https://github.com/orkestral/venom/commit/a4be96fd589ca40a0d8c85720bda36515caea59c))
- new function to change group permission ([9f4eeef](https://github.com/orkestral/venom/commit/9f4eeef3bfd8fffe61897d0c9051e1aa4bbe8241))

## [2.0.18](https://github.com/orkestral/venom/compare/v2.0.17...v2.0.18) (2020-10-22)

### Bug Fixes

- change deprecated function puppeteer ([eff5660](https://github.com/orkestral/venom/commit/eff5660edd031f46b6d4f6fdc68314586ec017ff))

## [2.0.17](https://github.com/orkestral/venom/compare/v2.0.16...v2.0.17) (2020-10-22)

### Bug Fixes

- remove function stream and fix userAgente ([7fe0701](https://github.com/orkestral/venom/commit/7fe0701f71da1d3e4b8b85152120bf547b691fbc))

## [2.0.16](https://github.com/orkestral/venom/compare/v2.0.15...v2.0.16) (2020-10-20)

### Bug Fixes

- **#222:** add option to inject options into puppeteer ([bbd907c](https://github.com/orkestral/venom/commit/bbd907c808a1990786ad7d48033bcad35ee48a84)), closes [#222](https://github.com/orkestral/venom/issues/222)

### Features

- **context:** delete token after disconnecting by phone and others ([e88fbd0](https://github.com/orkestral/venom/commit/e88fbd07b1cbd2d4f800366cd1a075db45771028))
- **context:** list mute chats and add mute chat ([0d37634](https://github.com/orkestral/venom/commit/0d3763498543f876e7c42661933e31a822d1e048))
- add new function to detect incoming call ([e6e2b7c](https://github.com/orkestral/venom/commit/e6e2b7c573383338954233757b3bd00c4aabc9a5))

## [2.0.15](https://github.com/orkestral/venom/compare/v2.0.14...v2.0.15) (2020-10-16)

### Bug Fixes

- fix function useHere() ([dd0564e](https://github.com/orkestral/venom/commit/dd0564e9fe975a3f61a9779053f3b81ab5b771a5))
- Update dependabot.yml ([472b407](https://github.com/orkestral/venom/commit/472b4070cfcdbe1f169aa18533d389d6e3346cd0))
- Update dependabot.yml ([a63d969](https://github.com/orkestral/venom/commit/a63d969e6d41cc79eb85b16c28abd89b3c22842c))
- Update dependabot.yml ([b41867f](https://github.com/orkestral/venom/commit/b41867f9ded889d74a91b208a355f10778b5538c))
- Update dependabot.yml ([c3f996f](https://github.com/orkestral/venom/commit/c3f996f6b8e99c3cd9b5558cca9a5dc05c005977))

### Features

- Create codeql-analysis workflow ([f35d3c1](https://github.com/orkestral/venom/commit/f35d3c151e2e85004ae1188ca8cdc803e2c52d69))
- Create dependabot ([2b59f52](https://github.com/orkestral/venom/commit/2b59f527a21efda62b1bfa7163828ff80f6a9ba4))
- create new function to detect disconnect ([1d70c9c](https://github.com/orkestral/venom/commit/1d70c9cc9a069a2b079e0ed40ee0488f84e3f27b))

## [2.0.14](https://github.com/orkestral/venom/compare/v2.0.11...v2.0.14) (2020-10-14)

### Bug Fixes

- change package ([9770cb6](https://github.com/orkestral/venom/commit/9770cb6d178877661531fe42e44070c51d64353c))

### Features

- **context:** client desconnected mobile ([0fafa3c](https://github.com/orkestral/venom/commit/0fafa3c3dbb71e2e7008eab1c69712b292d9f103))
- **context:** fix and add functions ([9222837](https://github.com/orkestral/venom/commit/9222837b7b8f5b328783b7077e7756155fbbdf01))
- **context:** return browserClose after logging in ([fed693c](https://github.com/orkestral/venom/commit/fed693c0229c7fe293ec45c13a82078ea3386f78))
- **context:** Treatment of the puppeteer.connect() function ([0dbebee](https://github.com/orkestral/venom/commit/0dbebeefeaaeeeb0e6327a876af18fd9691f9525))

## [2.0.12]

- fix is onAck
- fix create more than one function: onMessage, onAnyMessage, onStateChange and onAck
- new option createPathFileToken
- new option in create browserSessionToken
- new option catchQR: attempt
- new function getSessionTokenBrowser

## [2.0.11](https://github.com/orkestral/venom/compare/v2.0.10...v2.0.11) (2020-10-07)

## [2.0.10](https://github.com/orkestral/venom/compare/v2.0.9...v2.0.10) (2020-10-06)

## [2.0.9](https://github.com/orkestral/venom/compare/v2.0.8...v2.0.9) (2020-10-06)

### Bug Fixes

- change chromium args ([1a18c5d](https://github.com/orkestral/venom/commit/1a18c5dbfaefe25de543db6ee179ffd8a02f20ad))
- change user agent ([baf9598](https://github.com/orkestral/venom/commit/baf9598e876245c8f51f8d3791727af61eab3979))
- correct link update ([599c562](https://github.com/orkestral/venom/commit/599c5625be97b07f5857683dea3cbc77f5ea35cc))
- name chromium ([25c1a3d](https://github.com/orkestral/venom/commit/25c1a3d1aef646ae4a93026e7880274c6b56988e))

### Features

- new option to connect browserWSEndpoint ([2343e1a](https://github.com/orkestral/venom/commit/2343e1a39059dced4a13dc6b41fc4055ac55ddcc))
- new option to connect with external browser ([2a62bb9](https://github.com/orkestral/venom/commit/2a62bb9d254d6651e5040d84128043d696d6694d))

## [2.0.7 | 2.0.8](https://github.com/orkestral/venom/compare/v2.0.6...v2.0.8) (2020-09-29)

- Updating packages
- Improved message download
- Message error when try descrypt

## [2.0.6](https://github.com/orkestral/venom/compare/v2.0.4...v2.0.6) (2020-09-28)

- Fixed return type of create function
- Fix initialization with autoclose

## [2.0.4 | 2.0.5](https://github.com/orkestral/venom/compare/v2.0.3...v2.0.4) (2020-09-22)

- Change ascii welcome venom
- Update user agent
- Fix is connected

## [2.0.2 | 2.0.3](https://github.com/orkestral/venom/compare/a6ac61cd31c759dc75b1bfac3c6ecee645f54ee5...v2.0.3) (2020-09-21)

- add crete config folderNameToken, mkdirFolderToken
- fix package.json rxjs
- add option to disable message terminal Welcome Venom
- fix function to send by group id
- fix bug sendImageAsStickerGif, sendImageAsSticker
- return to chatid to object route treatment

## 2.0.1 (2020-09-14)

- fix bug initialization
- add checkNumberStatus, sendContactVcardList
- correction qrcode in terminal, sendContactVcard, setProfilePic, sendLocation
- add parameter in Callback Status Session: browserClose || qrReadSuccess \* || qrReadFail
- remove refreshQR and sendMessageToId

## 1.1.4 (2020-07-26)

- fix bug

## 1.1.2 (2020-07-24)

- fix bug autoclose

## 1.1.1 (2020-07-24)

- New functions

## 1.0.9 (2020-06-30)

- remove fix file mime type discovery

## 1.0.8 (2020-06-29)

- fix reply (with mention)
- add function autoClose
- add function joingroup
- add function getgroupinfofrominvitelink
- fix file mime type discovery

## 1.0.7 (2020-06-20)

- add setProfilePic
- Fix bug getAllNewMessages

## 1.0.6 (2020-06-19)

- Fix bug not exist folder dist

## 1.0.5 (2020-06-19)

- Fix bug useHere

## 1.0.4 (2020-06-15)

- Fix bug performace

## 1.0.3 (2020-06-14)

- Fix bug performace

## 1.0.2 (2020-06-13)

\*Fix bug disabled spinners

## 1.0.1 (2020-06-13)

- Session for Token - for [HelioSilva](https://github.com/orkestral/venom/commits?author=HelioSilva)
- Spinners disabled options - for [CosmicSnow](https://github.com/orkestral/venom/commits?author=CosmicSnow)

## 1.0.0 (2020-06-06)

- First version
- Init readme
- Do not force rxjs
- Initial commit
- Fix CatchQR erro
- QR catching
- Add Status of Session
