'use client'

import Link from 'next/link'
import { 
  Shield, 
  Github, 
  Twitter, 
  Linkedin, 
  Mail,
  MapPin,
  Phone,
  Globe,
  Heart,
  Zap,
  Lock,
  Users,
  BookOpen,
  HelpCircle,
  FileText,
  ExternalLink
} from 'lucide-react'

export default function CyberFooter() {
  const currentYear = new Date().getFullYear()

  const footerSections = [
    {
      title: 'Platform',
      links: [
        { name: 'OSINT Investigator', href: '/osint', icon: Globe },
        { name: 'Vulnerability Scanner', href: '/scanner', icon: Shield },
        { name: 'File Analyzer', href: '/file-analyzer', icon: FileText },
        { name: 'CVE Intelligence', href: '/cve', icon: Lock },
        { name: 'Google Dorking', href: '/dorking', icon: Globe },
        { name: 'Developer Tools', href: '/tools', icon: Zap }
      ]
    },
    {
      title: 'Community',
      links: [
        { name: 'Leaderboard', href: '/leaderboard', icon: Users },
        { name: '<PERSON>ug Bounty', href: '/bounty', icon: Zap },
        { name: 'Discord Server', href: 'https://discord.gg/kodexguard', icon: ExternalLink, external: true },
        { name: 'GitHub', href: 'https://github.com/kodexguard', icon: Github, external: true },
        { name: 'Blog', href: '/blog', icon: BookOpen },
        { name: 'Events', href: '/events', icon: Users }
      ]
    },
    {
      title: 'Resources',
      links: [
        { name: 'Documentation', href: '/docs', icon: BookOpen },
        { name: 'API Reference', href: '/docs/api', icon: FileText },
        { name: 'Tutorials', href: '/tutorials', icon: BookOpen },
        { name: 'Security Guide', href: '/security', icon: Shield },
        { name: 'Best Practices', href: '/best-practices', icon: Lock },
        { name: 'FAQ', href: '/faq', icon: HelpCircle }
      ]
    },
    {
      title: 'Company',
      links: [
        { name: 'About Us', href: '/about' },
        { name: 'Careers', href: '/careers' },
        { name: 'Contact', href: '/contact' },
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Security Policy', href: '/security-policy' }
      ]
    }
  ]

  const socialLinks = [
    { name: 'GitHub', href: 'https://github.com/kodexguard', icon: Github },
    { name: 'Twitter', href: 'https://twitter.com/kodexguard', icon: Twitter },
    { name: 'LinkedIn', href: 'https://linkedin.com/company/kodexguard', icon: Linkedin },
    { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail }
  ]

  const stats = [
    { label: 'Active Users', value: '50K+' },
    { label: 'Vulnerabilities Found', value: '1M+' },
    { label: 'Security Scans', value: '10M+' },
    { label: 'Countries', value: '150+' }
  ]

  return (
    <footer className="bg-cyber-dark border-t border-cyber-border">
      {/* Stats Section */}
      <div className="border-b border-cyber-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={stat.label} className="text-center group">
                <div className="text-3xl md:text-4xl font-bold text-cyber-primary animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stat.value}
                </div>
                <div className="text-gray-400 mt-2 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 group mb-6">
              <div className="relative">
                <Shield className="h-10 w-10 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold text-cyber-glow group-hover:animate-glitch">
                  KodeXGuard
                </span>
                <span className="text-xs text-cyber-secondary uppercase tracking-wider">
                  Cyber Security Platform
                </span>
              </div>
            </Link>
            
            <p className="text-gray-400 mb-6 leading-relaxed">
              The ultimate cybersecurity platform for OSINT investigation, vulnerability scanning, 
              and security research. Join thousands of security professionals worldwide.
            </p>

            {/* Contact Info */}
            <div className="space-y-3 text-sm">
              <div className="flex items-center space-x-3 text-gray-400">
                <MapPin className="h-4 w-4 text-cyber-primary" />
                <span>Jakarta, Indonesia</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Mail className="h-4 w-4 text-cyber-primary" />
                <a href="mailto:<EMAIL>" className="hover:text-cyber-primary transition-colors duration-200">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Phone className="h-4 w-4 text-cyber-primary" />
                <span>+62 21 1234 5678</span>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="lg:col-span-1">
              <h3 className="text-white font-bold mb-6 text-lg relative">
                {section.title}
                <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-cyber-primary"></div>
              </h3>
              <ul className="space-y-3">
                {section.links.map((link) => {
                  const Icon = link.icon
                  return (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        target={link.external ? '_blank' : undefined}
                        rel={link.external ? 'noopener noreferrer' : undefined}
                        className="flex items-center space-x-2 text-gray-400 hover:text-cyber-primary transition-colors duration-200 group"
                      >
                        {Icon && <Icon className="h-4 w-4 group-hover:animate-cyber-pulse" />}
                        <span>{link.name}</span>
                        {link.external && <ExternalLink className="h-3 w-3" />}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Section */}
        <div className="mt-16 pt-8 border-t border-cyber-border">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-white mb-4">
                Stay Updated with <span className="text-cyber-primary">Cyber Threats</span>
              </h3>
              <p className="text-gray-400">
                Get the latest security news, vulnerability alerts, and platform updates delivered to your inbox.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-cyber-card border border-cyber-border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-cyber-primary transition-colors duration-200"
              />
              <button className="btn-cyber-primary whitespace-nowrap">
                <Mail className="h-4 w-4 mr-2" />
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-cyber-border bg-cyber-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="flex items-center space-x-2 text-gray-400">
              <span>&copy; {currentYear} KodeXGuard. All rights reserved.</span>
              <span className="hidden md:inline">|</span>
              <span className="flex items-center space-x-1">
                <span>Made with</span>
                <Heart className="h-4 w-4 text-red-500 animate-pulse" />
                <span>for the cybersecurity community</span>
              </span>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-gray-400 text-sm">Follow us:</span>
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 rounded-lg text-gray-400 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-all duration-200 group"
                    title={social.name}
                  >
                    <Icon className="h-5 w-5 group-hover:animate-cyber-pulse" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Security Badge */}
          <div className="mt-6 pt-6 border-t border-cyber-border/50">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <Lock className="h-4 w-4 text-green-500" />
                  <span>SSL Secured</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-blue-500" />
                  <span>SOC 2 Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <span>99.9% Uptime</span>
                </div>
              </div>
              
              <div className="text-xs text-gray-500">
                <span>Platform Status: </span>
                <span className="text-green-500 font-medium">All Systems Operational</span>
                <div className="inline-block w-2 h-2 bg-green-500 rounded-full ml-2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cyber Grid Background Effect */}
      <div className="absolute inset-0 pointer-events-none opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>
    </footer>
  )
}
