"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./components/CyberHeader.tsx":
/*!************************************!*\
  !*** ./components/CyberHeader.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CyberHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CyberHeader(param) {\n    let { user } = param;\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Features\",\n            href: \"#features\",\n            dropdown: [\n                {\n                    name: \"OSINT Investigator\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    description: \"Advanced intelligence gathering\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    description: \"Automated security scanning\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    description: \"Malware detection & analysis\"\n                },\n                {\n                    name: \"CVE Intelligence\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    description: \"Vulnerability database\"\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    description: \"Advanced search queries\"\n                },\n                {\n                    name: \"Developer Tools\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    description: \"Security testing tools\"\n                }\n            ]\n        },\n        {\n            name: \"Pricing\",\n            href: \"/plan\"\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Docs\",\n            href: \"/docs\"\n        },\n        {\n            name: \"Community\",\n            href: \"/community\"\n        }\n    ];\n    const handleLogin = ()=>{\n        router.push(\"/login\");\n    };\n    const handleRegister = ()=>{\n        router.push(\"/register\");\n    };\n    const handleDashboard = ()=>{\n        router.push(\"/dashboard\");\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const isActive = (href)=>{\n        if (href.startsWith(\"#\")) return false;\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? \"bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border shadow-lg\" : \"bg-transparent\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-cyber-glow group-hover:animate-glitch\",\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                            children: \"Cyber Security Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setActiveDropdown(item.name),\n                                        onMouseLeave: ()=>setActiveDropdown(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 text-gray-300 hover:text-cyber-primary transition-colors duration-200 font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-80 bg-cyber-card border border-cyber-border rounded-lg shadow-xl animate-fade-in-up\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 grid grid-cols-1 gap-2\",\n                                                    children: item.dropdown.map((subItem)=>{\n                                                        const Icon = subItem.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: subItem.href,\n                                                            className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-cyber-secondary/10 transition-colors duration-200 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-cyber-primary group-hover:animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white group-hover:text-cyber-primary\",\n                                                                            children: subItem.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 159,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: subItem.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, subItem.name, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 31\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary\" : \"text-gray-300 hover:text-cyber-primary\"),\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 35\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 px-3 py-1 bg-cyber-secondary/20 border border-cyber-secondary rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyber-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-cyber-accent uppercase\",\n                                                children: user.plan\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-cyber-secondary/10 transition-colors duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-8 w-8 rounded-full border-2 border-cyber-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-cyber-primary/20 border-2 border-cyber-primary flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 top-full mt-2 w-48 bg-cyber-card border border-cyber-border rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleDashboard,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Dashboard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Logout\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogin,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-cyber-primary hover:text-white border border-cyber-primary hover:bg-cyber-primary/20 rounded-lg transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRegister,\n                                        className: \"btn-cyber-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Get Started\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {\n                                    variant: \"button\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: \"p-2 rounded-lg \".concat(themeClasses.textSecondary, \" hover:text-cyber-primary hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden \".concat(themeClasses.bgCard, \" border-t \").concat(themeClasses.border, \" animate-slide-in-right\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 space-y-4\",\n                    children: [\n                        navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.textSecondary, \" font-medium mb-2\"),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-2\",\n                                            children: item.dropdown.map((subItem)=>{\n                                                const Icon = subItem.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.href,\n                                                    className: \"flex items-center space-x-3 p-2 rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: themeClasses.textPrimary,\n                                                            children: subItem.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, subItem.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"block px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\") : \"\".concat(themeClasses.textSecondary, \" hover:text-cyber-primary hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\")),\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t \".concat(themeClasses.border, \" space-y-3\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleLogin();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 text-cyber-primary border border-cyber-primary rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleRegister();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Started\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(CyberHeader, \"F3oijKM3tVCOz8jkoNovbYM3h7k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = CyberHeader;\nvar _c;\n$RefreshReg$(_c, \"CyberHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CyberHeader.tsx\n"));

/***/ })

});