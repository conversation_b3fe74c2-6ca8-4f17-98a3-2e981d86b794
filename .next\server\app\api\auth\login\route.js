"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/middlewares/auth */ \"(rsc)/./middlewares/auth.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\n\n// Validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_3__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_3__.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_3__.string().min(1, \"Password is required\")\n});\nasync function loginHandler(req) {\n    try {\n        // Parse request body\n        const body = await req.json();\n        // Validate input\n        const validation = loginSchema.safeParse(body);\n        if (!validation.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: validation.error.errors.map((err)=>({\n                        field: err.path.join(\".\"),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        const { email, password } = validation.data;\n        // Login user\n        const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.login(email, password);\n        if (\"error\" in result) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error\n            }, {\n                status: 401\n            });\n        }\n        // Return success response\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Login successful\",\n            data: {\n                user: {\n                    id: result.user.id,\n                    username: result.user.username,\n                    email: result.user.email,\n                    fullName: result.user.fullName,\n                    avatar: result.user.avatar,\n                    bio: result.user.bio,\n                    role: result.user.role,\n                    plan: result.user.plan,\n                    planExpiry: result.user.planExpiry,\n                    emailVerified: result.user.emailVerified,\n                    lastLogin: result.user.lastLogin,\n                    stats: result.user.stats\n                },\n                tokens: result.tokens\n            }\n        });\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Login failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Apply middlewares\nconst POST = (0,_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.combineMiddlewares)(_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withCors, _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withErrorHandling)(loginHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// JWT Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nconst REFRESH_TOKEN_EXPIRES_IN = \"30d\";\n// Password configuration\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || \"12\");\nclass AuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            userId: user.id,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            type: \"refresh\"\n        }, JWT_SECRET, {\n            expiresIn: REFRESH_TOKEN_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        // Calculate expiration time in seconds\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(accessToken);\n        const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token) {\n        try {\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n                issuer: \"kodexguard\",\n                audience: \"kodexguard-users\"\n            });\n            return decoded;\n        } catch (error) {\n            console.error(\"Token verification failed:\", error);\n            return null;\n        }\n    }\n    // Hash password\n    static async hashPassword(password) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hash);\n    }\n    // Register new user\n    static async register(userData) {\n        try {\n            // Check if user already exists\n            const existingUser = await DatabaseUtils.findOne(\"SELECT id FROM users WHERE email = ? OR username = ?\", [\n                userData.email,\n                userData.username\n            ]);\n            if (existingUser) {\n                return {\n                    error: \"User with this email or username already exists\"\n                };\n            }\n            // Hash password\n            const passwordHash = await this.hashPassword(userData.password);\n            // Generate user ID\n            const userId = this.generateUUID();\n            // Create user\n            await DatabaseUtils.insert(\"users\", {\n                id: userId,\n                username: userData.username,\n                email: userData.email,\n                password_hash: passwordHash,\n                full_name: userData.fullName,\n                role: UserRole.USER,\n                plan: UserPlan.FREE,\n                is_active: true,\n                email_verified: false,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            // Create user stats\n            await DatabaseUtils.insert(\"user_stats\", {\n                id: this.generateUUID(),\n                user_id: userId,\n                total_scans: 0,\n                vulnerabilities_found: 0,\n                files_analyzed: 0,\n                osint_queries: 0,\n                api_calls: 0,\n                score: 0,\n                rank_position: 0,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            // Get created user\n            const user = await this.getUserById(userId);\n            if (!user) {\n                return {\n                    error: \"Failed to create user\"\n                };\n            }\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            // Store refresh token in Redis\n            await RedisUtils.set(`refresh_token:${userId}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                error: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const userRecord = await DatabaseUtils.findOne(\"SELECT * FROM users WHERE email = ? AND is_active = true\", [\n                email\n            ]);\n            if (!userRecord) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Verify password\n            const isValidPassword = await this.verifyPassword(password, userRecord.password_hash);\n            if (!isValidPassword) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Update last login\n            await DatabaseUtils.update(\"users\", {\n                last_login: new Date()\n            }, \"id = ?\", [\n                userRecord.id\n            ]);\n            // Get user with stats\n            const user = await this.getUserById(userRecord.id);\n            if (!user) {\n                return {\n                    error: \"User not found\"\n                };\n            }\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            // Store refresh token in Redis\n            await RedisUtils.set(`refresh_token:${user.id}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                error: \"Login failed\"\n            };\n        }\n    }\n    // Refresh access token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(refreshToken, JWT_SECRET);\n            if (decoded.type !== \"refresh\") {\n                return {\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Check if refresh token exists in Redis\n            const storedToken = await RedisUtils.get(`refresh_token:${decoded.userId}`);\n            if (storedToken !== refreshToken) {\n                return {\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Get user\n            const user = await this.getUserById(decoded.userId);\n            if (!user) {\n                return {\n                    error: \"User not found\"\n                };\n            }\n            // Generate new tokens\n            const tokens = this.generateTokens(user);\n            // Update refresh token in Redis\n            await RedisUtils.set(`refresh_token:${user.id}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            return {\n                error: \"Invalid refresh token\"\n            };\n        }\n    }\n    // Logout user\n    static async logout(userId) {\n        try {\n            // Remove refresh token from Redis\n            await RedisUtils.del(`refresh_token:${userId}`);\n            return true;\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return false;\n        }\n    }\n    // Get user by ID with stats\n    static async getUserById(userId) {\n        try {\n            const userRecord = await DatabaseUtils.findOne(`\n        SELECT \n          u.*,\n          us.total_scans,\n          us.vulnerabilities_found,\n          us.files_analyzed,\n          us.osint_queries,\n          us.api_calls,\n          us.score,\n          us.rank_position\n        FROM users u\n        LEFT JOIN user_stats us ON u.id = us.user_id\n        WHERE u.id = ? AND u.is_active = true\n      `, [\n                userId\n            ]);\n            if (!userRecord) {\n                return null;\n            }\n            // Get user API keys\n            const apiKeys = await DatabaseUtils.query(\"SELECT * FROM api_keys WHERE user_id = ? AND is_active = true\", [\n                userId\n            ]);\n            return {\n                id: userRecord.id,\n                username: userRecord.username,\n                email: userRecord.email,\n                fullName: userRecord.full_name,\n                avatar: userRecord.avatar,\n                bio: userRecord.bio,\n                role: userRecord.role,\n                plan: userRecord.plan,\n                planExpiry: userRecord.plan_expiry ? new Date(userRecord.plan_expiry) : undefined,\n                isActive: userRecord.is_active,\n                emailVerified: userRecord.email_verified,\n                createdAt: new Date(userRecord.created_at),\n                updatedAt: new Date(userRecord.updated_at),\n                lastLogin: userRecord.last_login ? new Date(userRecord.last_login) : undefined,\n                apiKeys: apiKeys || [],\n                stats: {\n                    totalScans: userRecord.total_scans || 0,\n                    vulnerabilitiesFound: userRecord.vulnerabilities_found || 0,\n                    filesAnalyzed: userRecord.files_analyzed || 0,\n                    osintQueries: userRecord.osint_queries || 0,\n                    apiCalls: userRecord.api_calls || 0,\n                    score: userRecord.score || 0,\n                    rank: userRecord.rank_position || 0\n                }\n            };\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Generate API Key\n    static async generateApiKey(userId, name, permissions = []) {\n        try {\n            // Generate API key\n            const keyId = this.generateUUID();\n            const apiKey = `kxg_${this.generateRandomString(32)}`;\n            const keyHash = await this.hashPassword(apiKey);\n            const keyPrefix = apiKey.substring(0, 12);\n            // Insert API key\n            await DatabaseUtils.insert(\"api_keys\", {\n                id: keyId,\n                user_id: userId,\n                name,\n                key_hash: keyHash,\n                key_prefix: keyPrefix,\n                permissions: JSON.stringify(permissions),\n                is_active: true,\n                usage_count: 0,\n                rate_limit: 100,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            return {\n                apiKey,\n                keyId\n            };\n        } catch (error) {\n            console.error(\"Generate API key error:\", error);\n            return {\n                error: \"Failed to generate API key\"\n            };\n        }\n    }\n    // Verify API Key\n    static async verifyApiKey(apiKey) {\n        try {\n            const keyPrefix = apiKey.substring(0, 12);\n            // Find API key by prefix\n            const keyRecord = await DatabaseUtils.findOne(\"SELECT * FROM api_keys WHERE key_prefix = ? AND is_active = true\", [\n                keyPrefix\n            ]);\n            if (!keyRecord) {\n                return null;\n            }\n            // Verify API key hash\n            const isValid = await this.verifyPassword(apiKey, keyRecord.key_hash);\n            if (!isValid) {\n                return null;\n            }\n            // Update usage count and last used\n            await DatabaseUtils.update(\"api_keys\", {\n                usage_count: keyRecord.usage_count + 1,\n                last_used: new Date(),\n                updated_at: new Date()\n            }, \"id = ?\", [\n                keyRecord.id\n            ]);\n            // Get user\n            const user = await this.getUserById(keyRecord.user_id);\n            if (!user) {\n                return null;\n            }\n            return {\n                user,\n                keyId: keyRecord.id\n            };\n        } catch (error) {\n            console.error(\"Verify API key error:\", error);\n            return null;\n        }\n    }\n    // Update user profile\n    static async updateProfile(userId, profileData) {\n        try {\n            const updateData = {\n                updated_at: new Date()\n            };\n            if (profileData.fullName) updateData.full_name = profileData.fullName;\n            if (profileData.bio !== undefined) updateData.bio = profileData.bio;\n            if (profileData.avatar !== undefined) updateData.avatar = profileData.avatar;\n            return await DatabaseUtils.update(\"users\", updateData, \"id = ?\", [\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            return false;\n        }\n    }\n    // Change password\n    static async changePassword(userId, currentPassword, newPassword) {\n        try {\n            // Get current user\n            const userRecord = await DatabaseUtils.findOne(\"SELECT password_hash FROM users WHERE id = ?\", [\n                userId\n            ]);\n            if (!userRecord) {\n                return {\n                    success: false,\n                    error: \"User not found\"\n                };\n            }\n            // Verify current password\n            const isValidPassword = await this.verifyPassword(currentPassword, userRecord.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    error: \"Current password is incorrect\"\n                };\n            }\n            // Hash new password\n            const newPasswordHash = await this.hashPassword(newPassword);\n            // Update password\n            const updated = await DatabaseUtils.update(\"users\", {\n                password_hash: newPasswordHash,\n                updated_at: new Date()\n            }, \"id = ?\", [\n                userId\n            ]);\n            if (updated) {\n                // Invalidate all refresh tokens for this user\n                await RedisUtils.del(`refresh_token:${userId}`);\n                return {\n                    success: true\n                };\n            }\n            return {\n                success: false,\n                error: \"Failed to update password\"\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                error: \"Failed to change password\"\n            };\n        }\n    }\n    // Delete API key\n    static async deleteApiKey(userId, keyId) {\n        try {\n            return await DatabaseUtils.update(\"api_keys\", {\n                is_active: false,\n                updated_at: new Date()\n            }, \"id = ? AND user_id = ?\", [\n                keyId,\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Delete API key error:\", error);\n            return false;\n        }\n    }\n    // Get user API keys\n    static async getUserApiKeys(userId) {\n        try {\n            return await DatabaseUtils.query(\"SELECT id, name, key_prefix, permissions, is_active, usage_count, rate_limit, last_used, created_at FROM api_keys WHERE user_id = ? ORDER BY created_at DESC\", [\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Get API keys error:\", error);\n            return [];\n        }\n    }\n    // Check rate limit\n    static async checkRateLimit(userId, keyId) {\n        try {\n            const key = keyId ? `rate_limit:api:${keyId}` : `rate_limit:user:${userId}`;\n            const window = 15 * 60 // 15 minutes\n            ;\n            const limit = 100 // requests per window\n            ;\n            const current = await RedisUtils.incr(key, window);\n            const remaining = Math.max(0, limit - current);\n            const resetTime = Math.floor(Date.now() / 1000) + window;\n            return {\n                allowed: current <= limit,\n                remaining,\n                resetTime\n            };\n        } catch (error) {\n            console.error(\"Rate limit check error:\", error);\n            return {\n                allowed: true,\n                remaining: 100,\n                resetTime: 0\n            };\n        }\n    }\n    // Generate random string\n    static generateRandomString(length) {\n        const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n        let result = \"\";\n        for(let i = 0; i < length; i++){\n            result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n    }\n    // Generate UUID\n    static generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c == \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./middlewares/auth.ts":
/*!*****************************!*\
  !*** ./middlewares/auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combineMiddlewares: () => (/* binding */ combineMiddlewares),\n/* harmony export */   withAdmin: () => (/* binding */ withAdmin),\n/* harmony export */   withApiKey: () => (/* binding */ withApiKey),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthOrApiKey: () => (/* binding */ withAuthOrApiKey),\n/* harmony export */   withCors: () => (/* binding */ withCors),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling),\n/* harmony export */   withOptionalAuth: () => (/* binding */ withOptionalAuth),\n/* harmony export */   withRole: () => (/* binding */ withRole),\n/* harmony export */   withSuperAdmin: () => (/* binding */ withSuperAdmin)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _types_user__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/user */ \"(rsc)/./types/user.ts\");\n\n\n\n// Authentication middleware\nfunction withAuth(handler) {\n    return async (req)=>{\n        try {\n            // Get token from Authorization header\n            const authHeader = req.headers.get(\"authorization\");\n            const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n            if (!token) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Verify token\n            const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n            if (!payload) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid or expired token\"\n                }, {\n                    status: 401\n                });\n            }\n            // Add user info to request\n            req.user = payload;\n            req.userId = payload.userId;\n            // Check rate limit\n            const rateLimit = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.checkRateLimit(payload.userId);\n            if (!rateLimit.allowed) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded\",\n                    resetTime: rateLimit.resetTime\n                }, {\n                    status: 429\n                });\n            }\n            // Add rate limit headers\n            const response = await handler(req);\n            response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n            response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n            return response;\n        } catch (error) {\n            console.error(\"Auth middleware error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication failed\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// API Key authentication middleware\nfunction withApiKey(handler) {\n    return async (req)=>{\n        try {\n            // Get API key from header\n            const apiKey = req.headers.get(\"x-api-key\");\n            if (!apiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"API key required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Verify API key\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n            if (!result) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid API key\"\n                }, {\n                    status: 401\n                });\n            }\n            // Add user info to request\n            req.user = {\n                userId: result.user.id,\n                email: result.user.email,\n                role: result.user.role,\n                plan: result.user.plan\n            };\n            req.userId = result.user.id;\n            // Check rate limit for API key\n            const rateLimit = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.checkRateLimit(result.user.id, result.keyId);\n            if (!rateLimit.allowed) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"API rate limit exceeded\",\n                    resetTime: rateLimit.resetTime\n                }, {\n                    status: 429\n                });\n            }\n            // Add rate limit headers\n            const response = await handler(req);\n            response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n            response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n            return response;\n        } catch (error) {\n            console.error(\"API key middleware error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API authentication failed\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Role-based authorization middleware\nfunction withRole(roles) {\n    return function(handler) {\n        return async (req)=>{\n            if (!req.user) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            if (!roles.includes(req.user.role)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Insufficient permissions\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(req);\n        };\n    };\n}\n// Admin only middleware\nfunction withAdmin(handler) {\n    return withRole([\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN,\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN\n    ])(handler);\n}\n// Super admin only middleware\nfunction withSuperAdmin(handler) {\n    return withRole([\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN\n    ])(handler);\n}\n// Combined auth middleware (supports both JWT and API key)\nfunction withAuthOrApiKey(handler) {\n    return async (req)=>{\n        // Try JWT first\n        const authHeader = req.headers.get(\"authorization\");\n        const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n        if (token) {\n            const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n            if (payload) {\n                req.user = payload;\n                req.userId = payload.userId;\n                return handler(req);\n            }\n        }\n        // Try API key\n        const apiKey = req.headers.get(\"x-api-key\");\n        if (apiKey) {\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n            if (result) {\n                req.user = {\n                    userId: result.user.id,\n                    email: result.user.email,\n                    role: result.user.role,\n                    plan: result.user.plan\n                };\n                req.userId = result.user.id;\n                return handler(req);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Authentication required\"\n        }, {\n            status: 401\n        });\n    };\n}\n// Optional auth middleware (doesn't require authentication but adds user info if available)\nfunction withOptionalAuth(handler) {\n    return async (req)=>{\n        try {\n            // Try JWT first\n            const authHeader = req.headers.get(\"authorization\");\n            const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n            if (token) {\n                const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n                if (payload) {\n                    req.user = payload;\n                    req.userId = payload.userId;\n                }\n            } else {\n                // Try API key\n                const apiKey = req.headers.get(\"x-api-key\");\n                if (apiKey) {\n                    const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n                    if (result) {\n                        req.user = {\n                            userId: result.user.id,\n                            email: result.user.email,\n                            role: result.user.role,\n                            plan: result.user.plan\n                        };\n                        req.userId = result.user.id;\n                    }\n                }\n            }\n            return handler(req);\n        } catch (error) {\n            console.error(\"Optional auth middleware error:\", error);\n            return handler(req);\n        }\n    };\n}\n// CORS middleware\nfunction withCors(handler) {\n    return async (req)=>{\n        // Handle preflight requests\n        if (req.method === \"OPTIONS\") {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200,\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, X-API-Key\",\n                    \"Access-Control-Max-Age\": \"86400\"\n                }\n            });\n        }\n        const response = await handler(req);\n        // Add CORS headers to response\n        response.headers.set(\"Access-Control-Allow-Origin\", \"*\");\n        response.headers.set(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n        response.headers.set(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization, X-API-Key\");\n        return response;\n    };\n}\n// Error handling middleware\nfunction withErrorHandling(handler) {\n    return async (req)=>{\n        try {\n            return await handler(req);\n        } catch (error) {\n            console.error(\"API Error:\", error);\n            if (error instanceof Error) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Internal server error\",\n                    message:  true ? error.message : 0\n                }, {\n                    status: 500\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Internal server error\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Combine multiple middlewares\nfunction combineMiddlewares(...middlewares) {\n    return (handler)=>{\n        return middlewares.reduceRight((acc, middleware)=>middleware(acc), handler);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9taWRkbGV3YXJlcy9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDRDtBQUNmO0FBT3ZDLDRCQUE0QjtBQUNyQixTQUFTRyxTQUFTQyxPQUE2RDtJQUNwRixPQUFPLE9BQU9DO1FBQ1osSUFBSTtZQUNGLHNDQUFzQztZQUN0QyxNQUFNQyxhQUFhRCxJQUFJRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztZQUNuQyxNQUFNQyxRQUFRSCxZQUFZSSxXQUFXLGFBQWFKLFdBQVdLLFNBQVMsQ0FBQyxLQUFLO1lBRTVFLElBQUksQ0FBQ0YsT0FBTztnQkFDVixPQUFPVCxxREFBWUEsQ0FBQ1ksSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBMEIsR0FDbkM7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSxlQUFlO1lBQ2YsTUFBTUMsVUFBVWQsa0RBQVdBLENBQUNlLFdBQVcsQ0FBQ1A7WUFDeEMsSUFBSSxDQUFDTSxTQUFTO2dCQUNaLE9BQU9mLHFEQUFZQSxDQUFDWSxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUEyQixHQUNwQztvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtZQUVBLDJCQUEyQjtZQUMzQlQsSUFBSVksSUFBSSxHQUFHRjtZQUNYVixJQUFJYSxNQUFNLEdBQUdILFFBQVFHLE1BQU07WUFFM0IsbUJBQW1CO1lBQ25CLE1BQU1DLFlBQVksTUFBTWxCLGtEQUFXQSxDQUFDbUIsY0FBYyxDQUFDTCxRQUFRRyxNQUFNO1lBQ2pFLElBQUksQ0FBQ0MsVUFBVUUsT0FBTyxFQUFFO2dCQUN0QixPQUFPckIscURBQVlBLENBQUNZLElBQUksQ0FDdEI7b0JBQ0VDLE9BQU87b0JBQ1BTLFdBQVdILFVBQVVHLFNBQVM7Z0JBQ2hDLEdBQ0E7b0JBQUVSLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSx5QkFBeUI7WUFDekIsTUFBTVMsV0FBVyxNQUFNbkIsUUFBUUM7WUFDL0JrQixTQUFTaEIsT0FBTyxDQUFDaUIsR0FBRyxDQUFDLHlCQUF5QkwsVUFBVU0sU0FBUyxDQUFDQyxRQUFRO1lBQzFFSCxTQUFTaEIsT0FBTyxDQUFDaUIsR0FBRyxDQUFDLHFCQUFxQkwsVUFBVUcsU0FBUyxDQUFDSSxRQUFRO1lBRXRFLE9BQU9IO1FBQ1QsRUFBRSxPQUFPVixPQUFPO1lBQ2RjLFFBQVFkLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDLE9BQU9iLHFEQUFZQSxDQUFDWSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXdCLEdBQ2pDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7SUFDRjtBQUNGO0FBRUEsb0NBQW9DO0FBQzdCLFNBQVNjLFdBQVd4QixPQUE2RDtJQUN0RixPQUFPLE9BQU9DO1FBQ1osSUFBSTtZQUNGLDBCQUEwQjtZQUMxQixNQUFNd0IsU0FBU3hCLElBQUlFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO1lBRS9CLElBQUksQ0FBQ3FCLFFBQVE7Z0JBQ1gsT0FBTzdCLHFEQUFZQSxDQUFDWSxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUFtQixHQUM1QjtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtZQUVBLGlCQUFpQjtZQUNqQixNQUFNZ0IsU0FBUyxNQUFNN0Isa0RBQVdBLENBQUM4QixZQUFZLENBQUNGO1lBQzlDLElBQUksQ0FBQ0MsUUFBUTtnQkFDWCxPQUFPOUIscURBQVlBLENBQUNZLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQWtCLEdBQzNCO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsMkJBQTJCO1lBQzNCVCxJQUFJWSxJQUFJLEdBQUc7Z0JBQ1RDLFFBQVFZLE9BQU9iLElBQUksQ0FBQ2UsRUFBRTtnQkFDdEJDLE9BQU9ILE9BQU9iLElBQUksQ0FBQ2dCLEtBQUs7Z0JBQ3hCQyxNQUFNSixPQUFPYixJQUFJLENBQUNpQixJQUFJO2dCQUN0QkMsTUFBTUwsT0FBT2IsSUFBSSxDQUFDa0IsSUFBSTtZQUN4QjtZQUNBOUIsSUFBSWEsTUFBTSxHQUFHWSxPQUFPYixJQUFJLENBQUNlLEVBQUU7WUFFM0IsK0JBQStCO1lBQy9CLE1BQU1iLFlBQVksTUFBTWxCLGtEQUFXQSxDQUFDbUIsY0FBYyxDQUFDVSxPQUFPYixJQUFJLENBQUNlLEVBQUUsRUFBRUYsT0FBT00sS0FBSztZQUMvRSxJQUFJLENBQUNqQixVQUFVRSxPQUFPLEVBQUU7Z0JBQ3RCLE9BQU9yQixxREFBWUEsQ0FBQ1ksSUFBSSxDQUN0QjtvQkFDRUMsT0FBTztvQkFDUFMsV0FBV0gsVUFBVUcsU0FBUztnQkFDaEMsR0FDQTtvQkFBRVIsUUFBUTtnQkFBSTtZQUVsQjtZQUVBLHlCQUF5QjtZQUN6QixNQUFNUyxXQUFXLE1BQU1uQixRQUFRQztZQUMvQmtCLFNBQVNoQixPQUFPLENBQUNpQixHQUFHLENBQUMseUJBQXlCTCxVQUFVTSxTQUFTLENBQUNDLFFBQVE7WUFDMUVILFNBQVNoQixPQUFPLENBQUNpQixHQUFHLENBQUMscUJBQXFCTCxVQUFVRyxTQUFTLENBQUNJLFFBQVE7WUFFdEUsT0FBT0g7UUFDVCxFQUFFLE9BQU9WLE9BQU87WUFDZGMsUUFBUWQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsT0FBT2IscURBQVlBLENBQUNZLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBNEIsR0FDckM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtJQUNGO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDL0IsU0FBU3VCLFNBQVNDLEtBQWlCO0lBQ3hDLE9BQU8sU0FBU2xDLE9BQTZEO1FBQzNFLE9BQU8sT0FBT0M7WUFDWixJQUFJLENBQUNBLElBQUlZLElBQUksRUFBRTtnQkFDYixPQUFPakIscURBQVlBLENBQUNZLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQTBCLEdBQ25DO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsSUFBSSxDQUFDd0IsTUFBTUMsUUFBUSxDQUFDbEMsSUFBSVksSUFBSSxDQUFDaUIsSUFBSSxHQUFHO2dCQUNsQyxPQUFPbEMscURBQVlBLENBQUNZLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQTJCLEdBQ3BDO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsT0FBT1YsUUFBUUM7UUFDakI7SUFDRjtBQUNGO0FBRUEsd0JBQXdCO0FBQ2pCLFNBQVNtQyxVQUFVcEMsT0FBNkQ7SUFDckYsT0FBT2lDLFNBQVM7UUFBQ25DLGlEQUFRQSxDQUFDdUMsS0FBSztRQUFFdkMsaURBQVFBLENBQUN3QyxXQUFXO0tBQUMsRUFBRXRDO0FBQzFEO0FBRUEsOEJBQThCO0FBQ3ZCLFNBQVN1QyxlQUFldkMsT0FBNkQ7SUFDMUYsT0FBT2lDLFNBQVM7UUFBQ25DLGlEQUFRQSxDQUFDd0MsV0FBVztLQUFDLEVBQUV0QztBQUMxQztBQUVBLDJEQUEyRDtBQUNwRCxTQUFTd0MsaUJBQWlCeEMsT0FBNkQ7SUFDNUYsT0FBTyxPQUFPQztRQUNaLGdCQUFnQjtRQUNoQixNQUFNQyxhQUFhRCxJQUFJRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUNuQyxNQUFNQyxRQUFRSCxZQUFZSSxXQUFXLGFBQWFKLFdBQVdLLFNBQVMsQ0FBQyxLQUFLO1FBRTVFLElBQUlGLE9BQU87WUFDVCxNQUFNTSxVQUFVZCxrREFBV0EsQ0FBQ2UsV0FBVyxDQUFDUDtZQUN4QyxJQUFJTSxTQUFTO2dCQUNYVixJQUFJWSxJQUFJLEdBQUdGO2dCQUNYVixJQUFJYSxNQUFNLEdBQUdILFFBQVFHLE1BQU07Z0JBQzNCLE9BQU9kLFFBQVFDO1lBQ2pCO1FBQ0Y7UUFFQSxjQUFjO1FBQ2QsTUFBTXdCLFNBQVN4QixJQUFJRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUMvQixJQUFJcUIsUUFBUTtZQUNWLE1BQU1DLFNBQVMsTUFBTTdCLGtEQUFXQSxDQUFDOEIsWUFBWSxDQUFDRjtZQUM5QyxJQUFJQyxRQUFRO2dCQUNWekIsSUFBSVksSUFBSSxHQUFHO29CQUNUQyxRQUFRWSxPQUFPYixJQUFJLENBQUNlLEVBQUU7b0JBQ3RCQyxPQUFPSCxPQUFPYixJQUFJLENBQUNnQixLQUFLO29CQUN4QkMsTUFBTUosT0FBT2IsSUFBSSxDQUFDaUIsSUFBSTtvQkFDdEJDLE1BQU1MLE9BQU9iLElBQUksQ0FBQ2tCLElBQUk7Z0JBQ3hCO2dCQUNBOUIsSUFBSWEsTUFBTSxHQUFHWSxPQUFPYixJQUFJLENBQUNlLEVBQUU7Z0JBQzNCLE9BQU81QixRQUFRQztZQUNqQjtRQUNGO1FBRUEsT0FBT0wscURBQVlBLENBQUNZLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUEwQixHQUNuQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDRGQUE0RjtBQUNyRixTQUFTK0IsaUJBQWlCekMsT0FBNkQ7SUFDNUYsT0FBTyxPQUFPQztRQUNaLElBQUk7WUFDRixnQkFBZ0I7WUFDaEIsTUFBTUMsYUFBYUQsSUFBSUUsT0FBTyxDQUFDQyxHQUFHLENBQUM7WUFDbkMsTUFBTUMsUUFBUUgsWUFBWUksV0FBVyxhQUFhSixXQUFXSyxTQUFTLENBQUMsS0FBSztZQUU1RSxJQUFJRixPQUFPO2dCQUNULE1BQU1NLFVBQVVkLGtEQUFXQSxDQUFDZSxXQUFXLENBQUNQO2dCQUN4QyxJQUFJTSxTQUFTO29CQUNYVixJQUFJWSxJQUFJLEdBQUdGO29CQUNYVixJQUFJYSxNQUFNLEdBQUdILFFBQVFHLE1BQU07Z0JBQzdCO1lBQ0YsT0FBTztnQkFDTCxjQUFjO2dCQUNkLE1BQU1XLFNBQVN4QixJQUFJRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztnQkFDL0IsSUFBSXFCLFFBQVE7b0JBQ1YsTUFBTUMsU0FBUyxNQUFNN0Isa0RBQVdBLENBQUM4QixZQUFZLENBQUNGO29CQUM5QyxJQUFJQyxRQUFRO3dCQUNWekIsSUFBSVksSUFBSSxHQUFHOzRCQUNUQyxRQUFRWSxPQUFPYixJQUFJLENBQUNlLEVBQUU7NEJBQ3RCQyxPQUFPSCxPQUFPYixJQUFJLENBQUNnQixLQUFLOzRCQUN4QkMsTUFBTUosT0FBT2IsSUFBSSxDQUFDaUIsSUFBSTs0QkFDdEJDLE1BQU1MLE9BQU9iLElBQUksQ0FBQ2tCLElBQUk7d0JBQ3hCO3dCQUNBOUIsSUFBSWEsTUFBTSxHQUFHWSxPQUFPYixJQUFJLENBQUNlLEVBQUU7b0JBQzdCO2dCQUNGO1lBQ0Y7WUFFQSxPQUFPNUIsUUFBUUM7UUFDakIsRUFBRSxPQUFPUSxPQUFPO1lBQ2RjLFFBQVFkLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE9BQU9ULFFBQVFDO1FBQ2pCO0lBQ0Y7QUFDRjtBQUVBLGtCQUFrQjtBQUNYLFNBQVN5QyxTQUFTMUMsT0FBb0Q7SUFDM0UsT0FBTyxPQUFPQztRQUNaLDRCQUE0QjtRQUM1QixJQUFJQSxJQUFJMEMsTUFBTSxLQUFLLFdBQVc7WUFDNUIsT0FBTyxJQUFJL0MscURBQVlBLENBQUMsTUFBTTtnQkFDNUJjLFFBQVE7Z0JBQ1JQLFNBQVM7b0JBQ1AsK0JBQStCO29CQUMvQixnQ0FBZ0M7b0JBQ2hDLGdDQUFnQztvQkFDaEMsMEJBQTBCO2dCQUM1QjtZQUNGO1FBQ0Y7UUFFQSxNQUFNZ0IsV0FBVyxNQUFNbkIsUUFBUUM7UUFFL0IsK0JBQStCO1FBQy9Ca0IsU0FBU2hCLE9BQU8sQ0FBQ2lCLEdBQUcsQ0FBQywrQkFBK0I7UUFDcERELFNBQVNoQixPQUFPLENBQUNpQixHQUFHLENBQUMsZ0NBQWdDO1FBQ3JERCxTQUFTaEIsT0FBTyxDQUFDaUIsR0FBRyxDQUFDLGdDQUFnQztRQUVyRCxPQUFPRDtJQUNUO0FBQ0Y7QUFFQSw0QkFBNEI7QUFDckIsU0FBU3lCLGtCQUFrQjVDLE9BQW9EO0lBQ3BGLE9BQU8sT0FBT0M7UUFDWixJQUFJO1lBQ0YsT0FBTyxNQUFNRCxRQUFRQztRQUN2QixFQUFFLE9BQU9RLE9BQU87WUFDZGMsUUFBUWQsS0FBSyxDQUFDLGNBQWNBO1lBRTVCLElBQUlBLGlCQUFpQm9DLE9BQU87Z0JBQzFCLE9BQU9qRCxxREFBWUEsQ0FBQ1ksSUFBSSxDQUN0QjtvQkFDRUMsT0FBTztvQkFDUHFDLFNBQVNDLEtBQXlCLEdBQWdCdEMsTUFBTXFDLE9BQU8sR0FBR0UsQ0FBU0E7Z0JBQzdFLEdBQ0E7b0JBQUV0QyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsT0FBT2QscURBQVlBLENBQUNZLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBd0IsR0FDakM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtJQUNGO0FBQ0Y7QUFFQSwrQkFBK0I7QUFDeEIsU0FBU3VDLG1CQUFtQixHQUFHQyxXQUFrQjtJQUN0RCxPQUFPLENBQUNsRDtRQUNOLE9BQU9rRCxZQUFZQyxXQUFXLENBQUMsQ0FBQ0MsS0FBS0MsYUFBZUEsV0FBV0QsTUFBTXBEO0lBQ3ZFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbWlkZGxld2FyZXMvYXV0aC50cz80MjBlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCB7IEF1dGhTZXJ2aWNlLCBUb2tlblBheWxvYWQgfSBmcm9tICdAL2xpYi9hdXRoJ1xuaW1wb3J0IHsgVXNlclJvbGUgfSBmcm9tICdAL3R5cGVzL3VzZXInXG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aGVudGljYXRlZFJlcXVlc3QgZXh0ZW5kcyBOZXh0UmVxdWVzdCB7XG4gIHVzZXI/OiBUb2tlblBheWxvYWRcbiAgdXNlcklkPzogc3RyaW5nXG59XG5cbi8vIEF1dGhlbnRpY2F0aW9uIG1pZGRsZXdhcmVcbmV4cG9ydCBmdW5jdGlvbiB3aXRoQXV0aChoYW5kbGVyOiAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+KSB7XG4gIHJldHVybiBhc3luYyAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCB0b2tlbiBmcm9tIEF1dGhvcml6YXRpb24gaGVhZGVyXG4gICAgICBjb25zdCBhdXRoSGVhZGVyID0gcmVxLmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJylcbiAgICAgIGNvbnN0IHRva2VuID0gYXV0aEhlYWRlcj8uc3RhcnRzV2l0aCgnQmVhcmVyICcpID8gYXV0aEhlYWRlci5zdWJzdHJpbmcoNykgOiBudWxsXG5cbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICAgKVxuICAgICAgfVxuXG4gICAgICAvLyBWZXJpZnkgdG9rZW5cbiAgICAgIGNvbnN0IHBheWxvYWQgPSBBdXRoU2VydmljZS52ZXJpZnlUb2tlbih0b2tlbilcbiAgICAgIGlmICghcGF5bG9hZCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ0ludmFsaWQgb3IgZXhwaXJlZCB0b2tlbicgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICAgKVxuICAgICAgfVxuXG4gICAgICAvLyBBZGQgdXNlciBpbmZvIHRvIHJlcXVlc3RcbiAgICAgIHJlcS51c2VyID0gcGF5bG9hZFxuICAgICAgcmVxLnVzZXJJZCA9IHBheWxvYWQudXNlcklkXG5cbiAgICAgIC8vIENoZWNrIHJhdGUgbGltaXRcbiAgICAgIGNvbnN0IHJhdGVMaW1pdCA9IGF3YWl0IEF1dGhTZXJ2aWNlLmNoZWNrUmF0ZUxpbWl0KHBheWxvYWQudXNlcklkKVxuICAgICAgaWYgKCFyYXRlTGltaXQuYWxsb3dlZCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBcbiAgICAgICAgICAgIGVycm9yOiAnUmF0ZSBsaW1pdCBleGNlZWRlZCcsXG4gICAgICAgICAgICByZXNldFRpbWU6IHJhdGVMaW1pdC5yZXNldFRpbWVcbiAgICAgICAgICB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MjkgfVxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCByYXRlIGxpbWl0IGhlYWRlcnNcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaGFuZGxlcihyZXEpXG4gICAgICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1SYXRlTGltaXQtUmVtYWluaW5nJywgcmF0ZUxpbWl0LnJlbWFpbmluZy50b1N0cmluZygpKVxuICAgICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1gtUmF0ZUxpbWl0LVJlc2V0JywgcmF0ZUxpbWl0LnJlc2V0VGltZS50b1N0cmluZygpKVxuXG4gICAgICByZXR1cm4gcmVzcG9uc2VcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aCBtaWRkbGV3YXJlIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQXV0aGVudGljYXRpb24gZmFpbGVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgIClcbiAgICB9XG4gIH1cbn1cblxuLy8gQVBJIEtleSBhdXRoZW50aWNhdGlvbiBtaWRkbGV3YXJlXG5leHBvcnQgZnVuY3Rpb24gd2l0aEFwaUtleShoYW5kbGVyOiAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+KSB7XG4gIHJldHVybiBhc3luYyAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCBBUEkga2V5IGZyb20gaGVhZGVyXG4gICAgICBjb25zdCBhcGlLZXkgPSByZXEuaGVhZGVycy5nZXQoJ3gtYXBpLWtleScpXG5cbiAgICAgIGlmICghYXBpS2V5KSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnQVBJIGtleSByZXF1aXJlZCcgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICAgKVxuICAgICAgfVxuXG4gICAgICAvLyBWZXJpZnkgQVBJIGtleVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgQXV0aFNlcnZpY2UudmVyaWZ5QXBpS2V5KGFwaUtleSlcbiAgICAgIGlmICghcmVzdWx0KSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnSW52YWxpZCBBUEkga2V5JyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCB1c2VyIGluZm8gdG8gcmVxdWVzdFxuICAgICAgcmVxLnVzZXIgPSB7XG4gICAgICAgIHVzZXJJZDogcmVzdWx0LnVzZXIuaWQsXG4gICAgICAgIGVtYWlsOiByZXN1bHQudXNlci5lbWFpbCxcbiAgICAgICAgcm9sZTogcmVzdWx0LnVzZXIucm9sZSxcbiAgICAgICAgcGxhbjogcmVzdWx0LnVzZXIucGxhblxuICAgICAgfVxuICAgICAgcmVxLnVzZXJJZCA9IHJlc3VsdC51c2VyLmlkXG5cbiAgICAgIC8vIENoZWNrIHJhdGUgbGltaXQgZm9yIEFQSSBrZXlcbiAgICAgIGNvbnN0IHJhdGVMaW1pdCA9IGF3YWl0IEF1dGhTZXJ2aWNlLmNoZWNrUmF0ZUxpbWl0KHJlc3VsdC51c2VyLmlkLCByZXN1bHQua2V5SWQpXG4gICAgICBpZiAoIXJhdGVMaW1pdC5hbGxvd2VkKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IFxuICAgICAgICAgICAgZXJyb3I6ICdBUEkgcmF0ZSBsaW1pdCBleGNlZWRlZCcsXG4gICAgICAgICAgICByZXNldFRpbWU6IHJhdGVMaW1pdC5yZXNldFRpbWVcbiAgICAgICAgICB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MjkgfVxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCByYXRlIGxpbWl0IGhlYWRlcnNcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaGFuZGxlcihyZXEpXG4gICAgICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1SYXRlTGltaXQtUmVtYWluaW5nJywgcmF0ZUxpbWl0LnJlbWFpbmluZy50b1N0cmluZygpKVxuICAgICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1gtUmF0ZUxpbWl0LVJlc2V0JywgcmF0ZUxpbWl0LnJlc2V0VGltZS50b1N0cmluZygpKVxuXG4gICAgICByZXR1cm4gcmVzcG9uc2VcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQVBJIGtleSBtaWRkbGV3YXJlIGVycm9yOicsIGVycm9yKVxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQVBJIGF1dGhlbnRpY2F0aW9uIGZhaWxlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApXG4gICAgfVxuICB9XG59XG5cbi8vIFJvbGUtYmFzZWQgYXV0aG9yaXphdGlvbiBtaWRkbGV3YXJlXG5leHBvcnQgZnVuY3Rpb24gd2l0aFJvbGUocm9sZXM6IFVzZXJSb2xlW10pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKGhhbmRsZXI6IChyZXE6IEF1dGhlbnRpY2F0ZWRSZXF1ZXN0KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZT4pIHtcbiAgICByZXR1cm4gYXN5bmMgKHJlcTogQXV0aGVudGljYXRlZFJlcXVlc3QpOiBQcm9taXNlPE5leHRSZXNwb25zZT4gPT4ge1xuICAgICAgaWYgKCFyZXEudXNlcikge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIGlmICghcm9sZXMuaW5jbHVkZXMocmVxLnVzZXIucm9sZSkpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdJbnN1ZmZpY2llbnQgcGVybWlzc2lvbnMnIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMyB9XG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGhhbmRsZXIocmVxKVxuICAgIH1cbiAgfVxufVxuXG4vLyBBZG1pbiBvbmx5IG1pZGRsZXdhcmVcbmV4cG9ydCBmdW5jdGlvbiB3aXRoQWRtaW4oaGFuZGxlcjogKHJlcTogQXV0aGVudGljYXRlZFJlcXVlc3QpID0+IFByb21pc2U8TmV4dFJlc3BvbnNlPikge1xuICByZXR1cm4gd2l0aFJvbGUoW1VzZXJSb2xlLkFETUlOLCBVc2VyUm9sZS5TVVBFUl9BRE1JTl0pKGhhbmRsZXIpXG59XG5cbi8vIFN1cGVyIGFkbWluIG9ubHkgbWlkZGxld2FyZVxuZXhwb3J0IGZ1bmN0aW9uIHdpdGhTdXBlckFkbWluKGhhbmRsZXI6IChyZXE6IEF1dGhlbnRpY2F0ZWRSZXF1ZXN0KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZT4pIHtcbiAgcmV0dXJuIHdpdGhSb2xlKFtVc2VyUm9sZS5TVVBFUl9BRE1JTl0pKGhhbmRsZXIpXG59XG5cbi8vIENvbWJpbmVkIGF1dGggbWlkZGxld2FyZSAoc3VwcG9ydHMgYm90aCBKV1QgYW5kIEFQSSBrZXkpXG5leHBvcnQgZnVuY3Rpb24gd2l0aEF1dGhPckFwaUtleShoYW5kbGVyOiAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+KSB7XG4gIHJldHVybiBhc3luYyAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiA9PiB7XG4gICAgLy8gVHJ5IEpXVCBmaXJzdFxuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSByZXEuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKVxuICAgIGNvbnN0IHRva2VuID0gYXV0aEhlYWRlcj8uc3RhcnRzV2l0aCgnQmVhcmVyICcpID8gYXV0aEhlYWRlci5zdWJzdHJpbmcoNykgOiBudWxsXG5cbiAgICBpZiAodG9rZW4pIHtcbiAgICAgIGNvbnN0IHBheWxvYWQgPSBBdXRoU2VydmljZS52ZXJpZnlUb2tlbih0b2tlbilcbiAgICAgIGlmIChwYXlsb2FkKSB7XG4gICAgICAgIHJlcS51c2VyID0gcGF5bG9hZFxuICAgICAgICByZXEudXNlcklkID0gcGF5bG9hZC51c2VySWRcbiAgICAgICAgcmV0dXJuIGhhbmRsZXIocmVxKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFRyeSBBUEkga2V5XG4gICAgY29uc3QgYXBpS2V5ID0gcmVxLmhlYWRlcnMuZ2V0KCd4LWFwaS1rZXknKVxuICAgIGlmIChhcGlLZXkpIHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IEF1dGhTZXJ2aWNlLnZlcmlmeUFwaUtleShhcGlLZXkpXG4gICAgICBpZiAocmVzdWx0KSB7XG4gICAgICAgIHJlcS51c2VyID0ge1xuICAgICAgICAgIHVzZXJJZDogcmVzdWx0LnVzZXIuaWQsXG4gICAgICAgICAgZW1haWw6IHJlc3VsdC51c2VyLmVtYWlsLFxuICAgICAgICAgIHJvbGU6IHJlc3VsdC51c2VyLnJvbGUsXG4gICAgICAgICAgcGxhbjogcmVzdWx0LnVzZXIucGxhblxuICAgICAgICB9XG4gICAgICAgIHJlcS51c2VySWQgPSByZXN1bHQudXNlci5pZFxuICAgICAgICByZXR1cm4gaGFuZGxlcihyZXEpXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyB9LFxuICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgKVxuICB9XG59XG5cbi8vIE9wdGlvbmFsIGF1dGggbWlkZGxld2FyZSAoZG9lc24ndCByZXF1aXJlIGF1dGhlbnRpY2F0aW9uIGJ1dCBhZGRzIHVzZXIgaW5mbyBpZiBhdmFpbGFibGUpXG5leHBvcnQgZnVuY3Rpb24gd2l0aE9wdGlvbmFsQXV0aChoYW5kbGVyOiAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+KSB7XG4gIHJldHVybiBhc3luYyAocmVxOiBBdXRoZW50aWNhdGVkUmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRyeSBKV1QgZmlyc3RcbiAgICAgIGNvbnN0IGF1dGhIZWFkZXIgPSByZXEuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKVxuICAgICAgY29uc3QgdG9rZW4gPSBhdXRoSGVhZGVyPy5zdGFydHNXaXRoKCdCZWFyZXIgJykgPyBhdXRoSGVhZGVyLnN1YnN0cmluZyg3KSA6IG51bGxcblxuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSBBdXRoU2VydmljZS52ZXJpZnlUb2tlbih0b2tlbilcbiAgICAgICAgaWYgKHBheWxvYWQpIHtcbiAgICAgICAgICByZXEudXNlciA9IHBheWxvYWRcbiAgICAgICAgICByZXEudXNlcklkID0gcGF5bG9hZC51c2VySWRcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gVHJ5IEFQSSBrZXlcbiAgICAgICAgY29uc3QgYXBpS2V5ID0gcmVxLmhlYWRlcnMuZ2V0KCd4LWFwaS1rZXknKVxuICAgICAgICBpZiAoYXBpS2V5KSB7XG4gICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgQXV0aFNlcnZpY2UudmVyaWZ5QXBpS2V5KGFwaUtleSlcbiAgICAgICAgICBpZiAocmVzdWx0KSB7XG4gICAgICAgICAgICByZXEudXNlciA9IHtcbiAgICAgICAgICAgICAgdXNlcklkOiByZXN1bHQudXNlci5pZCxcbiAgICAgICAgICAgICAgZW1haWw6IHJlc3VsdC51c2VyLmVtYWlsLFxuICAgICAgICAgICAgICByb2xlOiByZXN1bHQudXNlci5yb2xlLFxuICAgICAgICAgICAgICBwbGFuOiByZXN1bHQudXNlci5wbGFuXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXEudXNlcklkID0gcmVzdWx0LnVzZXIuaWRcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGhhbmRsZXIocmVxKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdPcHRpb25hbCBhdXRoIG1pZGRsZXdhcmUgZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4gaGFuZGxlcihyZXEpXG4gICAgfVxuICB9XG59XG5cbi8vIENPUlMgbWlkZGxld2FyZVxuZXhwb3J0IGZ1bmN0aW9uIHdpdGhDb3JzKGhhbmRsZXI6IChyZXE6IE5leHRSZXF1ZXN0KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZT4pIHtcbiAgcmV0dXJuIGFzeW5jIChyZXE6IE5leHRSZXF1ZXN0KTogUHJvbWlzZTxOZXh0UmVzcG9uc2U+ID0+IHtcbiAgICAvLyBIYW5kbGUgcHJlZmxpZ2h0IHJlcXVlc3RzXG4gICAgaWYgKHJlcS5tZXRob2QgPT09ICdPUFRJT05TJykge1xuICAgICAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UobnVsbCwge1xuICAgICAgICBzdGF0dXM6IDIwMCxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1PcmlnaW4nOiAnKicsXG4gICAgICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LU1ldGhvZHMnOiAnR0VULCBQT1NULCBQVVQsIERFTEVURSwgT1BUSU9OUycsXG4gICAgICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LUhlYWRlcnMnOiAnQ29udGVudC1UeXBlLCBBdXRob3JpemF0aW9uLCBYLUFQSS1LZXknLFxuICAgICAgICAgICdBY2Nlc3MtQ29udHJvbC1NYXgtQWdlJzogJzg2NDAwJyxcbiAgICAgICAgfSxcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBoYW5kbGVyKHJlcSlcblxuICAgIC8vIEFkZCBDT1JTIGhlYWRlcnMgdG8gcmVzcG9uc2VcbiAgICByZXNwb25zZS5oZWFkZXJzLnNldCgnQWNjZXNzLUNvbnRyb2wtQWxsb3ctT3JpZ2luJywgJyonKVxuICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdBY2Nlc3MtQ29udHJvbC1BbGxvdy1NZXRob2RzJywgJ0dFVCwgUE9TVCwgUFVULCBERUxFVEUsIE9QVElPTlMnKVxuICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdBY2Nlc3MtQ29udHJvbC1BbGxvdy1IZWFkZXJzJywgJ0NvbnRlbnQtVHlwZSwgQXV0aG9yaXphdGlvbiwgWC1BUEktS2V5JylcblxuICAgIHJldHVybiByZXNwb25zZVxuICB9XG59XG5cbi8vIEVycm9yIGhhbmRsaW5nIG1pZGRsZXdhcmVcbmV4cG9ydCBmdW5jdGlvbiB3aXRoRXJyb3JIYW5kbGluZyhoYW5kbGVyOiAocmVxOiBOZXh0UmVxdWVzdCkgPT4gUHJvbWlzZTxOZXh0UmVzcG9uc2U+KSB7XG4gIHJldHVybiBhc3luYyAocmVxOiBOZXh0UmVxdWVzdCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBhd2FpdCBoYW5kbGVyKHJlcSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQVBJIEVycm9yOicsIGVycm9yKVxuICAgICAgXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBcbiAgICAgICAgICAgIGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyxcbiAgICAgICAgICAgIG1lc3NhZ2U6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gZXJyb3IubWVzc2FnZSA6IHVuZGVmaW5lZFxuICAgICAgICAgIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgIClcbiAgICB9XG4gIH1cbn1cblxuLy8gQ29tYmluZSBtdWx0aXBsZSBtaWRkbGV3YXJlc1xuZXhwb3J0IGZ1bmN0aW9uIGNvbWJpbmVNaWRkbGV3YXJlcyguLi5taWRkbGV3YXJlczogYW55W10pIHtcbiAgcmV0dXJuIChoYW5kbGVyOiBhbnkpID0+IHtcbiAgICByZXR1cm4gbWlkZGxld2FyZXMucmVkdWNlUmlnaHQoKGFjYywgbWlkZGxld2FyZSkgPT4gbWlkZGxld2FyZShhY2MpLCBoYW5kbGVyKVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQXV0aFNlcnZpY2UiLCJVc2VyUm9sZSIsIndpdGhBdXRoIiwiaGFuZGxlciIsInJlcSIsImF1dGhIZWFkZXIiLCJoZWFkZXJzIiwiZ2V0IiwidG9rZW4iLCJzdGFydHNXaXRoIiwic3Vic3RyaW5nIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwicGF5bG9hZCIsInZlcmlmeVRva2VuIiwidXNlciIsInVzZXJJZCIsInJhdGVMaW1pdCIsImNoZWNrUmF0ZUxpbWl0IiwiYWxsb3dlZCIsInJlc2V0VGltZSIsInJlc3BvbnNlIiwic2V0IiwicmVtYWluaW5nIiwidG9TdHJpbmciLCJjb25zb2xlIiwid2l0aEFwaUtleSIsImFwaUtleSIsInJlc3VsdCIsInZlcmlmeUFwaUtleSIsImlkIiwiZW1haWwiLCJyb2xlIiwicGxhbiIsImtleUlkIiwid2l0aFJvbGUiLCJyb2xlcyIsImluY2x1ZGVzIiwid2l0aEFkbWluIiwiQURNSU4iLCJTVVBFUl9BRE1JTiIsIndpdGhTdXBlckFkbWluIiwid2l0aEF1dGhPckFwaUtleSIsIndpdGhPcHRpb25hbEF1dGgiLCJ3aXRoQ29ycyIsIm1ldGhvZCIsIndpdGhFcnJvckhhbmRsaW5nIiwiRXJyb3IiLCJtZXNzYWdlIiwicHJvY2VzcyIsInVuZGVmaW5lZCIsImNvbWJpbmVNaWRkbGV3YXJlcyIsIm1pZGRsZXdhcmVzIiwicmVkdWNlUmlnaHQiLCJhY2MiLCJtaWRkbGV3YXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./middlewares/auth.ts\n");

/***/ }),

/***/ "(rsc)/./types/user.ts":
/*!***********************!*\
  !*** ./types/user.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserPlan: () => (/* binding */ UserPlan),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"USER\"] = \"user\";\n})(UserRole || (UserRole = {}));\nvar UserPlan;\n(function(UserPlan) {\n    UserPlan[\"FREE\"] = \"free\";\n    UserPlan[\"STUDENT\"] = \"student\";\n    UserPlan[\"HOBBY\"] = \"hobby\";\n    UserPlan[\"BUGHUNTER\"] = \"bughunter\";\n    UserPlan[\"CYBERSECURITY\"] = \"cybersecurity\";\n})(UserPlan || (UserPlan = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./types/user.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/semver","vendor-chunks/@opentelemetry","vendor-chunks/next","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();