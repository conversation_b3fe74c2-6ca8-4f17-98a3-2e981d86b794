-- KodeXGuard Complete Database Schema
-- This creates a fully functional cybersecurity platform database

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS db_kodexguard;
USE db_kodexguard;

-- Set charset and collation
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- USER MANAGEMENT TABLES
-- =====================================================

-- Users table - Core user information
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    plan ENUM('Free', 'Pro', 'Expert', 'Elite') DEFAULT 'Free',
    level INT DEFAULT 1,
    score INT DEFAULT 0,
    streak_days INT DEFAULT 0,
    email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_plan (plan),
    INDEX idx_level (level),
    INDEX idx_score (score)
);

-- User sessions for authentication
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(500) NOT NULL,
    refresh_token VARCHAR(500),
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token(255)),
    INDEX idx_expires_at (expires_at)
);

-- User preferences and settings
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    theme ENUM('light', 'dark', 'cyberpunk') DEFAULT 'cyberpunk',
    language VARCHAR(10) DEFAULT 'en',
    notifications_email BOOLEAN DEFAULT TRUE,
    notifications_browser BOOLEAN DEFAULT TRUE,
    privacy_level ENUM('public', 'private', 'friends') DEFAULT 'private',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
);

-- =====================================================
-- SUBSCRIPTION & BILLING TABLES
-- =====================================================

-- Subscription plans
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    billing_cycle ENUM('monthly', 'yearly') NOT NULL,
    features JSON,
    limits JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User subscriptions
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    status ENUM('active', 'cancelled', 'expired', 'pending') DEFAULT 'pending',
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_ends_at (ends_at)
);

-- =====================================================
-- SECURITY OPERATIONS TABLES
-- =====================================================

-- Vulnerability scans
CREATE TABLE IF NOT EXISTS vulnerability_scans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    target_url VARCHAR(2048) NOT NULL,
    scan_type ENUM('basic', 'advanced', 'comprehensive') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    vulnerabilities_found INT DEFAULT 0,
    severity_critical INT DEFAULT 0,
    severity_high INT DEFAULT 0,
    severity_medium INT DEFAULT 0,
    severity_low INT DEFAULT 0,
    scan_results JSON,
    error_message TEXT,
    scan_duration INT, -- in seconds
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_scan_type (scan_type),
    INDEX idx_created_at (created_at),
    INDEX idx_target_url (target_url(255))
);

-- OSINT queries
CREATE TABLE IF NOT EXISTS osint_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    query_type ENUM('email', 'domain', 'ip', 'username', 'phone', 'company') NOT NULL,
    query_value VARCHAR(500) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    results JSON,
    sources_used JSON,
    confidence_score DECIMAL(3,2),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_query_type (query_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_query_value (query_value(255))
);

-- File analyses
CREATE TABLE IF NOT EXISTS file_analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100),
    file_hash_md5 VARCHAR(32),
    file_hash_sha256 VARCHAR(64),
    analysis_type ENUM('malware', 'webshell', 'suspicious', 'comprehensive') NOT NULL,
    status ENUM('pending', 'analyzing', 'completed', 'failed') DEFAULT 'pending',
    threat_detected BOOLEAN DEFAULT FALSE,
    threat_type VARCHAR(100),
    threat_family VARCHAR(100),
    confidence_score DECIMAL(3,2),
    analysis_results JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_threat_detected (threat_detected),
    INDEX idx_status (status),
    INDEX idx_file_hash_sha256 (file_hash_sha256),
    INDEX idx_created_at (created_at)
);

-- Google dorking queries
CREATE TABLE IF NOT EXISTS dorking_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    query_string TEXT NOT NULL,
    search_engine ENUM('google', 'bing', 'duckduckgo') DEFAULT 'google',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    results_found INT DEFAULT 0,
    results JSON,
    risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_risk_level (risk_level),
    INDEX idx_created_at (created_at)
);

-- CVE database
CREATE TABLE IF NOT EXISTS cve_database (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cve_id VARCHAR(20) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
    cvss_score DECIMAL(3,1),
    cvss_vector VARCHAR(200),
    cwe_id VARCHAR(20),
    published_date DATE,
    modified_date DATE,
    exploit_available BOOLEAN DEFAULT FALSE,
    patch_available BOOLEAN DEFAULT FALSE,
    affected_products JSON,
    references JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_cve_id (cve_id),
    INDEX idx_severity (severity),
    INDEX idx_cvss_score (cvss_score),
    INDEX idx_published_date (published_date),
    INDEX idx_exploit_available (exploit_available)
);

-- =====================================================
-- GAMIFICATION & ACHIEVEMENTS
-- =====================================================

-- Badges/Achievements master table
CREATE TABLE IF NOT EXISTS badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(100),
    color VARCHAR(20),
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common',
    category ENUM('scanning', 'osint', 'analysis', 'general', 'special') NOT NULL,
    requirements JSON,
    points_reward INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User badges (earned achievements)
CREATE TABLE IF NOT EXISTS user_badges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    badge_id INT NOT NULL,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_data JSON,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES badges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_badge (user_id, badge_id),
    INDEX idx_user_id (user_id),
    INDEX idx_earned_at (earned_at)
);

-- User activity log for points and streaks
CREATE TABLE IF NOT EXISTS user_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type ENUM('vulnerability_scan', 'osint_query', 'file_analysis', 'cve_search', 'dorking_query', 'login', 'achievement') NOT NULL,
    description TEXT,
    points_earned INT DEFAULT 0,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at),
    INDEX idx_points_earned (points_earned)
);

-- =====================================================
-- SYSTEM MANAGEMENT TABLES
-- =====================================================

-- Bot instances for automation
CREATE TABLE IF NOT EXISTS bot_instances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    type ENUM('scanner', 'osint', 'monitor', 'crawler', 'analyzer') NOT NULL,
    status ENUM('running', 'stopped', 'error', 'maintenance') DEFAULT 'stopped',
    version VARCHAR(20),
    tasks_completed INT DEFAULT 0,
    tasks_queued INT DEFAULT 0,
    tasks_failed INT DEFAULT 0,
    cpu_usage DECIMAL(5,2) DEFAULT 0,
    memory_usage DECIMAL(5,2) DEFAULT 0,
    uptime_seconds BIGINT DEFAULT 0,
    last_activity TIMESTAMP NULL,
    configuration JSON,
    error_log TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_last_activity (last_activity)
);

-- System logs
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
    category VARCHAR(50),
    message TEXT NOT NULL,
    context JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_category (category),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
);

-- API usage tracking
CREATE TABLE IF NOT EXISTS api_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    endpoint VARCHAR(255) NOT NULL,
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
    status_code INT NOT NULL,
    response_time_ms INT,
    request_size BIGINT,
    response_size BIGINT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_status_code (status_code),
    INDEX idx_created_at (created_at)
);

-- System settings
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- =====================================================
-- SECURITY & MONITORING TABLES
-- =====================================================

-- Security alerts
CREATE TABLE IF NOT EXISTS security_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alert_type ENUM('failed_login', 'suspicious_activity', 'rate_limit', 'malware_detected', 'vulnerability_found') NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INT NULL,
    ip_address VARCHAR(45),
    metadata JSON,
    status ENUM('open', 'investigating', 'resolved', 'false_positive') DEFAULT 'open',
    resolved_at TIMESTAMP NULL,
    resolved_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_alert_type (alert_type),
    INDEX idx_severity (severity),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
);

-- Rate limiting tracking
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL, -- IP address or user ID
    identifier_type ENUM('ip', 'user') NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    requests_count INT DEFAULT 1,
    window_start TIMESTAMP NOT NULL,
    window_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_endpoint (endpoint),
    INDEX idx_window_end (window_end)
);

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- DEFAULT DATA INSERTION
-- =====================================================

-- Insert default subscription plans
INSERT IGNORE INTO subscription_plans (name, price, currency, billing_cycle, features, limits) VALUES
('Free', 0.00, 'IDR', 'monthly',
 '["Basic vulnerability scanning", "Limited OSINT queries", "File analysis up to 10MB"]',
 '{"vulnerabilityScans": 10, "osintQueries": 5, "fileAnalyses": 3, "dorkingQueries": 2, "cveSearches": 10, "maxFileSize": 10485760}'),
('Pro', 29.99, 'IDR', 'monthly',
 '["Advanced vulnerability scanning", "Enhanced OSINT queries", "File analysis up to 100MB", "Priority support"]',
 '{"vulnerabilityScans": 100, "osintQueries": 50, "fileAnalyses": 25, "dorkingQueries": 20, "cveSearches": 100, "maxFileSize": *********}'),
('Expert', 79.99, 'IDR', 'monthly',
 '["Comprehensive scanning", "Unlimited OSINT", "Large file analysis", "API access", "Custom reports"]',
 '{"vulnerabilityScans": 500, "osintQueries": 200, "fileAnalyses": 100, "dorkingQueries": 100, "cveSearches": 500, "maxFileSize": 524288000}'),
('Elite', 199.99, 'IDR', 'monthly',
 '["Enterprise features", "Unlimited everything", "White-label reports", "Dedicated support", "Custom integrations"]',
 '{"vulnerabilityScans": -1, "osintQueries": -1, "fileAnalyses": -1, "dorkingQueries": -1, "cveSearches": -1, "maxFileSize": 1073741824}');

-- Insert default badges/achievements
INSERT IGNORE INTO badges (name, description, icon, color, rarity, category, requirements, points_reward) VALUES
('First Scan', 'Complete your first vulnerability scan', 'shield-check', '#10B981', 'common', 'scanning', '{"scans": 1}', 10),
('Scanner Novice', 'Complete 10 vulnerability scans', 'target', '#3B82F6', 'common', 'scanning', '{"scans": 10}', 25),
('Scanner Pro', 'Complete 100 vulnerability scans', 'zap', '#8B5CF6', 'rare', 'scanning', '{"scans": 100}', 100),
('Vulnerability Hunter', 'Find 50 vulnerabilities', 'bug', '#EF4444', 'epic', 'scanning', '{"vulnerabilities": 50}', 200),
('OSINT Rookie', 'Complete your first OSINT query', 'search', '#06B6D4', 'common', 'osint', '{"queries": 1}', 10),
('Information Gatherer', 'Complete 25 OSINT queries', 'eye', '#0EA5E9', 'rare', 'osint', '{"queries": 25}', 75),
('Digital Detective', 'Complete 100 OSINT queries', 'user-search', '#7C3AED', 'epic', 'osint', '{"queries": 100}', 150),
('File Guardian', 'Analyze your first file', 'file-text', '#F59E0B', 'common', 'analysis', '{"files": 1}', 10),
('Malware Hunter', 'Detect 10 threats in files', 'shield-alert', '#DC2626', 'rare', 'analysis', '{"threats": 10}', 100),
('Cyber Warrior', 'Reach level 25', 'crown', '#F59E0B', 'epic', 'general', '{"level": 25}', 250),
('Elite Hacker', 'Reach level 50', 'star', '#A855F7', 'legendary', 'general', '{"level": 50}', 500),
('Streak Master', 'Maintain a 30-day streak', 'flame', '#F97316', 'rare', 'general', '{"streak": 30}', 200),
('Point Collector', 'Earn 10,000 points', 'trophy', '#EAB308', 'epic', 'general', '{"points": 10000}', 300);

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('platform_name', 'KodeXGuard', 'string', 'Platform name', true),
('platform_version', '1.0.0', 'string', 'Current platform version', true),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', false),
('registration_enabled', 'true', 'boolean', 'Allow new user registrations', true),
('max_file_size', '*********', 'number', 'Maximum file upload size in bytes', false),
('rate_limit_requests', '100', 'number', 'Rate limit requests per hour', false),
('email_verification_required', 'false', 'boolean', 'Require email verification for new accounts', false),
('default_user_plan', 'Free', 'string', 'Default plan for new users', false);

-- Insert sample CVE data
INSERT IGNORE INTO cve_database (cve_id, description, severity, cvss_score, published_date, exploit_available, patch_available) VALUES
('CVE-2024-0001', 'Critical remote code execution vulnerability in popular web framework allowing attackers to execute arbitrary code', 'CRITICAL', 9.8, '2024-01-15', true, true),
('CVE-2024-0002', 'SQL injection vulnerability in database management interface allowing unauthorized data access', 'HIGH', 8.1, '2024-01-20', false, true),
('CVE-2024-0003', 'Cross-site scripting vulnerability in content management system affecting user sessions', 'MEDIUM', 6.1, '2024-01-25', true, false),
('CVE-2024-0004', 'Buffer overflow in network service leading to potential denial of service', 'HIGH', 7.5, '2024-02-01', true, true),
('CVE-2024-0005', 'Authentication bypass in admin panel allowing unauthorized access', 'CRITICAL', 9.1, '2024-02-05', false, false),
('CVE-2024-0006', 'Directory traversal vulnerability allowing file system access', 'MEDIUM', 5.3, '2024-02-10', true, true),
('CVE-2024-0007', 'Privilege escalation vulnerability in system service', 'HIGH', 8.8, '2024-02-15', false, true),
('CVE-2024-0008', 'Information disclosure vulnerability exposing sensitive data', 'MEDIUM', 4.3, '2024-02-20', false, false);

-- Insert default bot instances
INSERT IGNORE INTO bot_instances (name, type, status, version, configuration) VALUES
('VulnScanner-01', 'scanner', 'running', '1.0.0', '{"maxConcurrentScans": 5, "timeout": 300, "userAgent": "KodeXGuard-Scanner/1.0"}'),
('OSINT-Collector-01', 'osint', 'running', '1.0.0', '{"sources": ["haveibeenpwned", "shodan", "virustotal"], "timeout": 60}'),
('FileAnalyzer-01', 'analyzer', 'running', '1.0.0', '{"maxFileSize": *********, "scanEngines": ["clamav", "yara"], "timeout": 180}'),
('WebCrawler-01', 'crawler', 'stopped', '1.0.0', '{"maxDepth": 3, "respectRobots": true, "delay": 1000}'),
('ThreatMonitor-01', 'monitor', 'running', '1.0.0', '{"alertThreshold": "medium", "checkInterval": 300}');
