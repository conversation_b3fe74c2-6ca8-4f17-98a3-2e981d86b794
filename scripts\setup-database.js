const mysql = require('mysql2/promise')
const fs = require('fs').promises
const path = require('path')
const bcrypt = require('bcryptjs')

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rootkan',
  multipleStatements: true
}

async function setupDatabase() {
  let connection

  try {
    console.log('🚀 Setting up KodeXGuard database...')

    // Connect to MySQL server (without specifying database)
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to MySQL server')

    // Create database first
    console.log('📋 Creating database...')
    await connection.query('CREATE DATABASE IF NOT EXISTS db_kodexguard')
    await connection.query('USE db_kodexguard')

    // Read and execute complete schema
    console.log('🔨 Creating tables...')
    const schemaPath = path.join(__dirname, '..', 'database', 'complete-schema.sql')
    let schema = await fs.readFile(schemaPath, 'utf8')

    // Remove database creation statements since we already created it
    schema = schema.replace(/CREATE DATABASE IF NOT EXISTS db_kodexguard;/g, '')
    schema = schema.replace(/USE db_kodexguard;/g, '')

    // Split into individual statements and execute them
    const statements = schema.split(';').filter(stmt => stmt.trim().length > 0)

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.query(statement.trim())
        } catch (error) {
          // Skip errors for statements that might already exist
          if (!error.message.includes('already exists')) {
            console.log(`Warning: ${error.message}`)
          }
        }
      }
    }

    console.log('✅ Database schema created successfully')
    
    // Create admin user
    console.log('👤 Creating admin user...')
    await createAdminUser(connection)

    // Create sample users
    console.log('👥 Creating sample users...')
    await createSampleUsers(connection)

    // Create sample data
    console.log('📊 Creating sample data...')
    await createSampleData(connection)

    // Insert default data
    console.log('📊 Inserting default data...')
    await insertDefaultData(connection)
    
    console.log('✅ Default data inserted')

    // Create admin user
    console.log('👤 Creating admin user...')
    await createAdminUser(connection)
    
    console.log('✅ Admin user created')

    console.log('🎉 Database setup completed successfully!')

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function insertDefaultData(connection) {
  // Insert subscription plans
  const plans = [
    {
      name: 'Free',
      description: 'Basic security scanning for personal use',
      price: 0.00,
      billing_cycle: 'monthly',
      features: JSON.stringify([
        'Basic vulnerability scanning',
        'Limited OSINT queries',
        'Basic file analysis',
        'Community support'
      ]),
      limits: JSON.stringify({
        vulnerabilityScans: 5,
        osintQueries: 10,
        fileAnalyses: 3,
        dorkingQueries: 5,
        cveSearches: 20,
        maxFileSize: 10485760 // 10MB
      }),
      tier: 'free'
    },
    {
      name: 'Pro',
      description: 'Advanced security tools for professionals',
      price: 29.99,
      billing_cycle: 'monthly',
      features: JSON.stringify([
        'Advanced vulnerability scanning',
        'Unlimited OSINT queries',
        'Advanced file analysis',
        'Google dorking tools',
        'Priority support',
        'API access'
      ]),
      limits: JSON.stringify({
        vulnerabilityScans: 50,
        osintQueries: -1,
        fileAnalyses: 25,
        dorkingQueries: 100,
        cveSearches: -1,
        maxFileSize: ********* // 100MB
      }),
      tier: 'basic',
      popular: true
    },
    {
      name: 'Expert',
      description: 'Comprehensive security platform for experts',
      price: 79.99,
      billing_cycle: 'monthly',
      features: JSON.stringify([
        'Comprehensive vulnerability scanning',
        'Unlimited OSINT queries',
        'Advanced file analysis with sandboxing',
        'Advanced Google dorking',
        'CVE database access',
        'Custom integrations',
        'Priority support',
        'Team collaboration'
      ]),
      limits: JSON.stringify({
        vulnerabilityScans: 200,
        osintQueries: -1,
        fileAnalyses: 100,
        dorkingQueries: -1,
        cveSearches: -1,
        maxFileSize: 524288000 // 500MB
      }),
      tier: 'pro'
    },
    {
      name: 'Elite',
      description: 'Enterprise-grade security platform',
      price: 199.99,
      billing_cycle: 'monthly',
      features: JSON.stringify([
        'Unlimited everything',
        'Custom vulnerability signatures',
        'Advanced threat intelligence',
        'White-label solutions',
        'Dedicated support',
        'Custom integrations',
        'SLA guarantee',
        'Advanced analytics'
      ]),
      limits: JSON.stringify({
        vulnerabilityScans: -1,
        osintQueries: -1,
        fileAnalyses: -1,
        dorkingQueries: -1,
        cveSearches: -1,
        maxFileSize: 1073741824 // 1GB
      }),
      tier: 'enterprise'
    }
  ]

  for (const plan of plans) {
    await connection.execute(
      `INSERT INTO subscription_plans (name, description, price, billing_cycle, features, limits, tier, popular, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [plan.name, plan.description, plan.price, plan.billing_cycle, plan.features, plan.limits, plan.tier, plan.popular || false]
    )
  }

  // Insert badges
  const badges = [
    {
      name: 'First Scan',
      description: 'Completed your first vulnerability scan',
      icon: 'shield',
      color: 'blue',
      rarity: 'common',
      criteria: JSON.stringify({ scans: 1 })
    },
    {
      name: 'Scanner Pro',
      description: 'Completed 100 vulnerability scans',
      icon: 'target',
      color: 'purple',
      rarity: 'rare',
      criteria: JSON.stringify({ scans: 100 })
    },
    {
      name: 'OSINT Expert',
      description: 'Completed 50 OSINT queries',
      icon: 'search',
      color: 'green',
      rarity: 'rare',
      criteria: JSON.stringify({ osint_queries: 50 })
    },
    {
      name: 'Threat Hunter',
      description: 'Found 10 critical vulnerabilities',
      icon: 'bug',
      color: 'red',
      rarity: 'epic',
      criteria: JSON.stringify({ critical_vulns: 10 })
    },
    {
      name: 'Security Researcher',
      description: 'Analyzed 100 files for malware',
      icon: 'file',
      color: 'orange',
      rarity: 'rare',
      criteria: JSON.stringify({ file_analyses: 100 })
    },
    {
      name: 'Elite Hacker',
      description: 'Reached level 50',
      icon: 'crown',
      color: 'gold',
      rarity: 'legendary',
      criteria: JSON.stringify({ level: 50 })
    }
  ]

  for (const badge of badges) {
    await connection.execute(
      `INSERT INTO badges (name, description, icon, color, rarity, criteria, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, NOW())`,
      [badge.name, badge.description, badge.icon, badge.color, badge.rarity, badge.criteria]
    )
  }

  // Insert bot instances
  const bots = [
    {
      name: 'VulnScanner-01',
      type: 'scanner',
      status: 'running',
      config: JSON.stringify({
        maxConcurrentScans: 5,
        timeout: 300,
        userAgent: 'KodeXGuard-Scanner/1.0'
      })
    },
    {
      name: 'OSINT-Collector-01',
      type: 'osint',
      status: 'running',
      config: JSON.stringify({
        maxConcurrentQueries: 10,
        timeout: 60,
        sources: ['haveibeenpwned', 'shodan', 'virustotal']
      })
    },
    {
      name: 'FileAnalyzer-01',
      type: 'monitor',
      status: 'running',
      config: JSON.stringify({
        maxFileSize: *********,
        supportedTypes: ['exe', 'dll', 'pdf', 'doc', 'zip']
      })
    },
    {
      name: 'WebCrawler-01',
      type: 'crawler',
      status: 'stopped',
      config: JSON.stringify({
        maxDepth: 3,
        respectRobots: true,
        delay: 1000
      })
    }
  ]

  for (const bot of bots) {
    await connection.execute(
      `INSERT INTO bot_instances (name, type, status, config, created_at, updated_at) 
       VALUES (?, ?, ?, ?, NOW(), NOW())`,
      [bot.name, bot.type, bot.status, bot.config]
    )
  }

  // Insert system settings
  const settings = [
    { key: 'maintenance_mode', value: 'false', type: 'boolean', description: 'Enable maintenance mode' },
    { key: 'registration_enabled', value: 'true', type: 'boolean', description: 'Allow new user registrations' },
    { key: 'max_file_size', value: '*********', type: 'number', description: 'Maximum file upload size in bytes' },
    { key: 'rate_limit_requests', value: '100', type: 'number', description: 'Rate limit requests per window' },
    { key: 'rate_limit_window', value: '900', type: 'number', description: 'Rate limit window in seconds' },
    { key: 'email_verification_required', value: 'false', type: 'boolean', description: 'Require email verification for new accounts' },
    { key: 'default_user_plan', value: 'Free', type: 'string', description: 'Default plan for new users' },
    { key: 'session_timeout', value: '3600', type: 'number', description: 'Session timeout in seconds' }
  ]

  for (const setting of settings) {
    await connection.execute(
      `INSERT INTO system_settings (setting_key, setting_value, setting_type, description, created_at, updated_at) 
       VALUES (?, ?, ?, ?, NOW(), NOW())`,
      [setting.key, setting.value, setting.type, setting.description]
    )
  }

  console.log('✅ Default data inserted successfully')
}

async function createAdminUser(connection) {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456'
  const adminUsername = process.env.ADMIN_USERNAME || 'admin'

  // Check if admin user already exists
  const [existingAdmin] = await connection.execute(
    'SELECT id FROM users WHERE email = ? OR username = ?',
    [adminEmail, adminUsername]
  )

  if (existingAdmin.length > 0) {
    console.log('⚠️ Admin user already exists, skipping creation')
    return
  }

  // Hash password
  const passwordHash = await bcrypt.hash(adminPassword, 12)

  // Create admin user
  const [result] = await connection.execute(
    `INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, email_verified, created_at, updated_at) 
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
    [adminUsername, adminEmail, passwordHash, 'System Administrator', 'admin', 'Elite', 100, 10000, true]
  )

  const adminId = result.insertId

  // Create admin preferences
  await connection.execute(
    `INSERT INTO user_preferences (user_id, created_at, updated_at) 
     VALUES (?, NOW(), NOW())`,
    [adminId]
  )

  console.log('✅ Admin user created successfully')
  console.log(`📧 Email: ${adminEmail}`)
  console.log(`🔑 Password: ${adminPassword}`)
  console.log('⚠️ Please change the admin password after first login!')
}

// Run setup if called directly
if (require.main === module) {
  setupDatabase()
}

module.exports = { setupDatabase }
