name: Tests

on:
  push:
    branches:
      - master
      - '!dependabot/**'
  pull_request:
    branches:
      - '**'

env:
  CI: true

jobs:
  run:
    name: Node ${{ matrix.node }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        node: [14, 16, 18, 20]
        os: [ubuntu-latest]

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node }}

      - name: Install npm dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run test
        run: npm run test
