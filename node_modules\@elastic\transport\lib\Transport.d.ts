import * as http from 'node:http';
import { Connection } from './connection';
import Diagnostic from './Diagnostic';
import Serializer from './Serializer';
import { BaseConnectionPool } from './pool';
import { nodeFilterFn, nodeSelectorFn, generateRequestIdFn, RequestBody, RequestNDBody, TransportResult, Context } from './types';
import { kSniffEnabled, kNextSniff, kIsSniffing, kSniffInterval, kSniffOnConnectionFault, kSniffEndpoint, kRequestTimeout, kRetryOnTimeout, kCompression, kMaxRetries, kName, kOpaqueIdPrefix, kGenerateRequestId, kContext, kConnectionPool, kSerializer, kDiagnostic, kHeaders, kNodeFilter, kNodeSelector, kProductCheck, kMaxResponseSize, kMaxCompressedResponseSize, kJsonContentType, kNdjsonContentType, kAcceptHeader, kRedaction, kRetry<PERSON><PERSON>off, kOtelTracer, kOtelOptions } from './symbols';
import { Tracer } from '@opentelemetry/api';
export interface OpenTelemetryOptions {
    enabled?: boolean;
    suppressInternalInstrumentation?: boolean;
}
export interface TransportOptions {
    diagnostic?: Diagnostic;
    connectionPool: BaseConnectionPool;
    serializer?: Serializer;
    maxRetries?: number;
    requestTimeout?: number | string;
    retryOnTimeout?: boolean;
    compression?: boolean;
    sniffInterval?: number | boolean;
    sniffOnConnectionFault?: boolean;
    sniffEndpoint?: string;
    sniffOnStart?: boolean;
    nodeFilter?: nodeFilterFn;
    nodeSelector?: nodeSelectorFn;
    headers?: http.IncomingHttpHeaders;
    generateRequestId?: generateRequestIdFn;
    name?: string | symbol;
    opaqueIdPrefix?: string;
    context?: Context;
    productCheck?: string;
    maxResponseSize?: number;
    maxCompressedResponseSize?: number;
    vendoredHeaders?: {
        jsonContentType?: string;
        ndjsonContentType?: string;
        accept?: string;
    };
    redaction?: RedactionOptions;
    retryBackoff?: (min: number, max: number, attempt: number) => number;
    openTelemetry?: OpenTelemetryOptions;
}
export interface TransportRequestMetadata {
    name: string;
    pathParts?: Record<string, any>;
}
export interface TransportRequestParams {
    method: string;
    path: string;
    body?: RequestBody;
    bulkBody?: RequestNDBody;
    querystring?: Record<string, any> | string;
    meta?: TransportRequestMetadata;
}
export interface TransportRequestOptions {
    ignore?: number[];
    requestTimeout?: number | string;
    retryOnTimeout?: boolean;
    maxRetries?: number;
    asStream?: boolean;
    headers?: http.IncomingHttpHeaders;
    querystring?: Record<string, any>;
    compression?: boolean;
    id?: any;
    context?: Context;
    opaqueId?: string;
    signal?: AbortSignal;
    maxResponseSize?: number;
    maxCompressedResponseSize?: number;
    /**
      * Warning: If you set meta to true the result will no longer be
      * the response body, but an object containing the body, statusCode,
      * headers and meta keys.
      * You can use the destructuring assignment to update your code without
      * refactoring the entire code base:
      * From:
      * ```
      * const result = await client.method(params)
      * ```
      * To:
      * ```
      * const {
      *   body: result,
      *   statusCode,
      *   headers,
      *   meta
      * } = await client.method(params, { meta: true })
      * ```
      */
    meta?: boolean;
    redaction?: RedactionOptions;
    retryBackoff?: (min: number, max: number, attempt: number) => number;
    openTelemetry?: OpenTelemetryOptions;
}
export interface TransportRequestOptionsWithMeta extends TransportRequestOptions {
    meta: true;
}
export interface TransportRequestOptionsWithOutMeta extends TransportRequestOptions {
    meta: false;
}
export interface GetConnectionOptions {
    requestId: string | number;
    context: any;
}
export interface SniffOptions {
    requestId?: string | number;
    reason: string;
    context: any;
}
export interface RedactionOptions {
    type: 'off' | 'replace' | 'remove';
    additionalKeys?: string[];
}
export default class Transport {
    [kNodeFilter]: nodeFilterFn;
    [kNodeSelector]: nodeSelectorFn;
    [kHeaders]: http.IncomingHttpHeaders;
    [kDiagnostic]: Diagnostic;
    [kConnectionPool]: BaseConnectionPool;
    [kSerializer]: Serializer;
    [kContext]: Context;
    [kGenerateRequestId]: generateRequestIdFn;
    [kOpaqueIdPrefix]: string | null;
    [kName]: string | symbol;
    [kMaxRetries]: number;
    [kCompression]: boolean;
    [kRequestTimeout]: number;
    [kRetryOnTimeout]: boolean;
    [kSniffEnabled]: boolean;
    [kNextSniff]: number;
    [kIsSniffing]: boolean;
    [kSniffInterval]: number | boolean;
    [kSniffOnConnectionFault]: boolean;
    [kSniffEndpoint]: string | null;
    [kProductCheck]: string | null;
    [kMaxResponseSize]: number;
    [kMaxCompressedResponseSize]: number;
    [kJsonContentType]: string;
    [kNdjsonContentType]: string;
    [kAcceptHeader]: string;
    [kRedaction]: RedactionOptions;
    [kRetryBackoff]: (min: number, max: number, attempt: number) => number;
    [kOtelTracer]: Tracer;
    [kOtelOptions]: OpenTelemetryOptions;
    static sniffReasons: {
        SNIFF_ON_START: string;
        SNIFF_INTERVAL: string;
        SNIFF_ON_CONNECTION_FAULT: string;
        DEFAULT: string;
    };
    constructor(opts: TransportOptions);
    get connectionPool(): BaseConnectionPool;
    get sniffEnabled(): boolean;
    get nextSniff(): number | null;
    get sniffEndpoint(): string | null;
    get isSniffing(): boolean;
    set isSniffing(val: boolean);
    get diagnostic(): Diagnostic;
    private _request;
    request<TResponse = unknown>(params: TransportRequestParams, options?: TransportRequestOptionsWithOutMeta): Promise<TResponse>;
    request<TResponse = unknown, TContext = any>(params: TransportRequestParams, options?: TransportRequestOptionsWithMeta): Promise<TransportResult<TResponse, TContext>>;
    request<TResponse = unknown>(params: TransportRequestParams, options?: TransportRequestOptions): Promise<TResponse>;
    getConnection(opts: GetConnectionOptions): Connection | null;
    sniff(opts: SniffOptions): void;
}
export declare function generateRequestId(): generateRequestIdFn;
export declare function lowerCaseHeaders(oldHeaders?: http.IncomingHttpHeaders): http.IncomingHttpHeaders | null;
