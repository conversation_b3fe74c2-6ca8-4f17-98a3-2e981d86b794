import mysql from 'mysql2/promise'
import { createClient } from 'redis'
import { Client } from '@elastic/elasticsearch'

// MySQL Database Configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rootkan',
  database: process.env.DB_NAME || 'db_kodexguard',
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  connectionLimit: 10,
  queueLimit: 0,
}

// Create MySQL connection pool
const mysqlPool = mysql.createPool(dbConfig)

// For demo purposes, use mock database if MySQL is not available
import { MockDatabase } from './database-mock'

export const db = {
  async query(sql: string, params: any[] = []) {
    try {
      // Try MySQL first
      const [rows] = await mysqlPool.execute(sql, params)
      return rows
    } catch (error) {
      console.log('MySQL not available, using mock database')
      // Fallback to mock database
      return await MockDatabase.query(sql, params)
    }
  },

  async close() {
    await mysqlPool.end()
  }
}

// Test database connection
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const connection = await mysqlPool.getConnection()
    await connection.ping()
    connection.release()
    console.log('✅ MySQL database connected successfully')
    return true
  } catch (error) {
    console.error('❌ MySQL database connection failed, using mock database')
    return true // Return true for mock database
  }
}

// Redis Configuration
const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  password: process.env.REDIS_PASSWORD || undefined,
  socket: {
    reconnectStrategy: (retries: number) => Math.min(retries * 50, 500),
  },
}

// Create Redis client
export const redis = createClient(redisConfig)

// Redis connection handlers
redis.on('error', (err) => {
  console.error('❌ Redis Client Error:', err)
})

redis.on('connect', () => {
  console.log('✅ Redis connected successfully')
})

redis.on('reconnecting', () => {
  console.log('🔄 Redis reconnecting...')
})

redis.on('ready', () => {
  console.log('✅ Redis ready for operations')
})

// Initialize Redis connection
export async function initRedis(): Promise<boolean> {
  try {
    if (!redis.isOpen) {
      await redis.connect()
    }
    return true
  } catch (error) {
    console.error('❌ Redis connection failed:', error)
    return false
  }
}

// Elasticsearch Configuration
const elasticsearchConfig = {
  node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
  auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {
    username: process.env.ELASTICSEARCH_USERNAME,
    password: process.env.ELASTICSEARCH_PASSWORD,
  } : undefined,
  requestTimeout: 30000,
  pingTimeout: 3000,
  maxRetries: 3,
}

// Create Elasticsearch client
export const elasticsearch = new Client(elasticsearchConfig)

// Test Elasticsearch connection
export async function testElasticsearchConnection(): Promise<boolean> {
  try {
    const health = await elasticsearch.cluster.health()
    console.log('✅ Elasticsearch connected successfully:', health.cluster_name)
    return true
  } catch (error) {
    console.error('❌ Elasticsearch connection failed:', error)
    return false
  }
}

// Initialize all database connections
export async function initializeDatabases(): Promise<{
  mysql: boolean
  redis: boolean
  elasticsearch: boolean
}> {
  console.log('🚀 Initializing database connections...')
  
  const results = {
    mysql: await testDatabaseConnection(),
    redis: await initRedis(),
    elasticsearch: await testElasticsearchConnection(),
  }

  const allConnected = Object.values(results).every(Boolean)
  
  if (allConnected) {
    console.log('✅ All databases connected successfully')
  } else {
    console.warn('⚠️ Some database connections failed:', results)
  }

  return results
}

// Graceful shutdown
export async function closeDatabaseConnections(): Promise<void> {
  console.log('🔄 Closing database connections...')
  
  try {
    await mysqlPool.end()
    console.log('✅ MySQL connection closed')
  } catch (error) {
    console.error('❌ Error closing MySQL connection:', error)
  }

  try {
    if (redis.isOpen) {
      await redis.quit()
    }
    console.log('✅ Redis connection closed')
  } catch (error) {
    console.error('❌ Error closing Redis connection:', error)
  }

  try {
    await elasticsearch.close()
    console.log('✅ Elasticsearch connection closed')
  } catch (error) {
    console.error('❌ Error closing Elasticsearch connection:', error)
  }
}

// Database utility functions
export class DatabaseUtils {
  // Execute query with error handling
  static async query(sql: string, params?: any[]): Promise<any> {
    try {
      return await db.query(sql, params || [])
    } catch (error) {
      console.error('Database query error:', error)
      throw error
    }
  }

  // Get single record
  static async findOne(sql: string, params?: any[]): Promise<any> {
    const rows = await this.query(sql, params)
    return Array.isArray(rows) && rows.length > 0 ? rows[0] : null
  }

  // Get multiple records with pagination
  static async findMany(
    sql: string, 
    params?: any[], 
    page: number = 1, 
    limit: number = 10
  ): Promise<{ data: any[], total: number, page: number, limit: number }> {
    const offset = (page - 1) * limit
    
    // Get total count
    const countSql = sql.replace(/SELECT .+ FROM/, 'SELECT COUNT(*) as total FROM')
    const countResult = await this.findOne(countSql, params)
    const total = countResult?.total || 0
    
    // Get paginated data
    const dataSql = `${sql} LIMIT ${limit} OFFSET ${offset}`
    const data = await this.query(dataSql, params)
    
    return {
      data: Array.isArray(data) ? data : [],
      total,
      page,
      limit,
    }
  }

  // Insert record
  static async insert(table: string, data: Record<string, any>): Promise<string> {
    const fields = Object.keys(data).join(', ')
    const placeholders = Object.keys(data).map(() => '?').join(', ')
    const values = Object.values(data)
    
    const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`
    const result: any = await this.query(sql, values)
    
    return result.insertId || data.id
  }

  // Update record
  static async update(
    table: string, 
    data: Record<string, any>, 
    where: string, 
    whereParams?: any[]
  ): Promise<boolean> {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ')
    const values = [...Object.values(data), ...(whereParams || [])]
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`
    const result: any = await this.query(sql, values)
    
    return result.affectedRows > 0
  }

  // Delete record
  static async delete(table: string, where: string, whereParams?: any[]): Promise<boolean> {
    const sql = `DELETE FROM ${table} WHERE ${where}`
    const result: any = await this.query(sql, whereParams)
    
    return result.affectedRows > 0
  }
}

// Redis utility functions
export class RedisUtils {
  // Set value with expiration
  static async set(key: string, value: any, expireInSeconds?: number): Promise<boolean> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
      
      if (expireInSeconds) {
        await redis.setEx(key, expireInSeconds, serializedValue)
      } else {
        await redis.set(key, serializedValue)
      }
      
      return true
    } catch (error) {
      console.error('Redis set error:', error)
      return false
    }
  }

  // Get value
  static async get(key: string): Promise<any> {
    try {
      const value = await redis.get(key)
      if (!value) return null
      
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    } catch (error) {
      console.error('Redis get error:', error)
      return null
    }
  }

  // Delete key
  static async del(key: string): Promise<boolean> {
    try {
      const result = await redis.del(key)
      return result > 0
    } catch (error) {
      console.error('Redis delete error:', error)
      return false
    }
  }

  // Check if key exists
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Redis exists error:', error)
      return false
    }
  }

  // Increment counter
  static async incr(key: string, expireInSeconds?: number): Promise<number> {
    try {
      const result = await redis.incr(key)
      
      if (expireInSeconds && result === 1) {
        await redis.expire(key, expireInSeconds)
      }
      
      return result
    } catch (error) {
      console.error('Redis increment error:', error)
      return 0
    }
  }
}

// Export default database instance
export default db
