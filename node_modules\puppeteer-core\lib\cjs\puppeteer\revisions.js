"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '127.0.6533.88',
    'chrome-headless-shell': '127.0.6533.88',
    firefox: 'latest',
});
//# sourceMappingURL=revisions.js.map