import { ReactNode } from 'react'
import { LucideIcon } from 'lucide-react'

interface CardProps {
  children: ReactNode
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg'
  border?: boolean
  hover?: boolean
}

interface StatsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  trend?: {
    value: number
    isPositive: boolean
  }
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
  onClick?: () => void
}

interface FeatureCardProps {
  title: string
  description: string
  icon: LucideIcon
  href?: string
  badge?: string
  premium?: boolean
  onClick?: () => void
}

// Base Card Component
export function Card({ 
  children, 
  className = '', 
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false
}: CardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8'
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  }

  return (
    <div
      className={`
        bg-white rounded-lg
        ${paddingClasses[padding]}
        ${shadowClasses[shadow]}
        ${border ? 'border border-gray-200' : ''}
        ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
        ${className}
      `}
    >
      {children}
    </div>
  )
}

// Stats Card Component
export function StatsCard({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  color = 'blue',
  onClick 
}: StatsCardProps) {
  const colorClasses = {
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    red: 'bg-red-500 text-white',
    yellow: 'bg-yellow-500 text-white',
    purple: 'bg-purple-500 text-white',
    gray: 'bg-gray-500 text-white'
  }

  const trendColorClasses = {
    positive: 'text-green-600',
    negative: 'text-red-600'
  }

  return (
    <Card 
      className={onClick ? 'cursor-pointer' : ''}
      hover={!!onClick}
      onClick={onClick}
    >
      <div className="flex items-center">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {trend && (
            <p className={`text-sm ${trend.isPositive ? trendColorClasses.positive : trendColorClasses.negative}`}>
              {trend.isPositive ? '+' : ''}{trend.value}%
              <span className="text-gray-500 ml-1">vs last month</span>
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </Card>
  )
}

// Feature Card Component
export function FeatureCard({ 
  title, 
  description, 
  icon: Icon, 
  href, 
  badge,
  premium = false,
  onClick 
}: FeatureCardProps) {
  const CardWrapper = href ? 'a' : 'div'
  const cardProps = href ? { href } : {}

  return (
    <CardWrapper {...cardProps}>
      <Card 
        className={`h-full ${(href || onClick) ? 'cursor-pointer' : ''}`}
        hover={!!(href || onClick)}
        onClick={onClick}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-start justify-between mb-4">
            <div className={`p-3 rounded-lg ${premium ? 'bg-orange-100' : 'bg-primary-100'}`}>
              <Icon className={`h-6 w-6 ${premium ? 'text-orange-600' : 'text-primary-600'}`} />
            </div>
            {badge && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {badge}
              </span>
            )}
            {premium && (
              <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                Premium
              </span>
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
            <p className="text-gray-600 text-sm">{description}</p>
          </div>
        </div>
      </Card>
    </CardWrapper>
  )
}

// Alert Card Component
interface AlertCardProps {
  type: 'info' | 'success' | 'warning' | 'error'
  title?: string
  message: string
  dismissible?: boolean
  onDismiss?: () => void
}

export function AlertCard({ 
  type, 
  title, 
  message, 
  dismissible = false, 
  onDismiss 
}: AlertCardProps) {
  const typeClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  }

  return (
    <div className={`rounded-lg border p-4 ${typeClasses[type]}`}>
      <div className="flex items-start">
        <div className="flex-1">
          {title && (
            <h4 className="font-medium mb-1">{title}</h4>
          )}
          <p className="text-sm">{message}</p>
        </div>
        {dismissible && (
          <button
            onClick={onDismiss}
            className="ml-4 text-current hover:opacity-75"
          >
            ×
          </button>
        )}
      </div>
    </div>
  )
}

// Loading Card Component
export function LoadingCard({ className = '' }: { className?: string }) {
  return (
    <Card className={className}>
      <div className="animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="rounded-full bg-gray-300 h-12 w-12"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </Card>
  )
}

// Empty State Card Component
interface EmptyStateCardProps {
  icon: LucideIcon
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
  }
}

export function EmptyStateCard({ 
  icon: Icon, 
  title, 
  description, 
  action 
}: EmptyStateCardProps) {
  return (
    <Card className="text-center py-12">
      <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Icon className="h-6 w-6 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6">{description}</p>
      {action && (
        <button
          onClick={action.onClick}
          className="btn-primary"
        >
          {action.label}
        </button>
      )}
    </Card>
  )
}

export default Card
