{"name": "@pnpm/npm-conf", "version": "2.3.1", "description": "Get the npm config", "license": "MIT", "repository": "pnpm/npm-conf", "engines": {"node": ">=12"}, "files": ["index.js", "lib"], "keywords": ["conf", "config", "global", "npm", "path", "prefix", "rc"], "dependencies": {"@pnpm/config.env-replace": "^1.1.0", "@pnpm/network.ca-file": "^1.0.1", "config-chain": "^1.1.11"}, "devDependencies": {"@types/node": "^14.0.14", "babel-generator": "^6.24.1", "babel-traverse": "^6.24.1", "babylon": "^6.17.1", "eslint-import-resolver-node": "^0.3.2", "jest": "^25.1.0", "npm": "^5.0.4", "typescript": "^3.9.6"}, "scripts": {"__prepublishOnly": "node lib/make.js && tsc -p lib/tsconfig.make-out.json", "test": "jest"}}