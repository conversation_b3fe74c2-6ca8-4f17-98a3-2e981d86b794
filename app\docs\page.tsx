'use client'

import { useState } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { 
  Book, 
  Search, 
  FileText, 
  Code, 
  Shield, 
  Target, 
  Database, 
  Wrench,
  ChevronRight,
  ExternalLink,
  Download,
  Play,
  BookOpen,
  Lightbulb,
  Zap,
  Users,
  Globe,
  Lock,
  Eye,
  Terminal,
  Settings
} from 'lucide-react'

export default function DocsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('getting-started')

  const categories = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: Play,
      description: 'Quick start guide and basic setup'
    },
    {
      id: 'osint',
      name: 'OSINT Tools',
      icon: Globe,
      description: 'Open Source Intelligence gathering'
    },
    {
      id: 'scanner',
      name: 'Vulnerability Scanner',
      icon: Shield,
      description: 'Security scanning and assessment'
    },
    {
      id: 'file-analyzer',
      name: 'File Analyzer',
      icon: FileText,
      description: 'Malware and file analysis tools'
    },
    {
      id: 'cve',
      name: 'CVE Database',
      icon: Database,
      description: 'Vulnerability database and search'
    },
    {
      id: 'dorking',
      name: 'Google Dorking',
      icon: Search,
      description: 'Advanced search techniques'
    },
    {
      id: 'api',
      name: 'API Reference',
      icon: Code,
      description: 'REST API documentation'
    },
    {
      id: 'tools',
      name: 'Security Tools',
      icon: Wrench,
      description: 'Additional security utilities'
    }
  ]

  const quickStartGuides = [
    {
      title: 'Platform Overview',
      description: 'Learn about KodeXGuard features and capabilities',
      icon: BookOpen,
      time: '5 min read',
      difficulty: 'Beginner'
    },
    {
      title: 'First OSINT Investigation',
      description: 'Step-by-step guide to your first investigation',
      icon: Target,
      time: '15 min read',
      difficulty: 'Beginner'
    },
    {
      title: 'Running Vulnerability Scans',
      description: 'How to perform comprehensive security scans',
      icon: Shield,
      time: '20 min read',
      difficulty: 'Intermediate'
    },
    {
      title: 'API Integration',
      description: 'Integrate KodeXGuard into your workflow',
      icon: Code,
      time: '30 min read',
      difficulty: 'Advanced'
    }
  ]

  const popularDocs = [
    {
      title: 'OSINT Methodology',
      category: 'OSINT',
      views: '15.2K',
      updated: '2 days ago'
    },
    {
      title: 'CVE Search Techniques',
      category: 'CVE Database',
      views: '12.8K',
      updated: '1 week ago'
    },
    {
      title: 'Advanced Google Dorking',
      category: 'Dorking',
      views: '9.5K',
      updated: '3 days ago'
    },
    {
      title: 'API Authentication',
      category: 'API',
      views: '8.1K',
      updated: '5 days ago'
    }
  ]

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-cyber-glow">Documentation</span>{' '}
              <span className="text-cyber-pink">Center</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Comprehensive guides, tutorials, and API documentation to help you master cybersecurity tools and techniques
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search documentation..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-lg input-cyber text-lg"
                />
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {[
              { label: 'Documentation Pages', value: '150+', icon: FileText },
              { label: 'Video Tutorials', value: '45+', icon: Play },
              { label: 'Code Examples', value: '200+', icon: Code },
              { label: 'API Endpoints', value: '80+', icon: Settings }
            ].map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="card-cyber text-center">
                  <Icon className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
                  <div className="text-2xl font-bold text-white mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-400">
                    {stat.label}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="card-cyber">
                <h3 className="text-lg font-bold text-white mb-4">
                  Categories
                </h3>
                <div className="space-y-2">
                  {categories.map((category) => {
                    const Icon = category.icon
                    return (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                          selectedCategory === category.id
                            ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary' 
                            : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{category.name}</span>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 space-y-8">
              {/* Quick Start Guides */}
              <div>
                <h2 className="text-3xl font-bold text-white mb-6">
                  Quick Start Guides
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {quickStartGuides.map((guide, index) => {
                    const Icon = guide.icon
                    return (
                      <div key={index} className="card-cyber hover:scale-105 transition-transform duration-300 cursor-pointer">
                        <div className="flex items-start space-x-4">
                          <div className="p-3 rounded-lg bg-cyber-primary/20">
                            <Icon className="h-6 w-6 text-cyber-primary" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-white mb-2">
                              {guide.title}
                            </h3>
                            <p className="text-gray-300 mb-3 text-sm">
                              {guide.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                guide.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                                guide.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {guide.difficulty}
                              </span>
                              <span className="text-xs text-gray-400">
                                {guide.time}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Popular Documentation */}
              <div>
                <h2 className="text-3xl font-bold text-white mb-6">
                  Popular Documentation
                </h2>
                <div className="space-y-4">
                  {popularDocs.map((doc, index) => (
                    <div key={index} className="card-cyber hover:border-cyber-primary transition-colors duration-300 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-white mb-1">
                            {doc.title}
                          </h3>
                          <div className="flex items-center space-x-4">
                            <span className="text-sm px-2 py-1 rounded-full bg-cyber-secondary/20 text-cyber-secondary">
                              {doc.category}
                            </span>
                            <span className="text-sm text-gray-400">
                              {doc.views} views
                            </span>
                            <span className="text-sm text-gray-400">
                              Updated {doc.updated}
                            </span>
                          </div>
                        </div>
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* API Documentation */}
              <div className="card-cyber">
                <div className="flex items-center space-x-3 mb-6">
                  <Code className="h-8 w-8 text-cyber-primary" />
                  <h2 className="text-3xl font-bold text-white">
                    API Documentation
                  </h2>
                </div>
                <p className="text-gray-300 mb-6">
                  Integrate KodeXGuard's powerful cybersecurity tools into your applications with our comprehensive REST API.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-cyber-primary/10">
                    <h4 className="font-semibold text-white mb-2">Authentication</h4>
                    <p className="text-sm text-gray-300">
                      API key and OAuth 2.0 authentication methods
                    </p>
                  </div>
                  <div className="p-4 rounded-lg bg-cyber-secondary/10">
                    <h4 className="font-semibold text-white mb-2">Rate Limits</h4>
                    <p className="text-sm text-gray-300">
                      Request limits and best practices
                    </p>
                  </div>
                  <div className="p-4 rounded-lg bg-cyber-accent/10">
                    <h4 className="font-semibold text-white mb-2">SDKs</h4>
                    <p className="text-sm text-gray-300">
                      Python, JavaScript, and Go libraries
                    </p>
                  </div>
                </div>
                <div className="mt-6 flex flex-col sm:flex-row gap-4">
                  <button className="btn-cyber-primary">
                    View API Docs
                  </button>
                  <button className="flex items-center space-x-2 border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                    <Download className="h-4 w-4" />
                    <span>Download SDK</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
