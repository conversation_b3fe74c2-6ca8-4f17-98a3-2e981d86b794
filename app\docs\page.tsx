'use client'

import { useState } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { useThemeClasses } from '@/contexts/ThemeContext'
import { 
  Book, 
  Search, 
  FileText, 
  Code, 
  Shield, 
  Target, 
  Database, 
  Wrench,
  ChevronRight,
  ExternalLink,
  Download,
  Play,
  BookOpen,
  Lightbulb,
  Zap,
  Users,
  Globe,
  Lock,
  Eye,
  Terminal,
  Settings
} from 'lucide-react'

export default function DocsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('getting-started')
  const themeClasses = useThemeClasses()

  const categories = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: Play,
      description: 'Quick start guide and basic setup'
    },
    {
      id: 'osint',
      name: 'OSINT Tools',
      icon: Globe,
      description: 'Open Source Intelligence gathering'
    },
    {
      id: 'scanner',
      name: 'Vulnerability Scanner',
      icon: Shield,
      description: 'Security scanning and assessment'
    },
    {
      id: 'file-analyzer',
      name: '<PERSON> Analyzer',
      icon: FileText,
      description: 'Malware and file analysis tools'
    },
    {
      id: 'cve',
      name: 'CVE Database',
      icon: Database,
      description: 'Vulnerability database and search'
    },
    {
      id: 'dorking',
      name: 'Google Dorking',
      icon: Search,
      description: 'Advanced search techniques'
    },
    {
      id: 'api',
      name: 'API Reference',
      icon: Code,
      description: 'REST API documentation'
    },
    {
      id: 'tools',
      name: 'Security Tools',
      icon: Wrench,
      description: 'Additional security utilities'
    }
  ]

  const quickStartGuides = [
    {
      title: 'Platform Overview',
      description: 'Learn about KodeXGuard features and capabilities',
      icon: BookOpen,
      time: '5 min read',
      difficulty: 'Beginner'
    },
    {
      title: 'First OSINT Investigation',
      description: 'Step-by-step guide to your first investigation',
      icon: Target,
      time: '15 min read',
      difficulty: 'Beginner'
    },
    {
      title: 'Running Vulnerability Scans',
      description: 'How to perform comprehensive security scans',
      icon: Shield,
      time: '20 min read',
      difficulty: 'Intermediate'
    },
    {
      title: 'API Integration',
      description: 'Integrate KodeXGuard into your workflow',
      icon: Code,
      time: '30 min read',
      difficulty: 'Advanced'
    }
  ]

  const popularDocs = [
    {
      title: 'OSINT Methodology',
      category: 'OSINT',
      views: '15.2K',
      updated: '2 days ago'
    },
    {
      title: 'CVE Search Techniques',
      category: 'CVE Database',
      views: '12.8K',
      updated: '1 week ago'
    },
    {
      title: 'Advanced Google Dorking',
      category: 'Dorking',
      views: '9.5K',
      updated: '3 days ago'
    },
    {
      title: 'API Authentication',
      category: 'API',
      views: '8.1K',
      updated: '5 days ago'
    }
  ]

  const tutorials = [
    {
      title: 'Bug Bounty Reconnaissance',
      description: 'Complete guide to reconnaissance for bug bounty hunting',
      duration: '45 min',
      level: 'Intermediate',
      topics: ['OSINT', 'Subdomain Enumeration', 'Port Scanning']
    },
    {
      title: 'Malware Analysis Basics',
      description: 'Introduction to static and dynamic malware analysis',
      duration: '60 min',
      level: 'Advanced',
      topics: ['File Analysis', 'Reverse Engineering', 'Sandboxing']
    },
    {
      title: 'Web Application Security Testing',
      description: 'Comprehensive web app security assessment',
      duration: '90 min',
      level: 'Intermediate',
      topics: ['OWASP Top 10', 'SQL Injection', 'XSS']
    }
  ]

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className={themeClasses.isDark ? "text-cyber-glow" : "text-blue-600"}>Documentation</span>{' '}
              <span className={themeClasses.isDark ? "text-cyber-pink" : "text-pink-600"}>Center</span>
            </h1>
            <p className={`text-xl max-w-3xl mx-auto mb-8 ${themeClasses.textSecondary}`}>
              Comprehensive guides, tutorials, and API documentation to help you master cybersecurity tools and techniques
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <div className="relative">
                <Search className={`absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 ${themeClasses.textMuted}`} />
                <input
                  type="text"
                  placeholder="Search documentation..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-12 pr-4 py-4 rounded-lg ${themeClasses.input} text-lg`}
                />
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {[
              { label: 'Documentation Pages', value: '150+', icon: FileText },
              { label: 'Video Tutorials', value: '45+', icon: Play },
              { label: 'Code Examples', value: '200+', icon: Code },
              { label: 'API Endpoints', value: '80+', icon: Settings }
            ].map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className={`${themeClasses.card} text-center`}>
                  <Icon className={`h-8 w-8 text-cyber-primary mx-auto mb-3 ${themeClasses.isDark ? 'animate-cyber-pulse' : ''}`} />
                  <div className={`text-2xl font-bold ${themeClasses.textPrimary} mb-1`}>
                    {stat.value}
                  </div>
                  <div className={`text-sm ${themeClasses.textMuted}`}>
                    {stat.label}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className={themeClasses.card}>
                <h3 className={`text-lg font-bold ${themeClasses.textPrimary} mb-4`}>
                  Categories
                </h3>
                <div className="space-y-2">
                  {categories.map((category) => {
                    const Icon = category.icon
                    return (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                          selectedCategory === category.id
                            ? themeClasses.isDark 
                              ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary' 
                              : 'bg-blue-100 text-blue-600 border border-blue-500'
                            : `${themeClasses.textSecondary} hover:${themeClasses.textPrimary} hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-gray-100'}`
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{category.name}</span>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 space-y-8">
              {/* Quick Start Guides */}
              <div>
                <h2 className={`text-3xl font-bold ${themeClasses.textPrimary} mb-6`}>
                  Quick Start Guides
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {quickStartGuides.map((guide, index) => {
                    const Icon = guide.icon
                    return (
                      <div key={index} className={`${themeClasses.card} hover:scale-105 transition-transform duration-300 cursor-pointer`}>
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-lg ${themeClasses.isDark ? 'bg-cyber-primary/20' : 'bg-blue-100'}`}>
                            <Icon className={`h-6 w-6 ${themeClasses.isDark ? 'text-cyber-primary' : 'text-blue-600'}`} />
                          </div>
                          <div className="flex-1">
                            <h3 className={`text-lg font-bold ${themeClasses.textPrimary} mb-2`}>
                              {guide.title}
                            </h3>
                            <p className={`${themeClasses.textSecondary} mb-3 text-sm`}>
                              {guide.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                guide.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                                guide.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {guide.difficulty}
                              </span>
                              <span className={`text-xs ${themeClasses.textMuted}`}>
                                {guide.time}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Popular Documentation */}
              <div>
                <h2 className={`text-3xl font-bold ${themeClasses.textPrimary} mb-6`}>
                  Popular Documentation
                </h2>
                <div className="space-y-4">
                  {popularDocs.map((doc, index) => (
                    <div key={index} className={`${themeClasses.card} hover:${themeClasses.isDark ? 'border-cyber-primary' : 'border-blue-500'} transition-colors duration-300 cursor-pointer`}>
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className={`text-lg font-bold ${themeClasses.textPrimary} mb-1`}>
                            {doc.title}
                          </h3>
                          <div className="flex items-center space-x-4">
                            <span className={`text-sm px-2 py-1 rounded-full ${themeClasses.isDark ? 'bg-cyber-secondary/20 text-cyber-secondary' : 'bg-pink-100 text-pink-600'}`}>
                              {doc.category}
                            </span>
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              {doc.views} views
                            </span>
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              Updated {doc.updated}
                            </span>
                          </div>
                        </div>
                        <ChevronRight className={`h-5 w-5 ${themeClasses.textMuted}`} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Video Tutorials */}
              <div>
                <h2 className={`text-3xl font-bold ${themeClasses.textPrimary} mb-6`}>
                  Video Tutorials
                </h2>
                <div className="grid grid-cols-1 gap-6">
                  {tutorials.map((tutorial, index) => (
                    <div key={index} className={`${themeClasses.card} hover:scale-105 transition-transform duration-300`}>
                      <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6">
                        <div className={`w-full md:w-48 h-32 rounded-lg ${themeClasses.isDark ? 'bg-gradient-to-br from-cyber-primary/20 to-cyber-secondary/20' : 'bg-gradient-to-br from-blue-100 to-pink-100'} flex items-center justify-center`}>
                          <Play className={`h-12 w-12 ${themeClasses.isDark ? 'text-cyber-primary' : 'text-blue-600'}`} />
                        </div>
                        <div className="flex-1">
                          <h3 className={`text-xl font-bold ${themeClasses.textPrimary} mb-2`}>
                            {tutorial.title}
                          </h3>
                          <p className={`${themeClasses.textSecondary} mb-3`}>
                            {tutorial.description}
                          </p>
                          <div className="flex flex-wrap items-center gap-2 mb-3">
                            {tutorial.topics.map((topic, topicIndex) => (
                              <span key={topicIndex} className={`text-xs px-2 py-1 rounded-full ${themeClasses.isDark ? 'bg-cyber-accent/20 text-cyber-accent' : 'bg-yellow-100 text-yellow-800'}`}>
                                {topic}
                              </span>
                            ))}
                          </div>
                          <div className="flex items-center space-x-4">
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              {tutorial.duration}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              tutorial.level === 'Beginner' ? 'bg-green-100 text-green-800' :
                              tutorial.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {tutorial.level}
                            </span>
                          </div>
                        </div>
                        <div>
                          <button className={`${themeClasses.isDark ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200'}`}>
                            Watch Now
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* API Documentation */}
              <div className={themeClasses.card}>
                <div className="flex items-center space-x-3 mb-6">
                  <Code className={`h-8 w-8 ${themeClasses.isDark ? 'text-cyber-primary' : 'text-blue-600'}`} />
                  <h2 className={`text-3xl font-bold ${themeClasses.textPrimary}`}>
                    API Documentation
                  </h2>
                </div>
                <p className={`${themeClasses.textSecondary} mb-6`}>
                  Integrate KodeXGuard's powerful cybersecurity tools into your applications with our comprehensive REST API.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'}`}>
                    <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>Authentication</h4>
                    <p className={`text-sm ${themeClasses.textSecondary}`}>
                      API key and OAuth 2.0 authentication methods
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-secondary/10' : 'bg-pink-50'}`}>
                    <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>Rate Limits</h4>
                    <p className={`text-sm ${themeClasses.textSecondary}`}>
                      Request limits and best practices
                    </p>
                  </div>
                  <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-accent/10' : 'bg-yellow-50'}`}>
                    <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>SDKs</h4>
                    <p className={`text-sm ${themeClasses.textSecondary}`}>
                      Python, JavaScript, and Go libraries
                    </p>
                  </div>
                </div>
                <div className="mt-6 flex flex-col sm:flex-row gap-4">
                  <button className={`${themeClasses.isDark ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200'}`}>
                    View API Docs
                  </button>
                  <button className={`flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors duration-200 ${themeClasses.isDark ? 'border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black' : 'border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white'}`}>
                    <Download className="h-4 w-4" />
                    <span>Download SDK</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
