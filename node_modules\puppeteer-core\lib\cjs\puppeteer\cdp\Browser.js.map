{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/cdp/Browser.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAOH,kDAO2B;AAE3B,wDAAsE;AAKtE,2DAAsD;AACtD,qEAA6D;AAE7D,uEAA+D;AAC/D,2CAOqB;AAGrB;;GAEG;AACH,MAAa,UAAW,SAAQ,oBAAW;IAChC,QAAQ,GAAG,KAAK,CAAC;IAE1B,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C,EAC3C,iCAAiC,GAAG,IAAI;QAExC,MAAM,OAAO,GAAG,IAAI,UAAU,CAC5B,OAAO,EACP,UAAU,EACV,UAAU,EACV,eAAe,EACf,OAAO,EACP,aAAa,EACb,oBAAoB,EACpB,oBAAoB,EACpB,iCAAiC,CAClC,CAAC;QACF,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,UAAU,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBAC3D,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;QACL,CAAC;QACD,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,gBAAgB,CAAmB;IACnC,QAAQ,CAAgB;IACxB,WAAW,CAAa;IACxB,cAAc,CAAuB;IACrC,qBAAqB,CAAuB;IAC5C,qBAAqB,CAAwB;IAC7C,eAAe,CAAoB;IACnC,SAAS,GAAG,IAAI,GAAG,EAA6B,CAAC;IACjD,cAAc,CAAgB;IAE9B,YACE,OAAyC,EACzC,UAAsB,EACtB,UAAoB,EACpB,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C,EAC3C,oBAA2C,EAC3C,iCAAiC,GAAG,IAAI;QAExC,KAAK,EAAE,CAAC;QACR,OAAO,GAAG,OAAO,IAAI,QAAQ,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,qBAAqB;YACxB,oBAAoB;gBACpB,CAAC,GAAG,EAAE;oBACJ,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC,CAAC;QACL,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,8CAAoB,CAC5C,UAAU,EACV,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,IAAI,4CAAmB,CAC3C,UAAU,EACV,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,qBAAqB,EAC1B,iCAAiC,CAClC,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,qCAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACrE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAChB,SAAS,EACT,IAAI,qCAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,iBAAiB,GAAG,GAAG,EAAE;QACvB,IAAI,CAAC,IAAI,iDAA4B,SAAS,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,+BAAe,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,EAAE,6DAEpB,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,EAAE,mDAEpB,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,EAAE,yDAEpB,IAAI,CAAC,gBAAgB,CACtB,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,EAAE,+DAEpB,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACF,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;IACzC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,+BAAe,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,6DAErB,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,mDAErB,IAAI,CAAC,qBAAqB,CAC3B,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,yDAErB,IAAI,CAAC,gBAAgB,CACtB,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,+DAErB,IAAI,CAAC,mBAAmB,CACzB,CAAC;IACJ,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,wBAAwB,CAAC,oBAA2C;QAClE,IAAI,CAAC,qBAAqB;YACxB,oBAAoB;gBACpB,CAAC,CAAC,MAAc,EAAW,EAAE;oBAC3B,OAAO,CACL,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM;wBACxB,MAAM,CAAC,IAAI,EAAE,KAAK,iBAAiB;wBACnC,MAAM,CAAC,IAAI,EAAE,KAAK,SAAS,CAC5B,CAAC;gBACJ,CAAC,CAAC,CAAC;IACP,CAAC;IAED,wBAAwB;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAEQ,KAAK,CAAC,oBAAoB,CACjC,UAAiC,EAAE;QAEnC,MAAM,EAAC,WAAW,EAAE,eAAe,EAAC,GAAG,OAAO,CAAC;QAE/C,MAAM,EAAC,gBAAgB,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CACpD,6BAA6B,EAC7B;YACE,WAAW;YACX,eAAe,EAAE,eAAe,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;SAC9D,CACF,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,qCAAiB,CACnC,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,gBAAgB,CACjB,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC;IACjB,CAAC;IAEQ,eAAe;QACtB,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAEQ,qBAAqB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAkB;QACtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1D,gBAAgB,EAAE,SAAS;SAC5B,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,aAAa,GAAG,CACd,UAAsC,EACtC,OAAoB,EACpB,EAAE;QACF,MAAM,EAAC,gBAAgB,EAAC,GAAG,UAAU,CAAC;QACtC,MAAM,OAAO,GACX,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QAE3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,MAAM,aAAa,GAAG,CAAC,oBAA6B,EAAE,EAAE;YACtD,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;QAC3E,CAAC,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,uBAAW,CACjC,UAAU,EACV,OAAO,EACP,OAAO,EACP,IAAI,CAAC,cAAc,EACnB,aAAa,CACd,CAAC;QACF,IAAI,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,0BAAc,CACvB,UAAU,EACV,OAAO,EACP,OAAO,EACP,IAAI,CAAC,cAAc,EACnB,aAAa,EACb,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAC9B,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5C,OAAO,IAAI,sBAAU,CACnB,UAAU,EACV,OAAO,EACP,OAAO,EACP,IAAI,CAAC,cAAc,EACnB,aAAa,EACb,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAC9B,CAAC;QACJ,CAAC;QACD,IACE,UAAU,CAAC,IAAI,KAAK,gBAAgB;YACpC,UAAU,CAAC,IAAI,KAAK,eAAe,EACnC,CAAC;YACD,OAAO,IAAI,wBAAY,CACrB,UAAU,EACV,OAAO,EACP,OAAO,EACP,IAAI,CAAC,cAAc,EACnB,aAAa,CACd,CAAC;QACJ,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;IAEF,mBAAmB,GAAG,KAAK,EAAE,MAAiB,EAAE,EAAE;QAChD,IACE,MAAM,CAAC,gBAAgB,EAAE;YACzB,CAAC,MAAM,MAAM,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;gBAChD,gCAAoB,CAAC,OAAO,EAC9B,CAAC;YACD,IAAI,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC,CAAC;IAEF,qBAAqB,GAAG,KAAK,EAAE,MAAiB,EAAiB,EAAE;QACjE,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,gCAAoB,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACnC,IACE,MAAM,CAAC,gBAAgB,EAAE;YACzB,CAAC,MAAM,MAAM,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;gBAChD,gCAAoB,CAAC,OAAO,EAC9B,CAAC;YACD,IAAI,CAAC,IAAI,uDAA+B,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,8DAAsC,MAAM,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC,CAAC;IAEF,gBAAgB,GAAG,CAAC,EAAC,MAAM,EAAsB,EAAQ,EAAE;QACzD,IAAI,CAAC,IAAI,mDAA6B,MAAM,CAAC,CAAC;QAC9C,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,0DAAoC,MAAM,CAAC,CAAC;IAC1E,CAAC,CAAC;IAEF,mBAAmB,GAAG,CAAC,UAAsC,EAAQ,EAAE;QACrE,IAAI,CAAC,IAAI,yDAAgC,UAAU,CAAC,CAAC;IACvD,CAAC,CAAC;IAEO,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAkB;QAC3C,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACpE,GAAG,EAAE,aAAa;YAClB,gBAAgB,EAAE,SAAS,IAAI,SAAS;SACzC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YAC3C,OAAQ,CAAe,CAAC,SAAS,KAAK,QAAQ,CAAC;QACjD,CAAC,CAAC,CAAc,CAAC;QACjB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,GAAG,CAAC,CAAC;QAChE,CAAC;QACD,MAAM,WAAW,GACf,CAAC,MAAM,MAAM,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;YAClD,gCAAoB,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0CAA0C,QAAQ,GAAG,CAAC,CAAC;QACzE,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CACb,6CAA6C,SAAS,GAAG,CAC1D,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,OAAO;QACd,OAAO,KAAK,CAAC,IAAI,CACf,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CACnD,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAChB,OAAO,CACL,MAAM,CAAC,gBAAgB,EAAE;gBACzB,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,gCAAoB,CAAC,OAAO,CACrE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,MAAM;QACb,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;QACrC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAEQ,KAAK,CAAC,SAAS;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,SAAS,CAAC;IAC3B,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAEQ,UAAU;QACjB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,IAAa,SAAS;QACpB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACrD,CAAC;IAED,IAAa,SAAS;QACpB,OAAO;YACL,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE;SACnE,CAAC;IACJ,CAAC;CACF;AA5XD,gCA4XC"}