{"name": "wordwrapjs", "author": "<PERSON> <<EMAIL>>", "version": "5.1.0", "description": "Word-wrapping for javascript.", "repository": "https://github.com/75lb/wordwrapjs", "license": "MIT", "type": "module", "exports": {"import": "./index.js", "require": "./dist/index.cjs"}, "keywords": ["word", "line", "wrap", "text", "columns", "wordwrap"], "engines": {"node": ">=12.17"}, "scripts": {"test": "npm run dist && npm run test:ci", "test:ci": "test-runner test/test.?js", "docs": "jsdoc2md -t README.hbs index.js > README.md", "dist": "rollup -c"}, "devDependencies": {"jsdoc-to-markdown": "^7.0.1", "rollup": "^2.56.2", "rollup-plugin-node-resolve": "^5.2.0", "test-runner": "^0.9.8"}, "files": ["index.js", "dist"], "standard": {"ignore": ["dist", "tmp"], "envs": []}}