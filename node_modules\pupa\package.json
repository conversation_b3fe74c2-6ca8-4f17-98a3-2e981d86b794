{"name": "pupa", "version": "3.1.0", "description": "Simple micro templating", "license": "MIT", "repository": "sindresorhus/pupa", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["string", "formatting", "template", "object", "format", "interpolate", "interpolation", "templating", "expand", "simple", "replace", "placeholders", "values", "transform", "micro"], "dependencies": {"escape-goat": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "typescript": "^4.3.5", "xo": "^0.41.0"}}