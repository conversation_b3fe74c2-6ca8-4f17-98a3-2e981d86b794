"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DocsPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    const categories = [\n        {\n            id: \"getting-started\",\n            name: \"Getting Started\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"Quick start guide and basic setup\"\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"Open Source Intelligence gathering\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Security scanning and assessment\"\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Malware and file analysis tools\"\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Database\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Vulnerability database and search\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Advanced search techniques\"\n        },\n        {\n            id: \"api\",\n            name: \"API Reference\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"REST API documentation\"\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Additional security utilities\"\n        }\n    ];\n    const quickStartGuides = [\n        {\n            title: \"Platform Overview\",\n            description: \"Learn about KodeXGuard features and capabilities\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            time: \"5 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"First OSINT Investigation\",\n            description: \"Step-by-step guide to your first investigation\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            time: \"15 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"Running Vulnerability Scans\",\n            description: \"How to perform comprehensive security scans\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            time: \"20 min read\",\n            difficulty: \"Intermediate\"\n        },\n        {\n            title: \"API Integration\",\n            description: \"Integrate KodeXGuard into your workflow\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            time: \"30 min read\",\n            difficulty: \"Advanced\"\n        }\n    ];\n    const popularDocs = [\n        {\n            title: \"OSINT Methodology\",\n            category: \"OSINT\",\n            views: \"15.2K\",\n            updated: \"2 days ago\"\n        },\n        {\n            title: \"CVE Search Techniques\",\n            category: \"CVE Database\",\n            views: \"12.8K\",\n            updated: \"1 week ago\"\n        },\n        {\n            title: \"Advanced Google Dorking\",\n            category: \"Dorking\",\n            views: \"9.5K\",\n            updated: \"3 days ago\"\n        },\n        {\n            title: \"API Authentication\",\n            category: \"API\",\n            views: \"8.1K\",\n            updated: \"5 days ago\"\n        }\n    ];\n    const tutorials = [\n        {\n            title: \"Bug Bounty Reconnaissance\",\n            description: \"Complete guide to reconnaissance for bug bounty hunting\",\n            duration: \"45 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OSINT\",\n                \"Subdomain Enumeration\",\n                \"Port Scanning\"\n            ]\n        },\n        {\n            title: \"Malware Analysis Basics\",\n            description: \"Introduction to static and dynamic malware analysis\",\n            duration: \"60 min\",\n            level: \"Advanced\",\n            topics: [\n                \"File Analysis\",\n                \"Reverse Engineering\",\n                \"Sandboxing\"\n            ]\n        },\n        {\n            title: \"Web Application Security Testing\",\n            description: \"Comprehensive web app security assessment\",\n            duration: \"90 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OWASP Top 10\",\n                \"SQL Injection\",\n                \"XSS\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-glow\",\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-pink\",\n                                        children: \"Center\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Comprehensive guides, tutorials, and API documentation to help you master cybersecurity tools and techniques\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search documentation...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 rounded-lg input-cyber text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\",\n                        children: [\n                            {\n                                label: \"Documentation Pages\",\n                                value: \"150+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                label: \"Video Tutorials\",\n                                value: \"45+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                label: \"Code Examples\",\n                                value: \"200+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                label: \"API Endpoints\",\n                                value: \"80+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                            }\n                        ].map((stat, index)=>{\n                            const Icon = stat.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(themeClasses.card, \" text-center\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 \".concat(themeClasses.isDark ? \"animate-cyber-pulse\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: themeClasses.card,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: categories.map((category)=>{\n                                                const Icon = category.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedCategory(category.id),\n                                                    className: \"w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 \".concat(selectedCategory === category.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary\" : \"bg-blue-100 text-blue-600 border border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Quick Start Guides\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: quickStartGuides.map((guide, index)=>{\n                                                    const Icon = guide.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-100\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-6 w-6 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: guide.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3 text-sm\"),\n                                                                            children: guide.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(guide.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : guide.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: guide.difficulty\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs \".concat(themeClasses.textMuted),\n                                                                                    children: guide.time\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Popular Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: popularDocs.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                                                            children: doc.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-secondary/20 text-cyber-secondary\" : \"bg-pink-100 text-pink-600\"),\n                                                                                    children: doc.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        doc.views,\n                                                                                        \" views\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        \"Updated \",\n                                                                                        doc.updated\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 \".concat(themeClasses.textMuted)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Video Tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-6\",\n                                                children: tutorials.map((tutorial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full md:w-48 h-32 rounded-lg \".concat(themeClasses.isDark ? \"bg-gradient-to-br from-cyber-primary/20 to-cyber-secondary/20\" : \"bg-gradient-to-br from-blue-100 to-pink-100\", \" flex items-center justify-center\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"h-12 w-12 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: tutorial.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                            children: tutorial.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap items-center gap-2 mb-3\",\n                                                                            children: tutorial.topics.map((topic, topicIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-accent/20 text-cyber-accent\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                    children: topic\n                                                                                }, topicIndex, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: tutorial.duration\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(tutorial.level === \"Beginner\" ? \"bg-green-100 text-green-800\" : tutorial.level === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: tutorial.level\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                                        children: \"Watch Now\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold \".concat(themeClasses.textPrimary),\n                                                        children: \"API Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Integrate KodeXGuard's powerful cybersecurity tools into your applications with our comprehensive REST API.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Authentication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"API key and OAuth 2.0 authentication methods\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Rate Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Request limits and best practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"SDKs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Python, JavaScript, and Go libraries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                        children: \"View API Docs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Download SDK\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(DocsPage, \"bErKlB3njoaufpGnSTOHHJBBUlQ=\");\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/page.tsx\n"));

/***/ })

});