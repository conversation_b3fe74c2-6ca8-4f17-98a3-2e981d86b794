import axios from 'axios'
import { URL } from 'url'
import * as cheerio from 'cheerio'

export interface ScanTarget {
  id: number
  url: string
  scanType: 'basic' | 'advanced' | 'comprehensive'
  userId: number
}

export interface Vulnerability {
  id: string
  name: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: string
  cwe?: string
  cvss?: number
  evidence?: any
  recommendation: string
  references?: string[]
}

export interface ScanResult {
  vulnerabilities: Vulnerability[]
  summary: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
  }
  scanInfo: {
    target: string
    scanType: string
    duration: number
    timestamp: string
  }
}

export class VulnerabilityScanner {
  private userAgent = 'KodeXGuard-Scanner/1.0'
  private timeout = 30000

  async scan(target: ScanTarget): Promise<ScanResult> {
    const startTime = Date.now()
    const vulnerabilities: Vulnerability[] = []

    try {
      // Validate URL
      const parsedUrl = new URL(target.url)

      console.log(`Starting ${target.scanType} scan for ${target.url}`)

      // Basic scans - always run these
      const headerVulns = await this.checkHTTPHeaders(target.url)
      vulnerabilities.push(...headerVulns)

      const sslVulns = await this.checkSSL(target.url)
      vulnerabilities.push(...sslVulns)

      const dirVulns = await this.checkDirectoryTraversal(target.url)
      vulnerabilities.push(...dirVulns)

      const xssVulns = await this.checkXSS(target.url)
      vulnerabilities.push(...xssVulns)
      
      if (target.scanType === 'advanced' || target.scanType === 'comprehensive') {
        vulnerabilities.push(...await this.checkSQLInjection(target.url))
        vulnerabilities.push(...await this.checkCSRF(target.url))
        vulnerabilities.push(...await this.checkClickjacking(target.url))
        vulnerabilities.push(...await this.checkOpenRedirect(target.url))
      }
      
      if (target.scanType === 'comprehensive') {
        vulnerabilities.push(...await this.checkFileInclusion(target.url))
        vulnerabilities.push(...await this.checkCommandInjection(target.url))
        vulnerabilities.push(...await this.checkXXE(target.url))
        vulnerabilities.push(...await this.checkSSRF(target.url))
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      // Calculate summary
      const summary = this.calculateSummary(vulnerabilities)

      return {
        vulnerabilities,
        summary,
        scanInfo: {
          target: target.url,
          scanType: target.scanType,
          duration,
          timestamp: new Date().toISOString()
        }
      }

    } catch (error) {
      throw new Error(`Scan failed: ${error.message}`)
    }
  }

  private async checkHTTPHeaders(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []

    try {
      console.log(`Checking HTTP headers for ${url}`)

      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: { 'User-Agent': this.userAgent },
        validateStatus: () => true,
        maxRedirects: 5
      })

      const headers = response.headers
      console.log(`Response status: ${response.status}, Headers count: ${Object.keys(headers).length}`)

      // Check for missing security headers
      if (!headers['x-frame-options'] && !headers['content-security-policy']) {
        vulnerabilities.push({
          id: 'missing-xfo',
          name: 'Missing X-Frame-Options Header',
          description: 'The X-Frame-Options header is not set, making the site vulnerable to clickjacking attacks.',
          severity: 'medium',
          category: 'Security Headers',
          cwe: 'CWE-1021',
          recommendation: 'Set X-Frame-Options header to DENY or SAMEORIGIN',
          references: ['https://owasp.org/www-project-secure-headers/']
        })
      }

      if (!headers['x-content-type-options']) {
        vulnerabilities.push({
          id: 'missing-xcto',
          name: 'Missing X-Content-Type-Options Header',
          description: 'The X-Content-Type-Options header is not set, allowing MIME type sniffing.',
          severity: 'low',
          category: 'Security Headers',
          cwe: 'CWE-16',
          recommendation: 'Set X-Content-Type-Options header to nosniff'
        })
      }

      if (!headers['x-xss-protection']) {
        vulnerabilities.push({
          id: 'missing-xxp',
          name: 'Missing X-XSS-Protection Header',
          description: 'The X-XSS-Protection header is not set.',
          severity: 'low',
          category: 'Security Headers',
          recommendation: 'Set X-XSS-Protection header to 1; mode=block'
        })
      }

      if (!headers['strict-transport-security'] && url.startsWith('https://')) {
        vulnerabilities.push({
          id: 'missing-hsts',
          name: 'Missing HSTS Header',
          description: 'The Strict-Transport-Security header is not set for HTTPS site.',
          severity: 'medium',
          category: 'Security Headers',
          cwe: 'CWE-319',
          recommendation: 'Set Strict-Transport-Security header with appropriate max-age'
        })
      }

      // Check for information disclosure
      if (headers['server']) {
        vulnerabilities.push({
          id: 'server-disclosure',
          name: 'Server Information Disclosure',
          description: `Server header reveals server information: ${headers['server']}`,
          severity: 'low',
          category: 'Information Disclosure',
          evidence: { serverHeader: headers['server'] },
          recommendation: 'Remove or obfuscate server header'
        })
      }

    } catch (error) {
      console.error('HTTP headers check failed:', error)
    }

    return vulnerabilities
  }

  private async checkSSL(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []

    if (!url.startsWith('https://')) {
      vulnerabilities.push({
        id: 'no-ssl',
        name: 'No SSL/TLS Encryption',
        description: 'The website does not use SSL/TLS encryption.',
        severity: 'high',
        category: 'Encryption',
        cwe: 'CWE-319',
        recommendation: 'Implement SSL/TLS encryption for all communications'
      })
    }

    return vulnerabilities
  }

  private async checkDirectoryTraversal(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    const payloads = [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
      '....//....//....//etc/passwd'
    ]

    try {
      const parsedUrl = new URL(url)
      
      for (const payload of payloads) {
        const testUrl = `${parsedUrl.origin}/${payload}`
        
        try {
          const response = await axios.get(testUrl, {
            timeout: 5000,
            headers: { 'User-Agent': this.userAgent },
            validateStatus: () => true
          })

          if (response.status === 200 && 
              (response.data.includes('root:') || response.data.includes('localhost'))) {
            vulnerabilities.push({
              id: 'directory-traversal',
              name: 'Directory Traversal',
              description: 'The application is vulnerable to directory traversal attacks.',
              severity: 'high',
              category: 'Path Traversal',
              cwe: 'CWE-22',
              evidence: { payload, response: response.data.substring(0, 200) },
              recommendation: 'Implement proper input validation and sanitization'
            })
            break
          }
        } catch (error) {
          // Continue with next payload
        }
      }
    } catch (error) {
      console.error('Directory traversal check failed:', error)
    }

    return vulnerabilities
  }

  private async checkXSS(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    const payloads = [
      '<script>alert("XSS")</script>',
      '"><script>alert("XSS")</script>',
      'javascript:alert("XSS")',
      '<img src=x onerror=alert("XSS")>',
      '"><svg onload=alert("XSS")>',
      '\'"--></script><script>alert("XSS")</script>'
    ]

    try {
      console.log(`Checking XSS vulnerabilities for ${url}`)

      // First, get the page to find forms and parameters
      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: { 'User-Agent': this.userAgent },
        validateStatus: () => true
      })

      if (response.status !== 200) {
        console.log(`Cannot access ${url} for XSS testing, status: ${response.status}`)
        return vulnerabilities
      }

      const $ = cheerio.load(response.data)
      const forms = $('form')

      console.log(`Found ${forms.length} forms to test for XSS`)

      // Test forms for XSS
      for (let i = 0; i < forms.length; i++) {
        const form = $(forms[i])
        const action = form.attr('action') || ''
        const method = (form.attr('method') || 'get').toLowerCase()
        
        const inputs = form.find('input[type="text"], input[type="search"], textarea')
        
        for (let j = 0; j < inputs.length; j++) {
          const input = $(inputs[j])
          const name = input.attr('name')
          
          if (name) {
            for (const payload of payloads) {
              try {
                const testData = { [name]: payload }
                const testUrl = action.startsWith('http') ? action : new URL(action, url).href
                
                let testResponse
                if (method === 'post') {
                  testResponse = await axios.post(testUrl, testData, {
                    timeout: 5000,
                    headers: { 'User-Agent': this.userAgent },
                    validateStatus: () => true
                  })
                } else {
                  const params = new URLSearchParams(testData)
                  testResponse = await axios.get(`${testUrl}?${params}`, {
                    timeout: 5000,
                    headers: { 'User-Agent': this.userAgent },
                    validateStatus: () => true
                  })
                }

                if (testResponse.data.includes(payload)) {
                  vulnerabilities.push({
                    id: 'xss-reflected',
                    name: 'Reflected Cross-Site Scripting (XSS)',
                    description: `The application reflects user input without proper sanitization in parameter: ${name}`,
                    severity: 'high',
                    category: 'Cross-Site Scripting',
                    cwe: 'CWE-79',
                    evidence: { parameter: name, payload, form: action },
                    recommendation: 'Implement proper input validation and output encoding'
                  })
                  break
                }
              } catch (error) {
                // Continue with next payload
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('XSS check failed:', error)
    }

    return vulnerabilities
  }

  private async checkSQLInjection(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    const payloads = [
      "' OR '1'='1",
      "' OR 1=1--",
      "' UNION SELECT NULL--",
      "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
    ]

    try {
      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: { 'User-Agent': this.userAgent }
      })

      const $ = cheerio.load(response.data)
      const forms = $('form')

      for (let i = 0; i < forms.length; i++) {
        const form = $(forms[i])
        const action = form.attr('action') || ''
        const method = (form.attr('method') || 'get').toLowerCase()
        
        const inputs = form.find('input[type="text"], input[type="hidden"]')
        
        for (let j = 0; j < inputs.length; j++) {
          const input = $(inputs[j])
          const name = input.attr('name')
          
          if (name) {
            for (const payload of payloads) {
              try {
                const testData = { [name]: payload }
                const testUrl = action.startsWith('http') ? action : new URL(action, url).href
                
                let testResponse
                if (method === 'post') {
                  testResponse = await axios.post(testUrl, testData, {
                    timeout: 5000,
                    headers: { 'User-Agent': this.userAgent },
                    validateStatus: () => true
                  })
                } else {
                  const params = new URLSearchParams(testData)
                  testResponse = await axios.get(`${testUrl}?${params}`, {
                    timeout: 5000,
                    headers: { 'User-Agent': this.userAgent },
                    validateStatus: () => true
                  })
                }

                // Check for SQL error messages
                const sqlErrors = [
                  'mysql_fetch_array',
                  'ORA-01756',
                  'Microsoft OLE DB Provider',
                  'PostgreSQL query failed',
                  'SQLite error'
                ]

                const responseText = testResponse.data.toLowerCase()
                for (const error of sqlErrors) {
                  if (responseText.includes(error.toLowerCase())) {
                    vulnerabilities.push({
                      id: 'sql-injection',
                      name: 'SQL Injection',
                      description: `The application is vulnerable to SQL injection in parameter: ${name}`,
                      severity: 'critical',
                      category: 'Injection',
                      cwe: 'CWE-89',
                      evidence: { parameter: name, payload, error },
                      recommendation: 'Use parameterized queries and input validation'
                    })
                    break
                  }
                }
              } catch (error) {
                // Continue with next payload
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('SQL injection check failed:', error)
    }

    return vulnerabilities
  }

  private async checkCSRF(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []

    try {
      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: { 'User-Agent': this.userAgent }
      })

      const $ = cheerio.load(response.data)
      const forms = $('form[method="post"]')

      for (let i = 0; i < forms.length; i++) {
        const form = $(forms[i])
        const csrfTokens = form.find('input[name*="csrf"], input[name*="token"], input[name*="_token"]')
        
        if (csrfTokens.length === 0) {
          vulnerabilities.push({
            id: 'csrf-missing-token',
            name: 'Missing CSRF Protection',
            description: 'Form does not contain CSRF protection tokens.',
            severity: 'medium',
            category: 'Cross-Site Request Forgery',
            cwe: 'CWE-352',
            evidence: { formAction: form.attr('action') },
            recommendation: 'Implement CSRF tokens for all state-changing operations'
          })
        }
      }

    } catch (error) {
      console.error('CSRF check failed:', error)
    }

    return vulnerabilities
  }

  private async checkClickjacking(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []

    try {
      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: { 'User-Agent': this.userAgent }
      })

      const headers = response.headers
      const hasXFrameOptions = headers['x-frame-options']
      const hasCSP = headers['content-security-policy']
      
      let frameAncestorsSet = false
      if (hasCSP) {
        frameAncestorsSet = hasCSP.includes('frame-ancestors')
      }

      if (!hasXFrameOptions && !frameAncestorsSet) {
        vulnerabilities.push({
          id: 'clickjacking',
          name: 'Clickjacking Vulnerability',
          description: 'The page can be embedded in frames, making it vulnerable to clickjacking attacks.',
          severity: 'medium',
          category: 'Clickjacking',
          cwe: 'CWE-1021',
          recommendation: 'Set X-Frame-Options header or use CSP frame-ancestors directive'
        })
      }

    } catch (error) {
      console.error('Clickjacking check failed:', error)
    }

    return vulnerabilities
  }

  private async checkOpenRedirect(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    const payloads = [
      'http://evil.com',
      '//evil.com',
      'https://evil.com'
    ]

    try {
      const parsedUrl = new URL(url)
      const commonParams = ['redirect', 'url', 'next', 'return', 'goto', 'continue']

      for (const param of commonParams) {
        for (const payload of payloads) {
          try {
            const testUrl = `${url}?${param}=${encodeURIComponent(payload)}`
            const response = await axios.get(testUrl, {
              timeout: 5000,
              headers: { 'User-Agent': this.userAgent },
              maxRedirects: 0,
              validateStatus: (status) => status >= 200 && status < 400
            })

            const location = response.headers['location']
            if (location && (location.includes('evil.com') || location.startsWith('//'))) {
              vulnerabilities.push({
                id: 'open-redirect',
                name: 'Open Redirect',
                description: `The application redirects to external URLs via parameter: ${param}`,
                severity: 'medium',
                category: 'Open Redirect',
                cwe: 'CWE-601',
                evidence: { parameter: param, payload, location },
                recommendation: 'Validate redirect URLs against a whitelist'
              })
            }
          } catch (error) {
            // Continue with next payload
          }
        }
      }

    } catch (error) {
      console.error('Open redirect check failed:', error)
    }

    return vulnerabilities
  }

  private async checkFileInclusion(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    const payloads = [
      '../../../etc/passwd',
      'php://filter/read=convert.base64-encode/resource=index.php',
      'file:///etc/passwd'
    ]

    // This would implement file inclusion checks
    // Similar pattern to other vulnerability checks
    return vulnerabilities
  }

  private async checkCommandInjection(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    // Implementation for command injection checks
    return vulnerabilities
  }

  private async checkXXE(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    // Implementation for XXE checks
    return vulnerabilities
  }

  private async checkSSRF(url: string): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    // Implementation for SSRF checks
    return vulnerabilities
  }

  private calculateSummary(vulnerabilities: Vulnerability[]) {
    const summary = {
      total: vulnerabilities.length,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    }

    vulnerabilities.forEach(vuln => {
      summary[vuln.severity]++
    })

    return summary
  }
}
