"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommunityPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CommunityPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMembers: 15420,\n        activeToday: 1247,\n        totalPosts: 8934,\n        totalEvents: 156\n    });\n    const communityStats = [\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            label: \"Total Members\",\n            value: stats.totalMembers.toLocaleString(),\n            change: \"+12%\",\n            color: \"text-cyber-primary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Active Today\",\n            value: stats.activeToday.toLocaleString(),\n            change: \"+8%\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Total Posts\",\n            value: stats.totalPosts.toLocaleString(),\n            change: \"+15%\",\n            color: \"text-cyber-secondary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Events Hosted\",\n            value: stats.totalEvents.toLocaleString(),\n            change: \"+25%\",\n            color: \"text-cyber-accent\"\n        }\n    ];\n    const communityChannels = [\n        {\n            name: \"Discord Server\",\n            description: \"Real-time chat, voice channels, and community discussions\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            members: \"8.2K\",\n            link: \"#\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            name: \"Telegram Group\",\n            description: \"Quick updates, news, and instant notifications\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            members: \"5.1K\",\n            link: \"#\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"GitHub Community\",\n            description: \"Open source contributions and code collaboration\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            members: \"3.8K\",\n            link: \"#\",\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, tips, and cybersecurity news\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            members: \"12.5K\",\n            link: \"#\",\n            color: \"bg-black\"\n        }\n    ];\n    const upcomingEvents = [\n        {\n            title: \"Cybersecurity Workshop\",\n            date: \"2024-01-15\",\n            time: \"19:00 WIB\",\n            type: \"Workshop\",\n            participants: 156,\n            description: \"Advanced penetration testing techniques\"\n        },\n        {\n            title: \"Bug Bounty Bootcamp\",\n            date: \"2024-01-20\",\n            time: \"14:00 WIB\",\n            type: \"Bootcamp\",\n            participants: 89,\n            description: \"From beginner to professional bug hunter\"\n        },\n        {\n            title: \"CTF Competition\",\n            date: \"2024-01-25\",\n            time: \"10:00 WIB\",\n            type: \"Competition\",\n            participants: 234,\n            description: \"Capture The Flag challenge for all levels\"\n        }\n    ];\n    const topContributors = [\n        {\n            name: \"CyberNinja\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 15420,\n            badge: \"Elite Hunter\",\n            contributions: 89\n        },\n        {\n            name: \"SecurityMaster\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 12350,\n            badge: \"Bug Hunter\",\n            contributions: 67\n        },\n        {\n            name: \"PentestPro\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 9870,\n            badge: \"Security Expert\",\n            contributions: 54\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-glow\",\n                                        children: \"Join Our\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-pink\",\n                                        children: \"Cyber Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                                children: communityStats.map((stat, index)=>{\n                                    const Icon = stat.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-cyber text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-8 w-8 \".concat(stat.color, \" mx-auto mb-3 animate-cyber-pulse\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400 mb-1\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    stat.change,\n                                                    \" this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center mb-12\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                label: \"Overview\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                id: \"channels\",\n                                label: \"Channels\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                id: \"events\",\n                                label: \"Events\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: \"leaderboard\",\n                                label: \"Top Contributors\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 \".concat(activeTab === tab.id ? \"bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Welcome to KodeXGuard Community\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help each other grow in the field of cybersecurity.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Share security research and findings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Collaborate on bug bounty programs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Learn from industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Participate in competitions and CTFs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Community Guidelines\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83E\\uDD1D Be Respectful\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Treat all community members with respect and professionalism\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDD12 Ethical Practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Only discuss ethical hacking and responsible disclosure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDCDA Share Knowledge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Help others learn and grow in cybersecurity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"channels\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: communityChannels.map((channel, index)=>{\n                                    const Icon = channel.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(channel.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                            children: channel.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-4\"),\n                                                            children: channel.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                    children: [\n                                                                        channel.members,\n                                                                        \" members\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-lg \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\", \" transition-colors duration-200\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Join\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Upcoming Events\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    upcomingEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(event.type === \"Workshop\" ? \"bg-blue-100 text-blue-800\" : event.type === \"Bootcamp\" ? \"bg-green-100 text-green-800\" : \"bg-purple-100 text-purple-800\"),\n                                                                        children: event.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                        children: [\n                                                                            event.participants,\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 md:mt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                            children: \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"leaderboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Top Contributors This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    topContributors.map((contributor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full bg-gradient-to-r \".concat(index === 0 ? \"from-yellow-400 to-yellow-600\" : index === 1 ? \"from-gray-300 to-gray-500\" : \"from-orange-400 to-orange-600\", \" flex items-center justify-center\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: contributor.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: contributor.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: [\n                                                                    contributor.points.toLocaleString(),\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                children: [\n                                                                    contributor.contributions,\n                                                                    \" contributions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center \".concat(themeClasses.card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                children: \"Ready to Join Our Community?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg \".concat(themeClasses.textSecondary, \" mb-8 max-w-2xl mx-auto\"),\n                                children: \"Connect with like-minded cybersecurity professionals and take your skills to the next level\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                        children: \"Join Discord Server\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                        children: \"Follow on Twitter\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(CommunityPage, \"evBCmepTbB2aOrEHc2m+0lAaZ10=\");\n_c = CommunityPage;\nvar _c;\n$RefreshReg$(_c, \"CommunityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/community/page.tsx\n"));

/***/ })

});