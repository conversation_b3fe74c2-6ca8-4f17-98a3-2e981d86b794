import { elasticsearch } from './database'

// Elasticsearch index configurations
export const ELAST<PERSON>SEARCH_INDICES = {
  CVE: 'kodexguard-cve',
  SCAN_RESULTS: 'kodexguard-scan-results',
  OSINT_DATA: 'kodexguard-osint',
  FILE_ANALYSIS: 'kodexguard-files',
  DORK_RESULTS: 'kodexguard-dorks',
  ACTIVITY_LOGS: 'kodexguard-logs',
}

// Index mappings
const CVE_MAPPING = {
  properties: {
    cve_id: { type: 'keyword' },
    description: { 
      type: 'text',
      analyzer: 'standard',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    severity: { type: 'keyword' },
    cvss_score: { type: 'float' },
    cvss_vector: { type: 'keyword' },
    published_date: { type: 'date' },
    last_modified: { type: 'date' },
    references: { type: 'nested' },
    affected_products: { type: 'nested' },
    cwe_id: { type: 'keyword' },
    cwe_description: { type: 'text' },
    exploit_available: { type: 'boolean' },
    patch_available: { type: 'boolean' },
    tags: { type: 'keyword' },
    source: { type: 'keyword' },
    created_at: { type: 'date' },
    updated_at: { type: 'date' }
  }
}

const SCAN_RESULTS_MAPPING = {
  properties: {
    user_id: { type: 'keyword' },
    scan_type: { type: 'keyword' },
    target: { 
      type: 'text',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    status: { type: 'keyword' },
    results: { type: 'object', enabled: false },
    metadata: { type: 'object' },
    vulnerabilities: {
      type: 'nested',
      properties: {
        type: { type: 'keyword' },
        severity: { type: 'keyword' },
        description: { type: 'text' },
        location: { type: 'keyword' },
        payload: { type: 'keyword' }
      }
    },
    started_at: { type: 'date' },
    completed_at: { type: 'date' },
    created_at: { type: 'date' }
  }
}

const OSINT_DATA_MAPPING = {
  properties: {
    user_id: { type: 'keyword' },
    query_type: { type: 'keyword' },
    query_value: { 
      type: 'text',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    results: { type: 'object' },
    sources: { type: 'keyword' },
    confidence_score: { type: 'float' },
    created_at: { type: 'date' }
  }
}

const FILE_ANALYSIS_MAPPING = {
  properties: {
    user_id: { type: 'keyword' },
    file_name: { 
      type: 'text',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    file_hash: { type: 'keyword' },
    file_size: { type: 'long' },
    mime_type: { type: 'keyword' },
    is_malicious: { type: 'boolean' },
    threats: { type: 'keyword' },
    analysis_results: { type: 'object' },
    created_at: { type: 'date' }
  }
}

const DORK_RESULTS_MAPPING = {
  properties: {
    user_id: { type: 'keyword' },
    dork_query: { 
      type: 'text',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    results: {
      type: 'nested',
      properties: {
        title: { type: 'text' },
        url: { type: 'keyword' },
        snippet: { type: 'text' },
        domain: { type: 'keyword' }
      }
    },
    total_results: { type: 'integer' },
    created_at: { type: 'date' }
  }
}

const ACTIVITY_LOGS_MAPPING = {
  properties: {
    user_id: { type: 'keyword' },
    action: { type: 'keyword' },
    resource: { type: 'keyword' },
    resource_id: { type: 'keyword' },
    details: { type: 'object' },
    ip_address: { type: 'ip' },
    user_agent: { 
      type: 'text',
      fields: {
        keyword: { type: 'keyword' }
      }
    },
    created_at: { type: 'date' }
  }
}

// Elasticsearch utility class
export class ElasticsearchUtils {
  // Initialize all indices
  static async initializeIndices(): Promise<boolean> {
    try {
      console.log('🔄 Initializing Elasticsearch indices...')
      
      const indices = [
        { name: ELASTICSEARCH_INDICES.CVE, mapping: CVE_MAPPING },
        { name: ELASTICSEARCH_INDICES.SCAN_RESULTS, mapping: SCAN_RESULTS_MAPPING },
        { name: ELASTICSEARCH_INDICES.OSINT_DATA, mapping: OSINT_DATA_MAPPING },
        { name: ELASTICSEARCH_INDICES.FILE_ANALYSIS, mapping: FILE_ANALYSIS_MAPPING },
        { name: ELASTICSEARCH_INDICES.DORK_RESULTS, mapping: DORK_RESULTS_MAPPING },
        { name: ELASTICSEARCH_INDICES.ACTIVITY_LOGS, mapping: ACTIVITY_LOGS_MAPPING },
      ]

      for (const index of indices) {
        await this.createIndexIfNotExists(index.name, index.mapping)
      }

      console.log('✅ All Elasticsearch indices initialized')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Elasticsearch indices:', error)
      return false
    }
  }

  // Create index if it doesn't exist
  static async createIndexIfNotExists(indexName: string, mapping: any): Promise<void> {
    try {
      const exists = await elasticsearch.indices.exists({ index: indexName })
      
      if (!exists) {
        await elasticsearch.indices.create({
          index: indexName,
          body: {
            mappings: mapping,
            settings: {
              number_of_shards: 1,
              number_of_replicas: 0,
              analysis: {
                analyzer: {
                  custom_analyzer: {
                    type: 'custom',
                    tokenizer: 'standard',
                    filter: ['lowercase', 'stop']
                  }
                }
              }
            }
          }
        })
        console.log(`✅ Created Elasticsearch index: ${indexName}`)
      } else {
        console.log(`ℹ️ Elasticsearch index already exists: ${indexName}`)
      }
    } catch (error) {
      console.error(`❌ Error creating index ${indexName}:`, error)
      throw error
    }
  }

  // Index document
  static async indexDocument(
    indexName: string, 
    document: any, 
    id?: string
  ): Promise<string | null> {
    try {
      const response = await elasticsearch.index({
        index: indexName,
        id,
        body: document,
        refresh: 'wait_for'
      })
      
      return response._id
    } catch (error) {
      console.error('Elasticsearch index error:', error)
      return null
    }
  }

  // Search documents
  static async search(
    indexName: string, 
    query: any, 
    options: {
      from?: number
      size?: number
      sort?: any[]
    } = {}
  ): Promise<any> {
    try {
      const response = await elasticsearch.search({
        index: indexName,
        body: {
          query,
          from: options.from || 0,
          size: options.size || 10,
          sort: options.sort || [{ created_at: { order: 'desc' } }]
        }
      })
      
      return {
        hits: response.hits.hits.map((hit: any) => ({
          id: hit._id,
          score: hit._score,
          ...hit._source
        })),
        total: response.hits.total,
        took: response.took
      }
    } catch (error) {
      console.error('Elasticsearch search error:', error)
      return { hits: [], total: 0, took: 0 }
    }
  }

  // Update document
  static async updateDocument(
    indexName: string, 
    id: string, 
    document: any
  ): Promise<boolean> {
    try {
      await elasticsearch.update({
        index: indexName,
        id,
        body: {
          doc: document
        },
        refresh: 'wait_for'
      })
      
      return true
    } catch (error) {
      console.error('Elasticsearch update error:', error)
      return false
    }
  }

  // Delete document
  static async deleteDocument(indexName: string, id: string): Promise<boolean> {
    try {
      await elasticsearch.delete({
        index: indexName,
        id,
        refresh: 'wait_for'
      })
      
      return true
    } catch (error) {
      console.error('Elasticsearch delete error:', error)
      return false
    }
  }

  // Bulk operations
  static async bulkIndex(indexName: string, documents: any[]): Promise<boolean> {
    try {
      const body = documents.flatMap(doc => [
        { index: { _index: indexName, _id: doc.id } },
        doc
      ])
      
      const response = await elasticsearch.bulk({
        body,
        refresh: 'wait_for'
      })
      
      if (response.errors) {
        console.error('Bulk index errors:', response.items.filter((item: any) => item.index.error))
        return false
      }
      
      return true
    } catch (error) {
      console.error('Elasticsearch bulk index error:', error)
      return false
    }
  }

  // CVE specific search
  static async searchCVE(params: {
    query?: string
    severity?: string[]
    dateFrom?: string
    dateTo?: string
    hasExploit?: boolean
    hasPatch?: boolean
    page?: number
    limit?: number
  }): Promise<any> {
    const must: any[] = []
    const filter: any[] = []

    if (params.query) {
      must.push({
        multi_match: {
          query: params.query,
          fields: ['cve_id^3', 'description^2', 'cwe_description'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      })
    }

    if (params.severity && params.severity.length > 0) {
      filter.push({ terms: { severity: params.severity } })
    }

    if (params.dateFrom || params.dateTo) {
      const dateRange: any = {}
      if (params.dateFrom) dateRange.gte = params.dateFrom
      if (params.dateTo) dateRange.lte = params.dateTo
      filter.push({ range: { published_date: dateRange } })
    }

    if (params.hasExploit !== undefined) {
      filter.push({ term: { exploit_available: params.hasExploit } })
    }

    if (params.hasPatch !== undefined) {
      filter.push({ term: { patch_available: params.hasPatch } })
    }

    const query = {
      bool: {
        must: must.length > 0 ? must : [{ match_all: {} }],
        filter
      }
    }

    return this.search(ELASTICSEARCH_INDICES.CVE, query, {
      from: ((params.page || 1) - 1) * (params.limit || 10),
      size: params.limit || 10,
      sort: [{ cvss_score: { order: 'desc' } }, { published_date: { order: 'desc' } }]
    })
  }

  // OSINT specific search
  static async searchOSINT(userId: string, queryType?: string, limit: number = 10): Promise<any> {
    const filter: any[] = [{ term: { user_id: userId } }]
    
    if (queryType) {
      filter.push({ term: { query_type: queryType } })
    }

    const query = {
      bool: {
        filter
      }
    }

    return this.search(ELASTICSEARCH_INDICES.OSINT_DATA, query, {
      size: limit,
      sort: [{ created_at: { order: 'desc' } }]
    })
  }
}

export default ElasticsearchUtils
