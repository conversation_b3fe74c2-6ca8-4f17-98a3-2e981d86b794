/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/dashboard/page";
exports.ids = ["app/admin/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/dashboard/page.tsx */ \"(rsc)/./app/admin/dashboard/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/dashboard/page\",\n        pathname: \"/admin/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cadmin%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cadmin%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/dashboard/page.tsx */ \"(ssr)/./app/admin/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDYWRtaW4lNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXVHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/ZjZmMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxLb2RlLVhHdWFyZFxcXFxhcHBcXFxcYWRtaW5cXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cadmin%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Toast.tsx */ \"(ssr)/./components/Toast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/ThemeContext.tsx */ \"(ssr)/./contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RvYXN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDS29kZS1YR3VhcmQlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUEwSDtBQUMxSDtBQUNBLGtLQUFxSSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvPzlhMzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXEtvZGUtWEd1YXJkXFxcXGNvbXBvbmVudHNcXFxcVG9hc3QudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXEtvZGUtWEd1YXJkXFxcXGNvbnRleHRzXFxcXFRoZW1lQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/dashboard/page.tsx":
/*!**************************************!*\
  !*** ./app/admin/dashboard/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/__barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AdminPage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        activeUsers: 0,\n        totalScans: 0,\n        totalVulnerabilities: 0,\n        systemUptime: 0,\n        apiCalls: 0,\n        storageUsed: 0,\n        bandwidthUsed: 0\n    });\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        database: \"online\",\n        api: \"online\",\n        scanner: \"online\",\n        osint: \"online\",\n        fileAnalyzer: \"online\"\n    });\n    const [recentUsers, setRecentUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAdminData();\n    }, []);\n    const loadAdminData = async ()=>{\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            setStats({\n                totalUsers: 15420,\n                activeUsers: 1247,\n                totalScans: 89456,\n                totalVulnerabilities: 2341,\n                systemUptime: 99.8,\n                apiCalls: 1247890,\n                storageUsed: 78.5,\n                bandwidthUsed: 45.2\n            });\n            setRecentUsers([\n                {\n                    id: \"1\",\n                    username: \"CyberNinja\",\n                    email: \"<EMAIL>\",\n                    plan: \"Elite\",\n                    joinedAt: \"2024-01-15\",\n                    lastActive: \"2 minutes ago\",\n                    status: \"active\"\n                },\n                {\n                    id: \"2\",\n                    username: \"SecurityMaster\",\n                    email: \"<EMAIL>\",\n                    plan: \"Pro\",\n                    joinedAt: \"2024-02-01\",\n                    lastActive: \"15 minutes ago\",\n                    status: \"active\"\n                },\n                {\n                    id: \"3\",\n                    username: \"PentestPro\",\n                    email: \"<EMAIL>\",\n                    plan: \"Expert\",\n                    joinedAt: \"2024-03-10\",\n                    lastActive: \"1 hour ago\",\n                    status: \"inactive\"\n                }\n            ]);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading admin data:\", error);\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"text-green-400 bg-green-400/20\";\n            case \"offline\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"maintenance\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 29\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 30\n                }, this);\n            case \"maintenance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getUserStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"text-green-400\";\n            case \"inactive\":\n                return \"text-yellow-400\";\n            case \"banned\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading admin console...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Console\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"System administration and monitoring dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Online\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh Data\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg\",\n                    children: [\n                        {\n                            id: \"overview\",\n                            label: \"Overview\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                        },\n                        {\n                            id: \"users\",\n                            label: \"Users\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                        },\n                        {\n                            id: \"system\",\n                            label: \"System\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                        },\n                        {\n                            id: \"security\",\n                            label: \"Security\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        },\n                        {\n                            id: \"settings\",\n                            label: \"Settings\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                        }\n                    ].map((tab)=>{\n                        const Icon = tab.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-green-400 mt-1\",\n                                            children: [\n                                                \"+\",\n                                                stats.activeUsers,\n                                                \" active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalScans.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Scans\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-400 mt-1\",\n                                            children: \"+1.2K today\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalVulnerabilities.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Vulnerabilities\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-red-400 mt-1\",\n                                            children: \"45 critical\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: [\n                                                stats.systemUptime,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Uptime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-green-400 mt-1\",\n                                            children: \"99.8% this month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"System\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                    children: Object.entries(systemStatus).map(([service, status])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-lg bg-cyber-secondary/5 border border-cyber-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white capitalize\",\n                                                            children: service.replace(/([A-Z])/g, \" $1\").trim()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-bold ${getStatusColor(status)}`,\n                                                            children: [\n                                                                getStatusIcon(status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"capitalize\",\n                                                                    children: status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: status === \"online\" ? \"Running normally\" : status === \"offline\" ? \"Service unavailable\" : \"Under maintenance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, service, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-glow\",\n                                                    children: \"API\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-pink\",\n                                                    children: \"Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Total API Calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: stats.apiCalls.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Avg Response Time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: \"145ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Error Rate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-400 font-bold\",\n                                                            children: \"0.02%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Rate Limit Hits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400 font-bold\",\n                                                            children: \"12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-glow\",\n                                                    children: \"Resource\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-pink\",\n                                                    children: \"Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Storage Used\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: [\n                                                                        stats.storageUsed,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-cyber-dark rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: `${stats.storageUsed}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Bandwidth Used\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: [\n                                                                        stats.bandwidthUsed,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-cyber-dark rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-cyber-secondary h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: `${stats.bandwidthUsed}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"CPU Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: \"23%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Memory Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-bold\",\n                                                            children: \"67%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add User\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-primary mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: stats.totalUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: stats.activeUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-accent mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"1,247\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Premium Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-secondary mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"+12%\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Growth Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"Recent Users\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: recentUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-cyber-primary/20 flex items-center justify-center text-sm font-bold text-cyber-primary\",\n                                                            children: user.username.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: user.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-cyber-accent\",\n                                                                    children: user.plan\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"Plan\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `text-sm font-medium ${getUserStatusColor(user.status)}`,\n                                                                    children: user.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: user.lastActive\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-2 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"system\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Service Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(systemStatus).map(([service, status])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `w-3 h-3 rounded-full ${status === \"online\" ? \"bg-green-400\" : status === \"offline\" ? \"bg-red-400\" : \"bg-yellow-400\"}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium capitalize\",\n                                                                    children: service.replace(/([A-Z])/g, \" $1\").trim()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-green-500/20 text-green-400 hover:bg-green-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-red-500/20 text-red-400 hover:bg-red-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__.Stop, {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, service, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Database Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Backup Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-secondary/20 text-cyber-secondary hover:bg-cyber-secondary/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Restore Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-accent/20 text-cyber-accent hover:bg-cyber-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Optimize Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Security\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Security Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-red-500/10 border border-red-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400 font-medium\",\n                                                                    children: \"Critical Alert\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: \"Multiple failed login attempts detected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-yellow-400 font-medium\",\n                                                                    children: \"Warning\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: \"High API usage detected from IP *************\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Access Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Two-Factor Authentication\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Enabled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"IP Whitelist\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Rate Limiting\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Stop_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"Configuration\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Maintenance Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Enable system maintenance mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Debug Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Enable detailed logging\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-cyber-primary transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Load user from localStorage and verify token\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                if (!token || !storedUser) {\n                    router.push(\"/login\");\n                    return;\n                }\n                // Parse stored user\n                const userData = JSON.parse(storedUser);\n                setUser(userData);\n                // Verify token with server\n                const response = await fetch(\"/api/user/profile\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    // Token is invalid, redirect to login\n                    localStorage.removeItem(\"token\");\n                    localStorage.removeItem(\"refreshToken\");\n                    localStorage.removeItem(\"user\");\n                    router.push(\"/login\");\n                    return;\n                }\n                const { data } = await response.json();\n                // Update user data\n                setUser(data.user);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"refreshToken\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const closeSidebar = ()=>{\n        setIsSidebarOpen(false);\n    };\n    // Show loading spinner while checking auth\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if no user (will redirect)\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                user: user,\n                onMenuToggle: toggleSidebar,\n                isSidebarOpen: isSidebarOpen\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isOpen: isSidebarOpen,\n                        onClose: closeSidebar,\n                        user: user\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 lg:ml-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6 px-4 sm:px-6 lg:px-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user, onMenuToggle, isSidebarOpen }) {\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleLogout = async ()=>{\n        try {\n            // Call logout API\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"token\")}`\n                }\n            });\n            // Clear local storage\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            // Redirect to home\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"free\":\n                return \"text-gray-600\";\n            case \"student\":\n                return \"text-blue-600\";\n            case \"hobby\":\n                return \"text-green-600\";\n            case \"bughunter\":\n                return \"text-purple-600\";\n            case \"cybersecurity\":\n                return \"text-orange-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getPlanIcon = (plan)=>{\n        if (plan === \"cybersecurity\" || plan === \"bughunter\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 72,\n                columnNumber: 14\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-full mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onMenuToggle,\n                                    className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900 hidden sm:block\",\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-lg mx-4 hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search CVE, vulnerabilities, tools...\",\n                                        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                                    className: \"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                user ? /* User menu */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.fullName,\n                                                        className: \"h-8 w-8 rounded-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: user.fullName.charAt(0).toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden sm:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: user.fullName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-xs flex items-center space-x-1 ${getPlanColor(user.plan)}`,\n                                                                children: [\n                                                                    getPlanIcon(user.plan),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"capitalize\",\n                                                                        children: user.plan\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: user.fullName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-xs flex items-center space-x-1 mt-1 ${getPlanColor(user.plan)}`,\n                                                                children: [\n                                                                    getPlanIcon(user.plan),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"capitalize\",\n                                                                        children: [\n                                                                            user.plan,\n                                                                            \" Plan\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        onClick: ()=>setIsProfileOpen(false),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Profile\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/plan\",\n                                                        className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        onClick: ()=>setIsProfileOpen(false),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Upgrade Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        onClick: ()=>setIsProfileOpen(false),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Logout\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this) : /* Login/Register buttons */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/login\",\n                                            className: \"text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/register\",\n                                            className: \"btn-primary text-sm\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-3 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search CVE, vulnerabilities, tools...\",\n                                className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bomb.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bomb,Bot,ChevronDown,ChevronRight,Code,CreditCard,Database,FileText,Globe,LayoutDashboard,Lock,Search,Settings,Shield,Trophy,User,Wrench,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose, user }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"tools\"\n    ]);\n    const toggleExpanded = (name)=>{\n        setExpandedItems((prev)=>prev.includes(name) ? prev.filter((item)=>item !== name) : [\n                ...prev,\n                name\n            ]);\n    };\n    const menuItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT Investigator\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            badge: \"Hot\"\n        },\n        {\n            name: \"Vulnerability Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            premium: true\n        },\n        {\n            name: \"CVE Intelligence\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            children: [\n                {\n                    name: \"CVE Search\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                },\n                {\n                    name: \"Daily Updates\",\n                    href: \"/cve?tab=daily\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                },\n                {\n                    name: \"Trending CVEs\",\n                    href: \"/cve?tab=trending\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            premium: true\n        },\n        {\n            name: \"Google Dorking\",\n            href: \"/dorking\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: [\n                {\n                    name: \"Dork Presets\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Custom Dorks\",\n                    href: \"/dorking?tab=custom\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                },\n                {\n                    name: \"Execution History\",\n                    href: \"/dorking?tab=history\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Developer Tools\",\n            href: \"/tools\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            children: [\n                {\n                    name: \"Hash Generator\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                },\n                {\n                    name: \"Encoder/Decoder\",\n                    href: \"/tools?tab=encode\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                },\n                {\n                    name: \"Payload Generator\",\n                    href: \"/tools?tab=payload\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                },\n                {\n                    name: \"API Playground\",\n                    href: \"/playground\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            premium: true,\n            adminOnly: true\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            name: \"Profile & API Keys\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n        },\n        {\n            name: \"Plans & Billing\",\n            href: \"/plan\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n        }\n    ];\n    // Add admin menu items\n    if (user?.role === \"admin\" || user?.role === \"super_admin\") {\n        menuItems.push({\n            name: \"Admin Panel\",\n            href: \"/admin\",\n            icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            adminOnly: true,\n            children: [\n                {\n                    name: \"Dashboard\",\n                    href: \"/admin/dashboard\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                },\n                {\n                    name: \"User Management\",\n                    href: \"/admin/users\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n                },\n                {\n                    name: \"Plan Management\",\n                    href: \"/admin/plans\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                },\n                {\n                    name: \"Bot Management\",\n                    href: \"/admin/bots\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                },\n                {\n                    name: \"System Settings\",\n                    href: \"/admin/settings\",\n                    icon: _barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                }\n            ]\n        });\n    }\n    const isActive = (href)=>{\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\";\n        }\n        return pathname.startsWith(href);\n    };\n    const canAccess = (item)=>{\n        // Check admin access\n        if (item.adminOnly && user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n            return false;\n        }\n        // Check premium access\n        if (item.premium && user?.plan === \"free\") {\n            return false;\n        }\n        return true;\n    };\n    const renderMenuItem = (item, level = 0)=>{\n        if (!canAccess(item)) {\n            return null;\n        }\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.name);\n        const itemIsActive = isActive(item.href);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex items-center ${level > 0 ? \"pl-4\" : \"\"}`,\n                    children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>toggleExpanded(item.name),\n                        className: `flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${itemIsActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 text-left\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, this),\n                            item.premium && user?.plan === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 text-orange-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this),\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        onClick: onClose,\n                        className: `flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${itemIsActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-5 w-5 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-red-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this),\n                            item.premium && user?.plan === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 mt-1 space-y-1\",\n                    children: item.children?.map((child)=>renderMenuItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, item.name, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${isOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200 lg:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bomb_Bot_ChevronDown_ChevronRight_Code_CreditCard_Database_FileText_Globe_LayoutDashboard_Lock_Search_Settings_Shield_Trophy_User_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-2 overflow-y-auto\",\n                            children: menuItems.map((item)=>renderMenuItem(item))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"KodeXGuard v1.0.0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1\",\n                                        children: \"\\xa9 2025 All rights reserved\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUNKO0FBQ2lCO0FBcUJ4QjtBQXFCTixTQUFTc0IsUUFBUSxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsSUFBSSxFQUFnQjtJQUNyRSxNQUFNQyxXQUFXeEIsNERBQVdBO0lBQzVCLE1BQU0sQ0FBQ3lCLGVBQWVDLGlCQUFpQixHQUFHNUIsK0NBQVFBLENBQVc7UUFBQztLQUFRO0lBRXRFLE1BQU02QixpQkFBaUIsQ0FBQ0M7UUFDdEJGLGlCQUFpQkcsQ0FBQUEsT0FDZkEsS0FBS0MsUUFBUSxDQUFDRixRQUNWQyxLQUFLRSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLFNBQVNKLFFBQzdCO21CQUFJQztnQkFBTUQ7YUFBSztJQUV2QjtJQUVBLE1BQU1LLFlBQXdCO1FBQzVCO1lBQ0VMLE1BQU07WUFDTk0sTUFBTTtZQUNOQyxNQUFNbEMsaU9BQWVBO1FBQ3ZCO1FBQ0E7WUFDRTJCLE1BQU07WUFDTk0sTUFBTTtZQUNOQyxNQUFNakMsaU9BQU1BO1lBQ1prQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFUixNQUFNO1lBQ05NLE1BQU07WUFDTkMsTUFBTWhDLGlPQUFNQTtZQUNaa0MsU0FBUztRQUNYO1FBQ0E7WUFDRVQsTUFBTTtZQUNOTSxNQUFNO1lBQ05DLE1BQU1uQixpT0FBUUE7WUFDZHNCLFVBQVU7Z0JBQ1I7b0JBQUVWLE1BQU07b0JBQWNNLE1BQU07b0JBQVFDLE1BQU1qQyxpT0FBTUE7Z0JBQUM7Z0JBQ2pEO29CQUFFMEIsTUFBTTtvQkFBaUJNLE1BQU07b0JBQWtCQyxNQUFNaEIsaU9BQVFBO2dCQUFDO2dCQUNoRTtvQkFBRVMsTUFBTTtvQkFBaUJNLE1BQU07b0JBQXFCQyxNQUFNbEIsaU9BQUdBO2dCQUFDO2FBQy9EO1FBQ0g7UUFDQTtZQUNFVyxNQUFNO1lBQ05NLE1BQU07WUFDTkMsTUFBTTlCLGtPQUFRQTtZQUNkZ0MsU0FBUztRQUNYO1FBQ0E7WUFDRVQsTUFBTTtZQUNOTSxNQUFNO1lBQ05DLE1BQU03QixrT0FBS0E7WUFDWGdDLFVBQVU7Z0JBQ1I7b0JBQUVWLE1BQU07b0JBQWdCTSxNQUFNO29CQUFZQyxNQUFNbEIsaU9BQUdBO2dCQUFDO2dCQUNwRDtvQkFBRVcsTUFBTTtvQkFBZ0JNLE1BQU07b0JBQXVCQyxNQUFNNUIsa09BQUlBO2dCQUFDO2dCQUNoRTtvQkFBRXFCLE1BQU07b0JBQXFCTSxNQUFNO29CQUF3QkMsTUFBTWhCLGlPQUFRQTtnQkFBQzthQUMzRTtRQUNIO1FBQ0E7WUFDRVMsTUFBTTtZQUNOTSxNQUFNO1lBQ05DLE1BQU0zQixrT0FBTUE7WUFDWjhCLFVBQVU7Z0JBQ1I7b0JBQUVWLE1BQU07b0JBQWtCTSxNQUFNO29CQUFVQyxNQUFNakIsa09BQUlBO2dCQUFDO2dCQUNyRDtvQkFBRVUsTUFBTTtvQkFBbUJNLE1BQU07b0JBQXFCQyxNQUFNNUIsa09BQUlBO2dCQUFDO2dCQUNqRTtvQkFBRXFCLE1BQU07b0JBQXFCTSxNQUFNO29CQUFzQkMsTUFBTS9CLGtPQUFJQTtnQkFBQztnQkFDcEU7b0JBQUV3QixNQUFNO29CQUFrQk0sTUFBTTtvQkFBZUMsTUFBTTNCLGtPQUFNQTtnQkFBQzthQUM3RDtRQUNIO1FBQ0E7WUFDRW9CLE1BQU07WUFDTk0sTUFBTTtZQUNOQyxNQUFNMUIsa09BQUdBO1lBQ1Q0QixTQUFTO1lBQ1RFLFdBQVc7UUFDYjtRQUNBO1lBQ0VYLE1BQU07WUFDTk0sTUFBTTtZQUNOQyxNQUFNekIsa09BQU1BO1FBQ2Q7UUFDQTtZQUNFa0IsTUFBTTtZQUNOTSxNQUFNO1lBQ05DLE1BQU14QixrT0FBSUE7UUFDWjtRQUNBO1lBQ0VpQixNQUFNO1lBQ05NLE1BQU07WUFDTkMsTUFBTXZCLGtPQUFVQTtRQUNsQjtLQUNEO0lBRUQsdUJBQXVCO0lBQ3ZCLElBQUlXLE1BQU1pQixTQUFTLFdBQVdqQixNQUFNaUIsU0FBUyxlQUFlO1FBQzFEUCxVQUFVUSxJQUFJLENBQUM7WUFDYmIsTUFBTTtZQUNOTSxNQUFNO1lBQ05DLE1BQU10QixrT0FBUUE7WUFDZDBCLFdBQVc7WUFDWEQsVUFBVTtnQkFDUjtvQkFBRVYsTUFBTTtvQkFBYU0sTUFBTTtvQkFBb0JDLE1BQU1sQyxpT0FBZUE7Z0JBQUM7Z0JBQ3JFO29CQUFFMkIsTUFBTTtvQkFBbUJNLE1BQU07b0JBQWdCQyxNQUFNeEIsa09BQUlBO2dCQUFDO2dCQUM1RDtvQkFBRWlCLE1BQU07b0JBQW1CTSxNQUFNO29CQUFnQkMsTUFBTXZCLGtPQUFVQTtnQkFBQztnQkFDbEU7b0JBQUVnQixNQUFNO29CQUFrQk0sTUFBTTtvQkFBZUMsTUFBTTFCLGtPQUFHQTtnQkFBQztnQkFDekQ7b0JBQUVtQixNQUFNO29CQUFtQk0sTUFBTTtvQkFBbUJDLE1BQU10QixrT0FBUUE7Z0JBQUM7YUFDcEU7UUFDSDtJQUNGO0lBRUEsTUFBTTZCLFdBQVcsQ0FBQ1I7UUFDaEIsSUFBSUEsU0FBUyxjQUFjO1lBQ3pCLE9BQU9WLGFBQWE7UUFDdEI7UUFDQSxPQUFPQSxTQUFTbUIsVUFBVSxDQUFDVDtJQUM3QjtJQUVBLE1BQU1VLFlBQVksQ0FBQ1o7UUFDakIscUJBQXFCO1FBQ3JCLElBQUlBLEtBQUtPLFNBQVMsSUFBSWhCLE1BQU1pQixTQUFTLFdBQVdqQixNQUFNaUIsU0FBUyxlQUFlO1lBQzVFLE9BQU87UUFDVDtRQUVBLHVCQUF1QjtRQUN2QixJQUFJUixLQUFLSyxPQUFPLElBQUlkLE1BQU1zQixTQUFTLFFBQVE7WUFDekMsT0FBTztRQUNUO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNkLE1BQWdCZSxRQUFRLENBQUM7UUFDL0MsSUFBSSxDQUFDSCxVQUFVWixPQUFPO1lBQ3BCLE9BQU87UUFDVDtRQUVBLE1BQU1nQixjQUFjaEIsS0FBS00sUUFBUSxJQUFJTixLQUFLTSxRQUFRLENBQUNXLE1BQU0sR0FBRztRQUM1RCxNQUFNQyxhQUFhekIsY0FBY0ssUUFBUSxDQUFDRSxLQUFLSixJQUFJO1FBQ25ELE1BQU11QixlQUFlVCxTQUFTVixLQUFLRSxJQUFJO1FBRXZDLHFCQUNFLDhEQUFDa0I7OzhCQUNDLDhEQUFDQTtvQkFBSUMsV0FBVyxDQUFDLGtCQUFrQixFQUFFTixRQUFRLElBQUksU0FBUyxHQUFHLENBQUM7OEJBQzNEQyw0QkFDQyw4REFBQ007d0JBQ0NDLFNBQVMsSUFBTTVCLGVBQWVLLEtBQUtKLElBQUk7d0JBQ3ZDeUIsV0FBVyxDQUFDLG9GQUFvRixFQUM5RkYsZUFDSSxvQ0FDQSxrQ0FDTCxDQUFDOzswQ0FFRiw4REFBQ25CLEtBQUtHLElBQUk7Z0NBQUNrQixXQUFVOzs7Ozs7MENBQ3JCLDhEQUFDRztnQ0FBS0gsV0FBVTswQ0FBb0JyQixLQUFLSixJQUFJOzs7Ozs7NEJBQzVDSSxLQUFLSSxLQUFLLGtCQUNULDhEQUFDb0I7Z0NBQUtILFdBQVU7MENBQ2JyQixLQUFLSSxLQUFLOzs7Ozs7NEJBR2RKLEtBQUtLLE9BQU8sSUFBSWQsTUFBTXNCLFNBQVMsd0JBQzlCLDhEQUFDM0Isa09BQUlBO2dDQUFDbUMsV0FBVTs7Ozs7OzRCQUVqQkgsMkJBQ0MsOERBQUNwQyxrT0FBV0E7Z0NBQUN1QyxXQUFVOzs7OztxREFFdkIsOERBQUN0QyxrT0FBWUE7Z0NBQUNzQyxXQUFVOzs7Ozs7Ozs7Ozs2Q0FJNUIsOERBQUN0RCxpREFBSUE7d0JBQ0htQyxNQUFNRixLQUFLRSxJQUFJO3dCQUNmcUIsU0FBU2pDO3dCQUNUK0IsV0FBVyxDQUFDLG9GQUFvRixFQUM5RkYsZUFDSSxvQ0FDQSxrQ0FDTCxDQUFDOzswQ0FFRiw4REFBQ25CLEtBQUtHLElBQUk7Z0NBQUNrQixXQUFVOzs7Ozs7MENBQ3JCLDhEQUFDRztnQ0FBS0gsV0FBVTswQ0FBVXJCLEtBQUtKLElBQUk7Ozs7Ozs0QkFDbENJLEtBQUtJLEtBQUssa0JBQ1QsOERBQUNvQjtnQ0FBS0gsV0FBVTswQ0FDYnJCLEtBQUtJLEtBQUs7Ozs7Ozs0QkFHZEosS0FBS0ssT0FBTyxJQUFJZCxNQUFNc0IsU0FBUyx3QkFDOUIsOERBQUMzQixrT0FBSUE7Z0NBQUNtQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztnQkFPdkJMLGVBQWVFLDRCQUNkLDhEQUFDRTtvQkFBSUMsV0FBVTs4QkFDWnJCLEtBQUtNLFFBQVEsRUFBRW1CLElBQUlDLENBQUFBLFFBQVNaLGVBQWVZLE9BQU9YLFFBQVE7Ozs7Ozs7V0F0RHZEZixLQUFLSixJQUFJOzs7OztJQTJEdkI7SUFFQSxxQkFDRTs7WUFFR1Asd0JBQ0MsOERBQUMrQjtnQkFDQ0MsV0FBVTtnQkFDVkUsU0FBU2pDOzs7Ozs7MEJBS2IsOERBQUM4QjtnQkFDQ0MsV0FBVyxDQUFDLGtKQUFrSixFQUM1SmhDLFNBQVMsa0JBQWtCLG9CQUM1QixDQUFDOzBCQUVGLDRFQUFDK0I7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2xELGlPQUFNQTs0Q0FBQ2tELFdBQVU7Ozs7OztzREFDbEIsOERBQUNHOzRDQUFLSCxXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7OzhDQUVwRCw4REFBQ0M7b0NBQ0NDLFNBQVNqQztvQ0FDVCtCLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7OztzQ0FNSCw4REFBQ007NEJBQUlOLFdBQVU7c0NBQ1pwQixVQUFVd0IsR0FBRyxDQUFDekIsQ0FBQUEsT0FBUWMsZUFBZWQ7Ozs7OztzQ0FJeEMsOERBQUNvQjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTztrREFBRTs7Ozs7O2tEQUNILDhEQUFDQTt3Q0FBRVAsV0FBVTtrREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL2NvbXBvbmVudHMvU2lkZWJhci50c3g/NmJhOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgXG4gIExheW91dERhc2hib2FyZCxcbiAgU2VhcmNoLFxuICBTaGllbGQsXG4gIEJvbWIsXG4gIEZpbGVUZXh0LFxuICBHbG9iZSxcbiAgQ29kZSxcbiAgV3JlbmNoLFxuICBCb3QsXG4gIFRyb3BoeSxcbiAgVXNlcixcbiAgQ3JlZGl0Q2FyZCxcbiAgU2V0dGluZ3MsXG4gIENoZXZyb25Eb3duLFxuICBDaGV2cm9uUmlnaHQsXG4gIERhdGFiYXNlLFxuICBaYXAsXG4gIExvY2ssXG4gIEFjdGl2aXR5XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFNpZGViYXJQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhblxuICBvbkNsb3NlPzogKCkgPT4gdm9pZFxuICB1c2VyPzoge1xuICAgIHJvbGU6IHN0cmluZ1xuICAgIHBsYW46IHN0cmluZ1xuICB9XG59XG5cbmludGVyZmFjZSBNZW51SXRlbSB7XG4gIG5hbWU6IHN0cmluZ1xuICBocmVmOiBzdHJpbmdcbiAgaWNvbjogYW55XG4gIGJhZGdlPzogc3RyaW5nXG4gIGNoaWxkcmVuPzogTWVudUl0ZW1bXVxuICBhZG1pbk9ubHk/OiBib29sZWFuXG4gIHByZW1pdW0/OiBib29sZWFuXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNpZGViYXIoeyBpc09wZW4sIG9uQ2xvc2UsIHVzZXIgfTogU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBjb25zdCBbZXhwYW5kZWRJdGVtcywgc2V0RXhwYW5kZWRJdGVtc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oWyd0b29scyddKVxuXG4gIGNvbnN0IHRvZ2dsZUV4cGFuZGVkID0gKG5hbWU6IHN0cmluZykgPT4ge1xuICAgIHNldEV4cGFuZGVkSXRlbXMocHJldiA9PiBcbiAgICAgIHByZXYuaW5jbHVkZXMobmFtZSkgXG4gICAgICAgID8gcHJldi5maWx0ZXIoaXRlbSA9PiBpdGVtICE9PSBuYW1lKVxuICAgICAgICA6IFsuLi5wcmV2LCBuYW1lXVxuICAgIClcbiAgfVxuXG4gIGNvbnN0IG1lbnVJdGVtczogTWVudUl0ZW1bXSA9IFtcbiAgICB7XG4gICAgICBuYW1lOiAnRGFzaGJvYXJkJyxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkJyxcbiAgICAgIGljb246IExheW91dERhc2hib2FyZFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ09TSU5UIEludmVzdGlnYXRvcicsXG4gICAgICBocmVmOiAnL29zaW50JyxcbiAgICAgIGljb246IFNlYXJjaCxcbiAgICAgIGJhZGdlOiAnSG90J1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ1Z1bG5lcmFiaWxpdHkgU2Nhbm5lcicsXG4gICAgICBocmVmOiAnL3NjYW5uZXInLFxuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgcHJlbWl1bTogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0NWRSBJbnRlbGxpZ2VuY2UnLFxuICAgICAgaHJlZjogJy9jdmUnLFxuICAgICAgaWNvbjogRGF0YWJhc2UsXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICB7IG5hbWU6ICdDVkUgU2VhcmNoJywgaHJlZjogJy9jdmUnLCBpY29uOiBTZWFyY2ggfSxcbiAgICAgICAgeyBuYW1lOiAnRGFpbHkgVXBkYXRlcycsIGhyZWY6ICcvY3ZlP3RhYj1kYWlseScsIGljb246IEFjdGl2aXR5IH0sXG4gICAgICAgIHsgbmFtZTogJ1RyZW5kaW5nIENWRXMnLCBocmVmOiAnL2N2ZT90YWI9dHJlbmRpbmcnLCBpY29uOiBaYXAgfVxuICAgICAgXVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0ZpbGUgQW5hbHl6ZXInLFxuICAgICAgaHJlZjogJy9maWxlLWFuYWx5emVyJyxcbiAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgcHJlbWl1bTogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0dvb2dsZSBEb3JraW5nJyxcbiAgICAgIGhyZWY6ICcvZG9ya2luZycsXG4gICAgICBpY29uOiBHbG9iZSxcbiAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgIHsgbmFtZTogJ0RvcmsgUHJlc2V0cycsIGhyZWY6ICcvZG9ya2luZycsIGljb246IFphcCB9LFxuICAgICAgICB7IG5hbWU6ICdDdXN0b20gRG9ya3MnLCBocmVmOiAnL2Rvcmtpbmc/dGFiPWN1c3RvbScsIGljb246IENvZGUgfSxcbiAgICAgICAgeyBuYW1lOiAnRXhlY3V0aW9uIEhpc3RvcnknLCBocmVmOiAnL2Rvcmtpbmc/dGFiPWhpc3RvcnknLCBpY29uOiBBY3Rpdml0eSB9XG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnRGV2ZWxvcGVyIFRvb2xzJyxcbiAgICAgIGhyZWY6ICcvdG9vbHMnLFxuICAgICAgaWNvbjogV3JlbmNoLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgeyBuYW1lOiAnSGFzaCBHZW5lcmF0b3InLCBocmVmOiAnL3Rvb2xzJywgaWNvbjogTG9jayB9LFxuICAgICAgICB7IG5hbWU6ICdFbmNvZGVyL0RlY29kZXInLCBocmVmOiAnL3Rvb2xzP3RhYj1lbmNvZGUnLCBpY29uOiBDb2RlIH0sXG4gICAgICAgIHsgbmFtZTogJ1BheWxvYWQgR2VuZXJhdG9yJywgaHJlZjogJy90b29scz90YWI9cGF5bG9hZCcsIGljb246IEJvbWIgfSxcbiAgICAgICAgeyBuYW1lOiAnQVBJIFBsYXlncm91bmQnLCBocmVmOiAnL3BsYXlncm91bmQnLCBpY29uOiBXcmVuY2ggfVxuICAgICAgXVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0JvdCBDZW50ZXInLFxuICAgICAgaHJlZjogJy9ib3QnLFxuICAgICAgaWNvbjogQm90LFxuICAgICAgcHJlbWl1bTogdHJ1ZSxcbiAgICAgIGFkbWluT25seTogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0xlYWRlcmJvYXJkJyxcbiAgICAgIGhyZWY6ICcvbGVhZGVyYm9hcmQnLFxuICAgICAgaWNvbjogVHJvcGh5XG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnUHJvZmlsZSAmIEFQSSBLZXlzJyxcbiAgICAgIGhyZWY6ICcvcHJvZmlsZScsXG4gICAgICBpY29uOiBVc2VyXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnUGxhbnMgJiBCaWxsaW5nJyxcbiAgICAgIGhyZWY6ICcvcGxhbicsXG4gICAgICBpY29uOiBDcmVkaXRDYXJkXG4gICAgfVxuICBdXG5cbiAgLy8gQWRkIGFkbWluIG1lbnUgaXRlbXNcbiAgaWYgKHVzZXI/LnJvbGUgPT09ICdhZG1pbicgfHwgdXNlcj8ucm9sZSA9PT0gJ3N1cGVyX2FkbWluJykge1xuICAgIG1lbnVJdGVtcy5wdXNoKHtcbiAgICAgIG5hbWU6ICdBZG1pbiBQYW5lbCcsXG4gICAgICBocmVmOiAnL2FkbWluJyxcbiAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgYWRtaW5Pbmx5OiB0cnVlLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgeyBuYW1lOiAnRGFzaGJvYXJkJywgaHJlZjogJy9hZG1pbi9kYXNoYm9hcmQnLCBpY29uOiBMYXlvdXREYXNoYm9hcmQgfSxcbiAgICAgICAgeyBuYW1lOiAnVXNlciBNYW5hZ2VtZW50JywgaHJlZjogJy9hZG1pbi91c2VycycsIGljb246IFVzZXIgfSxcbiAgICAgICAgeyBuYW1lOiAnUGxhbiBNYW5hZ2VtZW50JywgaHJlZjogJy9hZG1pbi9wbGFucycsIGljb246IENyZWRpdENhcmQgfSxcbiAgICAgICAgeyBuYW1lOiAnQm90IE1hbmFnZW1lbnQnLCBocmVmOiAnL2FkbWluL2JvdHMnLCBpY29uOiBCb3QgfSxcbiAgICAgICAgeyBuYW1lOiAnU3lzdGVtIFNldHRpbmdzJywgaHJlZjogJy9hZG1pbi9zZXR0aW5ncycsIGljb246IFNldHRpbmdzIH1cbiAgICAgIF1cbiAgICB9KVxuICB9XG5cbiAgY29uc3QgaXNBY3RpdmUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGhyZWYgPT09ICcvZGFzaGJvYXJkJykge1xuICAgICAgcmV0dXJuIHBhdGhuYW1lID09PSAnL2Rhc2hib2FyZCdcbiAgICB9XG4gICAgcmV0dXJuIHBhdGhuYW1lLnN0YXJ0c1dpdGgoaHJlZilcbiAgfVxuXG4gIGNvbnN0IGNhbkFjY2VzcyA9IChpdGVtOiBNZW51SXRlbSkgPT4ge1xuICAgIC8vIENoZWNrIGFkbWluIGFjY2Vzc1xuICAgIGlmIChpdGVtLmFkbWluT25seSAmJiB1c2VyPy5yb2xlICE9PSAnYWRtaW4nICYmIHVzZXI/LnJvbGUgIT09ICdzdXBlcl9hZG1pbicpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cblxuICAgIC8vIENoZWNrIHByZW1pdW0gYWNjZXNzXG4gICAgaWYgKGl0ZW0ucHJlbWl1bSAmJiB1c2VyPy5wbGFuID09PSAnZnJlZScpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cblxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBjb25zdCByZW5kZXJNZW51SXRlbSA9IChpdGVtOiBNZW51SXRlbSwgbGV2ZWwgPSAwKSA9PiB7XG4gICAgaWYgKCFjYW5BY2Nlc3MoaXRlbSkpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgY29uc3QgaGFzQ2hpbGRyZW4gPSBpdGVtLmNoaWxkcmVuICYmIGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID4gMFxuICAgIGNvbnN0IGlzRXhwYW5kZWQgPSBleHBhbmRlZEl0ZW1zLmluY2x1ZGVzKGl0ZW0ubmFtZSlcbiAgICBjb25zdCBpdGVtSXNBY3RpdmUgPSBpc0FjdGl2ZShpdGVtLmhyZWYpXG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBrZXk9e2l0ZW0ubmFtZX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgJHtsZXZlbCA+IDAgPyAncGwtNCcgOiAnJ31gfT5cbiAgICAgICAgICB7aGFzQ2hpbGRyZW4gPyAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUV4cGFuZGVkKGl0ZW0ubmFtZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBweC0zIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgaXRlbUlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICA/ICdiZy1wcmltYXJ5LTEwMCB0ZXh0LXByaW1hcnktNzAwJ1xuICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxpdGVtLmljb24gY2xhc3NOYW1lPVwiaC01IHctNSBtci0zXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtbGVmdFwiPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICB7aXRlbS5iYWRnZSAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCBtci0yXCI+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5iYWRnZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHtpdGVtLnByZW1pdW0gJiYgdXNlcj8ucGxhbiA9PT0gJ2ZyZWUnICYmIChcbiAgICAgICAgICAgICAgICA8TG9jayBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtb3JhbmdlLTUwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGl0ZW1Jc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgPyAnYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTcwMCdcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aXRlbS5pY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItM1wiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXgtMVwiPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICB7aXRlbS5iYWRnZSAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAge2l0ZW0uYmFkZ2V9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICB7aXRlbS5wcmVtaXVtICYmIHVzZXI/LnBsYW4gPT09ICdmcmVlJyAmJiAoXG4gICAgICAgICAgICAgICAgPExvY2sgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW9yYW5nZS01MDBcIiAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZW5kZXIgY2hpbGRyZW4gKi99XG4gICAgICAgIHtoYXNDaGlsZHJlbiAmJiBpc0V4cGFuZGVkICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTQgbXQtMSBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIHtpdGVtLmNoaWxkcmVuPy5tYXAoY2hpbGQgPT4gcmVuZGVyTWVudUl0ZW0oY2hpbGQsIGxldmVsICsgMSkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIE1vYmlsZSBvdmVybGF5ICovfVxuICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgei00MCBsZzpoaWRkZW5cIlxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgaW5zZXQteS0wIGxlZnQtMCB6LTUwIHctNjQgYmctd2hpdGUgc2hhZG93LWxnIHRyYW5zZm9ybSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgbGc6dHJhbnNsYXRlLXgtMCBsZzpzdGF0aWMgbGc6aW5zZXQtMCAke1xuICAgICAgICAgIGlzT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCdcbiAgICAgICAgfWB9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cbiAgICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgbGc6aGlkZGVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1wcmltYXJ5LTYwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5Lb2RlWEd1YXJkPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLW1kIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktNiBzcGFjZS15LTIgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICB7bWVudUl0ZW1zLm1hcChpdGVtID0+IHJlbmRlck1lbnVJdGVtKGl0ZW0pKX1cbiAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8cD5Lb2RlWEd1YXJkIHYxLjAuMDwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMVwiPsKpIDIwMjUgQWxsIHJpZ2h0cyByZXNlcnZlZDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJMaW5rIiwidXNlUGF0aG5hbWUiLCJMYXlvdXREYXNoYm9hcmQiLCJTZWFyY2giLCJTaGllbGQiLCJCb21iIiwiRmlsZVRleHQiLCJHbG9iZSIsIkNvZGUiLCJXcmVuY2giLCJCb3QiLCJUcm9waHkiLCJVc2VyIiwiQ3JlZGl0Q2FyZCIsIlNldHRpbmdzIiwiQ2hldnJvbkRvd24iLCJDaGV2cm9uUmlnaHQiLCJEYXRhYmFzZSIsIlphcCIsIkxvY2siLCJBY3Rpdml0eSIsIlNpZGViYXIiLCJpc09wZW4iLCJvbkNsb3NlIiwidXNlciIsInBhdGhuYW1lIiwiZXhwYW5kZWRJdGVtcyIsInNldEV4cGFuZGVkSXRlbXMiLCJ0b2dnbGVFeHBhbmRlZCIsIm5hbWUiLCJwcmV2IiwiaW5jbHVkZXMiLCJmaWx0ZXIiLCJpdGVtIiwibWVudUl0ZW1zIiwiaHJlZiIsImljb24iLCJiYWRnZSIsInByZW1pdW0iLCJjaGlsZHJlbiIsImFkbWluT25seSIsInJvbGUiLCJwdXNoIiwiaXNBY3RpdmUiLCJzdGFydHNXaXRoIiwiY2FuQWNjZXNzIiwicGxhbiIsInJlbmRlck1lbnVJdGVtIiwibGV2ZWwiLCJoYXNDaGlsZHJlbiIsImxlbmd0aCIsImlzRXhwYW5kZWQiLCJpdGVtSXNBY3RpdmUiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsIm1hcCIsImNoaWxkIiwibmF2IiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast,default auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove after duration\n        const duration = toast.duration || 5000;\n        setTimeout(()=>{\n            removeToast(id);\n        }, duration);\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"success\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"error\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"warning\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"info\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            success,\n            error,\n            warning,\n            info\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast,\n                onRemove: ()=>removeToast(toast.id)\n            }, toast.id, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastItem({ toast, onRemove }) {\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n      max-w-sm w-full shadow-lg rounded-lg border pointer-events-auto\n      ${getBackgroundColor()}\n      animate-slide-in-right\n    `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm font-medium ${getTextColor()}`,\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${toast.title ? \"mt-1\" : \"\"} ${getTextColor()}`,\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toast.action.onClick,\n                                    className: `text-sm font-medium underline hover:no-underline ${getTextColor()}`,\n                                    children: toast.action.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRemove,\n                            className: `rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for standalone usage\nconst toast = {\n    success: (message, title)=>{\n        // This will only work if ToastProvider is available\n        console.log(\"Success:\", title || \"\", message);\n    },\n    error: (message, title)=>{\n        console.log(\"Error:\", title || \"\", message);\n    },\n    warning: (message, title)=>{\n        console.log(\"Warning:\", title || \"\", message);\n    },\n    info: (message, title)=>{\n        console.log(\"Info:\", title || \"\", message);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRjtBQUNKO0FBd0IvRSxNQUFNUyw2QkFBZVQsb0RBQWFBLENBQStCVTtBQUUxRCxTQUFTQyxjQUFjLEVBQUVDLFFBQVEsRUFBMkI7SUFDakUsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdaLCtDQUFRQSxDQUFVLEVBQUU7SUFFaEQsTUFBTWEsV0FBV1osa0RBQVdBLENBQUMsQ0FBQ2E7UUFDNUIsTUFBTUMsS0FBS0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7UUFDaEQsTUFBTUMsV0FBVztZQUFFLEdBQUdOLEtBQUs7WUFBRUM7UUFBRztRQUVoQ0gsVUFBVVMsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1EO2FBQVM7UUFFckMsNkJBQTZCO1FBQzdCLE1BQU1FLFdBQVdSLE1BQU1RLFFBQVEsSUFBSTtRQUNuQ0MsV0FBVztZQUNUQyxZQUFZVDtRQUNkLEdBQUdPO0lBQ0wsR0FBRyxFQUFFO0lBRUwsTUFBTUUsY0FBY3ZCLGtEQUFXQSxDQUFDLENBQUNjO1FBQy9CSCxVQUFVUyxDQUFBQSxPQUFRQSxLQUFLSSxNQUFNLENBQUNYLENBQUFBLFFBQVNBLE1BQU1DLEVBQUUsS0FBS0E7SUFDdEQsR0FBRyxFQUFFO0lBRUwsTUFBTVcsVUFBVXpCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNaUIsUUFBUTdCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDMUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBU0Y7WUFBU0M7UUFBTTtJQUMzQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNa0IsVUFBVTlCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNbUIsT0FBTy9CLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDekNmLFNBQVM7WUFBRWdCLE1BQU07WUFBUUY7WUFBU0M7UUFBTTtJQUMxQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixxQkFDRSw4REFBQ04sYUFBYTBCLFFBQVE7UUFBQ0MsT0FBTztZQUM1QnZCO1lBQ0FFO1lBQ0FXO1lBQ0FFO1lBQ0FJO1lBQ0FDO1lBQ0FDO1FBQ0Y7O1lBQ0d0QjswQkFDRCw4REFBQ3lCOzs7Ozs7Ozs7OztBQUdQO0FBRU8sU0FBU0M7SUFDZCxNQUFNQyxVQUFVdEMsaURBQVVBLENBQUNRO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLFNBQVNGO0lBQ1AsTUFBTSxFQUFFeEIsTUFBTSxFQUFFYSxXQUFXLEVBQUUsR0FBR1k7SUFFaEMscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7a0JBQ1o3QixPQUFPOEIsR0FBRyxDQUFDM0IsQ0FBQUEsc0JBQ1YsOERBQUM0QjtnQkFFQzVCLE9BQU9BO2dCQUNQNkIsVUFBVSxJQUFNbkIsWUFBWVYsTUFBTUMsRUFBRTtlQUYvQkQsTUFBTUMsRUFBRTs7Ozs7Ozs7OztBQU92QjtBQUVBLFNBQVMyQixVQUFVLEVBQ2pCNUIsS0FBSyxFQUNMNkIsUUFBUSxFQUlUO0lBQ0MsTUFBTUMsVUFBVTtRQUNkLE9BQVE5QixNQUFNZSxJQUFJO1lBQ2hCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMxQix3SEFBV0E7b0JBQUNxQyxXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BDLHdIQUFXQTtvQkFBQ29DLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbEMsd0hBQWFBO29CQUFDa0MsV0FBVTs7Ozs7O1lBQ2xDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNuQyx3SEFBSUE7b0JBQUNtQyxXQUFVOzs7Ozs7UUFDM0I7SUFDRjtJQUVBLE1BQU1LLHFCQUFxQjtRQUN6QixPQUFRL0IsTUFBTWUsSUFBSTtZQUNoQixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTWlCLGVBQWU7UUFDbkIsT0FBUWhDLE1BQU1lLElBQUk7WUFDaEIsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVTtRQUFJQyxXQUFXLENBQUM7O01BRWYsRUFBRUsscUJBQXFCOztJQUV6QixDQUFDO2tCQUNDLDRFQUFDTjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaSTs7Ozs7O2tDQUdILDhEQUFDTDt3QkFBSUMsV0FBVTs7NEJBQ1oxQixNQUFNYyxLQUFLLGtCQUNWLDhEQUFDbUI7Z0NBQUVQLFdBQVcsQ0FBQyxvQkFBb0IsRUFBRU0sZUFBZSxDQUFDOzBDQUNsRGhDLE1BQU1jLEtBQUs7Ozs7OzswQ0FHaEIsOERBQUNtQjtnQ0FBRVAsV0FBVyxDQUFDLFFBQVEsRUFBRTFCLE1BQU1jLEtBQUssR0FBRyxTQUFTLEdBQUcsQ0FBQyxFQUFFa0IsZUFBZSxDQUFDOzBDQUNuRWhDLE1BQU1hLE9BQU87Ozs7Ozs0QkFHZmIsTUFBTWtDLE1BQU0sa0JBQ1gsOERBQUNUO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDUztvQ0FDQ0MsU0FBU3BDLE1BQU1rQyxNQUFNLENBQUNFLE9BQU87b0NBQzdCVixXQUFXLENBQUMsaURBQWlELEVBQUVNLGVBQWUsQ0FBQzs4Q0FFOUVoQyxNQUFNa0MsTUFBTSxDQUFDRyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNM0IsOERBQUNaO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDUzs0QkFDQ0MsU0FBU1A7NEJBQ1RILFdBQVcsQ0FBQyx1QkFBdUIsRUFBRU0sZUFBZSx3RkFBd0YsQ0FBQztzQ0FFN0ksNEVBQUM1Qyx3SEFBQ0E7Z0NBQUNzQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8zQjtBQUVBLHdDQUF3QztBQUNqQyxNQUFNMUIsUUFBUTtJQUNuQlksU0FBUyxDQUFDQyxTQUFpQkM7UUFDekIsb0RBQW9EO1FBQ3BEd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FHLE9BQU8sQ0FBQ0gsU0FBaUJDO1FBQ3ZCd0IsUUFBUUMsR0FBRyxDQUFDLFVBQVV6QixTQUFTLElBQUlEO0lBQ3JDO0lBQ0FJLFNBQVMsQ0FBQ0osU0FBaUJDO1FBQ3pCd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FLLE1BQU0sQ0FBQ0wsU0FBaUJDO1FBQ3RCd0IsUUFBUUMsR0FBRyxDQUFDLFNBQVN6QixTQUFTLElBQUlEO0lBQ3BDO0FBQ0YsRUFBQztBQUVELGlFQUFlbEIsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9jb21wb25lbnRzL1RvYXN0LnRzeD85MzlhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgWCwgQ2hlY2tDaXJjbGUsIEFsZXJ0Q2lyY2xlLCBJbmZvLCBBbGVydFRyaWFuZ2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgVG9hc3Qge1xuICBpZDogc3RyaW5nXG4gIHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcicgfCAnd2FybmluZycgfCAnaW5mbydcbiAgdGl0bGU/OiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIGR1cmF0aW9uPzogbnVtYmVyXG4gIGFjdGlvbj86IHtcbiAgICBsYWJlbDogc3RyaW5nXG4gICAgb25DbGljazogKCkgPT4gdm9pZFxuICB9XG59XG5cbmludGVyZmFjZSBUb2FzdENvbnRleHRUeXBlIHtcbiAgdG9hc3RzOiBUb2FzdFtdXG4gIGFkZFRvYXN0OiAodG9hc3Q6IE9taXQ8VG9hc3QsICdpZCc+KSA9PiB2b2lkXG4gIHJlbW92ZVRvYXN0OiAoaWQ6IHN0cmluZykgPT4gdm9pZFxuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4gdm9pZFxuICBlcnJvcjogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgd2FybmluZzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgaW5mbzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbn1cblxuY29uc3QgVG9hc3RDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUb2FzdENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3RvYXN0cywgc2V0VG9hc3RzXSA9IHVzZVN0YXRlPFRvYXN0W10+KFtdKVxuXG4gIGNvbnN0IGFkZFRvYXN0ID0gdXNlQ2FsbGJhY2soKHRvYXN0OiBPbWl0PFRvYXN0LCAnaWQnPikgPT4ge1xuICAgIGNvbnN0IGlkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpXG4gICAgY29uc3QgbmV3VG9hc3QgPSB7IC4uLnRvYXN0LCBpZCB9XG4gICAgXG4gICAgc2V0VG9hc3RzKHByZXYgPT4gWy4uLnByZXYsIG5ld1RvYXN0XSlcblxuICAgIC8vIEF1dG8gcmVtb3ZlIGFmdGVyIGR1cmF0aW9uXG4gICAgY29uc3QgZHVyYXRpb24gPSB0b2FzdC5kdXJhdGlvbiB8fCA1MDAwXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICByZW1vdmVUb2FzdChpZClcbiAgICB9LCBkdXJhdGlvbilcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVtb3ZlVG9hc3QgPSB1c2VDYWxsYmFjaygoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFRvYXN0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHRvYXN0ID0+IHRvYXN0LmlkICE9PSBpZCkpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHN1Y2Nlc3MgPSB1c2VDYWxsYmFjaygobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGFkZFRvYXN0KHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlLCB0aXRsZSB9KVxuICB9LCBbYWRkVG9hc3RdKVxuXG4gIGNvbnN0IGVycm9yID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHtcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3Qgd2FybmluZyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnd2FybmluZycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3QgaW5mbyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnaW5mbycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgcmV0dXJuIChcbiAgICA8VG9hc3RDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7XG4gICAgICB0b2FzdHMsXG4gICAgICBhZGRUb2FzdCxcbiAgICAgIHJlbW92ZVRvYXN0LFxuICAgICAgc3VjY2VzcyxcbiAgICAgIGVycm9yLFxuICAgICAgd2FybmluZyxcbiAgICAgIGluZm9cbiAgICB9fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUb2FzdCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVG9hc3RDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUb2FzdCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVG9hc3RQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZnVuY3Rpb24gVG9hc3RDb250YWluZXIoKSB7XG4gIGNvbnN0IHsgdG9hc3RzLCByZW1vdmVUb2FzdCB9ID0gdXNlVG9hc3QoKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XG4gICAgICB7dG9hc3RzLm1hcCh0b2FzdCA9PiAoXG4gICAgICAgIDxUb2FzdEl0ZW1cbiAgICAgICAgICBrZXk9e3RvYXN0LmlkfVxuICAgICAgICAgIHRvYXN0PXt0b2FzdH1cbiAgICAgICAgICBvblJlbW92ZT17KCkgPT4gcmVtb3ZlVG9hc3QodG9hc3QuaWQpfVxuICAgICAgICAvPlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gVG9hc3RJdGVtKHsgXG4gIHRvYXN0LCBcbiAgb25SZW1vdmUgXG59OiB7IFxuICB0b2FzdDogVG9hc3RcbiAgb25SZW1vdmU6ICgpID0+IHZvaWQgXG59KSB7XG4gIGNvbnN0IGdldEljb24gPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gPEluZm8gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRCYWNrZ3JvdW5kQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC01MCBib3JkZXItcmVkLTIwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy01MCBib3JkZXIteWVsbG93LTIwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFRleHRDb2xvciA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHRvYXN0LnR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JlZW4tODAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ3RleHQtcmVkLTgwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ3RleHQteWVsbG93LTgwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ3RleHQtYmx1ZS04MDAnXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgbWF4LXctc20gdy1mdWxsIHNoYWRvdy1sZyByb3VuZGVkLWxnIGJvcmRlciBwb2ludGVyLWV2ZW50cy1hdXRvXG4gICAgICAke2dldEJhY2tncm91bmRDb2xvcigpfVxuICAgICAgYW5pbWF0ZS1zbGlkZS1pbi1yaWdodFxuICAgIGB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICB7Z2V0SWNvbigpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyB3LTAgZmxleC0xXCI+XG4gICAgICAgICAgICB7dG9hc3QudGl0bGUgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgICAge3RvYXN0LnRpdGxlfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RvYXN0LnRpdGxlID8gJ210LTEnIDogJyd9ICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgIHt0b2FzdC5tZXNzYWdlfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7dG9hc3QuYWN0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9hc3QuYWN0aW9uLm9uQ2xpY2t9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtIHVuZGVybGluZSBob3Zlcjpuby11bmRlcmxpbmUgJHtnZXRUZXh0Q29sb3IoKX1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHt0b2FzdC5hY3Rpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTQgZmxleC1zaHJpbmstMCBmbGV4XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVtb3ZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLW1kIGlubGluZS1mbGV4ICR7Z2V0VGV4dENvbG9yKCl9IGhvdmVyOm9wYWNpdHktNzUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctY3VycmVudGB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gVXRpbGl0eSBmdW5jdGlvbiBmb3Igc3RhbmRhbG9uZSB1c2FnZVxuZXhwb3J0IGNvbnN0IHRvYXN0ID0ge1xuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIC8vIFRoaXMgd2lsbCBvbmx5IHdvcmsgaWYgVG9hc3RQcm92aWRlciBpcyBhdmFpbGFibGVcbiAgICBjb25zb2xlLmxvZygnU3VjY2VzczonLCB0aXRsZSB8fCAnJywgbWVzc2FnZSlcbiAgfSxcbiAgZXJyb3I6IChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0Vycm9yOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICB3YXJuaW5nOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdXYXJuaW5nOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICBpbmZvOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdJbmZvOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IFRvYXN0UHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJYIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkluZm8iLCJBbGVydFRyaWFuZ2xlIiwiVG9hc3RDb250ZXh0IiwidW5kZWZpbmVkIiwiVG9hc3RQcm92aWRlciIsImNoaWxkcmVuIiwidG9hc3RzIiwic2V0VG9hc3RzIiwiYWRkVG9hc3QiLCJ0b2FzdCIsImlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwibmV3VG9hc3QiLCJwcmV2IiwiZHVyYXRpb24iLCJzZXRUaW1lb3V0IiwicmVtb3ZlVG9hc3QiLCJmaWx0ZXIiLCJzdWNjZXNzIiwibWVzc2FnZSIsInRpdGxlIiwidHlwZSIsImVycm9yIiwid2FybmluZyIsImluZm8iLCJQcm92aWRlciIsInZhbHVlIiwiVG9hc3RDb250YWluZXIiLCJ1c2VUb2FzdCIsImNvbnRleHQiLCJFcnJvciIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsIlRvYXN0SXRlbSIsIm9uUmVtb3ZlIiwiZ2V0SWNvbiIsImdldEJhY2tncm91bmRDb2xvciIsImdldFRleHRDb2xvciIsInAiLCJhY3Rpb24iLCJidXR0b24iLCJvbkNsaWNrIiwibGFiZWwiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeClasses: () => (/* binding */ useThemeClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeClasses auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setThemeState(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const root = document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"dark\", \"light\");\n        // Add current theme class\n        root.classList.add(theme);\n        // Update CSS variables based on theme\n        if (theme === \"light\") {\n            // Light theme variables\n            root.style.setProperty(\"--cyber-bg-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#f8fafc\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#1e293b\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#475569\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#64748b\");\n            root.style.setProperty(\"--cyber-border\", \"#e2e8f0\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#0ea5e9\");\n            // Keep cyber colors but adjust opacity for light mode\n            root.style.setProperty(\"--cyber-primary\", \"#0ea5e9\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ec4899\");\n            root.style.setProperty(\"--cyber-accent\", \"#f59e0b\");\n        } else {\n            // Dark theme variables (cyberpunk)\n            root.style.setProperty(\"--cyber-bg-primary\", \"#0a0a0f\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#1a1a2e\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#16213e\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#0f0f23\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#1e1e3f\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#b0b0b0\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#808080\");\n            root.style.setProperty(\"--cyber-border\", \"#333366\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-primary\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ff0080\");\n            root.style.setProperty(\"--cyber-accent\", \"#ffff00\");\n        }\n        // Save to localStorage\n        localStorage.setItem(\"theme\", theme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === \"dark\" ? \"light\" : \"dark\");\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 95,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Hook for theme-aware styling\nfunction useThemeClasses() {\n    const { theme } = useTheme();\n    return {\n        // Background classes\n        bgPrimary: theme === \"dark\" ? \"bg-cyber-dark\" : \"bg-white\",\n        bgSecondary: theme === \"dark\" ? \"bg-cyber-secondary\" : \"bg-gray-50\",\n        bgCard: theme === \"dark\" ? \"bg-cyber-card\" : \"bg-white\",\n        // Text classes\n        textPrimary: theme === \"dark\" ? \"text-white\" : \"text-gray-900\",\n        textSecondary: theme === \"dark\" ? \"text-gray-300\" : \"text-gray-600\",\n        textMuted: theme === \"dark\" ? \"text-gray-400\" : \"text-gray-500\",\n        // Border classes\n        border: theme === \"dark\" ? \"border-gray-700\" : \"border-gray-200\",\n        borderBright: theme === \"dark\" ? \"border-cyber-primary\" : \"border-blue-500\",\n        // Button classes\n        btnPrimary: theme === \"dark\" ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\",\n        btnSecondary: theme === \"dark\" ? \"btn-cyber-secondary\" : \"bg-gray-200 hover:bg-gray-300 text-gray-900\",\n        // Input classes\n        input: theme === \"dark\" ? \"input-cyber\" : \"bg-white border-gray-300 text-gray-900 focus:border-blue-500\",\n        // Card classes\n        card: theme === \"dark\" ? \"card-cyber\" : \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n        // Effects\n        glow: theme === \"dark\" ? \"animate-cyber-glow\" : \"\",\n        pulse: theme === \"dark\" ? \"animate-cyber-pulse\" : \"\",\n        // Theme identifier\n        isDark: theme === \"dark\",\n        isLight: theme === \"light\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Stop,Trash2,TrendingUp,Upload,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Activity: () => (/* reexport safe */ _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   AlertTriangle: () => (/* reexport safe */ _icons_alert_triangle_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   Bug: () => (/* reexport safe */ _icons_bug_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   Clock: () => (/* reexport safe */ _icons_clock_js__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_7__["default"]),
/* harmony export */   Edit: () => (/* reexport safe */ _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_8__["default"]),
/* harmony export */   Pause: () => (/* reexport safe */ _icons_pause_js__WEBPACK_IMPORTED_MODULE_9__["default"]),
/* harmony export */   Play: () => (/* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_10__["default"]),
/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_11__["default"]),
/* harmony export */   RefreshCw: () => (/* reexport safe */ _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_12__["default"]),
/* harmony export */   Server: () => (/* reexport safe */ _icons_server_js__WEBPACK_IMPORTED_MODULE_13__["default"]),
/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_14__["default"]),
/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_15__["default"]),
/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_16__["default"]),
/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_17__["default"]),
/* harmony export */   Upload: () => (/* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_18__["default"]),
/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_19__["default"]),
/* harmony export */   XCircle: () => (/* reexport safe */ _icons_x_circle_js__WEBPACK_IMPORTED_MODULE_20__["default"])
/* harmony export */ });
/* harmony import */ var _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/activity.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js");
/* harmony import */ var _icons_alert_triangle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/alert-triangle.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js");
/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js");
/* harmony import */ var _icons_bug_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/bug.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js");
/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/check-circle.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js");
/* harmony import */ var _icons_clock_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/clock.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js");
/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/crown.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js");
/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/download.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js");
/* harmony import */ var _icons_pen_square_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/pen-square.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js");
/* harmony import */ var _icons_pause_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/pause.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js");
/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/play.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js");
/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/plus.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js");
/* harmony import */ var _icons_refresh_cw_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/refresh-cw.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js");
/* harmony import */ var _icons_server_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./icons/server.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js");
/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./icons/settings.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js");
/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./icons/shield.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js");
/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./icons/trash-2.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js");
/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./icons/trending-up.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js");
/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./icons/upload.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js");
/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./icons/users.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js");
/* harmony import */ var _icons_x_circle_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./icons/x-circle.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js");























/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6662484993ed\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vYXBwL2dsb2JhbHMuY3NzPzY0YTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NjYyNDg0OTkzZWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/admin/dashboard/page.tsx":
/*!**************************************!*\
  !*** ./app/admin/dashboard/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\app\admin\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(rsc)/./components/Toast.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Cybersecurity & Bug Hunting Platform\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan komunitas Bug Hunter\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} scrollbar-cyber`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   toast: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#useToast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#toast`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1),
/* harmony export */   useThemeClasses: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useTheme`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useThemeClasses`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fdashboard%2Fpage&page=%2Fadmin%2Fdashboard%2Fpage&appPaths=%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();