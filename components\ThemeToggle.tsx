'use client'

import { useTheme } from '@/contexts/ThemeContext'
import { Sun, Moon, Monitor } from 'lucide-react'
import { useState, useRef, useEffect } from 'react'

interface ThemeToggleProps {
  variant?: 'button' | 'switch' | 'dropdown'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  className?: string
}

export default function ThemeToggle({ 
  variant = 'switch', 
  size = 'md', 
  showLabel = false,
  className = '' 
}: ThemeToggleProps) {
  const { theme, toggleTheme, setTheme } = useTheme()
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const sizeClasses = {
    sm: 'h-6 w-11',
    md: 'h-7 w-12',
    lg: 'h-8 w-14'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  if (variant === 'button') {
    return (
      <button
        onClick={toggleTheme}
        className={`
          inline-flex items-center justify-center p-2 rounded-lg
          transition-all duration-300 group
          ${theme === 'dark' 
            ? 'bg-gray-800 hover:bg-gray-700 text-yellow-400 hover:text-yellow-300' 
            : 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800'
          }
          ${className}
        `}
        title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
      >
        {theme === 'dark' ? (
          <Sun className={`${iconSizes[size]} group-hover:rotate-180 transition-transform duration-500`} />
        ) : (
          <Moon className={`${iconSizes[size]} group-hover:-rotate-12 transition-transform duration-300`} />
        )}
        {showLabel && (
          <span className="ml-2 text-sm font-medium">
            {theme === 'dark' ? 'Light' : 'Dark'}
          </span>
        )}
      </button>
    )
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className={`
            inline-flex items-center justify-center p-2 rounded-lg
            transition-all duration-300
            ${theme === 'dark' 
              ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
              : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }
          `}
          title="Theme options"
        >
          {theme === 'dark' ? (
            <Moon className={iconSizes[size]} />
          ) : (
            <Sun className={iconSizes[size]} />
          )}
        </button>

        {showDropdown && (
          <div className={`
            absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-50
            ${theme === 'dark' 
              ? 'bg-gray-800 border border-gray-700' 
              : 'bg-white border border-gray-200'
            }
          `}>
            <div className="py-1">
              <button
                onClick={() => {
                  setTheme('light')
                  setShowDropdown(false)
                }}
                className={`
                  w-full flex items-center px-4 py-2 text-sm transition-colors
                  ${theme === 'light' 
                    ? 'bg-blue-50 text-blue-700' 
                    : theme === 'dark' 
                      ? 'text-gray-300 hover:bg-gray-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }
                `}
              >
                <Sun className="h-4 w-4 mr-3" />
                Light Mode
              </button>
              
              <button
                onClick={() => {
                  setTheme('dark')
                  setShowDropdown(false)
                }}
                className={`
                  w-full flex items-center px-4 py-2 text-sm transition-colors
                  ${theme === 'dark' 
                    ? 'bg-gray-700 text-gray-100' 
                    : 'text-gray-700 hover:bg-gray-100'
                  }
                `}
              >
                <Moon className="h-4 w-4 mr-3" />
                Dark Mode
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Default switch variant
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {showLabel && (
        <span className={`text-sm font-medium ${
          theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
        }`}>
          {theme === 'dark' ? 'Dark' : 'Light'}
        </span>
      )}
      
      <button
        onClick={toggleTheme}
        className={`
          relative inline-flex ${sizeClasses[size]} items-center rounded-full
          transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2
          ${theme === 'dark' 
            ? 'bg-cyber-primary focus:ring-cyber-primary shadow-lg shadow-cyan-500/25' 
            : 'bg-gray-300 focus:ring-blue-500'
          }
        `}
        title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
      >
        <span className="sr-only">Toggle theme</span>
        
        {/* Switch thumb */}
        <span
          className={`
            inline-block h-5 w-5 transform rounded-full transition-all duration-300
            ${theme === 'dark' 
              ? 'translate-x-6 bg-gray-900 shadow-lg' 
              : 'translate-x-1 bg-white shadow-md'
            }
          `}
        >
          {/* Icon inside thumb */}
          <span className="flex items-center justify-center h-full w-full">
            {theme === 'dark' ? (
              <Moon className="h-3 w-3 text-cyber-primary" />
            ) : (
              <Sun className="h-3 w-3 text-yellow-500" />
            )}
          </span>
        </span>
        
        {/* Background icons */}
        <span className="absolute left-1 top-1/2 transform -translate-y-1/2">
          <Sun className={`h-3 w-3 transition-opacity duration-300 ${
            theme === 'dark' ? 'opacity-0' : 'opacity-100 text-yellow-500'
          }`} />
        </span>
        <span className="absolute right-1 top-1/2 transform -translate-y-1/2">
          <Moon className={`h-3 w-3 transition-opacity duration-300 ${
            theme === 'dark' ? 'opacity-100 text-white' : 'opacity-0'
          }`} />
        </span>
      </button>
    </div>
  )
}

// Preset variants for common use cases
export function HeaderThemeToggle() {
  return <ThemeToggle variant="switch" size="md" className="ml-4" />
}

export function SidebarThemeToggle() {
  return <ThemeToggle variant="button" size="sm" showLabel className="w-full justify-start" />
}

export function SettingsThemeToggle() {
  return <ThemeToggle variant="dropdown" size="md" />
}
