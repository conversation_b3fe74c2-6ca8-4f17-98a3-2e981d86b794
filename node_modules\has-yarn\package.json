{"name": "has-yarn", "version": "3.0.0", "description": "Check if a project is using Yarn", "license": "MIT", "repository": "sindresorhus/has-yarn", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["yarn", "has", "detect", "is", "project", "app", "module", "package", "manager", "npm"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}