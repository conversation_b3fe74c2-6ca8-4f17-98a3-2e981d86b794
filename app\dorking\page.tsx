'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  Globe, 
  Search, 
  Plus, 
  Star,
  Eye,
  Copy,
  Download,
  Play,
  BookOpen,
  Zap,
  Shield,
  Database,
  Code,
  AlertTriangle,
  Clock,
  History,
  Filter,
  ExternalLink
} from 'lucide-react'

interface DorkPreset {
  id: string
  name: string
  description: string
  query: string
  category: 'sensitive_files' | 'login_pages' | 'database_dumps' | 'config_files' | 'error_messages' | 'directory_listing' | 'vulnerable_apps' | 'iot_devices'
  tags: string[]
  usageCount: number
  isActive: boolean
  createdBy?: string
}

interface CustomDork {
  id: string
  name: string
  query: string
  description: string
  tags: string[]
  isPrivate: boolean
  usageCount: number
  createdAt: string
}

interface DorkResult {
  id: string
  query: string
  results: SearchResult[]
  executedAt: string
  totalResults: number
}

interface SearchResult {
  title: string
  url: string
  snippet: string
  domain: string
}

export default function DorkingPage() {
  const [activeTab, setActiveTab] = useState('presets')
  const [dorkPresets, setDorkPresets] = useState<DorkPreset[]>([])
  const [customDorks, setCustomDorks] = useState<CustomDork[]>([])
  const [dorkHistory, setDorkHistory] = useState<DorkResult[]>([])
  const [selectedCategory, setSelectedCategory] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [customQuery, setCustomQuery] = useState('')
  const [isExecuting, setIsExecuting] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showResultModal, setShowResultModal] = useState(false)
  const [selectedResult, setSelectedResult] = useState<DorkResult | null>(null)
  const [newDork, setNewDork] = useState({
    name: '',
    query: '',
    description: '',
    tags: '',
    isPrivate: true
  })
  const [stats, setStats] = useState({
    totalPresets: 0,
    customDorks: 0,
    totalExecutions: 0,
    successfulQueries: 0
  })

  const { success, error, warning } = useToast()

  useEffect(() => {
    loadStats()
    loadDorkPresets()
    loadCustomDorks()
    loadDorkHistory()
  }, [])

  const loadStats = async () => {
    setTimeout(() => {
      setStats({
        totalPresets: 156,
        customDorks: 23,
        totalExecutions: 1247,
        successfulQueries: 1156
      })
    }, 1000)
  }

  const loadDorkPresets = async () => {
    setTimeout(() => {
      setDorkPresets([
        {
          id: '1',
          name: 'Admin Panel Finder',
          description: 'Find admin login panels and administrative interfaces',
          query: 'inurl:admin.php OR inurl:administrator OR inurl:admin/login',
          category: 'login_pages',
          tags: ['admin', 'login', 'panel'],
          usageCount: 1247,
          isActive: true
        },
        {
          id: '2',
          name: 'Database Backup Files',
          description: 'Search for exposed database backup files',
          query: 'filetype:sql OR filetype:dump OR "backup.sql"',
          category: 'database_dumps',
          tags: ['database', 'backup', 'sql'],
          usageCount: 856,
          isActive: true
        },
        {
          id: '3',
          name: 'Configuration Files',
          description: 'Find exposed configuration files',
          query: 'filetype:conf OR filetype:config OR filetype:ini',
          category: 'config_files',
          tags: ['config', 'settings', 'files'],
          usageCount: 634,
          isActive: true
        },
        {
          id: '4',
          name: 'Directory Listings',
          description: 'Find open directory listings',
          query: 'intitle:"Index of /" OR intitle:"Directory Listing"',
          category: 'directory_listing',
          tags: ['directory', 'listing', 'open'],
          usageCount: 423,
          isActive: true
        }
      ])
    }, 1500)
  }

  const loadCustomDorks = async () => {
    setTimeout(() => {
      setCustomDorks([
        {
          id: '1',
          name: 'My Custom Admin Search',
          query: 'site:example.com inurl:admin',
          description: 'Custom search for admin pages on specific domain',
          tags: ['custom', 'admin'],
          isPrivate: true,
          usageCount: 12,
          createdAt: '2025-01-14T10:00:00Z'
        }
      ])
    }, 2000)
  }

  const loadDorkHistory = async () => {
    setTimeout(() => {
      setDorkHistory([
        {
          id: '1',
          query: 'inurl:admin.php',
          results: [
            {
              title: 'Admin Login - Example Site',
              url: 'https://example.com/admin.php',
              snippet: 'Administrator login page for content management',
              domain: 'example.com'
            }
          ],
          executedAt: '2025-01-14T10:30:00Z',
          totalResults: 1
        }
      ])
    }, 2500)
  }

  const categories = [
    { value: 'sensitive_files', label: 'Sensitive Files', icon: Shield, color: 'text-red-600' },
    { value: 'login_pages', label: 'Login Pages', icon: Database, color: 'text-blue-600' },
    { value: 'database_dumps', label: 'Database Dumps', icon: Database, color: 'text-purple-600' },
    { value: 'config_files', label: 'Config Files', icon: Code, color: 'text-green-600' },
    { value: 'error_messages', label: 'Error Messages', icon: AlertTriangle, color: 'text-yellow-600' },
    { value: 'directory_listing', label: 'Directory Listing', icon: Globe, color: 'text-orange-600' },
    { value: 'vulnerable_apps', label: 'Vulnerable Apps', icon: Shield, color: 'text-red-600' },
    { value: 'iot_devices', label: 'IoT Devices', icon: Zap, color: 'text-indigo-600' }
  ]

  const executeDork = async (query: string) => {
    if (!query.trim()) {
      warning('Please enter a dork query')
      return
    }

    setIsExecuting(true)
    try {
      // Simulate dork execution
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Mock results
      const mockResults: SearchResult[] = [
        {
          title: 'Admin Panel - Example Site',
          url: 'https://example.com/admin',
          snippet: 'Administrator login page for site management',
          domain: 'example.com'
        },
        {
          title: 'Login Portal - Test Site',
          url: 'https://test.com/login',
          snippet: 'User authentication portal',
          domain: 'test.com'
        }
      ]

      const newResult: DorkResult = {
        id: Date.now().toString(),
        query,
        results: mockResults,
        executedAt: new Date().toISOString(),
        totalResults: mockResults.length
      }

      setDorkHistory(prev => [newResult, ...prev])
      setSelectedResult(newResult)
      setShowResultModal(true)
      
      success(`Found ${mockResults.length} results for dork query`)

    } catch (err) {
      error('Dork execution failed. Please try again.')
    } finally {
      setIsExecuting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    success('Copied to clipboard!')
  }

  const createCustomDork = async () => {
    if (!newDork.name.trim() || !newDork.query.trim()) {
      warning('Please fill in name and query fields')
      return
    }

    const customDork: CustomDork = {
      id: Date.now().toString(),
      name: newDork.name,
      query: newDork.query,
      description: newDork.description,
      tags: newDork.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      isPrivate: newDork.isPrivate,
      usageCount: 0,
      createdAt: new Date().toISOString()
    }

    setCustomDorks(prev => [customDork, ...prev])
    setShowCreateModal(false)
    setNewDork({ name: '', query: '', description: '', tags: '', isPrivate: true })
    success('Custom dork created successfully!')
  }

  const filteredPresets = dorkPresets.filter(preset => {
    const matchesCategory = !selectedCategory || preset.category === selectedCategory
    const matchesSearch = !searchQuery || 
      preset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      preset.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      preset.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const presetColumns = [
    {
      key: 'name',
      label: 'Name',
      render: (value: string, row: DorkPreset) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{row.description}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (value: string) => {
        const category = categories.find(c => c.value === value)
        const Icon = category?.icon || Globe
        return (
          <div className="flex items-center space-x-2">
            <Icon className={`h-4 w-4 ${category?.color || 'text-gray-400'}`} />
            <span className="capitalize">{value.replace('_', ' ')}</span>
          </div>
        )
      }
    },
    {
      key: 'usageCount',
      label: 'Usage',
      render: (value: number) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: DorkPreset) => (
        <div className="flex space-x-2">
          <button
            onClick={() => copyToClipboard(row.query)}
            className="text-gray-600 hover:text-gray-700"
            title="Copy query"
          >
            <Copy className="h-4 w-4" />
          </button>
          <button
            onClick={() => executeDork(row.query)}
            className="text-primary-600 hover:text-primary-700"
            title="Execute dork"
            disabled={isExecuting}
          >
            <Play className="h-4 w-4" />
          </button>
          <button
            onClick={() => window.open(`https://www.google.com/search?q=${encodeURIComponent(row.query)}`, '_blank')}
            className="text-blue-600 hover:text-blue-700"
            title="Open in Google"
          >
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  const customDorkColumns = [
    {
      key: 'name',
      label: 'Name',
      render: (value: string, row: CustomDork) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{row.description}</div>
        </div>
      )
    },
    {
      key: 'query',
      label: 'Query',
      render: (value: string) => (
        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
          {value.length > 50 ? value.substring(0, 50) + '...' : value}
        </span>
      )
    },
    {
      key: 'isPrivate',
      label: 'Visibility',
      render: (value: boolean) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'
        }`}>
          {value ? 'Private' : 'Public'}
        </span>
      )
    },
    {
      key: 'usageCount',
      label: 'Usage',
      render: (value: number) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: CustomDork) => (
        <div className="flex space-x-2">
          <button
            onClick={() => copyToClipboard(row.query)}
            className="text-gray-600 hover:text-gray-700"
          >
            <Copy className="h-4 w-4" />
          </button>
          <button
            onClick={() => executeDork(row.query)}
            className="text-primary-600 hover:text-primary-700"
            disabled={isExecuting}
          >
            <Play className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  const historyColumns = [
    {
      key: 'query',
      label: 'Query',
      render: (value: string) => (
        <span className="font-mono text-sm">{value}</span>
      )
    },
    {
      key: 'totalResults',
      label: 'Results',
      render: (value: number) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'executedAt',
      label: 'Executed',
      render: (value: string) => new Date(value).toLocaleString()
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: DorkResult) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedResult(row)
              setShowResultModal(true)
            }}
            className="text-primary-600 hover:text-primary-700"
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => executeDork(row.query)}
            className="text-green-600 hover:text-green-700"
            disabled={isExecuting}
          >
            <Play className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Globe className="h-6 w-6 mr-2 text-primary-600" />
              Google Dorking
            </h1>
            <p className="text-gray-600 mt-1">
              Advanced Google search queries for security research
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('presets')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'presets'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <BookOpen className="h-4 w-4 mr-2 inline" />
                Presets
              </button>
              <button
                onClick={() => setActiveTab('custom')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'custom'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Star className="h-4 w-4 mr-2 inline" />
                Custom
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'history'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <History className="h-4 w-4 mr-2 inline" />
                History
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Presets"
            value={stats.totalPresets}
            icon={BookOpen}
            color="blue"
            trend={{ value: 5, isPositive: true }}
          />
          <StatsCard
            title="Custom Dorks"
            value={stats.customDorks}
            icon={Star}
            color="yellow"
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="Total Executions"
            value={stats.totalExecutions}
            icon={Play}
            color="green"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Success Rate"
            value={`${Math.round((stats.successfulQueries / stats.totalExecutions) * 100)}%`}
            icon={Zap}
            color="purple"
            trend={{ value: 3, isPositive: true }}
          />
        </div>

        {/* Quick Execute */}
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Quick Execute
          </h3>
          <div className="flex space-x-3">
            <input
              type="text"
              value={customQuery}
              onChange={(e) => setCustomQuery(e.target.value)}
              placeholder="Enter your Google dork query..."
              className="input-field flex-1"
              onKeyPress={(e) => e.key === 'Enter' && executeDork(customQuery)}
            />
            <button
              onClick={() => executeDork(customQuery)}
              disabled={isExecuting || !customQuery.trim()}
              className="btn-primary"
            >
              {isExecuting ? (
                <div className="flex items-center space-x-2">
                  <Loading size="sm" />
                  <span>Executing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Play className="h-4 w-4" />
                  <span>Execute</span>
                </div>
              )}
            </button>
          </div>
        </Card>

        {/* Main Content */}
        {activeTab === 'presets' ? (
          <div className="space-y-6">
            {/* Filters */}
            <Card>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div className="flex space-x-4">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search presets..."
                    className="input-field w-64"
                  />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="input-field"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>
                <button
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedCategory('')
                  }}
                  className="btn-secondary"
                >
                  Clear Filters
                </button>
              </div>
            </Card>

            {/* Presets Table */}
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Dork Presets ({filteredPresets.length})
                </h3>
              </div>
              
              <DataTable
                columns={presetColumns}
                data={filteredPresets}
                searchable={false}
                pagination={{
                  currentPage: 1,
                  totalPages: 1,
                  pageSize: 10,
                  totalItems: filteredPresets.length,
                  onPageChange: () => {}
                }}
              />
            </Card>
          </div>
        ) : activeTab === 'custom' ? (
          <div className="space-y-6">
            {/* Create Button */}
            <Card>
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Custom Dorks
                </h3>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="btn-primary"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Dork
                </button>
              </div>
            </Card>

            {/* Custom Dorks Table */}
            <Card>
              <DataTable
                columns={customDorkColumns}
                data={customDorks}
                searchable
                pagination={{
                  currentPage: 1,
                  totalPages: 1,
                  pageSize: 10,
                  totalItems: customDorks.length,
                  onPageChange: () => {}
                }}
              />
            </Card>
          </div>
        ) : (
          /* History Tab */
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Execution History
              </h3>
            </div>
            
            <DataTable
              columns={historyColumns}
              data={dorkHistory}
              searchable
              pagination={{
                currentPage: 1,
                totalPages: 1,
                pageSize: 10,
                totalItems: dorkHistory.length,
                onPageChange: () => {}
              }}
            />
          </Card>
        )}

        {/* Create Custom Dork Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create Custom Dork"
          size="md"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name
              </label>
              <input
                type="text"
                value={newDork.name}
                onChange={(e) => setNewDork(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter dork name"
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Query
              </label>
              <textarea
                value={newDork.query}
                onChange={(e) => setNewDork(prev => ({ ...prev, query: e.target.value }))}
                placeholder="Enter Google dork query"
                rows={3}
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newDork.description}
                onChange={(e) => setNewDork(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this dork searches for"
                rows={2}
                className="input-field"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags (comma separated)
              </label>
              <input
                type="text"
                value={newDork.tags}
                onChange={(e) => setNewDork(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="admin, login, config"
                className="input-field"
              />
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={newDork.isPrivate}
                  onChange={(e) => setNewDork(prev => ({ ...prev, isPrivate: e.target.checked }))}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Keep this dork private</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                onClick={() => setShowCreateModal(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={createCustomDork}
                className="btn-primary"
              >
                Create Dork
              </button>
            </div>
          </div>
        </Modal>

        {/* Results Modal */}
        <Modal
          isOpen={showResultModal}
          onClose={() => setShowResultModal(false)}
          title={`Dork Results - ${selectedResult?.query}`}
          size="lg"
        >
          {selectedResult && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm text-gray-500">Query:</span>
                  <span className="ml-2 font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                    {selectedResult.query}
                  </span>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Results:</span>
                  <span className="ml-2 font-medium">{selectedResult.totalResults}</span>
                </div>
              </div>

              <div className="space-y-3">
                {selectedResult.results.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                    <h4 className="font-medium text-primary-600 hover:text-primary-700">
                      <a href={result.url} target="_blank" rel="noopener noreferrer">
                        {result.title}
                      </a>
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">{result.snippet}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">{result.domain}</span>
                      <a
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-primary-600 hover:text-primary-700"
                      >
                        Visit Site →
                      </a>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => copyToClipboard(selectedResult.query)}
                  className="btn-secondary"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Query
                </button>
                <button
                  onClick={() => setShowResultModal(false)}
                  className="btn-primary"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  )
}
