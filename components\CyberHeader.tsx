'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { useTheme, useThemeClasses } from '@/contexts/ThemeContext'
import ThemeToggle from '@/components/ThemeToggle'
import {
  Shield,
  Menu,
  X,
  User,
  LogIn,
  UserPlus,
  Search,
  Globe,
  FileText,
  Database,
  Wrench,
  Trophy,
  Crown,
  Zap,
  ChevronDown,
  Activity
} from 'lucide-react'

interface CyberHeaderProps {
  user?: {
    id: string
    username: string
    email: string
    fullName: string
    avatar?: string
    role: string
    plan: string
  }
}

export default function CyberHeader({ user }: CyberHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const { theme } = useTheme()
  const themeClasses = useThemeClasses()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navigation = [
    { 
      name: 'Features', 
      href: '#features',
      dropdown: [
        { name: 'OSINT Investigator', href: '/osint', icon: Search, description: 'Advanced intelligence gathering' },
        { name: 'Vulnerability Scanner', href: '/scanner', icon: Shield, description: 'Automated security scanning' },
        { name: 'File Analyzer', href: '/file-analyzer', icon: FileText, description: 'Malware detection & analysis' },
        { name: 'CVE Intelligence', href: '/cve', icon: Database, description: 'Vulnerability database' },
        { name: 'Google Dorking', href: '/dorking', icon: Globe, description: 'Advanced search queries' },
        { name: 'Developer Tools', href: '/tools', icon: Wrench, description: 'Security testing tools' }
      ]
    },
    { name: 'Pricing', href: '/plan' },
    { name: 'Leaderboard', href: '/leaderboard', icon: Trophy },
    { name: 'Docs', href: '/docs' },
    { name: 'Community', href: '/community' }
  ]

  const handleLogin = () => {
    router.push('/login')
  }

  const handleRegister = () => {
    router.push('/register')
  }

  const handleDashboard = () => {
    router.push('/dashboard')
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const isActive = (href: string) => {
    if (href.startsWith('#')) return false
    return pathname === href || pathname.startsWith(href + '/')
  }

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? `${themeClasses.bgCard}/95 backdrop-blur-md border-b ${themeClasses.border} shadow-lg`
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Shield className={`h-8 w-8 text-cyber-primary ${themeClasses.isDark ? 'animate-cyber-glow' : ''}`} />
              {themeClasses.isDark && (
                <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
              )}
            </div>
            <div className="flex flex-col">
              <span className={`text-xl font-bold ${themeClasses.isDark ? 'text-cyber-glow group-hover:animate-glitch' : 'text-blue-600'}`}>
                KodeXGuard
              </span>
              <span className={`text-xs uppercase tracking-wider ${themeClasses.isDark ? 'text-cyber-secondary' : 'text-gray-500'}`}>
                Cyber Security Platform
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.dropdown ? (
                  <div
                    className="relative"
                    onMouseEnter={() => setActiveDropdown(item.name)}
                    onMouseLeave={() => setActiveDropdown(null)}
                  >
                    <button className={`flex items-center space-x-1 ${themeClasses.textSecondary} hover:text-cyber-primary transition-colors duration-200 font-medium`}>
                      <span>{item.name}</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>
                    
                    {activeDropdown === item.name && (
                      <div className={`absolute top-full left-0 mt-2 w-80 ${themeClasses.bgCard} border ${themeClasses.border} rounded-lg shadow-xl animate-fade-in-up`}>
                        <div className="p-4 grid grid-cols-1 gap-2">
                          {item.dropdown.map((subItem) => {
                            const Icon = subItem.icon
                            return (
                              <Link
                                key={subItem.name}
                                href={subItem.href}
                                className={`flex items-center space-x-3 p-3 rounded-lg hover:${themeClasses.isDark ? 'bg-cyber-secondary/10' : 'bg-blue-50'} transition-colors duration-200 group`}
                              >
                                <Icon className={`h-5 w-5 text-cyber-primary ${themeClasses.isDark ? 'group-hover:animate-cyber-pulse' : ''}`} />
                                <div>
                                  <div className={`font-medium ${themeClasses.textPrimary} group-hover:text-cyber-primary`}>
                                    {subItem.name}
                                  </div>
                                  <div className={`text-sm ${themeClasses.textMuted}`}>
                                    {subItem.description}
                                  </div>
                                </div>
                              </Link>
                            )
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-1 font-medium transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-cyber-primary'
                        : `${themeClasses.textSecondary} hover:text-cyber-primary`
                    }`}
                  >
                    {item.icon && <item.icon className="h-4 w-4" />}
                    <span>{item.name}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* User Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle variant="switch" size="md" />

            {user ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-3 py-1 bg-cyber-secondary/20 border border-cyber-secondary rounded-full">
                  <Crown className="h-4 w-4 text-cyber-accent" />
                  <span className="text-sm font-medium text-cyber-accent uppercase">
                    {user.plan}
                  </span>
                </div>
                
                <div className="relative group">
                  <button className={`flex items-center space-x-2 p-2 rounded-lg hover:${themeClasses.isDark ? 'bg-cyber-secondary/10' : 'bg-gray-100'} transition-colors duration-200`}>
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.username}
                        className="h-8 w-8 rounded-full border-2 border-cyber-primary"
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-cyber-primary/20 border-2 border-cyber-primary flex items-center justify-center">
                        <User className="h-4 w-4 text-cyber-primary" />
                      </div>
                    )}
                    <span className={`${themeClasses.textPrimary} font-medium`}>{user.username}</span>
                    <ChevronDown className={`h-4 w-4 ${themeClasses.textMuted}`} />
                  </button>

                  <div className={`absolute right-0 top-full mt-2 w-48 ${themeClasses.bgCard} border ${themeClasses.border} rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200`}>
                    <div className="p-2">
                      <button
                        onClick={handleDashboard}
                        className={`w-full flex items-center space-x-2 px-3 py-2 text-left ${themeClasses.textPrimary} hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'} rounded-lg transition-colors duration-200`}
                      >
                        <Activity className="h-4 w-4 text-cyber-primary" />
                        <span>Dashboard</span>
                      </button>
                      <button
                        onClick={handleLogout}
                        className="w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors duration-200"
                      >
                        <X className="h-4 w-4" />
                        <span>Logout</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleLogin}
                  className={`flex items-center space-x-2 px-4 py-2 text-cyber-primary hover:${themeClasses.textPrimary} border border-cyber-primary hover:${themeClasses.isDark ? 'bg-cyber-primary/20' : 'bg-blue-50'} rounded-lg transition-all duration-200`}
                >
                  <LogIn className="h-4 w-4" />
                  <span>Login</span>
                </button>
                <button
                  onClick={handleRegister}
                  className={themeClasses.isDark ? "btn-cyber-primary" : "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Get Started
                </button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle variant="button" size="sm" />
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-2 rounded-lg ${themeClasses.textSecondary} hover:text-cyber-primary hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'} transition-colors duration-200`}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className={`md:hidden ${themeClasses.bgCard} border-t ${themeClasses.border} animate-slide-in-right`}>
          <div className="px-4 py-6 space-y-4">
            {navigation.map((item) => (
              <div key={item.name}>
                {item.dropdown ? (
                  <div>
                    <div className={`${themeClasses.textSecondary} font-medium mb-2`}>{item.name}</div>
                    <div className="pl-4 space-y-2">
                      {item.dropdown.map((subItem) => {
                        const Icon = subItem.icon
                        return (
                          <Link
                            key={subItem.name}
                            href={subItem.href}
                            className={`flex items-center space-x-3 p-2 rounded-lg hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'} transition-colors duration-200`}
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            <Icon className="h-4 w-4 text-cyber-primary" />
                            <span className={themeClasses.textPrimary}>{subItem.name}</span>
                          </Link>
                        )
                      })}
                    </div>
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`block px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      isActive(item.href)
                        ? `text-cyber-primary ${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'}`
                        : `${themeClasses.textSecondary} hover:text-cyber-primary hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'}`
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}

            {!user && (
              <div className={`pt-4 border-t ${themeClasses.border} space-y-3`}>
                <button
                  onClick={() => {
                    handleLogin()
                    setIsMobileMenuOpen(false)
                  }}
                  className={`w-full flex items-center justify-center space-x-2 px-4 py-2 text-cyber-primary border border-cyber-primary rounded-lg hover:${themeClasses.isDark ? 'bg-cyber-primary/20' : 'bg-blue-50'} transition-colors duration-200`}
                >
                  <LogIn className="h-4 w-4" />
                  <span>Login</span>
                </button>
                <button
                  onClick={() => {
                    handleRegister()
                    setIsMobileMenuOpen(false)
                  }}
                  className={`w-full ${themeClasses.isDark ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200'}`}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Get Started
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  )
}
