{"name": "kodexguard", "version": "1.0.0", "description": "KodeXGuard - Platform Cybersecurity & Bug Hunting", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@elastic/elasticsearch": "^8.10.0", "@hookform/resolvers": "^3.3.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "autoprefixer": "^10.4.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.292.0", "mysql2": "^3.6.0", "next": "^14.0.0", "node-telegram-bot-api": "^0.64.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.47.0", "recharts": "^2.8.0", "redis": "^4.6.0", "sharp": "^0.32.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "venom-bot": "^5.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/node-telegram-bot-api": "^0.64.0"}, "engines": {"node": ">=18.0.0"}}