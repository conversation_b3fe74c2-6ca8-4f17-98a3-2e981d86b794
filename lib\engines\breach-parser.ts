import { createReadStream, createWriteStream } from 'fs'
import { readFile, writeFile, unlink, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import { createHash } from 'crypto'
import { createGunzip, createUnzip } from 'zlib'
import { pipeline } from 'stream/promises'
import { Transform } from 'stream'
import path from 'path'
import axios from 'axios'
import { db } from '../database'

export interface BreachData {
  id: string
  name: string
  domain: string
  breachDate: Date
  addedDate: Date
  modifiedDate: Date
  pwnCount: number
  description: string
  logoPath?: string
  dataClasses: string[]
  isVerified: boolean
  isFabricated: boolean
  isSensitive: boolean
  isRetired: boolean
  isSpamList: boolean
  isMalware: boolean
  isSubscriptionFree: boolean
}

export interface BreachRecord {
  id: string
  breachId: string
  email: string
  emailHash: string
  password?: string
  passwordHash?: string
  username?: string
  name?: string
  phone?: string
  address?: string
  additionalData: Record<string, any>
  dateAdded: Date
}

export interface ParseJob {
  id: string
  fileName: string
  filePath: string
  fileSize: number
  fileHash: string
  format: 'csv' | 'json' | 'txt' | 'sql' | 'xml' | 'custom'
  status: 'pending' | 'parsing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  recordsFound: number
  recordsParsed: number
  recordsValid: number
  errors: string[]
  startTime: Date
  endTime?: Date
  breachInfo?: BreachData
}

export interface ParseOptions {
  delimiter?: string
  hasHeader?: boolean
  emailColumn?: number | string
  passwordColumn?: number | string
  usernameColumn?: number | string
  customMapping?: Record<string, number | string>
  skipDuplicates?: boolean
  validateEmails?: boolean
  hashPasswords?: boolean
  chunkSize?: number
}

export class BreachParser {
  private jobs: Map<string, ParseJob> = new Map()
  private breachesPath: string
  private tempPath: string

  constructor() {
    this.breachesPath = process.env.BREACHES_PATH || './breaches'
    this.tempPath = process.env.TEMP_PATH || './temp'
    this.initializeParser()
  }

  private async initializeParser() {
    console.log('🔓 Breach Parser initialized')
    await this.createTables()
    await this.setupDirectories()
    await this.loadKnownBreaches()
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS breaches (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(255) UNIQUE NOT NULL,
          domain VARCHAR(255),
          breach_date DATE,
          added_date DATE,
          modified_date DATE,
          pwn_count BIGINT DEFAULT 0,
          description TEXT,
          logo_path VARCHAR(500),
          data_classes JSON,
          is_verified BOOLEAN DEFAULT FALSE,
          is_fabricated BOOLEAN DEFAULT FALSE,
          is_sensitive BOOLEAN DEFAULT FALSE,
          is_retired BOOLEAN DEFAULT FALSE,
          is_spam_list BOOLEAN DEFAULT FALSE,
          is_malware BOOLEAN DEFAULT FALSE,
          is_subscription_free BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS breach_records (
          id VARCHAR(36) PRIMARY KEY,
          breach_id VARCHAR(36),
          email VARCHAR(255) NOT NULL,
          email_hash VARCHAR(64) NOT NULL,
          password_hash VARCHAR(255),
          username VARCHAR(255),
          name VARCHAR(255),
          phone VARCHAR(50),
          address TEXT,
          additional_data JSON,
          date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (breach_id) REFERENCES breaches(id) ON DELETE CASCADE,
          INDEX idx_email_hash (email_hash),
          INDEX idx_breach_id (breach_id),
          INDEX idx_email (email),
          INDEX idx_username (username)
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS parse_jobs (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          file_name VARCHAR(255) NOT NULL,
          file_path VARCHAR(500) NOT NULL,
          file_size BIGINT NOT NULL,
          file_hash VARCHAR(64) NOT NULL,
          format ENUM('csv', 'json', 'txt', 'sql', 'xml', 'custom') NOT NULL,
          status ENUM('pending', 'parsing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
          progress INT DEFAULT 0,
          records_found BIGINT DEFAULT 0,
          records_parsed BIGINT DEFAULT 0,
          records_valid BIGINT DEFAULT 0,
          errors JSON,
          breach_id VARCHAR(36),
          options JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL,
          FOREIGN KEY (breach_id) REFERENCES breaches(id) ON DELETE SET NULL
        )
      `)

      console.log('✅ Breach Parser tables created')
    } catch (error) {
      console.error('❌ Error creating breach parser tables:', error)
    }
  }

  private async setupDirectories() {
    try {
      const dirs = [this.breachesPath, this.tempPath, 
                   path.join(this.breachesPath, 'raw'),
                   path.join(this.breachesPath, 'processed'),
                   path.join(this.breachesPath, 'logs')]
      
      for (const dir of dirs) {
        if (!existsSync(dir)) {
          await mkdir(dir, { recursive: true })
        }
      }

      console.log('✅ Breach Parser directories setup completed')
    } catch (error) {
      console.error('❌ Error setting up directories:', error)
    }
  }

  private async loadKnownBreaches() {
    try {
      // Load known breaches from HIBP API or local database
      const knownBreaches = await this.fetchHIBPBreaches()
      
      for (const breach of knownBreaches) {
        await this.upsertBreach(breach)
      }

      console.log(`✅ Loaded ${knownBreaches.length} known breaches`)
    } catch (error) {
      console.error('❌ Error loading known breaches:', error)
    }
  }

  private async fetchHIBPBreaches(): Promise<BreachData[]> {
    try {
      // Mock HIBP API call - replace with real API
      const mockBreaches: BreachData[] = [
        {
          id: this.generateId(),
          name: 'Adobe',
          domain: 'adobe.com',
          breachDate: new Date('2013-10-04'),
          addedDate: new Date('2013-12-04'),
          modifiedDate: new Date('2013-12-04'),
          pwnCount: *********,
          description: 'In October 2013, 153 million Adobe accounts were breached with each containing an internal ID, username, email, encrypted password and a password hint in plain text.',
          dataClasses: ['Email addresses', 'Password hints', 'Passwords', 'Usernames'],
          isVerified: true,
          isFabricated: false,
          isSensitive: false,
          isRetired: false,
          isSpamList: false,
          isMalware: false,
          isSubscriptionFree: true
        },
        {
          id: this.generateId(),
          name: 'LinkedIn',
          domain: 'linkedin.com',
          breachDate: new Date('2012-05-05'),
          addedDate: new Date('2016-05-21'),
          modifiedDate: new Date('2016-05-21'),
          pwnCount: *********,
          description: 'In May 2012, LinkedIn was breached and the passwords of 164 million accounts were stolen.',
          dataClasses: ['Email addresses', 'Passwords'],
          isVerified: true,
          isFabricated: false,
          isSensitive: false,
          isRetired: false,
          isSpamList: false,
          isMalware: false,
          isSubscriptionFree: true
        }
      ]

      return mockBreaches
    } catch (error) {
      console.error('Error fetching HIBP breaches:', error)
      return []
    }
  }

  async startParseJob(filePath: string, fileName: string, format: string, options: ParseOptions = {}, userId?: number): Promise<string> {
    const jobId = this.generateId()
    
    // Calculate file hash
    const fileBuffer = await readFile(filePath)
    const fileHash = createHash('sha256').update(fileBuffer).digest('hex')
    
    const job: ParseJob = {
      id: jobId,
      fileName,
      filePath,
      fileSize: fileBuffer.length,
      fileHash,
      format: format as any,
      status: 'pending',
      progress: 0,
      recordsFound: 0,
      recordsParsed: 0,
      recordsValid: 0,
      errors: [],
      startTime: new Date()
    }

    this.jobs.set(jobId, job)

    // Save to database
    await db.query(`
      INSERT INTO parse_jobs (id, user_id, file_name, file_path, file_size, file_hash, format, status, options, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
    `, [jobId, userId || null, fileName, filePath, job.fileSize, fileHash, format, JSON.stringify(options)])

    // Start parsing
    this.executeParseJob(jobId, options)

    return jobId
  }

  private async executeParseJob(jobId: string, options: ParseOptions) {
    const job = this.jobs.get(jobId)
    if (!job) return

    try {
      job.status = 'parsing'
      await this.updateJobStatus(jobId, 'parsing')

      console.log(`🔓 Starting parse job ${jobId} for ${job.fileName}`)

      let records: BreachRecord[] = []

      switch (job.format) {
        case 'csv':
          records = await this.parseCSV(job, options)
          break
        case 'json':
          records = await this.parseJSON(job, options)
          break
        case 'txt':
          records = await this.parseTXT(job, options)
          break
        case 'sql':
          records = await this.parseSQL(job, options)
          break
        default:
          throw new Error(`Unsupported format: ${job.format}`)
      }

      job.recordsFound = records.length
      job.recordsParsed = records.length
      job.recordsValid = records.filter(r => this.validateRecord(r)).length

      // Save records to database
      await this.saveRecords(jobId, records, options)

      job.status = 'completed'
      job.endTime = new Date()
      job.progress = 100

      await this.updateJobStatus(jobId, 'completed')
      console.log(`🎉 Parse job ${jobId} completed: ${job.recordsValid} valid records`)

    } catch (error) {
      job.status = 'failed'
      job.errors.push(`Parse job failed: ${error}`)
      await this.updateJobStatus(jobId, 'failed')
      console.error(`❌ Parse job ${jobId} failed:`, error)
    }
  }

  private async parseCSV(job: ParseJob, options: ParseOptions): Promise<BreachRecord[]> {
    const records: BreachRecord[] = []
    const fileContent = await readFile(job.filePath, 'utf-8')
    const lines = fileContent.split('\n')
    
    const delimiter = options.delimiter || ','
    const hasHeader = options.hasHeader !== false
    const startLine = hasHeader ? 1 : 0
    
    let headers: string[] = []
    if (hasHeader && lines.length > 0) {
      headers = lines[0].split(delimiter).map(h => h.trim().replace(/"/g, ''))
    }

    for (let i = startLine; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      try {
        const values = this.parseCSVLine(line, delimiter)
        const record = this.mapCSVRecord(values, headers, options, job.id)
        
        if (record) {
          records.push(record)
        }

        // Update progress
        job.progress = Math.floor((i / lines.length) * 100)
        if (i % 1000 === 0) {
          await this.updateJobProgress(job.id, job.progress)
        }

      } catch (error) {
        job.errors.push(`Line ${i + 1}: ${error}`)
      }
    }

    return records
  }

  private async parseJSON(job: ParseJob, options: ParseOptions): Promise<BreachRecord[]> {
    const records: BreachRecord[] = []
    const fileContent = await readFile(job.filePath, 'utf-8')
    
    try {
      const data = JSON.parse(fileContent)
      const items = Array.isArray(data) ? data : [data]

      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        const record = this.mapJSONRecord(item, options, job.id)
        
        if (record) {
          records.push(record)
        }

        // Update progress
        job.progress = Math.floor((i / items.length) * 100)
        if (i % 1000 === 0) {
          await this.updateJobProgress(job.id, job.progress)
        }
      }

    } catch (error) {
      throw new Error(`Invalid JSON format: ${error}`)
    }

    return records
  }

  private async parseTXT(job: ParseJob, options: ParseOptions): Promise<BreachRecord[]> {
    const records: BreachRecord[] = []
    const fileContent = await readFile(job.filePath, 'utf-8')
    const lines = fileContent.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      try {
        // Assume format: email:password or email
        const parts = line.split(':')
        const email = parts[0]?.trim()
        const password = parts[1]?.trim()

        if (email && this.isValidEmail(email)) {
          const record: BreachRecord = {
            id: this.generateId(),
            breachId: job.breachInfo?.id || '',
            email,
            emailHash: this.hashEmail(email),
            password: password || undefined,
            passwordHash: password ? this.hashPassword(password) : undefined,
            additionalData: {},
            dateAdded: new Date()
          }

          records.push(record)
        }

        // Update progress
        job.progress = Math.floor((i / lines.length) * 100)
        if (i % 1000 === 0) {
          await this.updateJobProgress(job.id, job.progress)
        }

      } catch (error) {
        job.errors.push(`Line ${i + 1}: ${error}`)
      }
    }

    return records
  }

  private async parseSQL(job: ParseJob, options: ParseOptions): Promise<BreachRecord[]> {
    // Basic SQL dump parser - would need more sophisticated parsing for real SQL dumps
    const records: BreachRecord[] = []
    const fileContent = await readFile(job.filePath, 'utf-8')
    
    // Extract INSERT statements
    const insertRegex = /INSERT INTO.*?VALUES\s*\((.*?)\);/gi
    let match

    while ((match = insertRegex.exec(fileContent)) !== null) {
      try {
        const values = match[1].split(',').map(v => v.trim().replace(/'/g, ''))
        
        // Assume first column is email, second is password
        if (values.length >= 1) {
          const email = values[0]
          const password = values[1] || undefined

          if (this.isValidEmail(email)) {
            const record: BreachRecord = {
              id: this.generateId(),
              breachId: job.breachInfo?.id || '',
              email,
              emailHash: this.hashEmail(email),
              password,
              passwordHash: password ? this.hashPassword(password) : undefined,
              additionalData: {},
              dateAdded: new Date()
            }

            records.push(record)
          }
        }

      } catch (error) {
        job.errors.push(`SQL parse error: ${error}`)
      }
    }

    return records
  }

  private parseCSVLine(line: string, delimiter: string): string[] {
    const values: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === delimiter && !inQuotes) {
        values.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    values.push(current.trim())
    return values
  }

  private mapCSVRecord(values: string[], headers: string[], options: ParseOptions, jobId: string): BreachRecord | null {
    const emailIndex = this.getColumnIndex(options.emailColumn, headers, ['email', 'mail', 'e-mail'])
    const passwordIndex = this.getColumnIndex(options.passwordColumn, headers, ['password', 'pass', 'pwd'])
    const usernameIndex = this.getColumnIndex(options.usernameColumn, headers, ['username', 'user', 'login'])

    const email = values[emailIndex]?.trim()
    if (!email || !this.isValidEmail(email)) {
      return null
    }

    return {
      id: this.generateId(),
      breachId: jobId,
      email,
      emailHash: this.hashEmail(email),
      password: values[passwordIndex]?.trim() || undefined,
      passwordHash: values[passwordIndex] ? this.hashPassword(values[passwordIndex]) : undefined,
      username: values[usernameIndex]?.trim() || undefined,
      additionalData: this.extractAdditionalData(values, headers, options),
      dateAdded: new Date()
    }
  }

  private mapJSONRecord(item: any, options: ParseOptions, jobId: string): BreachRecord | null {
    const email = item.email || item.mail || item['e-mail']
    if (!email || !this.isValidEmail(email)) {
      return null
    }

    return {
      id: this.generateId(),
      breachId: jobId,
      email,
      emailHash: this.hashEmail(email),
      password: item.password || item.pass || item.pwd || undefined,
      passwordHash: item.password ? this.hashPassword(item.password) : undefined,
      username: item.username || item.user || item.login || undefined,
      name: item.name || item.fullname || undefined,
      phone: item.phone || item.telephone || undefined,
      address: item.address || undefined,
      additionalData: item,
      dateAdded: new Date()
    }
  }

  private getColumnIndex(specified: number | string | undefined, headers: string[], defaults: string[]): number {
    if (typeof specified === 'number') {
      return specified
    }
    
    if (typeof specified === 'string') {
      const index = headers.findIndex(h => h.toLowerCase() === specified.toLowerCase())
      if (index !== -1) return index
    }
    
    // Try default column names
    for (const defaultName of defaults) {
      const index = headers.findIndex(h => h.toLowerCase().includes(defaultName))
      if (index !== -1) return index
    }
    
    return -1
  }

  private extractAdditionalData(values: string[], headers: string[], options: ParseOptions): Record<string, any> {
    const data: Record<string, any> = {}
    
    for (let i = 0; i < Math.min(values.length, headers.length); i++) {
      const header = headers[i]
      const value = values[i]
      
      if (header && value && !['email', 'password', 'username'].includes(header.toLowerCase())) {
        data[header] = value
      }
    }
    
    return data
  }

  private validateRecord(record: BreachRecord): boolean {
    return this.isValidEmail(record.email)
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private hashEmail(email: string): string {
    return createHash('sha256').update(email.toLowerCase()).digest('hex')
  }

  private hashPassword(password: string): string {
    return createHash('sha256').update(password).digest('hex')
  }

  private async saveRecords(jobId: string, records: BreachRecord[], options: ParseOptions) {
    const batchSize = options.chunkSize || 1000
    
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      
      for (const record of batch) {
        try {
          if (options.skipDuplicates) {
            // Check for existing record
            const existing = await db.query(
              'SELECT id FROM breach_records WHERE email_hash = ? AND breach_id = ?',
              [record.emailHash, record.breachId]
            )
            
            if (existing.length > 0) continue
          }

          await db.query(`
            INSERT INTO breach_records (id, breach_id, email, email_hash, password_hash, username, name, phone, address, additional_data, date_added)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            record.id,
            record.breachId,
            record.email,
            record.emailHash,
            record.passwordHash,
            record.username,
            record.name,
            record.phone,
            record.address,
            JSON.stringify(record.additionalData)
          ])

        } catch (error) {
          console.error('Error saving record:', error)
        }
      }
    }
  }

  private async upsertBreach(breach: BreachData) {
    try {
      await db.query(`
        INSERT INTO breaches (id, name, domain, breach_date, added_date, modified_date, pwn_count, description, data_classes, is_verified, is_fabricated, is_sensitive, is_retired, is_spam_list, is_malware, is_subscription_free)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        modified_date = VALUES(modified_date),
        pwn_count = VALUES(pwn_count),
        description = VALUES(description),
        data_classes = VALUES(data_classes),
        updated_at = NOW()
      `, [
        breach.id,
        breach.name,
        breach.domain,
        breach.breachDate,
        breach.addedDate,
        breach.modifiedDate,
        breach.pwnCount,
        breach.description,
        JSON.stringify(breach.dataClasses),
        breach.isVerified,
        breach.isFabricated,
        breach.isSensitive,
        breach.isRetired,
        breach.isSpamList,
        breach.isMalware,
        breach.isSubscriptionFree
      ])
    } catch (error) {
      console.error('Error upserting breach:', error)
    }
  }

  private async updateJobStatus(jobId: string, status: string) {
    try {
      const job = this.jobs.get(jobId)
      await db.query(`
        UPDATE parse_jobs 
        SET status = ?, progress = ?, records_found = ?, records_parsed = ?, records_valid = ?, errors = ?, updated_at = NOW(), completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [
        status,
        job?.progress || 0,
        job?.recordsFound || 0,
        job?.recordsParsed || 0,
        job?.recordsValid || 0,
        JSON.stringify(job?.errors || []),
        jobId
      ])
    } catch (error) {
      console.error('Error updating job status:', error)
    }
  }

  private async updateJobProgress(jobId: string, progress: number) {
    try {
      await db.query('UPDATE parse_jobs SET progress = ?, updated_at = NOW() WHERE id = ?', [progress, jobId])
    } catch (error) {
      console.error('Error updating job progress:', error)
    }
  }

  async searchBreaches(email: string): Promise<BreachRecord[]> {
    try {
      const emailHash = this.hashEmail(email)
      const records = await db.query(`
        SELECT br.*, b.name as breach_name, b.breach_date, b.data_classes
        FROM breach_records br
        JOIN breaches b ON br.breach_id = b.id
        WHERE br.email_hash = ?
        ORDER BY b.breach_date DESC
      `, [emailHash])

      return records as BreachRecord[]
    } catch (error) {
      console.error('Error searching breaches:', error)
      return []
    }
  }

  async getJob(jobId: string): Promise<ParseJob | null> {
    return this.jobs.get(jobId) || null
  }

  async getAllJobs(): Promise<ParseJob[]> {
    return Array.from(this.jobs.values())
  }

  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId)
    if (job && job.status === 'parsing') {
      job.status = 'cancelled'
      await this.updateJobStatus(jobId, 'cancelled')
      return true
    }
    return false
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const breachParser = new BreachParser()
