@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cyberpunk Theme Variables */
:root {
  /* Cyberpunk Color Palette */
  --cyber-primary: #00ffff;
  --cyber-secondary: #ff0080;
  --cyber-accent: #ffff00;
  --cyber-success: #00ff41;
  --cyber-warning: #ff8c00;
  --cyber-danger: #ff073a;

  /* Dark Theme */
  --cyber-bg-primary: #0a0a0f;
  --cyber-bg-secondary: #1a1a2e;
  --cyber-bg-tertiary: #16213e;
  --cyber-bg-card: #0f0f23;
  --cyber-bg-hover: #1e1e3f;

  /* Text Colors */
  --cyber-text-primary: #ffffff;
  --cyber-text-secondary: #b0b0b0;
  --cyber-text-muted: #808080;
  --cyber-text-accent: #00ffff;

  /* Borders & Effects */
  --cyber-border: #333366;
  --cyber-border-bright: #00ffff;
  --cyber-glow: 0 0 20px rgba(0, 255, 255, 0.5);
  --cyber-glow-pink: 0 0 20px rgba(255, 0, 128, 0.5);
  --cyber-glow-yellow: 0 0 20px rgba(255, 255, 0, 0.5);

  /* Legacy Primary Colors (for compatibility) */
  --primary-50: #0a0a0f;
  --primary-100: #1a1a2e;
  --primary-200: #16213e;
  --primary-300: #0f0f23;
  --primary-400: #00ffff;
  --primary-500: #00ffff;
  --primary-600: #00cccc;
  --primary-700: #009999;
  --primary-800: #006666;
  --primary-900: #003333;
}

/* Cyberpunk Components */
@layer components {
  /* Cyberpunk Buttons */
  .btn-cyber-primary {
    @apply inline-flex items-center px-6 py-3 border-2 border-cyan-400 text-sm font-bold rounded-lg text-cyan-400 bg-transparent hover:bg-cyan-400 hover:text-black focus:outline-none focus:ring-2 focus:ring-cyan-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .btn-cyber-primary:hover {
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
    transform: translateY(-2px);
  }

  .btn-cyber-secondary {
    @apply inline-flex items-center px-6 py-3 border-2 border-pink-500 text-sm font-bold rounded-lg text-pink-500 bg-transparent hover:bg-pink-500 hover:text-black focus:outline-none focus:ring-2 focus:ring-pink-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300;
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .btn-cyber-secondary:hover {
    box-shadow: 0 0 30px rgba(255, 0, 128, 0.6);
    transform: translateY(-2px);
  }

  .btn-cyber-danger {
    @apply inline-flex items-center px-6 py-3 border-2 border-red-500 text-sm font-bold rounded-lg text-red-500 bg-transparent hover:bg-red-500 hover:text-black focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300;
    box-shadow: 0 0 20px rgba(255, 7, 58, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  /* Legacy Buttons (for compatibility) */
  .btn-primary {
    @apply btn-cyber-primary;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200;
  }

  .btn-danger {
    @apply btn-cyber-danger;
  }

  /* Cyberpunk Input Fields */
  .input-cyber {
    @apply block w-full px-4 py-3 border-2 border-gray-600 rounded-lg bg-gray-900 text-cyan-400 placeholder-gray-500 focus:outline-none focus:border-cyan-400 transition-all duration-300;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
  }

  .input-cyber:focus {
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 255, 255, 0.3);
  }

  .input-field {
    @apply input-cyber;
  }

  .input-error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 7, 58, 0.3);
  }

  /* Cyberpunk Cards */
  .card-cyber {
    @apply bg-gray-900 border border-gray-700 rounded-lg p-6 transition-all duration-300;
    background: linear-gradient(135deg, rgba(10, 10, 15, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .card-cyber:hover {
    border-color: var(--cyber-primary);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .card-hover {
    @apply card-cyber;
  }

  /* Cyberpunk Gradients */
  .gradient-cyber {
    background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
  }

  .gradient-cyber-glow {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 128, 0.1) 100%);
  }

  .gradient-bg {
    @apply gradient-cyber;
  }

  /* Glass Effect */
  .glass-cyber {
    @apply backdrop-blur-md border border-gray-700;
    background: rgba(10, 10, 15, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-effect {
    @apply glass-cyber;
  }

  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply status-badge bg-green-100 text-green-800;
  }

  .status-warning {
    @apply status-badge bg-yellow-100 text-yellow-800;
  }

  .status-error {
    @apply status-badge bg-red-100 text-red-800;
  }

  .status-info {
    @apply status-badge bg-blue-100 text-blue-800;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  .skeleton {
    @apply animate-pulse bg-gray-300 rounded;
  }

  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
  }

  .tooltip.show {
    @apply opacity-100;
  }
}

/* Cyberpunk Animations */
@keyframes cyberGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 255, 255, 0.8);
  }
}

@keyframes cyberGlowPink {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 0, 128, 0.8);
  }
}

@keyframes cyberPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes cyberScan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes matrixRain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

/* Cyberpunk Animation Classes */
.animate-cyber-glow {
  animation: cyberGlow 2s ease-in-out infinite;
}

.animate-cyber-glow-pink {
  animation: cyberGlowPink 2s ease-in-out infinite;
}

.animate-cyber-pulse {
  animation: cyberPulse 1.5s ease-in-out infinite;
}

.animate-cyber-scan {
  animation: cyberScan 2s linear infinite;
}

.animate-matrix-rain {
  animation: matrixRain 3s linear infinite;
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Cyberpunk Text Effects */
.text-cyber-glow {
  color: var(--cyber-primary);
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.text-cyber-pink {
  color: var(--cyber-secondary);
  text-shadow: 0 0 10px rgba(255, 0, 128, 0.8);
}

.text-cyber-yellow {
  color: var(--cyber-accent);
  text-shadow: 0 0 10px rgba(255, 255, 0, 0.8);
}

/* Cyberpunk Borders */
.border-cyber {
  border: 2px solid var(--cyber-border);
}

.border-cyber-glow {
  border: 2px solid var(--cyber-primary);
  box-shadow: var(--cyber-glow);
}

.border-cyber-pink {
  border: 2px solid var(--cyber-secondary);
  box-shadow: var(--cyber-glow-pink);
}

/* Cyberpunk Backgrounds */
.bg-cyber-dark {
  background-color: var(--cyber-bg-primary);
}

.bg-cyber-card {
  background-color: var(--cyber-bg-card);
}

.bg-cyber-secondary {
  background-color: var(--cyber-bg-secondary);
}

/* Cyberpunk Scrollbar */
.scrollbar-cyber::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-cyber::-webkit-scrollbar-track {
  background: var(--cyber-bg-secondary);
}

.scrollbar-cyber::-webkit-scrollbar-thumb {
  background: var(--cyber-primary);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.scrollbar-cyber::-webkit-scrollbar-thumb:hover {
  background: var(--cyber-secondary);
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.7);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: #1e293b;
  }

  ::-webkit-scrollbar-thumb {
    background: #475569;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Utility classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

/* Code syntax highlighting */
.code-block {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
  font-family: 'JetBrains Mono', monospace;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply text-danger-600 text-sm mt-1;
}

/* Table styles */
.table {
  @apply w-full border-collapse;
}

.table th {
  @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

/* Badge styles */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-success-100 text-success-800;
}

.badge-warning {
  @apply bg-warning-100 text-warning-800;
}

.badge-danger {
  @apply bg-danger-100 text-danger-800;
}

.badge-info {
  @apply bg-primary-100 text-primary-800;
}
