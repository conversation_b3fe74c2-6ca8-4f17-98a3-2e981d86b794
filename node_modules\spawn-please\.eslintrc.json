{
  "env": {
    "es6": true,
    "mocha": true,
    "node": true
  },
  "extends": ["raine", "prettier"],
  "rules": {
    // This is not a Typescript project, so types are intentionally written in JSDOC.
    // https://www.typescriptlang.org/docs/handbook/declaration-files/dts-from-js.html
    // https://github.com/gajus/eslint-plugin-jsdoc/blob/main/.README/rules/no-types.md
    "jsdoc/no-types": 0
  }
}
