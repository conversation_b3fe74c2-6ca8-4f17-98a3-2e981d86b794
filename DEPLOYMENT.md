# 🚀 KodeXGuard - Panduan Deployment Lengkap

## 📋 **PREREQUISITES**

### **System Requirements**
- **Node.js**: 18.0+ atau Bun.js 1.0+
- **MySQL**: 8.0+
- **Redis**: 7.0+ (optional, untuk caching)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 10GB free space
- **OS**: Windows 10+, macOS 10.15+, Ubuntu 20.04+

### **External Services (Optional)**
- **VirusTotal API Key**: Untuk file analysis
- **Shodan API Key**: Untuk IP scanning
- **Have I Been Pwned API Key**: Untuk breach checking
- **Google Custom Search API**: Untuk dorking
- **Hunter.io API Key**: Untuk email verification

---

## ⚡ **QUICK START (Recommended)**

### **1. Clone Repository**
```bash
git clone https://github.com/your-username/kodexguard.git
cd kodexguard
```

### **2. Automated Setup**
```bash
# Jalankan setup otomatis (Linux/macOS)
chmod +x setup.sh
./setup.sh

# Atau untuk Windows
npm run setup
```

### **3. Configure Environment**
```bash
# Edit file .env dengan konfigurasi Anda
cp .env.example .env
nano .env  # atau gunakan editor favorit Anda
```

### **4. Start Application**
```bash
# Development
npm run dev

# Production
npm run build
npm start

# Dengan PM2
npm run pm2:start
```

---

## 🔧 **MANUAL SETUP**

### **Step 1: Install Dependencies**
```bash
# Install Node.js dependencies
npm install

# Atau dengan Bun
bun install

# Install additional security packages
npm install mysql2 bcryptjs jsonwebtoken zod axios cheerio
npm install helmet cors rate-limiter-flexible
npm install multer file-type validator sanitize-html
npm install winston morgan redis nodemailer
npm install crypto-js speakeasy qrcode
```

### **Step 2: Database Setup**
```bash
# 1. Create MySQL database
mysql -u root -p
CREATE DATABASE kodexguard;
exit

# 2. Run database setup
npm run setup:db

# 3. Verify database
mysql -u root -p kodexguard
SHOW TABLES;
```

### **Step 3: Environment Configuration**
```bash
# Copy environment template
cp .env.example .env
```

**Edit .env file:**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=kodexguard

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key

# External API Keys (Optional)
VIRUSTOTAL_API_KEY=your_virustotal_api_key
SHODAN_API_KEY=your_shodan_api_key
HIBP_API_KEY=your_haveibeenpwned_api_key
HUNTER_API_KEY=your_hunter_io_api_key
GOOGLE_API_KEY=your_google_api_key
GOOGLE_CSE_ID=your_google_cse_id

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Application Configuration
NODE_ENV=production
PORT=3000
APP_URL=https://your-domain.com
```

### **Step 4: Build & Start**
```bash
# Build for production
npm run build

# Start application
npm start

# Or with PM2 for production
npm install -g pm2
npm run pm2:start
```

---

## 🐳 **DOCKER DEPLOYMENT**

### **1. Using Docker Compose (Recommended)**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_USER=kodexguard
      - DB_PASSWORD=secure_password
      - DB_NAME=kodexguard
    depends_on:
      - mysql
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=kodexguard
      - MYSQL_USER=kodexguard
      - MYSQL_PASSWORD=secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### **2. Deploy with Docker**
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

---

## ☁️ **CLOUD DEPLOYMENT**

### **1. Vercel Deployment**
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
# Configure database connection
```

### **2. Railway Deployment**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### **3. DigitalOcean App Platform**
```yaml
# .do/app.yaml
name: kodexguard
services:
- name: web
  source_dir: /
  github:
    repo: your-username/kodexguard
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: DB_HOST
    value: ${db.HOSTNAME}
  - key: DB_USER
    value: ${db.USERNAME}
  - key: DB_PASSWORD
    value: ${db.PASSWORD}
  - key: DB_NAME
    value: ${db.DATABASE}

databases:
- name: db
  engine: MYSQL
  version: "8"
```

---

## 🔒 **PRODUCTION SECURITY**

### **1. SSL/TLS Configuration**
```bash
# Using Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### **2. Nginx Configuration**
```nginx
# /etc/nginx/sites-available/kodexguard
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### **3. Firewall Configuration**
```bash
# UFW Firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3306  # MySQL (only if external access needed)
```

---

## 📊 **MONITORING & LOGGING**

### **1. PM2 Monitoring**
```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs kodexguard

# Restart application
pm2 restart kodexguard

# Auto-restart on system reboot
pm2 startup
pm2 save
```

### **2. Log Management**
```bash
# Application logs location
./logs/app.log
./logs/error.log
./logs/access.log

# Rotate logs
sudo logrotate -f /etc/logrotate.d/kodexguard
```

### **3. Health Checks**
```bash
# API health check
curl http://localhost:3000/api/health

# Database connection check
curl http://localhost:3000/api/health/database

# System status
curl http://localhost:3000/api/tools/status
```

---

## 🔧 **MAINTENANCE**

### **1. Database Backup**
```bash
# Create backup
mysqldump -u root -p kodexguard > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
mysql -u root -p kodexguard < backup_20240101_120000.sql
```

### **2. Update Application**
```bash
# Pull latest changes
git pull origin main

# Install new dependencies
npm install

# Run migrations (if any)
npm run db:migrate

# Rebuild and restart
npm run build
pm2 restart kodexguard
```

### **3. Performance Optimization**
```bash
# Optimize MySQL
mysql_secure_installation

# Enable MySQL slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

# Monitor performance
pm2 monit
htop
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **1. Database Connection Error**
```bash
# Check MySQL service
sudo systemctl status mysql
sudo systemctl start mysql

# Verify credentials
mysql -u root -p -e "SELECT 1;"

# Check firewall
sudo ufw status
```

#### **2. Port Already in Use**
```bash
# Find process using port 3000
lsof -i :3000
netstat -tulpn | grep 3000

# Kill process
kill -9 <PID>
```

#### **3. Permission Errors**
```bash
# Fix file permissions
sudo chown -R $USER:$USER /path/to/kodexguard
chmod -R 755 /path/to/kodexguard

# Fix upload directory
mkdir -p uploads/file-analysis
chmod 755 uploads/file-analysis
```

#### **4. Memory Issues**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Monitor memory usage
free -h
ps aux | grep node
```

---

## 📞 **SUPPORT**

### **Getting Help**
- **Documentation**: Check README.md and FEATURES.md
- **Issues**: Create GitHub issue with detailed description
- **Logs**: Include relevant log files when reporting issues
- **Environment**: Specify OS, Node.js version, and configuration

### **Performance Tuning**
- **Database**: Optimize MySQL configuration
- **Caching**: Implement Redis caching
- **CDN**: Use CDN for static assets
- **Load Balancing**: Scale horizontally with multiple instances

---

## ✅ **DEPLOYMENT CHECKLIST**

### **Pre-deployment**
- [ ] Environment variables configured
- [ ] Database schema created
- [ ] SSL certificates installed
- [ ] Firewall configured
- [ ] Backup strategy implemented

### **Post-deployment**
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Logs accessible
- [ ] Performance optimized
- [ ] Security hardened

### **Go Live**
- [ ] DNS configured
- [ ] Load testing completed
- [ ] Monitoring alerts set up
- [ ] Backup verified
- [ ] Team notified

---

**🎉 Selamat! KodeXGuard siap untuk production dengan semua fitur yang fungsional 100%!**
