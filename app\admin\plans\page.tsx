'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { 
  Crown, 
  Star, 
  Shield,
  Zap,
  Users,
  TrendingUp,
  DollarSign,
  Calendar,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  BarChart3,
  Activity,
  Award,
  Gem
} from 'lucide-react'

interface Plan {
  id: string
  name: string
  description: string
  price: number
  billingCycle: 'monthly' | 'yearly'
  features: string[]
  limits: {
    scans: number
    osintQueries: number
    fileAnalysis: number
    apiCalls: number
    storage: number
  }
  subscribers: number
  revenue: number
  status: 'active' | 'inactive' | 'deprecated'
  tier: 'free' | 'basic' | 'pro' | 'enterprise'
  popular: boolean
}

export default function AdminPlansPage() {
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  useEffect(() => {
    loadPlans()
  }, [])

  const loadPlans = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockPlans: Plan[] = [
        {
          id: '1',
          name: 'Free',
          description: 'Basic cybersecurity tools for beginners',
          price: 0,
          billingCycle: 'monthly',
          features: [
            'Basic vulnerability scanning',
            'Limited OSINT queries',
            'Community support',
            'Basic file analysis'
          ],
          limits: {
            scans: 10,
            osintQueries: 50,
            fileAnalysis: 5,
            apiCalls: 100,
            storage: 100
          },
          subscribers: 12450,
          revenue: 0,
          status: 'active',
          tier: 'free',
          popular: false
        },
        {
          id: '2',
          name: 'Pro',
          description: 'Advanced tools for security professionals',
          price: 29,
          billingCycle: 'monthly',
          features: [
            'Advanced vulnerability scanning',
            'Unlimited OSINT queries',
            'Priority support',
            'Advanced file analysis',
            'API access',
            'Custom reports'
          ],
          limits: {
            scans: 100,
            osintQueries: -1,
            fileAnalysis: 50,
            apiCalls: 1000,
            storage: 1000
          },
          subscribers: 2847,
          revenue: 82563,
          status: 'active',
          tier: 'pro',
          popular: true
        },
        {
          id: '3',
          name: 'Expert',
          description: 'Professional-grade cybersecurity platform',
          price: 99,
          billingCycle: 'monthly',
          features: [
            'Enterprise vulnerability scanning',
            'Advanced OSINT intelligence',
            'Dedicated support',
            'AI-powered analysis',
            'Full API access',
            'White-label reports',
            'Team collaboration'
          ],
          limits: {
            scans: 500,
            osintQueries: -1,
            fileAnalysis: 200,
            apiCalls: 5000,
            storage: 5000
          },
          subscribers: 567,
          revenue: 56133,
          status: 'active',
          tier: 'enterprise',
          popular: false
        },
        {
          id: '4',
          name: 'Elite',
          description: 'Ultimate cybersecurity solution for enterprises',
          price: 299,
          billingCycle: 'monthly',
          features: [
            'Unlimited everything',
            'Custom integrations',
            '24/7 dedicated support',
            'On-premise deployment',
            'Custom development',
            'SLA guarantees'
          ],
          limits: {
            scans: -1,
            osintQueries: -1,
            fileAnalysis: -1,
            apiCalls: -1,
            storage: -1
          },
          subscribers: 89,
          revenue: 26611,
          status: 'active',
          tier: 'enterprise',
          popular: false
        }
      ]

      setPlans(mockPlans)
      setLoading(false)
    } catch (error) {
      console.error('Error loading plans:', error)
      setLoading(false)
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'free': return 'text-gray-400 bg-gray-400/20'
      case 'basic': return 'text-blue-400 bg-blue-400/20'
      case 'pro': return 'text-purple-400 bg-purple-400/20'
      case 'enterprise': return 'text-yellow-400 bg-yellow-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'free': return Shield
      case 'basic': return Star
      case 'pro': return Crown
      case 'enterprise': return Gem
      default: return Shield
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400'
      case 'inactive': return 'text-yellow-400'
      case 'deprecated': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const formatLimit = (limit: number) => {
    if (limit === -1) return 'Unlimited'
    if (limit >= 1000) return `${(limit / 1000).toFixed(0)}K`
    return limit.toString()
  }

  const totalRevenue = plans.reduce((sum, plan) => sum + plan.revenue, 0)
  const totalSubscribers = plans.reduce((sum, plan) => sum + plan.subscribers, 0)

  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading subscription plans...</div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">Subscription</span>{' '}
              <span className="text-cyber-pink">Plans</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Manage pricing plans and subscription tiers
            </p>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <button className="btn-cyber-secondary">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </button>
            <button className="btn-cyber-primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Plan
            </button>
          </div>
        </div>

        {/* Revenue Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="card-cyber text-center">
            <DollarSign className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              ${totalRevenue.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Monthly Revenue</div>
          </div>
          
          <div className="card-cyber text-center">
            <Users className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {totalSubscribers.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Total Subscribers</div>
          </div>
          
          <div className="card-cyber text-center">
            <TrendingUp className="h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              ${(totalRevenue / totalSubscribers).toFixed(2)}
            </div>
            <div className="text-sm text-gray-400">ARPU</div>
          </div>
          
          <div className="card-cyber text-center">
            <Crown className="h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {plans.filter(p => p.tier !== 'free').length}
            </div>
            <div className="text-sm text-gray-400">Premium Plans</div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {plans.map((plan) => {
            const TierIcon = getTierIcon(plan.tier)
            return (
              <div
                key={plan.id}
                className={`card-cyber relative overflow-hidden ${
                  plan.popular ? 'border-2 border-cyber-primary' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-4 right-4 bg-cyber-primary text-black px-3 py-1 rounded-full text-xs font-bold">
                    POPULAR
                  </div>
                )}

                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${getTierColor(plan.tier)}`}>
                      <TierIcon className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">{plan.name}</h3>
                      <p className="text-gray-400">{plan.description}</p>
                    </div>
                  </div>
                  
                  <div className={`text-sm font-medium ${getStatusColor(plan.status)}`}>
                    {plan.status}
                  </div>
                </div>

                <div className="mb-6">
                  <div className="flex items-baseline space-x-2">
                    <span className="text-4xl font-bold text-cyber-primary">
                      ${plan.price}
                    </span>
                    <span className="text-gray-400">/{plan.billingCycle}</span>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="text-lg font-bold text-white mb-3">Features</h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2 text-gray-300">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-6">
                  <h4 className="text-lg font-bold text-white mb-3">Limits</h4>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Scans:</span>
                      <span className="text-white font-medium">{formatLimit(plan.limits.scans)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">OSINT:</span>
                      <span className="text-white font-medium">{formatLimit(plan.limits.osintQueries)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Files:</span>
                      <span className="text-white font-medium">{formatLimit(plan.limits.fileAnalysis)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">API:</span>
                      <span className="text-white font-medium">{formatLimit(plan.limits.apiCalls)}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-6 grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-xl font-bold text-cyber-secondary">
                      {plan.subscribers.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-400">Subscribers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-cyber-accent">
                      ${plan.revenue.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-400">Revenue</div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="flex-1 btn-cyber-primary text-sm">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Plan
                  </button>
                  <button className="p-2 rounded-lg bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors">
                    <Eye className="h-4 w-4" />
                  </button>
                  <button className="p-2 rounded-lg bg-gray-500/20 text-gray-400 hover:bg-gray-500/30 transition-colors">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
