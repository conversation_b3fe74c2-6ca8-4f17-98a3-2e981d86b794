const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rootkan',
  database: 'db_kodexguard'
}

async function seedSampleData() {
  let connection

  try {
    console.log('🌱 Seeding sample data...')
    connection = await mysql.createConnection(dbConfig)
    
    // Create sample users
    console.log('👥 Creating sample users...')
    await createSampleUsers(connection)
    
    // Create sample vulnerability scans
    console.log('🔍 Creating sample vulnerability scans...')
    await createSampleScans(connection)
    
    // Create sample OSINT queries
    console.log('🕵️ Creating sample OSINT queries...')
    await createSampleOSINTQueries(connection)
    
    // Create sample file analyses
    console.log('📁 Creating sample file analyses...')
    await createSampleFileAnalyses(connection)
    
    // Create sample dorking queries
    console.log('🔎 Creating sample dorking queries...')
    await createSampleDorkingQueries(connection)
    
    // Create sample CVE data
    console.log('🗄️ Creating sample CVE data...')
    await createSampleCVEData(connection)
    
    // Create sample bot instances
    console.log('🤖 Creating sample bot instances...')
    await createSampleBotInstances(connection)
    
    // Create sample system logs
    console.log('📊 Creating sample system logs...')
    await createSampleSystemLogs(connection)
    
    console.log('✅ Sample data seeded successfully!')

  } catch (error) {
    console.error('❌ Sample data seeding failed:', error)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function createSampleUsers(connection) {
  const users = [
    {
      username: 'cyberwarrior',
      email: '<EMAIL>',
      full_name: 'Alex Chen',
      plan: 'Pro',
      level: 15,
      score: 2500,
      streak_days: 7
    },
    {
      username: 'securityexpert',
      email: '<EMAIL>',
      full_name: 'Sarah Johnson',
      plan: 'Expert',
      level: 28,
      score: 8950,
      streak_days: 12
    },
    {
      username: 'pentester',
      email: '<EMAIL>',
      full_name: 'Mike Rodriguez',
      plan: 'Elite',
      level: 42,
      score: 15420,
      streak_days: 25
    },
    {
      username: 'hackerninja',
      email: '<EMAIL>',
      full_name: 'Emma Wilson',
      plan: 'Free',
      level: 8,
      score: 890,
      streak_days: 3
    },
    {
      username: 'cybersleuth',
      email: '<EMAIL>',
      full_name: 'David Kim',
      plan: 'Pro',
      level: 22,
      score: 5670,
      streak_days: 15
    }
  ]

  for (const user of users) {
    const passwordHash = await bcrypt.hash('password123', 12)
    
    const [result] = await connection.execute(
      `INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) 
       VALUES (?, ?, ?, ?, 'user', ?, ?, ?, ?, true, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), NOW(), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 24) HOUR))`,
      [user.username, user.email, passwordHash, user.full_name, user.plan, user.level, user.score, user.streak_days]
    )

    const userId = result.insertId

    // Create user preferences
    await connection.execute(
      `INSERT INTO user_preferences (user_id, created_at, updated_at) 
       VALUES (?, NOW(), NOW())`,
      [userId]
    )
  }
}

async function createSampleScans(connection) {
  const scanTargets = [
    'https://example.com',
    'https://testsite.org',
    'https://vulnerable-app.com',
    'https://secure-site.net',
    'https://old-website.com',
    'https://api.service.com',
    'https://admin.portal.com',
    'https://staging.app.com'
  ]

  const scanTypes = ['basic', 'advanced', 'comprehensive']
  const statuses = ['completed', 'completed', 'completed', 'failed', 'running']

  for (let i = 0; i < 50; i++) {
    const userId = Math.floor(Math.random() * 5) + 1
    const targetUrl = scanTargets[Math.floor(Math.random() * scanTargets.length)]
    const scanType = scanTypes[Math.floor(Math.random() * scanTypes.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const vulnerabilitiesFound = status === 'completed' ? Math.floor(Math.random() * 20) : 0
    const severityCritical = Math.floor(vulnerabilitiesFound * 0.1)
    const severityHigh = Math.floor(vulnerabilitiesFound * 0.2)
    const severityMedium = Math.floor(vulnerabilitiesFound * 0.4)
    const severityLow = vulnerabilitiesFound - severityCritical - severityHigh - severityMedium

    const scanResults = {
      summary: {
        total: vulnerabilitiesFound,
        critical: severityCritical,
        high: severityHigh,
        medium: severityMedium,
        low: severityLow
      },
      vulnerabilities: []
    }

    await connection.execute(
      `INSERT INTO vulnerability_scans (user_id, target_url, scan_type, status, vulnerabilities_found, severity_critical, severity_high, severity_medium, severity_low, scan_results, created_at, completed_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})`,
      [userId, targetUrl, scanType, status, vulnerabilitiesFound, severityCritical, severityHigh, severityMedium, severityLow, JSON.stringify(scanResults)]
    )
  }
}

async function createSampleOSINTQueries(connection) {
  const queryTypes = ['email', 'domain', 'ip', 'username', 'phone']
  const queryValues = [
    '<EMAIL>',
    'example.com',
    '***********',
    'johndoe123',
    '+1234567890',
    '<EMAIL>',
    'vulnerable-site.com',
    '********',
    'hacker2023',
    '+9876543210'
  ]

  for (let i = 0; i < 30; i++) {
    const userId = Math.floor(Math.random() * 5) + 1
    const queryType = queryTypes[Math.floor(Math.random() * queryTypes.length)]
    const queryValue = queryValues[Math.floor(Math.random() * queryValues.length)]
    const status = Math.random() > 0.1 ? 'completed' : 'failed'

    const results = {
      found: Math.random() > 0.3,
      sources: ['haveibeenpwned', 'shodan', 'virustotal'],
      data: {}
    }

    await connection.execute(
      `INSERT INTO osint_queries (user_id, query_type, query_value, status, results, created_at, completed_at) 
       VALUES (?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})`,
      [userId, queryType, queryValue, status, JSON.stringify(results)]
    )
  }
}

async function createSampleFileAnalyses(connection) {
  const filenames = [
    'suspicious.exe',
    'malware.dll',
    'webshell.php',
    'trojan.zip',
    'virus.bat',
    'backdoor.js',
    'ransomware.exe',
    'clean-file.pdf'
  ]

  for (let i = 0; i < 25; i++) {
    const userId = Math.floor(Math.random() * 5) + 1
    const filename = filenames[Math.floor(Math.random() * filenames.length)]
    const fileSize = Math.floor(Math.random() * 10000000) + 1000
    const threatDetected = Math.random() > 0.6
    const status = Math.random() > 0.1 ? 'completed' : 'failed'

    const analysisResults = {
      threatDetected,
      threatType: threatDetected ? ['malware', 'webshell', 'trojan'][Math.floor(Math.random() * 3)] : null,
      confidenceScore: threatDetected ? Math.random() * 0.4 + 0.6 : Math.random() * 0.3,
      signatures: []
    }

    await connection.execute(
      `INSERT INTO file_analyses (user_id, filename, file_size, file_type, analysis_type, status, threat_detected, threat_type, confidence_score, analysis_results, created_at, completed_at) 
       VALUES (?, ?, ?, 'application/octet-stream', 'malware', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})`,
      [userId, filename, fileSize, status, threatDetected, analysisResults.threatType, analysisResults.confidenceScore, JSON.stringify(analysisResults)]
    )
  }
}

async function createSampleDorkingQueries(connection) {
  const dorkingQueries = [
    'site:example.com filetype:sql',
    'intitle:"Index of" "Parent Directory"',
    'inurl:admin login',
    'filetype:pdf confidential',
    'site:target.com inurl:wp-admin',
    '"SQL syntax error"',
    'intitle:"phpMyAdmin"',
    'filetype:log error'
  ]

  for (let i = 0; i < 20; i++) {
    const userId = Math.floor(Math.random() * 5) + 1
    const queryString = dorkingQueries[Math.floor(Math.random() * dorkingQueries.length)]
    const status = Math.random() > 0.1 ? 'completed' : 'failed'
    const resultsFound = status === 'completed' ? Math.floor(Math.random() * 50) : 0

    const results = {
      results: [],
      totalFound: resultsFound
    }

    await connection.execute(
      `INSERT INTO dorking_queries (user_id, query_string, status, results_found, results, created_at, completed_at) 
       VALUES (?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})`,
      [userId, queryString, status, resultsFound, JSON.stringify(results)]
    )
  }
}

async function createSampleCVEData(connection) {
  const sampleCVEs = [
    {
      cve_id: 'CVE-2024-0001',
      description: 'Critical remote code execution vulnerability in web application framework',
      severity: 'CRITICAL',
      cvss_score: 9.8,
      exploit_available: true,
      patch_available: true
    },
    {
      cve_id: 'CVE-2024-0002',
      description: 'SQL injection vulnerability in database management system',
      severity: 'HIGH',
      cvss_score: 8.1,
      exploit_available: false,
      patch_available: true
    },
    {
      cve_id: 'CVE-2024-0003',
      description: 'Cross-site scripting vulnerability in content management system',
      severity: 'MEDIUM',
      cvss_score: 6.1,
      exploit_available: true,
      patch_available: false
    }
  ]

  for (const cve of sampleCVEs) {
    await connection.execute(
      `INSERT INTO cve_database (cve_id, description, severity, cvss_score, published_date, exploit_available, patch_available, created_at, updated_at) 
       VALUES (?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ?, ?, NOW(), NOW())`,
      [cve.cve_id, cve.description, cve.severity, cve.cvss_score, cve.exploit_available, cve.patch_available]
    )
  }
}

async function createSampleBotInstances(connection) {
  const bots = [
    {
      name: 'VulnScanner-01',
      type: 'scanner',
      status: 'running',
      tasks_completed: 1250,
      tasks_queued: 5,
      cpu_usage: 45.2,
      memory_usage: 67.8
    },
    {
      name: 'OSINT-Collector-01',
      type: 'osint',
      status: 'running',
      tasks_completed: 890,
      tasks_queued: 12,
      cpu_usage: 23.1,
      memory_usage: 34.5
    },
    {
      name: 'FileAnalyzer-01',
      type: 'monitor',
      status: 'running',
      tasks_completed: 567,
      tasks_queued: 3,
      cpu_usage: 78.9,
      memory_usage: 89.2
    }
  ]

  for (const bot of bots) {
    await connection.execute(
      `INSERT INTO bot_instances (name, type, status, tasks_completed, tasks_queued, cpu_usage, memory_usage, uptime_seconds, last_activity, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())`,
      [bot.name, bot.type, bot.status, bot.tasks_completed, bot.tasks_queued, bot.cpu_usage, bot.memory_usage, 86400 * 7] // 7 days uptime
    )
  }
}

async function createSampleSystemLogs(connection) {
  const logMessages = [
    'User login successful',
    'Vulnerability scan completed',
    'OSINT query executed',
    'File analysis completed',
    'CVE search performed',
    'System backup completed',
    'Database maintenance completed',
    'Security alert: Multiple failed login attempts'
  ]

  for (let i = 0; i < 100; i++) {
    const userId = Math.random() > 0.3 ? Math.floor(Math.random() * 5) + 1 : null
    const message = logMessages[Math.floor(Math.random() * logMessages.length)]
    const level = ['info', 'warning', 'error'][Math.floor(Math.random() * 3)]

    await connection.execute(
      `INSERT INTO system_logs (user_id, level, message, created_at) 
       VALUES (?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY))`,
      [userId, level, message]
    )
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedSampleData()
}

module.exports = { seedSampleData }
