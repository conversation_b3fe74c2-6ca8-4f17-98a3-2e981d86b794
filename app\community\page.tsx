'use client'

import { useState } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { 
  Users, 
  MessageSquare, 
  Trophy, 
  Calendar,
  MapPin,
  ExternalLink,
  Github,
  Twitter,
  MessageCircle,
  Send,
  Shield,
  Zap,
  Target,
  Award,
  Activity
} from 'lucide-react'

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState('overview')

  const communityStats = [
    {
      icon: Users,
      label: 'Total Members',
      value: '15,420',
      change: '+12%',
      color: 'text-cyber-primary'
    },
    {
      icon: Activity,
      label: 'Active Today',
      value: '1,247',
      change: '+8%',
      color: 'text-green-400'
    },
    {
      icon: MessageSquare,
      label: 'Total Posts',
      value: '8,934',
      change: '+15%',
      color: 'text-cyber-secondary'
    },
    {
      icon: Calendar,
      label: 'Events Hosted',
      value: '156',
      change: '+25%',
      color: 'text-cyber-accent'
    }
  ]

  const communityChannels = [
    {
      name: 'Discord Server',
      description: 'Real-time chat, voice channels, and community discussions',
      icon: MessageCircle,
      members: '8.2K',
      link: '#',
      color: 'bg-indigo-500'
    },
    {
      name: 'Telegram Group',
      description: 'Quick updates, news, and instant notifications',
      icon: Send,
      members: '5.1K',
      link: '#',
      color: 'bg-blue-500'
    },
    {
      name: 'GitHub Community',
      description: 'Open source contributions and code collaboration',
      icon: Github,
      members: '3.8K',
      link: '#',
      color: 'bg-gray-800'
    },
    {
      name: 'Twitter/X',
      description: 'Latest updates, tips, and cybersecurity news',
      icon: Twitter,
      members: '12.5K',
      link: '#',
      color: 'bg-black'
    }
  ]

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-cyber-glow">Join Our</span>{' '}
              <span className="text-cyber-pink">Cyber Community</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world
            </p>
            
            {/* Community Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              {communityStats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <div key={index} className="card-cyber text-center">
                    <Icon className={`h-8 w-8 ${stat.color} mx-auto mb-3 animate-cyber-pulse`} />
                    <div className="text-2xl font-bold text-white mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-400 mb-1">
                      {stat.label}
                    </div>
                    <div className="text-xs text-green-400">
                      {stat.change} this month
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex flex-wrap justify-center mb-12">
            {[
              { id: 'overview', label: 'Overview', icon: Users },
              { id: 'channels', label: 'Channels', icon: MessageSquare },
              { id: 'events', label: 'Events', icon: Calendar },
              { id: 'leaderboard', label: 'Top Contributors', icon: Trophy }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary' 
                      : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="mb-16">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="card-cyber">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    Welcome to KodeXGuard Community
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, 
                    bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help 
                    each other grow in the field of cybersecurity.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Shield className="h-5 w-5 text-cyber-primary" />
                      <span className="text-white">Share security research and findings</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Target className="h-5 w-5 text-cyber-secondary" />
                      <span className="text-white">Collaborate on bug bounty programs</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Zap className="h-5 w-5 text-cyber-accent" />
                      <span className="text-white">Learn from industry experts</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Award className="h-5 w-5 text-green-400" />
                      <span className="text-white">Participate in competitions and CTFs</span>
                    </div>
                  </div>
                </div>

                <div className="card-cyber">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    Community Guidelines
                  </h3>
                  <div className="space-y-4">
                    <div className="p-4 rounded-lg bg-cyber-primary/10">
                      <h4 className="font-semibold text-white mb-2">🤝 Be Respectful</h4>
                      <p className="text-sm text-gray-300">
                        Treat all community members with respect and professionalism
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-cyber-secondary/10">
                      <h4 className="font-semibold text-white mb-2">🔒 Ethical Practices</h4>
                      <p className="text-sm text-gray-300">
                        Only discuss ethical hacking and responsible disclosure
                      </p>
                    </div>
                    <div className="p-4 rounded-lg bg-cyber-accent/10">
                      <h4 className="font-semibold text-white mb-2">📚 Share Knowledge</h4>
                      <p className="text-sm text-gray-300">
                        Help others learn and grow in cybersecurity
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'channels' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {communityChannels.map((channel, index) => {
                  const Icon = channel.icon
                  return (
                    <div key={index} className="card-cyber hover:scale-105 transition-transform duration-300">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg ${channel.color}`}>
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-white mb-2">
                            {channel.name}
                          </h3>
                          <p className="text-gray-300 mb-4">
                            {channel.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-400">
                              {channel.members} members
                            </span>
                            <button className="btn-cyber-primary">
                              <span>Join</span>
                              <ExternalLink className="h-4 w-4 ml-2" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {activeTab === 'events' && (
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-cyber-primary mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Upcoming Events
                </h3>
                <p className="text-gray-300 mb-6">
                  Stay tuned for exciting cybersecurity events, workshops, and competitions!
                </p>
                <button className="btn-cyber-primary">
                  View Event Calendar
                </button>
              </div>
            )}

            {activeTab === 'leaderboard' && (
              <div className="text-center py-12">
                <Trophy className="h-16 w-16 text-cyber-accent mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Top Contributors
                </h3>
                <p className="text-gray-300 mb-6">
                  Recognize and celebrate our most active community members!
                </p>
                <button className="btn-cyber-secondary">
                  View Leaderboard
                </button>
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className="text-center card-cyber">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Join Our Community?
            </h3>
            <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
              Connect with like-minded cybersecurity professionals and take your skills to the next level
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-cyber-primary">
                Join Discord Server
              </button>
              <button className="border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black px-8 py-3 rounded-lg font-medium transition-colors duration-200">
                Follow on Twitter
              </button>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
