'use client'

import { useState, useEffect } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { useThemeClasses } from '@/contexts/ThemeContext'
import { 
  Users, 
  MessageSquare, 
  Trophy, 
  Star, 
  Calendar,
  MapPin,
  ExternalLink,
  Github,
  Twitter,
  Discord,
  Telegram,
  Shield,
  Zap,
  Target,
  Award,
  TrendingUp,
  Activity
} from 'lucide-react'

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState({
    totalMembers: 15420,
    activeToday: 1247,
    totalPosts: 8934,
    totalEvents: 156
  })
  const themeClasses = useThemeClasses()

  const communityStats = [
    {
      icon: Users,
      label: 'Total Members',
      value: stats.totalMembers.toLocaleString(),
      change: '+12%',
      color: 'text-cyber-primary'
    },
    {
      icon: Activity,
      label: 'Active Today',
      value: stats.activeToday.toLocaleString(),
      change: '+8%',
      color: 'text-green-400'
    },
    {
      icon: MessageSquare,
      label: 'Total Posts',
      value: stats.totalPosts.toLocaleString(),
      change: '+15%',
      color: 'text-cyber-secondary'
    },
    {
      icon: Calendar,
      label: 'Events Hosted',
      value: stats.totalEvents.toLocaleString(),
      change: '+25%',
      color: 'text-cyber-accent'
    }
  ]

  const communityChannels = [
    {
      name: 'Discord Server',
      description: 'Real-time chat, voice channels, and community discussions',
      icon: Discord,
      members: '8.2K',
      link: '#',
      color: 'bg-indigo-500'
    },
    {
      name: 'Telegram Group',
      description: 'Quick updates, news, and instant notifications',
      icon: Telegram,
      members: '5.1K',
      link: '#',
      color: 'bg-blue-500'
    },
    {
      name: 'GitHub Community',
      description: 'Open source contributions and code collaboration',
      icon: Github,
      members: '3.8K',
      link: '#',
      color: 'bg-gray-800'
    },
    {
      name: 'Twitter/X',
      description: 'Latest updates, tips, and cybersecurity news',
      icon: Twitter,
      members: '12.5K',
      link: '#',
      color: 'bg-black'
    }
  ]

  const upcomingEvents = [
    {
      title: 'Cybersecurity Workshop',
      date: '2024-01-15',
      time: '19:00 WIB',
      type: 'Workshop',
      participants: 156,
      description: 'Advanced penetration testing techniques'
    },
    {
      title: 'Bug Bounty Bootcamp',
      date: '2024-01-20',
      time: '14:00 WIB',
      type: 'Bootcamp',
      participants: 89,
      description: 'From beginner to professional bug hunter'
    },
    {
      title: 'CTF Competition',
      date: '2024-01-25',
      time: '10:00 WIB',
      type: 'Competition',
      participants: 234,
      description: 'Capture The Flag challenge for all levels'
    }
  ]

  const topContributors = [
    {
      name: 'CyberNinja',
      avatar: '/api/placeholder/40/40',
      points: 15420,
      badge: 'Elite Hunter',
      contributions: 89
    },
    {
      name: 'SecurityMaster',
      avatar: '/api/placeholder/40/40',
      points: 12350,
      badge: 'Bug Hunter',
      contributions: 67
    },
    {
      name: 'PentestPro',
      avatar: '/api/placeholder/40/40',
      points: 9870,
      badge: 'Security Expert',
      contributions: 54
    }
  ]

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className={themeClasses.isDark ? "text-cyber-glow" : "text-blue-600"}>Join Our</span>{' '}
              <span className={themeClasses.isDark ? "text-cyber-pink" : "text-pink-600"}>Cyber Community</span>
            </h1>
            <p className={`text-xl max-w-3xl mx-auto mb-8 ${themeClasses.textSecondary}`}>
              Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world
            </p>
            
            {/* Community Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              {communityStats.map((stat, index) => {
                const Icon = stat.icon
                return (
                  <div key={index} className={`${themeClasses.card} text-center`}>
                    <Icon className={`h-8 w-8 ${stat.color} mx-auto mb-3 ${themeClasses.isDark ? 'animate-cyber-pulse' : ''}`} />
                    <div className={`text-2xl font-bold ${themeClasses.textPrimary} mb-1`}>
                      {stat.value}
                    </div>
                    <div className={`text-sm ${themeClasses.textMuted} mb-1`}>
                      {stat.label}
                    </div>
                    <div className="text-xs text-green-400">
                      {stat.change} this month
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex flex-wrap justify-center mb-12">
            {[
              { id: 'overview', label: 'Overview', icon: Users },
              { id: 'channels', label: 'Channels', icon: MessageSquare },
              { id: 'events', label: 'Events', icon: Calendar },
              { id: 'leaderboard', label: 'Top Contributors', icon: Trophy }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? themeClasses.isDark 
                        ? 'bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary' 
                        : 'bg-blue-100 text-blue-600 border-2 border-blue-500'
                      : `${themeClasses.textSecondary} hover:${themeClasses.textPrimary} hover:${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-gray-100'}`
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </div>

          {/* Tab Content */}
          <div className="mb-16">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className={themeClasses.card}>
                  <h3 className={`text-2xl font-bold ${themeClasses.textPrimary} mb-4`}>
                    Welcome to KodeXGuard Community
                  </h3>
                  <p className={`${themeClasses.textSecondary} mb-6`}>
                    Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, 
                    bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help 
                    each other grow in the field of cybersecurity.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Shield className="h-5 w-5 text-cyber-primary" />
                      <span className={themeClasses.textPrimary}>Share security research and findings</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Target className="h-5 w-5 text-cyber-secondary" />
                      <span className={themeClasses.textPrimary}>Collaborate on bug bounty programs</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Zap className="h-5 w-5 text-cyber-accent" />
                      <span className={themeClasses.textPrimary}>Learn from industry experts</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Award className="h-5 w-5 text-green-400" />
                      <span className={themeClasses.textPrimary}>Participate in competitions and CTFs</span>
                    </div>
                  </div>
                </div>

                <div className={themeClasses.card}>
                  <h3 className={`text-2xl font-bold ${themeClasses.textPrimary} mb-4`}>
                    Community Guidelines
                  </h3>
                  <div className="space-y-4">
                    <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-primary/10' : 'bg-blue-50'}`}>
                      <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>🤝 Be Respectful</h4>
                      <p className={`text-sm ${themeClasses.textSecondary}`}>
                        Treat all community members with respect and professionalism
                      </p>
                    </div>
                    <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-secondary/10' : 'bg-pink-50'}`}>
                      <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>🔒 Ethical Practices</h4>
                      <p className={`text-sm ${themeClasses.textSecondary}`}>
                        Only discuss ethical hacking and responsible disclosure
                      </p>
                    </div>
                    <div className={`p-4 rounded-lg ${themeClasses.isDark ? 'bg-cyber-accent/10' : 'bg-yellow-50'}`}>
                      <h4 className={`font-semibold ${themeClasses.textPrimary} mb-2`}>📚 Share Knowledge</h4>
                      <p className={`text-sm ${themeClasses.textSecondary}`}>
                        Help others learn and grow in cybersecurity
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'channels' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {communityChannels.map((channel, index) => {
                  const Icon = channel.icon
                  return (
                    <div key={index} className={`${themeClasses.card} hover:scale-105 transition-transform duration-300`}>
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg ${channel.color}`}>
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className={`text-xl font-bold ${themeClasses.textPrimary} mb-2`}>
                            {channel.name}
                          </h3>
                          <p className={`${themeClasses.textSecondary} mb-4`}>
                            {channel.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              {channel.members} members
                            </span>
                            <button className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${themeClasses.isDark ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white'} transition-colors duration-200`}>
                              <span>Join</span>
                              <ExternalLink className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {activeTab === 'events' && (
              <div className="space-y-6">
                <h3 className={`text-2xl font-bold ${themeClasses.textPrimary} mb-6`}>
                  Upcoming Events
                </h3>
                {upcomingEvents.map((event, index) => (
                  <div key={index} className={`${themeClasses.card} hover:${themeClasses.isDark ? 'border-cyber-primary' : 'border-blue-500'} transition-colors duration-300`}>
                    <div className="flex flex-col md:flex-row md:items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            event.type === 'Workshop' ? 'bg-blue-100 text-blue-800' :
                            event.type === 'Bootcamp' ? 'bg-green-100 text-green-800' :
                            'bg-purple-100 text-purple-800'
                          }`}>
                            {event.type}
                          </span>
                          <span className={`text-sm ${themeClasses.textMuted}`}>
                            {event.participants} participants
                          </span>
                        </div>
                        <h4 className={`text-xl font-bold ${themeClasses.textPrimary} mb-2`}>
                          {event.title}
                        </h4>
                        <p className={`${themeClasses.textSecondary} mb-3`}>
                          {event.description}
                        </p>
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-cyber-primary" />
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              {event.date}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-cyber-secondary" />
                            <span className={`text-sm ${themeClasses.textMuted}`}>
                              {event.time}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 md:mt-0">
                        <button className={`${themeClasses.isDark ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200'}`}>
                          Register
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'leaderboard' && (
              <div className="space-y-6">
                <h3 className={`text-2xl font-bold ${themeClasses.textPrimary} mb-6`}>
                  Top Contributors This Month
                </h3>
                {topContributors.map((contributor, index) => (
                  <div key={index} className={`${themeClasses.card} hover:scale-105 transition-transform duration-300`}>
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${
                          index === 0 ? 'from-yellow-400 to-yellow-600' :
                          index === 1 ? 'from-gray-300 to-gray-500' :
                          'from-orange-400 to-orange-600'
                        } flex items-center justify-center`}>
                          <span className="text-white font-bold">#{index + 1}</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-lg font-bold ${themeClasses.textPrimary}`}>
                          {contributor.name}
                        </h4>
                        <p className={`text-sm ${themeClasses.textSecondary}`}>
                          {contributor.badge}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${themeClasses.textPrimary}`}>
                          {contributor.points.toLocaleString()} pts
                        </div>
                        <div className={`text-sm ${themeClasses.textMuted}`}>
                          {contributor.contributions} contributions
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className={`text-center ${themeClasses.card}`}>
            <h3 className={`text-3xl font-bold ${themeClasses.textPrimary} mb-4`}>
              Ready to Join Our Community?
            </h3>
            <p className={`text-lg ${themeClasses.textSecondary} mb-8 max-w-2xl mx-auto`}>
              Connect with like-minded cybersecurity professionals and take your skills to the next level
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className={themeClasses.isDark ? "btn-cyber-primary" : "bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200"}>
                Join Discord Server
              </button>
              <button className={`px-8 py-3 rounded-lg font-medium transition-colors duration-200 ${themeClasses.isDark ? 'border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black' : 'border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white'}`}>
                Follow on Twitter
              </button>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
