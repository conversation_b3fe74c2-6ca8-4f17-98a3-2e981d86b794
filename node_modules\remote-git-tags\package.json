{"name": "remote-git-tags", "version": "3.0.0", "description": "Get tags from a remote Git repo", "license": "MIT", "repository": "sindresorhus/remote-git-tags", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["git", "tags", "tag", "remote", "ls-remote", "ls", "repo", "repository", "commit", "sha", "url"], "devDependencies": {"ava": "^2.1.0", "xo": "^0.24.0"}}