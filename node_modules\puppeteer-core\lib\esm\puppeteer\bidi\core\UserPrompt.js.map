{"version": 3, "file": "UserPrompt.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/UserPrompt.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAE,eAAe,EAAC,MAAM,0BAA0B,CAAC;AAC1E,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAoBxE;;GAEG;IACU,UAAU;sBAAS,YAAY;;;;iBAA/B,UAAW,SAAQ,WAQ9B;;;YAsEA,wKAAQ,OAAO,6DAGd;YAMD,qKAAM,MAAM,6DAOX;;;QArFD,MAAM,CAAC,IAAI,CACT,eAAgC,EAChC,IAAqD;YAErD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YACzD,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,OAAO,GAlBI,mDAAU,CAkBJ;QACjB,OAAO,CAAoB;QAClB,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QACrC,eAAe,CAAkB;QACjC,IAAI,CAAkD;QAE/D,YACE,OAAwB,EACxB,IAAqD;YAErD,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;YAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,WAAW;YACT,MAAM,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CACjD,IAAI,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CACvC,CAAC;YACF,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBAChD,IAAI,CAAC,OAAO,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,cAAc,CAAC,EAAE,CAAC,kCAAkC,EAAE,UAAU,CAAC,EAAE;gBACjE,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC;oBACnD,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACD,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QACD,IAAI,OAAO;YACT,IACE,IAAI,CAAC,IAAI,CAAC,OAAO,6DAA8C;gBAC/D,IAAI,CAAC,IAAI,CAAC,OAAO,+DAA+C,EAChE,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAGO,OAAO,CAAC,MAAe;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,MAAM,CAAC,UAAyB,EAAE;YACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC,EAAE;gBAC3D,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;aAC3B,CAAC,CAAC;YACH,oEAAoE;YACpE,OAAO,IAAI,CAAC,OAAQ,CAAC;QACvB,CAAC;QAED,yBAnBC,eAAe,yBAMf,eAAe,CAAa,MAAM,CAAC,EAAE;gBACpC,wCAAwC;gBACxC,OAAO,MAAM,CAAC,OAAQ,CAAC;YACzB,CAAC,CAAC,GAUD,aAAa,EAAC;YACb,IAAI,CAAC,OAAO;gBACV,6FAA6F,CAAC;YAChG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE5C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SAvGU,UAAU"}