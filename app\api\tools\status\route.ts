import { NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET() {
  try {
    // Get tool usage statistics
    const scanStats = await db.query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM vulnerability_scans
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `)

    const osintStats = await db.query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM osint_queries
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `)

    const fileStats = await db.query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'analyzing' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN threat_detected = 1 THEN 1 ELSE 0 END) as threats
      FROM file_analyses
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `)

    const dorkingStats = await db.query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM dorking_queries
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `)

    // Get bot status
    const botStatus = await db.query(`
      SELECT name, type, status, tasks_completed, tasks_queued, cpu_usage, memory_usage
      FROM bot_instances
      ORDER BY name ASC
    `)

    // Calculate overall system health
    const totalTasks = (scanStats[0]?.total || 0) + (osintStats[0]?.total || 0) + 
                      (fileStats[0]?.total || 0) + (dorkingStats[0]?.total || 0)
    const completedTasks = (scanStats[0]?.completed || 0) + (osintStats[0]?.completed || 0) + 
                          (fileStats[0]?.completed || 0) + (dorkingStats[0]?.completed || 0)
    const failedTasks = (scanStats[0]?.failed || 0) + (osintStats[0]?.failed || 0) + 
                       (fileStats[0]?.failed || 0) + (dorkingStats[0]?.failed || 0)

    const successRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 100
    const errorRate = totalTasks > 0 ? Math.round((failedTasks / totalTasks) * 100) : 0

    return NextResponse.json({
      success: true,
      data: {
        tools: {
          vulnerabilityScanner: {
            name: 'Vulnerability Scanner',
            status: 'operational',
            uptime: 99.8,
            todayUsage: scanStats[0]?.total || 0,
            successRate: scanStats[0]?.total > 0 ? 
              Math.round(((scanStats[0]?.completed || 0) / scanStats[0]?.total) * 100) : 100,
            averageTime: '2.3 minutes',
            stats: scanStats[0]
          },
          osintLookup: {
            name: 'OSINT Lookup',
            status: 'operational',
            uptime: 99.9,
            todayUsage: osintStats[0]?.total || 0,
            successRate: osintStats[0]?.total > 0 ? 
              Math.round(((osintStats[0]?.completed || 0) / osintStats[0]?.total) * 100) : 100,
            averageTime: '45 seconds',
            stats: osintStats[0]
          },
          fileAnalyzer: {
            name: 'File Analyzer',
            status: 'operational',
            uptime: 99.7,
            todayUsage: fileStats[0]?.total || 0,
            successRate: fileStats[0]?.total > 0 ? 
              Math.round(((fileStats[0]?.completed || 0) / fileStats[0]?.total) * 100) : 100,
            averageTime: '1.8 minutes',
            threatsDetected: fileStats[0]?.threats || 0,
            stats: fileStats[0]
          },
          cveDatabase: {
            name: 'CVE Database',
            status: 'operational',
            uptime: 99.9,
            todayUsage: Math.floor(Math.random() * 100) + 50, // Mock data
            successRate: 99,
            averageTime: '0.5 seconds',
            totalEntries: 89340 // Mock data
          },
          googleDorking: {
            name: 'Google Dorking',
            status: 'operational',
            uptime: 99.6,
            todayUsage: dorkingStats[0]?.total || 0,
            successRate: dorkingStats[0]?.total > 0 ? 
              Math.round(((dorkingStats[0]?.completed || 0) / dorkingStats[0]?.total) * 100) : 100,
            averageTime: '30 seconds',
            stats: dorkingStats[0]
          }
        },
        bots: botStatus.map((bot: any) => ({
          name: bot.name,
          type: bot.type,
          status: bot.status,
          tasksCompleted: bot.tasks_completed,
          tasksQueued: bot.tasks_queued,
          cpuUsage: bot.cpu_usage,
          memoryUsage: bot.memory_usage,
          health: bot.status === 'running' ? 'healthy' : 
                  bot.status === 'error' ? 'unhealthy' : 'maintenance'
        })),
        system: {
          overallHealth: successRate >= 95 ? 'healthy' : 
                        successRate >= 85 ? 'degraded' : 'unhealthy',
          successRate,
          errorRate,
          totalTasks,
          completedTasks,
          failedTasks,
          activeConnections: Math.floor(Math.random() * 100) + 50,
          queueSize: (scanStats[0]?.running || 0) + (osintStats[0]?.running || 0) + 
                    (fileStats[0]?.running || 0) + (dorkingStats[0]?.running || 0),
          lastUpdated: new Date().toISOString()
        }
      }
    })

  } catch (error) {
    console.error('Tools status error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get tools status',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
