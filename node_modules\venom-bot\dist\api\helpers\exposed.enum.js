"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExposedFn = void 0;
var ExposedFn;
(function (ExposedFn) {
    ExposedFn["OnMessage"] = "onMessage";
    ExposedFn["OnMessageEdit"] = "onMessageEdit";
    ExposedFn["OnMessageDelete"] = "onMessageDelete";
    ExposedFn["OnMessageReaction"] = "onMessageReaction";
    ExposedFn["OnAnyMessage"] = "onAnyMessage";
    ExposedFn["onAck"] = "onAck";
    ExposedFn["onParticipantsChanged"] = "onParticipantsChanged";
    ExposedFn["onStateChange"] = "onStateChange";
    ExposedFn["onIncomingCall"] = "onIncomingCall";
    ExposedFn["onInterfaceChange"] = "onInterfaceChange";
    ExposedFn["onStreamChange"] = "onStreamChange";
    ExposedFn["onFilePicThumb"] = "onFilePicThumb";
    ExposedFn["onChatState"] = "onChatState";
    ExposedFn["onUnreadMessage"] = "onUnreadMessage";
    ExposedFn["onPoll"] = "onPoll";
})(ExposedFn || (exports.ExposedFn = ExposedFn = {}));
//# sourceMappingURL=exposed.enum.js.map