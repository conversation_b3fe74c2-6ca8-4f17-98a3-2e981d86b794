# KodeXGuard API Documentation

Comprehensive API documentation for KodeXGuard cybersecurity & bug hunting platform.

## Base URL

```
Production: https://api.kodexguard.com
Development: http://localhost:3000/api
```

## Authentication

KodeXGuard API supports two authentication methods:

### 1. JWT Bearer Token
```http
Authorization: Bearer <your_jwt_token>
```

### 2. API Key
```http
X-API-Key: <your_api_key>
```

## Rate Limiting

API requests are rate-limited based on your plan:

| Plan | Requests per minute | Daily limit |
|------|-------------------|-------------|
| Free | 10 | 100 |
| Student | 30 | 1000 |
| Hobby | 60 | 5000 |
| Bug Hunter | 120 | 15000 |
| Cybersecurity | 300 | 50000 |

Rate limit headers are included in responses:
```http
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Response Format

All API responses follow this format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

## Authentication Endpoints

### Register User
```http
POST /api/auth/register
```

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "fullName": "John Doe",
  "password": "SecurePass123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "username": "johndoe",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "role": "user",
      "plan": "free",
      "emailVerified": false,
      "createdAt": "2025-01-14T10:00:00Z"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token",
      "expiresIn": 604800
    }
  }
}
```

### Login User
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

### Refresh Token
```http
POST /api/auth/refresh
```

**Request Body:**
```json
{
  "refreshToken": "refresh_token"
}
```

### Logout
```http
POST /api/auth/logout
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

## User Management

### Get User Profile
```http
GET /api/user/profile
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

### Update User Profile
```http
PUT /api/user/profile
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "fullName": "John Smith",
  "bio": "Senior Security Researcher",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

### Change Password
```http
POST /api/user/change-password
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "currentPassword": "OldPass123",
  "newPassword": "NewSecurePass456"
}
```

## API Key Management

### Get API Keys
```http
GET /api/user/api-keys
```

### Create API Key
```http
POST /api/user/api-keys
```

**Request Body:**
```json
{
  "name": "Development API",
  "permissions": ["scan", "osint"]
}
```

### Delete API Key
```http
DELETE /api/user/api-keys/{keyId}
```

## Plans & Subscriptions

### Get Available Plans
```http
GET /api/plans
```

### Subscribe to Plan
```http
POST /api/plans/subscribe
```

**Request Body:**
```json
{
  "planId": "uuid",
  "paymentMethod": "manual_transfer"
}
```

## OSINT Endpoints

### Perform OSINT Lookup
```http
POST /api/osint/lookup
```

**Request Body:**
```json
{
  "type": "email",
  "target": "<EMAIL>",
  "sources": ["breaches", "social", "domains"]
}
```

### Get OSINT History
```http
GET /api/osint/history
```

## Vulnerability Scanner

### Start Vulnerability Scan
```http
POST /api/scanner/scan
```

**Request Body:**
```json
{
  "target": "https://example.com",
  "scanType": "full",
  "options": {
    "checkSQLi": true,
    "checkXSS": true,
    "checkLFI": true,
    "checkRCE": true
  }
}
```

### Get Scan Results
```http
GET /api/scanner/results/{scanId}
```

### Get Scan History
```http
GET /api/scanner/history
```

## File Analyzer

### Upload and Analyze File
```http
POST /api/file-analyzer/upload
```

**Headers:**
```http
Content-Type: multipart/form-data
```

**Request Body:**
```
file: <binary_file_data>
```

### Get Analysis Results
```http
GET /api/file-analyzer/results/{fileId}
```

## CVE Intelligence

### Search CVE Database
```http
GET /api/cve/search?q=apache&severity=critical&year=2024
```

### Get CVE Details
```http
GET /api/cve/{cveId}
```

### Get Daily CVE Updates
```http
GET /api/cve/daily
```

## Google Dorking

### Get Dork Presets
```http
GET /api/dorking/presets
```

### Execute Dork Query
```http
POST /api/dorking/execute
```

**Request Body:**
```json
{
  "query": "inurl:admin.php",
  "maxResults": 100
}
```

### Save Custom Dork
```http
POST /api/dorking/custom
```

**Request Body:**
```json
{
  "name": "Admin Panel Finder",
  "query": "inurl:admin.php",
  "description": "Find admin panels",
  "tags": ["admin", "panel"]
}
```

## Tools

### Hash Generator
```http
POST /api/tools/hash
```

**Request Body:**
```json
{
  "text": "password123",
  "algorithms": ["md5", "sha1", "sha256"]
}
```

### Encoder/Decoder
```http
POST /api/tools/encode
```

**Request Body:**
```json
{
  "text": "Hello World",
  "type": "base64",
  "operation": "encode"
}
```

### Payload Generator
```http
POST /api/tools/payload
```

**Request Body:**
```json
{
  "type": "xss",
  "target": "input",
  "options": {
    "bypass": ["filter", "waf"]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## SDKs and Libraries

### JavaScript/Node.js
```bash
npm install kodexguard-sdk
```

### Python
```bash
pip install kodexguard-python
```

### PHP
```bash
composer require kodexguard/php-sdk
```

## Support

- Documentation: https://docs.kodexguard.com
- Support Email: <EMAIL>
- Discord: https://discord.gg/kodexguard
- GitHub: https://github.com/kodexguard/api
