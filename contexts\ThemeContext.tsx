'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

type Theme = 'dark' | 'light'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  setTheme: (theme: Theme) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('dark')
  const [mounted, setMounted] = useState(false)

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    const initialTheme = savedTheme || systemTheme
    
    setThemeState(initialTheme)
    setMounted(true)
  }, [])

  // Apply theme to document
  useEffect(() => {
    if (!mounted) return

    const root = document.documentElement
    
    // Remove existing theme classes
    root.classList.remove('dark', 'light')
    
    // Add current theme class
    root.classList.add(theme)
    
    // Update CSS variables based on theme
    if (theme === 'light') {
      // Light theme variables
      root.style.setProperty('--cyber-bg-primary', '#ffffff')
      root.style.setProperty('--cyber-bg-secondary', '#f8fafc')
      root.style.setProperty('--cyber-bg-tertiary', '#f1f5f9')
      root.style.setProperty('--cyber-bg-card', '#ffffff')
      root.style.setProperty('--cyber-bg-hover', '#f1f5f9')
      
      root.style.setProperty('--cyber-text-primary', '#1e293b')
      root.style.setProperty('--cyber-text-secondary', '#475569')
      root.style.setProperty('--cyber-text-muted', '#64748b')
      
      root.style.setProperty('--cyber-border', '#e2e8f0')
      root.style.setProperty('--cyber-border-bright', '#0ea5e9')
      
      // Keep cyber colors but adjust opacity for light mode
      root.style.setProperty('--cyber-primary', '#0ea5e9')
      root.style.setProperty('--cyber-secondary', '#ec4899')
      root.style.setProperty('--cyber-accent', '#f59e0b')
    } else {
      // Dark theme variables (cyberpunk)
      root.style.setProperty('--cyber-bg-primary', '#0a0a0f')
      root.style.setProperty('--cyber-bg-secondary', '#1a1a2e')
      root.style.setProperty('--cyber-bg-tertiary', '#16213e')
      root.style.setProperty('--cyber-bg-card', '#0f0f23')
      root.style.setProperty('--cyber-bg-hover', '#1e1e3f')
      
      root.style.setProperty('--cyber-text-primary', '#ffffff')
      root.style.setProperty('--cyber-text-secondary', '#b0b0b0')
      root.style.setProperty('--cyber-text-muted', '#808080')
      
      root.style.setProperty('--cyber-border', '#333366')
      root.style.setProperty('--cyber-border-bright', '#00ffff')
      
      root.style.setProperty('--cyber-primary', '#00ffff')
      root.style.setProperty('--cyber-secondary', '#ff0080')
      root.style.setProperty('--cyber-accent', '#ffff00')
    }
    
    // Save to localStorage
    localStorage.setItem('theme', theme)
  }, [theme, mounted])

  const toggleTheme = () => {
    setThemeState(prev => prev === 'dark' ? 'light' : 'dark')
  }

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return <div className="min-h-screen bg-cyber-dark">{children}</div>
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Hook for theme-aware styling
export function useThemeClasses() {
  const { theme } = useTheme()
  
  return {
    // Background classes
    bgPrimary: theme === 'dark' ? 'bg-cyber-dark' : 'bg-white',
    bgSecondary: theme === 'dark' ? 'bg-cyber-secondary' : 'bg-gray-50',
    bgCard: theme === 'dark' ? 'bg-cyber-card' : 'bg-white',
    
    // Text classes
    textPrimary: theme === 'dark' ? 'text-white' : 'text-gray-900',
    textSecondary: theme === 'dark' ? 'text-gray-300' : 'text-gray-600',
    textMuted: theme === 'dark' ? 'text-gray-400' : 'text-gray-500',
    
    // Border classes
    border: theme === 'dark' ? 'border-gray-700' : 'border-gray-200',
    borderBright: theme === 'dark' ? 'border-cyber-primary' : 'border-blue-500',
    
    // Button classes
    btnPrimary: theme === 'dark' ? 'btn-cyber-primary' : 'bg-blue-600 hover:bg-blue-700 text-white',
    btnSecondary: theme === 'dark' ? 'btn-cyber-secondary' : 'bg-gray-200 hover:bg-gray-300 text-gray-900',
    
    // Input classes
    input: theme === 'dark' ? 'input-cyber' : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500',
    
    // Card classes
    card: theme === 'dark' ? 'card-cyber' : 'bg-white border border-gray-200 rounded-lg p-6 shadow-sm',
    
    // Effects
    glow: theme === 'dark' ? 'animate-cyber-glow' : '',
    pulse: theme === 'dark' ? 'animate-cyber-pulse' : '',
    
    // Theme identifier
    isDark: theme === 'dark',
    isLight: theme === 'light'
  }
}
