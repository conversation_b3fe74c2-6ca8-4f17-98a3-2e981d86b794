import { EventEmitter } from 'node:events';
import { ElasticsearchClientError } from './errors';
import { ConnectionRequestOptions } from './connection';
import { ResurrectEvent } from './pool';
import { DiagnosticResult, DiagnosticResultResponse } from './types';
export type DiagnosticListener = (err: ElasticsearchClientError | null, meta: any | null) => void;
export type DiagnosticListenerFull = (err: ElasticsearchClientError | null, meta: DiagnosticResult | null) => void;
export type DiagnosticListenerFullResponse = (err: ElasticsearchClientError | null, meta: DiagnosticResultResponse | null) => void;
export type DiagnosticListenerLight = (err: ElasticsearchClientError | null, meta: ConnectionRequestOptions | null) => void;
export type DiagnosticListenerResurrect = (err: ElasticsearchClientError | null, meta: ResurrectEvent | null) => void;
export declare enum events {
    RESPONSE = "response",
    REQUEST = "request",
    SNIFF = "sniff",
    RESURRECT = "resurrect",
    SERIALIZATION = "serialization",
    DESERIALIZATION = "deserialization"
}
export default class Diagnostic extends EventEmitter {
    on(event: 'request', listener: DiagnosticListenerFull): this;
    on(event: 'response', listener: DiagnosticListenerFullResponse): this;
    on(event: 'serialization', listener: DiagnosticListenerFull): this;
    on(event: 'sniff', listener: DiagnosticListenerFull): this;
    on(event: 'deserialization', listener: DiagnosticListenerLight): this;
    on(event: 'resurrect', listener: DiagnosticListenerResurrect): this;
    once(event: 'request', listener: DiagnosticListenerFull): this;
    once(event: 'response', listener: DiagnosticListenerFullResponse): this;
    once(event: 'serialization', listener: DiagnosticListenerFull): this;
    once(event: 'sniff', listener: DiagnosticListenerFull): this;
    once(event: 'deserialization', listener: DiagnosticListenerLight): this;
    once(event: 'resurrect', listener: DiagnosticListenerResurrect): this;
    off(event: string, listener: DiagnosticListener): this;
}
