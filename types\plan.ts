export interface Plan {
  id: string
  name: string
  type: PlanType
  price: number
  currency: string
  duration: PlanDuration
  features: PlanFeature[]
  limits: PlanLimits
  isActive: boolean
  isPopular: boolean
  description: string
  createdAt: Date
  updatedAt: Date
}

export enum PlanType {
  FREE = 'free',
  STUDENT = 'student',
  HOBBY = 'hobby',
  BUGHUNTER = 'bughunter',
  CYBERSECURITY = 'cybersecurity'
}

export enum PlanDuration {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export interface PlanFeature {
  id: string
  name: string
  description: string
  isIncluded: boolean
}

export interface PlanLimits {
  dailyScans: number
  monthlyScans: number
  fileUploadSize: number // in MB
  apiCallsPerDay: number
  apiCallsPerMonth: number
  osintQueries: number
  concurrentScans: number
  botAccess: boolean
  prioritySupport: boolean
  customIntegrations: boolean
  advancedFeatures: boolean
}

export interface Subscription {
  id: string
  userId: string
  planId: string
  plan: Plan
  status: SubscriptionStatus
  startDate: Date
  endDate: Date
  autoRenew: boolean
  paymentMethod: PaymentMethod
  amount: number
  currency: string
  createdAt: Date
  updatedAt: Date
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
  SUSPENDED = 'suspended'
}

export interface PaymentMethod {
  type: PaymentType
  provider: string
  details: Record<string, any>
}

export enum PaymentType {
  MANUAL_TRANSFER = 'manual_transfer',
  TRIPAY = 'tripay',
  MIDTRANS = 'midtrans',
  XENDIT = 'xendit'
}

export interface PaymentRequest {
  planId: string
  duration: PlanDuration
  paymentMethod: PaymentType
  returnUrl?: string
}

export interface PaymentResponse {
  paymentId: string
  paymentUrl?: string
  qrCode?: string
  virtualAccount?: string
  instructions: string
  amount: number
  expiresAt: Date
}
