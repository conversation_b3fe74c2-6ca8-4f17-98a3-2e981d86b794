{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,kEAAkE;AAClE,mEAAmE;AACnE,kEAAkE;AAClE,kEAAkE;AAClE,0BAA0B;AA+H1B,MAAM,OAAO,YAAa,SAAQ,KAAK;IACtC,YAAY,KAAY;QACvB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;CACD;AACD,MAAM,OAAO,UAAW,SAAQ,KAAK;IACpC,YAAY,KAAY;QACvB,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;CACD", "sourcesContent": ["// Type definitions for cacheable-request 6.0\n// Project: https://github.com/lukechilds/cacheable-request#readme\n// Definitions by: BendingBender <https://github.com/BendingBender>\n//                 <PERSON> <https://github.com/paul<PERSON>>\n// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped\n// TypeScript Version: 2.3\n\n/// <reference types=\"node\" />\n\nimport {request, RequestOptions, ClientRequest, ServerResponse} from 'node:http';\nimport {URL} from 'node:url';\nimport {EventEmitter} from 'node:events';\nimport {Buffer} from 'node:buffer';\nimport {Store} from 'keyv';\nimport ResponseLike from 'responselike';\nimport {CachePolicyObject} from 'http-cache-semantics';\n\nexport type RequestFn = typeof request;\nexport type RequestFunction = typeof request;\nexport type CacheResponse = ServerResponse | typeof ResponseLike;\n\nexport type CacheableRequestFunction = (\n\toptions: CacheableOptions,\n\tcb?: (response: CacheResponse) => void\n) => Emitter;\n\nexport type CacheableOptions = Options & RequestOptions | string | URL;\n\nexport type StorageAdapter = Store<any>;\n\nexport interface Options {\n\t/**\n\t\t\t * If the cache should be used. Setting this to `false` will completely bypass the cache for the current request.\n\t\t\t * @default true\n\t\t\t */\n\tcache?: boolean | undefined;\n\n\t/**\n\t\t\t * If set to `true` once a cached resource has expired it is deleted and will have to be re-requested.\n\t\t\t *\n\t\t\t * If set to `false`, after a cached resource's TTL expires it is kept in the cache and will be revalidated\n\t\t\t * on the next request with `If-None-Match`/`If-Modified-Since` headers.\n\t\t\t * @default false\n\t\t\t */\n\tstrictTtl?: boolean | undefined;\n\n\t/**\n\t\t\t * Limits TTL. The `number` represents milliseconds.\n\t\t\t * @default undefined\n\t\t\t */\n\tmaxTtl?: number | undefined;\n\n\t/**\n\t\t\t * When set to `true`, if the DB connection fails we will automatically fallback to a network request.\n\t\t\t * DB errors will still be emitted to notify you of the problem even though the request callback may succeed.\n\t\t\t * @default false\n\t\t\t */\n\tautomaticFailover?: boolean | undefined;\n\n\t/**\n * Forces refreshing the cache. If the response could be retrieved from the cache, it will perform a\n * new request and override the cache instead.\n * @default false\n */\n\tforceRefresh?: boolean | undefined;\n\tremoteAddress?: boolean | undefined;\n\n\turl?: string | undefined;\n\n\theaders?: Record<string, string | string[] | undefined>;\n\n\tbody?: Buffer;\n}\n\nexport interface CacheValue extends Record<string, any> {\n\turl: string;\n\tstatusCode: number;\n\tbody: Buffer | string;\n\tcachePolicy: CachePolicyObject;\n}\n\nexport interface Emitter extends EventEmitter {\n\taddListener(event: 'request', listener: (request: ClientRequest) => void): this;\n\taddListener(\n\t\tevent: 'response',\n\t\tlistener: (response: CacheResponse) => void\n\t): this;\n\taddListener(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\ton(event: 'request', listener: (request: ClientRequest) => void): this;\n\ton(event: 'response', listener: (response: CacheResponse) => void): this;\n\ton(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\tonce(event: 'request', listener: (request: ClientRequest) => void): this;\n\tonce(event: 'response', listener: (response: CacheResponse) => void): this;\n\tonce(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\tprependListener(event: 'request', listener: (request: ClientRequest) => void): this;\n\tprependListener(\n\t\tevent: 'response',\n\t\tlistener: (response: CacheResponse) => void\n\t): this;\n\tprependListener(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\tprependOnceListener(event: 'request', listener: (request: ClientRequest) => void): this;\n\tprependOnceListener(\n\t\tevent: 'response',\n\t\tlistener: (response: CacheResponse) => void\n\t): this;\n\tprependOnceListener(\n\t\tevent: 'error',\n\t\tlistener: (error: RequestError | CacheError) => void\n\t): this;\n\tremoveListener(event: 'request', listener: (request: ClientRequest) => void): this;\n\tremoveListener(\n\t\tevent: 'response',\n\t\tlistener: (response: CacheResponse) => void\n\t): this;\n\tremoveListener(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\toff(event: 'request', listener: (request: ClientRequest) => void): this;\n\toff(event: 'response', listener: (response: CacheResponse) => void): this;\n\toff(event: 'error', listener: (error: RequestError | CacheError) => void): this;\n\tremoveAllListeners(event?: 'request' | 'response' | 'error'): this;\n\tlisteners(event: 'request'): Array<(request: ClientRequest) => void>;\n\tlisteners(event: 'response'): Array<(response: CacheResponse) => void>;\n\tlisteners(event: 'error'): Array<(error: RequestError | CacheError) => void>;\n\trawListeners(event: 'request'): Array<(request: ClientRequest) => void>;\n\trawListeners(event: 'response'): Array<(response: CacheResponse) => void>;\n\trawListeners(event: 'error'): Array<(error: RequestError | CacheError) => void>;\n\temit(event: 'request', request: ClientRequest): boolean;\n\temit(event: 'response', response: CacheResponse): boolean;\n\temit(event: 'error', error: RequestError | CacheError): boolean;\n\teventNames(): Array<'request' | 'response' | 'error'>;\n\tlistenerCount(type: 'request' | 'response' | 'error'): number;\n}\n\nexport class RequestError extends Error {\n\tconstructor(error: Error) {\n\t\tsuper(error.message);\n\t\tObject.assign(this, error);\n\t}\n}\nexport class CacheError extends Error {\n\tconstructor(error: Error) {\n\t\tsuper(error.message);\n\t\tObject.assign(this, error);\n\t}\n}\n\nexport interface UrlOption {\n\tpath: string;\n\tpathname?: string;\n\tsearch?: string;\n}\n"]}