{"version": 3, "file": "BaseConnection.js", "sourceRoot": "", "sources": ["../../src/connection/BaseConnection.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AA6LH,wCAgBC;AAUD,oDAkBC;AAED,oDAOC;AAED,4BAcC;;AAhQD,yCAAmC;AAKnC,uEAAsC;AAStC,sCAA8C;AAC9C,wCAAiE;AA2DjE;;GAEG;AACH,MAAqB,cAAc;IAqBjC,YAAa,IAAuB;;QApBpC;;;;;WAAQ;QACR;;;;;WAAgC;QAChC;;;;;WAAU;QACV;;;;;WAAe;QACf;;;;;WAAiC;QACjC;;;;;WAAiB;QACjB;;;;;WAAwB;QACxB;;;;;WAAqB;QACrB;;;;;WAAc;QACd;;;;;WAAyB;QACzB;;;;;WAAuB;QACvB;;;;;WAAiB;QACjB;;;;;WAA+B;QAC/B;;;;;WAAyB;QAQvB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,GAAG,GAAG,MAAA,IAAI,CAAC,GAAG,mCAAI,IAAI,CAAA;QAC3B,IAAI,CAAC,EAAE,GAAG,MAAA,IAAI,CAAC,EAAE,mCAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAC7C,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,OAAO,GAAG,MAAA,IAAI,CAAC,OAAO,mCAAI,KAAK,CAAA;QACpC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAA;QACzB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;QACtB,IAAI,CAAC,iBAAiB,GAAG,MAAA,IAAI,CAAC,iBAAiB,mCAAI,GAAG,CAAA;QACtD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QAC/C,IAAI,CAAC,iBAAO,CAAC,GAAG,MAAA,IAAI,CAAC,MAAM,mCAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAA;QAC5D,IAAI,CAAC,qBAAW,CAAC,GAAG,MAAA,IAAI,CAAC,UAAU,mCAAI,IAAI,oBAAU,EAAE,CAAA;QACvD,IAAI,CAAC,wBAAc,CAAC,GAAG,MAAA,IAAI,CAAC,aAAa,mCAAI,IAAI,CAAA;QACjD,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,2BAAkB,CAAC,sBAAsB,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAA;QAC1E,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,iBAAO,CAAC,CAAA;IACtB,CAAC;IAED,IAAI,MAAM,CAAE,MAAc;QACxB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,2BAAkB,CAAC,wBAAwB,MAAM,GAAG,CAAC,CAAA;QACjE,CAAC;QACD,IAAI,CAAC,iBAAO,CAAC,GAAG,MAAM,CAAA;IACxB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,qBAAW,CAAC,CAAA;IAC1B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAE,MAA+B,EAAE,OAAY;QAC1D,MAAM,IAAI,2BAAkB,CAAC,8DAA8D,CAAC,CAAA;IAC9F,CAAC;IAED,0BAA0B;IAC1B,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,2BAAkB,CAAC,4DAA4D,CAAC,CAAA;IAC5F,CAAC;IAED,qDAAqD;IACrD,4DAA4D;IAC5D,iDAAiD;IACjD,wDAAwD;IACxD,OA5DC,iBAAO,OACP,wBAAc,OACd,qBAAW,EA0DX,mBAAO,CAAC,MAAM,EAAC,CAAE,KAAa,EAAE,OAA4B;QAC3D,MAAM,EACJ,aAAa,EACb,GAAG,OAAO,EACX,GAAG,IAAI,CAAC,OAAO,CAAA;QAEhB,OAAO;YACL,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACnC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAA;IACH,CAAC;IAED,MAAM;QACJ,MAAM,EACJ,aAAa,EACb,GAAG,OAAO,EACX,GAAG,IAAI,CAAC,OAAO,CAAA;QAEhB,OAAO;YACL,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACnC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAA;IACH,CAAC;;AAlFM;;;;WAAW;QAChB,KAAK,EAAE,OAAO;QACd,IAAI,EAAE,MAAM;KACb;EAHc,CAGd;kBAnBkB,cAAc;AAqGnC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;IACxD,mBAAmB;KAClB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;AAEvC,SAAS,SAAS,CAAE,GAAW;IAC7B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,OAAO,GAAG,CAAA;IAClC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AAC9E,CAAC;AAED,SAAgB,cAAc,CAAE,UAAoC,EAAE,EAAE,IAA0C;IAChH,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;QAClD,0BAA0B;QAC1B,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACpC,OAAO,CAAC,aAAa,GAAG,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YAChH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,MAAM,EAAE,CAAA;YACjD,CAAC;QACH,CAAC;aAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,MAAM,EAAE,CAAA;QACjD,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC1D,OAAO,CAAC,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QACxG,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,SAAS,YAAY,CAAE,IAAyB;IAC9C,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAA;AAC5B,CAAC;AAED,SAAS,YAAY,CAAE,IAAyB;IAC9C,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAA;AAC5B,CAAC;AAED,SAAgB,oBAAoB,CAAE,MAAiB;IACrD,IAAI,WAAW,GAAG,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACjD,OAAO,WAAW,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnE,sBAAsB;QACtB,IAAI,WAAW,CAAC,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,wCAAwC;QACxC,wFAAwF;QACxF,IAAI,WAAW,CAAC,cAAc,KAAK,WAAW,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;YAChF,MAAK;QACP,CAAC;QAED,oBAAoB;QACpB,WAAW,GAAG,WAAW,CAAC,iBAAiB,CAAA;IAC7C,CAAC;IACD,OAAO,WAAW,CAAA;AACpB,CAAC;AAED,SAAgB,oBAAoB,CAAE,KAAoB,EAAE,KAAoB;IAC9E,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC3D,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAChD,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAChD,OAAO,EAAE,KAAK,EAAE,CAAA;IAClB,CAAC;IACD,OAAO,KAAK,KAAK,KAAK,CAAA;AACxB,CAAC;AAED,SAAgB,QAAQ,CAAE,WAA8B;IACtD,MAAM,WAAW,GAAG;QAClB,oCAAoC;QACpC,qCAAqC;QACrC,4CAA4C;QAC5C,mBAAmB;QACnB,qCAAqC;QACrC,kBAAkB;QAClB,oCAAoC;KACrC,CAAA;IAED,OAAO,WAAW;SACf,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACvC,QAAQ,CAAC,IAAI,CAAC,CAAA;AACnB,CAAC"}