'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { 
  Settings, 
  Shield, 
  Database,
  Mail,
  Bell,
  Globe,
  Lock,
  Key,
  Server,
  Zap,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  BarChart3
} from 'lucide-react'

export default function AdminSettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [settings, setSettings] = useState({
    general: {
      siteName: 'KodeXGuard',
      siteDescription: 'Advanced Cybersecurity Platform',
      maintenanceMode: false,
      debugMode: true,
      allowRegistration: true,
      requireEmailVerification: true
    },
    security: {
      twoFactorAuth: true,
      passwordMinLength: 8,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      ipWhitelist: true,
      rateLimiting: true
    },
    email: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUsername: '<EMAIL>',
      smtpPassword: '••••••••',
      fromEmail: '<EMAIL>',
      fromName: 'KodeXGuard'
    },
    api: {
      rateLimit: 1000,
      maxRequestSize: 10,
      enableCors: true,
      apiVersion: 'v1',
      enableLogging: true,
      cacheTimeout: 300
    }
  })

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }))
  }

  const handleSaveSettings = () => {
    // Save settings logic here
    console.log('Saving settings:', settings)
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">System</span>{' '}
              <span className="text-cyber-pink">Settings</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Configure and manage system settings
            </p>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <button className="btn-cyber-secondary">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset to Default
            </button>
            <button 
              onClick={handleSaveSettings}
              className="btn-cyber-primary"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg">
          {[
            { id: 'general', label: 'General', icon: Settings },
            { id: 'security', label: 'Security', icon: Shield },
            { id: 'email', label: 'Email', icon: Mail },
            { id: 'api', label: 'API', icon: Zap }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                    : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* General Settings */}
        {activeTab === 'general' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">General</span>{' '}
                <span className="text-cyber-pink">Configuration</span>
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Site Name
                    </label>
                    <input
                      type="text"
                      value={settings.general.siteName}
                      onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Site Description
                    </label>
                    <input
                      type="text"
                      value={settings.general.siteDescription}
                      onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Maintenance Mode</h3>
                      <p className="text-gray-400 text-sm">Enable site-wide maintenance mode</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('general', 'maintenanceMode', !settings.general.maintenanceMode)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.general.maintenanceMode ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.general.maintenanceMode ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Debug Mode</h3>
                      <p className="text-gray-400 text-sm">Enable detailed error logging</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('general', 'debugMode', !settings.general.debugMode)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.general.debugMode ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.general.debugMode ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Allow Registration</h3>
                      <p className="text-gray-400 text-sm">Allow new users to register</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('general', 'allowRegistration', !settings.general.allowRegistration)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.general.allowRegistration ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.general.allowRegistration ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Email Verification</h3>
                      <p className="text-gray-400 text-sm">Require email verification for new accounts</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('general', 'requireEmailVerification', !settings.general.requireEmailVerification)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.general.requireEmailVerification ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.general.requireEmailVerification ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Settings */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">Security</span>{' '}
                <span className="text-cyber-pink">Configuration</span>
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Password Min Length
                    </label>
                    <input
                      type="number"
                      value={settings.security.passwordMinLength}
                      onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Session Timeout (minutes)
                    </label>
                    <input
                      type="number"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                      <p className="text-gray-400 text-sm">Require 2FA for admin accounts</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Enabled</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">IP Whitelist</h3>
                      <p className="text-gray-400 text-sm">Restrict admin access to specific IPs</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Active</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Rate Limiting</h3>
                      <p className="text-gray-400 text-sm">Limit API requests per user</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Configured</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Email Settings */}
        {activeTab === 'email' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">Email</span>{' '}
                <span className="text-cyber-pink">Configuration</span>
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      SMTP Host
                    </label>
                    <input
                      type="text"
                      value={settings.email.smtpHost}
                      onChange={(e) => handleSettingChange('email', 'smtpHost', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      SMTP Port
                    </label>
                    <input
                      type="number"
                      value={settings.email.smtpPort}
                      onChange={(e) => handleSettingChange('email', 'smtpPort', parseInt(e.target.value))}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      From Email
                    </label>
                    <input
                      type="email"
                      value={settings.email.fromEmail}
                      onChange={(e) => handleSettingChange('email', 'fromEmail', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      From Name
                    </label>
                    <input
                      type="text"
                      value={settings.email.fromName}
                      onChange={(e) => handleSettingChange('email', 'fromName', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <button className="btn-cyber-primary">
                    <Mail className="h-4 w-4 mr-2" />
                    Test Email Configuration
                  </button>
                  <div className="flex items-center space-x-2 text-green-400">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Email service is working</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* API Settings */}
        {activeTab === 'api' && (
          <div className="space-y-6">
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">API</span>{' '}
                <span className="text-cyber-pink">Configuration</span>
              </h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Rate Limit (requests/hour)
                    </label>
                    <input
                      type="number"
                      value={settings.api.rateLimit}
                      onChange={(e) => handleSettingChange('api', 'rateLimit', parseInt(e.target.value))}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Max Request Size (MB)
                    </label>
                    <input
                      type="number"
                      value={settings.api.maxRequestSize}
                      onChange={(e) => handleSettingChange('api', 'maxRequestSize', parseInt(e.target.value))}
                      className="w-full px-4 py-3 rounded-lg input-cyber"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">Enable CORS</h3>
                      <p className="text-gray-400 text-sm">Allow cross-origin requests</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('api', 'enableCors', !settings.api.enableCors)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.api.enableCors ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.api.enableCors ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5">
                    <div>
                      <h3 className="text-white font-medium">API Logging</h3>
                      <p className="text-gray-400 text-sm">Log all API requests and responses</p>
                    </div>
                    <button
                      onClick={() => handleSettingChange('api', 'enableLogging', !settings.api.enableLogging)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        settings.api.enableLogging ? 'bg-cyber-primary' : 'bg-gray-600'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.api.enableLogging ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
