'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard,
  Search,
  Shield,
  Bomb,
  FileText,
  Globe,
  Code,
  Wrench,
  Bot,
  Trophy,
  User,
  CreditCard,
  Settings,
  ChevronDown,
  ChevronRight,
  Database,
  Zap,
  Lock,
  Activity
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onClose?: () => void
  user?: {
    role: string
    plan: string
  }
}

interface MenuItem {
  name: string
  href: string
  icon: any
  badge?: string
  children?: MenuItem[]
  adminOnly?: boolean
  premium?: boolean
}

export default function Sidebar({ isOpen, onClose, user }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>(['tools'])

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev => 
      prev.includes(name) 
        ? prev.filter(item => item !== name)
        : [...prev, name]
    )
  }

  const menuItems: MenuItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard
    },
    {
      name: 'OSINT Investigator',
      href: '/osint',
      icon: Search,
      badge: 'Hot'
    },
    {
      name: 'Vulnerability Scanner',
      href: '/scanner',
      icon: Shield,
      premium: true
    },
    {
      name: 'CVE Intelligence',
      href: '/cve',
      icon: Database,
      children: [
        { name: 'CVE Search', href: '/cve', icon: Search },
        { name: 'Daily Updates', href: '/cve?tab=daily', icon: Activity },
        { name: 'Trending CVEs', href: '/cve?tab=trending', icon: Zap }
      ]
    },
    {
      name: 'File Analyzer',
      href: '/file-analyzer',
      icon: FileText,
      premium: true
    },
    {
      name: 'Google Dorking',
      href: '/dorking',
      icon: Globe,
      children: [
        { name: 'Dork Presets', href: '/dorking', icon: Zap },
        { name: 'Custom Dorks', href: '/dorking?tab=custom', icon: Code },
        { name: 'Execution History', href: '/dorking?tab=history', icon: Activity }
      ]
    },
    {
      name: 'Developer Tools',
      href: '/tools',
      icon: Wrench,
      children: [
        { name: 'Hash Generator', href: '/tools', icon: Lock },
        { name: 'Encoder/Decoder', href: '/tools?tab=encode', icon: Code },
        { name: 'Payload Generator', href: '/tools?tab=payload', icon: Bomb },
        { name: 'API Playground', href: '/playground', icon: Wrench }
      ]
    },
    {
      name: 'Bot Center',
      href: '/bot',
      icon: Bot,
      premium: true,
      adminOnly: true
    },
    {
      name: 'Leaderboard',
      href: '/leaderboard',
      icon: Trophy
    },
    {
      name: 'Profile & API Keys',
      href: '/profile',
      icon: User
    },
    {
      name: 'Plans & Billing',
      href: '/plan',
      icon: CreditCard
    }
  ]

  // Add admin menu items
  if (user?.role === 'admin' || user?.role === 'super_admin') {
    menuItems.push({
      name: 'Admin Panel',
      href: '/admin',
      icon: Settings,
      adminOnly: true,
      children: [
        { name: 'Dashboard', href: '/admin/dashboard', icon: LayoutDashboard },
        { name: 'User Management', href: '/admin/users', icon: User },
        { name: 'Plan Management', href: '/admin/plans', icon: CreditCard },
        { name: 'Bot Management', href: '/admin/bots', icon: Bot },
        { name: 'System Settings', href: '/admin/settings', icon: Settings }
      ]
    })
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  const canAccess = (item: MenuItem) => {
    // Check admin access
    if (item.adminOnly && user?.role !== 'admin' && user?.role !== 'super_admin') {
      return false
    }

    // Check premium access
    if (item.premium && user?.plan === 'free') {
      return false
    }

    return true
  }

  const renderMenuItem = (item: MenuItem, level = 0) => {
    if (!canAccess(item)) {
      return null
    }

    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.name)
    const itemIsActive = isActive(item.href)

    return (
      <div key={item.name}>
        <div className={`flex items-center ${level > 0 ? 'pl-4' : ''}`}>
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.name)}
              className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                itemIsActive
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <item.icon className="h-5 w-5 mr-3" />
              <span className="flex-1 text-left">{item.name}</span>
              {item.badge && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2">
                  {item.badge}
                </span>
              )}
              {item.premium && user?.plan === 'free' && (
                <Lock className="h-4 w-4 text-orange-500 mr-2" />
              )}
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              onClick={onClose}
              className={`flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                itemIsActive
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <item.icon className="h-5 w-5 mr-3" />
              <span className="flex-1">{item.name}</span>
              {item.badge && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {item.badge}
                </span>
              )}
              {item.premium && user?.plan === 'free' && (
                <Lock className="h-4 w-4 text-orange-500" />
              )}
            </Link>
          )}
        </div>

        {/* Render children */}
        {hasChildren && isExpanded && (
          <div className="ml-4 mt-1 space-y-1">
            {item.children?.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-primary-600" />
              <span className="text-xl font-bold text-gray-900">KodeXGuard</span>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              ×
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {menuItems.map(item => renderMenuItem(item))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>KodeXGuard v1.0.0</p>
              <p className="mt-1">© 2025 All rights reserved</p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
