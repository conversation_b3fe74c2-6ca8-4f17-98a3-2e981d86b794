# Installation
> `npm install --save @types/semver-utils`

# Summary
This package contains type definitions for semver-utils (https://git.coolaj86.com/coolaj86/semver-utils.js).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/semver-utils.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/semver-utils/index.d.ts)
````ts
export interface SemVer {
    semver?: string | undefined;
    version?: string | undefined;
    major?: string | undefined;
    minor?: string | undefined;
    patch?: string | undefined;
    release?: string | undefined;
    build?: string | undefined;
    operator?: string | undefined;
}

export function parse(version: string): SemVer;
export function stringify(version: SemVer): string;
export function parseRange(range: string): SemVer[];
export function stringifyRange(version: SemVer[]): string;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/JamieMagee).
