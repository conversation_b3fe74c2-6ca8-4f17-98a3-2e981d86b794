import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { z } from 'zod'

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  fullName: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = registerSchema.parse(body)
    
    // Get client info
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    request.ip || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    
    // Attempt registration
    const result = await RealAuthService.register({
      username: validatedData.username,
      email: validatedData.email,
      password: validatedData.password,
      fullName: validatedData.fullName
    })
    
    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: result.message || 'Registration failed' 
        },
        { status: 400 }
      )
    }
    
    // Create response with user data
    const response = NextResponse.json({
      success: true,
      message: 'Registration successful',
      user: {
        id: result.user!.id,
        username: result.user!.username,
        email: result.user!.email,
        fullName: result.user!.full_name,
        role: result.user!.role,
        plan: result.user!.plan,
        level: result.user!.level,
        score: result.user!.score,
        streak: result.user!.streak_days
      }
    })
    
    // Set HTTP-only cookies for tokens
    response.cookies.set('accessToken', result.tokens!.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    })
    
    response.cookies.set('refreshToken', result.tokens!.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/'
    })
    
    return response
    
  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid input data',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
