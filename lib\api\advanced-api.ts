import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '../database'
import { AuthService } from '../auth'
import { crawlingEngine } from '../engines/crawling-engine'
import { scannerEngine } from '../engines/scanner-engine'
import { malwareAnalyzer } from '../engines/malware-analyzer'
import { breachParser } from '../engines/breach-parser'
import { searchEngine } from '../engines/search-engine'
import { queueSystem } from '../engines/queue-system'

export interface APIKey {
  id: string
  userId: number
  name: string
  key: string
  keyHash: string
  permissions: string[]
  rateLimit: {
    requests: number
    window: number // seconds
  }
  isActive: boolean
  lastUsed?: Date
  expiresAt?: Date
  createdAt: Date
}

export interface APIUsage {
  id: string
  apiKeyId: string
  endpoint: string
  method: string
  statusCode: number
  responseTime: number
  requestSize: number
  responseSize: number
  ipAddress: string
  userAgent: string
  timestamp: Date
}

export interface RateLimit {
  keyId: string
  requests: number
  windowStart: Date
  windowEnd: Date
}

export class AdvancedAPISystem {
  private rateLimits: Map<string, RateLimit> = new Map()
  private apiKeys: Map<string, APIKey> = new Map()

  constructor() {
    this.initializeAPI()
  }

  private async initializeAPI() {
    console.log('🌐 Advanced API System initializing...')
    await this.createTables()
    await this.loadAPIKeys()
    await this.setupRateLimiting()
    console.log('✅ Advanced API System initialized')
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS api_keys (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT NOT NULL,
          name VARCHAR(255) NOT NULL,
          key_hash VARCHAR(255) UNIQUE NOT NULL,
          permissions JSON NOT NULL,
          rate_limit_requests INT DEFAULT 1000,
          rate_limit_window INT DEFAULT 3600,
          is_active BOOLEAN DEFAULT TRUE,
          last_used TIMESTAMP NULL,
          expires_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_id (user_id),
          INDEX idx_key_hash (key_hash),
          INDEX idx_active (is_active)
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS api_usage (
          id VARCHAR(36) PRIMARY KEY,
          api_key_id VARCHAR(36),
          endpoint VARCHAR(255) NOT NULL,
          method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
          status_code INT NOT NULL,
          response_time_ms INT NOT NULL,
          request_size BIGINT DEFAULT 0,
          response_size BIGINT DEFAULT 0,
          ip_address VARCHAR(45),
          user_agent TEXT,
          error_message TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
          INDEX idx_api_key (api_key_id),
          INDEX idx_endpoint (endpoint),
          INDEX idx_created_at (created_at)
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS api_rate_limits (
          id VARCHAR(36) PRIMARY KEY,
          api_key_id VARCHAR(36) NOT NULL,
          requests_count INT DEFAULT 0,
          window_start TIMESTAMP NOT NULL,
          window_end TIMESTAMP NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
          INDEX idx_api_key (api_key_id),
          INDEX idx_window (window_start, window_end)
        )
      `)

      console.log('✅ Advanced API tables created')
    } catch (error) {
      console.error('❌ Error creating API tables:', error)
    }
  }

  private async loadAPIKeys() {
    try {
      const keys = await db.query('SELECT * FROM api_keys WHERE is_active = TRUE')
      
      for (const key of keys) {
        this.apiKeys.set(key.key_hash, {
          id: key.id,
          userId: key.user_id,
          name: key.name,
          key: '', // Don't store actual key in memory
          keyHash: key.key_hash,
          permissions: JSON.parse(key.permissions),
          rateLimit: {
            requests: key.rate_limit_requests,
            window: key.rate_limit_window
          },
          isActive: key.is_active,
          lastUsed: key.last_used,
          expiresAt: key.expires_at,
          createdAt: key.created_at
        })
      }

      console.log(`✅ Loaded ${keys.length} API keys`)
    } catch (error) {
      console.error('❌ Error loading API keys:', error)
    }
  }

  private async setupRateLimiting() {
    // Clean up old rate limit records every minute
    setInterval(async () => {
      try {
        await db.query(`
          DELETE FROM api_rate_limits 
          WHERE window_end < DATE_SUB(NOW(), INTERVAL 1 HOUR)
        `)
      } catch (error) {
        console.error('Error cleaning up rate limits:', error)
      }
    }, 60000)
  }

  async authenticate(request: NextRequest): Promise<{ success: boolean; apiKey?: APIKey; error?: string }> {
    try {
      // Get API key from header
      const authHeader = request.headers.get('authorization')
      const apiKeyHeader = request.headers.get('x-api-key')
      
      let apiKey = ''
      
      if (authHeader?.startsWith('Bearer ')) {
        apiKey = authHeader.substring(7)
      } else if (apiKeyHeader) {
        apiKey = apiKeyHeader
      } else {
        return { success: false, error: 'API key required' }
      }

      // Hash the provided key
      const keyHash = this.hashAPIKey(apiKey)
      const apiKeyData = this.apiKeys.get(keyHash)

      if (!apiKeyData) {
        return { success: false, error: 'Invalid API key' }
      }

      // Check if key is active
      if (!apiKeyData.isActive) {
        return { success: false, error: 'API key is inactive' }
      }

      // Check if key is expired
      if (apiKeyData.expiresAt && apiKeyData.expiresAt < new Date()) {
        return { success: false, error: 'API key has expired' }
      }

      // Update last used
      await this.updateLastUsed(apiKeyData.id)

      return { success: true, apiKey: apiKeyData }

    } catch (error) {
      console.error('API authentication error:', error)
      return { success: false, error: 'Authentication failed' }
    }
  }

  async checkRateLimit(apiKey: APIKey, endpoint: string): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    try {
      const now = new Date()
      const windowStart = new Date(now.getTime() - (apiKey.rateLimit.window * 1000))
      
      // Get current usage in window
      const usage = await db.query(`
        SELECT COUNT(*) as count 
        FROM api_usage 
        WHERE api_key_id = ? AND created_at >= ? AND created_at <= ?
      `, [apiKey.id, windowStart, now])

      const currentRequests = usage[0]?.count || 0
      const remaining = Math.max(0, apiKey.rateLimit.requests - currentRequests)
      const resetTime = new Date(now.getTime() + (apiKey.rateLimit.window * 1000))

      return {
        allowed: currentRequests < apiKey.rateLimit.requests,
        remaining,
        resetTime
      }

    } catch (error) {
      console.error('Rate limit check error:', error)
      return { allowed: false, remaining: 0, resetTime: new Date() }
    }
  }

  async checkPermission(apiKey: APIKey, permission: string): Promise<boolean> {
    return apiKey.permissions.includes(permission) || apiKey.permissions.includes('*')
  }

  async logAPIUsage(
    apiKeyId: string,
    endpoint: string,
    method: string,
    statusCode: number,
    responseTime: number,
    requestSize: number,
    responseSize: number,
    ipAddress: string,
    userAgent: string,
    errorMessage?: string
  ): Promise<void> {
    try {
      await db.query(`
        INSERT INTO api_usage (id, api_key_id, endpoint, method, status_code, response_time_ms, request_size, response_size, ip_address, user_agent, error_message, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        this.generateId(),
        apiKeyId,
        endpoint,
        method,
        statusCode,
        responseTime,
        requestSize,
        responseSize,
        ipAddress,
        userAgent,
        errorMessage || null
      ])
    } catch (error) {
      console.error('Error logging API usage:', error)
    }
  }

  // API Endpoints

  async handleCrawlRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    if (!await this.checkPermission(apiKey, 'crawl')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const schema = z.object({
      url: z.string().url(),
      depth: z.number().min(1).max(5).default(2),
      maxPages: z.number().min(1).max(1000).default(100),
      respectRobots: z.boolean().default(true),
      delay: z.number().min(0).max(10000).default(1000)
    })

    try {
      const body = await request.json()
      const data = schema.parse(body)

      const sessionId = await crawlingEngine.startCrawl({
        id: this.generateId(),
        url: data.url,
        depth: data.depth,
        maxPages: data.maxPages,
        respectRobots: data.respectRobots,
        userAgent: 'KodeXGuard-API/1.0',
        delay: data.delay,
        filters: [],
        extractors: []
      }, apiKey.userId)

      return NextResponse.json({
        success: true,
        sessionId,
        message: 'Crawl session started',
        estimatedTime: `${Math.ceil(data.maxPages * data.delay / 1000 / 60)} minutes`
      })

    } catch (error) {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 })
    }
  }

  async handleScanRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    if (!await this.checkPermission(apiKey, 'scan')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const schema = z.object({
      target: z.string(),
      scanType: z.enum(['nmap', 'masscan', 'zmap']),
      ports: z.string().default('1-1000'),
      options: z.object({
        aggressive: z.boolean().default(false),
        serviceDetection: z.boolean().default(true),
        osDetection: z.boolean().default(false),
        timing: z.number().min(1).max(5).default(3)
      }).default({})
    })

    try {
      const body = await request.json()
      const data = schema.parse(body)

      const sessionId = await scannerEngine.startScan({
        id: this.generateId(),
        target: data.target,
        scanType: data.scanType,
        ports: data.ports,
        options: {
          aggressive: data.options.aggressive,
          serviceDetection: data.options.serviceDetection,
          osDetection: data.options.osDetection,
          scriptScan: false,
          timing: data.options.timing,
          maxRetries: 3,
          timeout: 30000
        }
      }, apiKey.userId)

      return NextResponse.json({
        success: true,
        sessionId,
        message: 'Scan session started',
        estimatedTime: '2-10 minutes'
      })

    } catch (error) {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 })
    }
  }

  async handleMalwareAnalysisRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    if (!await this.checkPermission(apiKey, 'malware-analysis')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // This would handle file upload and analysis
    return NextResponse.json({
      success: true,
      message: 'Malware analysis endpoint - file upload required',
      supportedFormats: ['exe', 'dll', 'pdf', 'doc', 'zip']
    })
  }

  async handleSearchRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    if (!await this.checkPermission(apiKey, 'search')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const schema = z.object({
      query: z.string().min(1),
      type: z.array(z.string()).optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0)
    })

    try {
      const { searchParams } = new URL(request.url)
      const data = schema.parse({
        query: searchParams.get('q') || '',
        type: searchParams.get('type')?.split(','),
        limit: parseInt(searchParams.get('limit') || '20'),
        offset: parseInt(searchParams.get('offset') || '0')
      })

      const results = await searchEngine.search({
        query: data.query,
        filters: data.type ? { type: data.type } : undefined,
        pagination: {
          page: Math.floor(data.offset / data.limit) + 1,
          size: data.limit
        }
      }, apiKey.userId)

      return NextResponse.json({
        success: true,
        data: results.results,
        total: results.total,
        took: results.took
      })

    } catch (error) {
      return NextResponse.json({ error: 'Invalid request parameters' }, { status: 400 })
    }
  }

  async handleBreachSearchRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    if (!await this.checkPermission(apiKey, 'breach-search')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const schema = z.object({
      email: z.string().email()
    })

    try {
      const { searchParams } = new URL(request.url)
      const data = schema.parse({
        email: searchParams.get('email') || ''
      })

      const breaches = await breachParser.searchBreaches(data.email)

      return NextResponse.json({
        success: true,
        email: data.email,
        breaches: breaches.map(breach => ({
          id: breach.id,
          breachName: (breach as any).breach_name,
          breachDate: (breach as any).breach_date,
          dataClasses: (breach as any).data_classes
        })),
        total: breaches.length
      })

    } catch (error) {
      return NextResponse.json({ error: 'Invalid email address' }, { status: 400 })
    }
  }

  async handleStatusRequest(request: NextRequest, apiKey: APIKey): Promise<NextResponse> {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const type = searchParams.get('type') // crawl, scan, analysis

    if (!sessionId || !type) {
      return NextResponse.json({ error: 'sessionId and type required' }, { status: 400 })
    }

    try {
      let session: any = null

      switch (type) {
        case 'crawl':
          session = await crawlingEngine.getSession(sessionId)
          break
        case 'scan':
          session = await scannerEngine.getSession(sessionId)
          break
        case 'analysis':
          session = await malwareAnalyzer.getSession(sessionId)
          break
        default:
          return NextResponse.json({ error: 'Invalid session type' }, { status: 400 })
      }

      if (!session) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        session: {
          id: session.id,
          status: session.status,
          progress: session.progress || 0,
          startTime: session.startTime,
          endTime: session.endTime,
          results: session.status === 'completed' ? session.results : undefined
        }
      })

    } catch (error) {
      return NextResponse.json({ error: 'Failed to get session status' }, { status: 500 })
    }
  }

  async createAPIKey(userId: number, name: string, permissions: string[], rateLimit?: { requests: number; window: number }, expiresAt?: Date): Promise<{ key: string; id: string }> {
    const keyId = this.generateId()
    const apiKey = this.generateAPIKey()
    const keyHash = this.hashAPIKey(apiKey)

    await db.query(`
      INSERT INTO api_keys (id, user_id, name, key_hash, permissions, rate_limit_requests, rate_limit_window, expires_at, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      keyId,
      userId,
      name,
      keyHash,
      JSON.stringify(permissions),
      rateLimit?.requests || 1000,
      rateLimit?.window || 3600,
      expiresAt || null
    ])

    // Add to memory cache
    this.apiKeys.set(keyHash, {
      id: keyId,
      userId,
      name,
      key: '',
      keyHash,
      permissions,
      rateLimit: rateLimit || { requests: 1000, window: 3600 },
      isActive: true,
      createdAt: new Date()
    })

    return { key: apiKey, id: keyId }
  }

  private async updateLastUsed(apiKeyId: string): Promise<void> {
    try {
      await db.query('UPDATE api_keys SET last_used = NOW() WHERE id = ?', [apiKeyId])
    } catch (error) {
      console.error('Error updating last used:', error)
    }
  }

  private hashAPIKey(key: string): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(key).digest('hex')
  }

  private generateAPIKey(): string {
    const crypto = require('crypto')
    return 'kxg_' + crypto.randomBytes(32).toString('hex')
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const advancedAPI = new AdvancedAPISystem()
