import { NextRequest, NextResponse } from 'next/server'
import { PlanService } from '@/lib/plan'
import { withAuth, withCors, withErrorHandling, combineMiddlewares, AuthenticatedRequest } from '@/middlewares/auth'
import { z } from 'zod'

// Validation schema
const subscribeSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  paymentMethod: z.enum(['manual_transfer', 'tripay', 'midtrans', 'xendit']),
  duration: z.enum(['monthly', 'yearly']).optional()
})

// POST /api/plans/subscribe - Create subscription
async function subscribeHandler(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 400 }
      )
    }

    // Parse request body
    const body = await req.json()

    // Validate input
    const validation = subscribeSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      )
    }

    const { planId, paymentMethod } = validation.data

    // Get plan details
    const plan = await PlanService.getPlanById(planId)
    if (!plan) {
      return NextResponse.json(
        { error: 'Plan not found' },
        { status: 404 }
      )
    }

    // Create subscription
    const result = await PlanService.createSubscription(
      req.userId,
      planId,
      paymentMethod,
      plan.price
    )

    if ('error' in result) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    // Return subscription details
    return NextResponse.json({
      success: true,
      message: 'Subscription created successfully',
      data: {
        subscriptionId: result.subscriptionId,
        paymentId: result.paymentId,
        plan: {
          name: plan.name,
          price: plan.price,
          currency: plan.currency,
          duration: plan.duration
        },
        paymentMethod,
        instructions: paymentMethod === 'manual_transfer' ? {
          bankAccount: 'BCA **********',
          accountName: 'KodeXGuard',
          amount: plan.price,
          note: `Payment for ${plan.name} plan - ${result.subscriptionId}`
        } : null
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Subscribe API error:', error)
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}

// Apply middlewares
export const POST = combineMiddlewares(
  withCors,
  withErrorHandling,
  withAuth
)(subscribeHandler)
