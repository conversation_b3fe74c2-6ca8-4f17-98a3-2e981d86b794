"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommunityPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(app-pages-browser)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CommunityPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMembers: 15420,\n        activeToday: 1247,\n        totalPosts: 8934,\n        totalEvents: 156\n    });\n    const themeClasses = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useThemeClasses)();\n    const communityStats = [\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Total Members\",\n            value: stats.totalMembers.toLocaleString(),\n            change: \"+12%\",\n            color: \"text-cyber-primary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Active Today\",\n            value: stats.activeToday.toLocaleString(),\n            change: \"+8%\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Total Posts\",\n            value: stats.totalPosts.toLocaleString(),\n            change: \"+15%\",\n            color: \"text-cyber-secondary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: \"Events Hosted\",\n            value: stats.totalEvents.toLocaleString(),\n            change: \"+25%\",\n            color: \"text-cyber-accent\"\n        }\n    ];\n    const communityChannels = [\n        {\n            name: \"Discord Server\",\n            description: \"Real-time chat, voice channels, and community discussions\",\n            icon: Discord,\n            members: \"8.2K\",\n            link: \"#\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            name: \"Telegram Group\",\n            description: \"Quick updates, news, and instant notifications\",\n            icon: Telegram,\n            members: \"5.1K\",\n            link: \"#\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"GitHub Community\",\n            description: \"Open source contributions and code collaboration\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            members: \"3.8K\",\n            link: \"#\",\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, tips, and cybersecurity news\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            members: \"12.5K\",\n            link: \"#\",\n            color: \"bg-black\"\n        }\n    ];\n    const upcomingEvents = [\n        {\n            title: \"Cybersecurity Workshop\",\n            date: \"2024-01-15\",\n            time: \"19:00 WIB\",\n            type: \"Workshop\",\n            participants: 156,\n            description: \"Advanced penetration testing techniques\"\n        },\n        {\n            title: \"Bug Bounty Bootcamp\",\n            date: \"2024-01-20\",\n            time: \"14:00 WIB\",\n            type: \"Bootcamp\",\n            participants: 89,\n            description: \"From beginner to professional bug hunter\"\n        },\n        {\n            title: \"CTF Competition\",\n            date: \"2024-01-25\",\n            time: \"10:00 WIB\",\n            type: \"Competition\",\n            participants: 234,\n            description: \"Capture The Flag challenge for all levels\"\n        }\n    ];\n    const topContributors = [\n        {\n            name: \"CyberNinja\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 15420,\n            badge: \"Elite Hunter\",\n            contributions: 89\n        },\n        {\n            name: \"SecurityMaster\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 12350,\n            badge: \"Bug Hunter\",\n            contributions: 67\n        },\n        {\n            name: \"PentestPro\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 9870,\n            badge: \"Security Expert\",\n            contributions: 54\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-glow\" : \"text-blue-600\",\n                                        children: \"Join Our\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-pink\" : \"text-pink-600\",\n                                        children: \"Cyber Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl max-w-3xl mx-auto mb-8 \".concat(themeClasses.textSecondary),\n                                children: \"Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                                children: communityStats.map((stat, index)=>{\n                                    const Icon = stat.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" text-center\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-8 w-8 \".concat(stat.color, \" mx-auto mb-3 \").concat(themeClasses.isDark ? \"animate-cyber-pulse\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm \".concat(themeClasses.textMuted, \" mb-1\"),\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    stat.change,\n                                                    \" this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center mb-12\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                label: \"Overview\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                id: \"channels\",\n                                label: \"Channels\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: \"events\",\n                                label: \"Events\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            },\n                            {\n                                id: \"leaderboard\",\n                                label: \"Top Contributors\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 \".concat(activeTab === tab.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary\" : \"bg-blue-100 text-blue-600 border-2 border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Welcome to KodeXGuard Community\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help each other grow in the field of cybersecurity.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Share security research and findings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Collaborate on bug bounty programs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Learn from industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Participate in competitions and CTFs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Community Guidelines\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83E\\uDD1D Be Respectful\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Treat all community members with respect and professionalism\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDD12 Ethical Practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Only discuss ethical hacking and responsible disclosure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDCDA Share Knowledge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Help others learn and grow in cybersecurity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"channels\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: communityChannels.map((channel, index)=>{\n                                    const Icon = channel.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(channel.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                            children: channel.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-4\"),\n                                                            children: channel.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                    children: [\n                                                                        channel.members,\n                                                                        \" members\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-lg \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\", \" transition-colors duration-200\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Join\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Upcoming Events\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    upcomingEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(event.type === \"Workshop\" ? \"bg-blue-100 text-blue-800\" : event.type === \"Bootcamp\" ? \"bg-green-100 text-green-800\" : \"bg-purple-100 text-purple-800\"),\n                                                                        children: event.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                        children: [\n                                                                            event.participants,\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 md:mt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                            children: \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"leaderboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Top Contributors This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    topContributors.map((contributor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full bg-gradient-to-r \".concat(index === 0 ? \"from-yellow-400 to-yellow-600\" : index === 1 ? \"from-gray-300 to-gray-500\" : \"from-orange-400 to-orange-600\", \" flex items-center justify-center\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: contributor.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: contributor.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: [\n                                                                    contributor.points.toLocaleString(),\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                children: [\n                                                                    contributor.contributions,\n                                                                    \" contributions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center \".concat(themeClasses.card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                children: \"Ready to Join Our Community?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg \".concat(themeClasses.textSecondary, \" mb-8 max-w-2xl mx-auto\"),\n                                children: \"Connect with like-minded cybersecurity professionals and take your skills to the next level\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                        children: \"Join Discord Server\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                        children: \"Follow on Twitter\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(CommunityPage, \"F0NgutkfAWy8nhkP5ZGTG+irf/8=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useThemeClasses\n    ];\n});\n_c = CommunityPage;\nvar _c;\n$RefreshReg$(_c, \"CommunityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/community/page.tsx\n"));

/***/ })

});