/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/community/page";
exports.ids = ["app/community/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunity%2Fpage&page=%2Fcommunity%2Fpage&appPaths=%2Fcommunity%2Fpage&pagePath=private-next-app-dir%2Fcommunity%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunity%2Fpage&page=%2Fcommunity%2Fpage&appPaths=%2Fcommunity%2Fpage&pagePath=private-next-app-dir%2Fcommunity%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'community',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/community/page.tsx */ \"(rsc)/./app/community/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/community/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/community/page\",\n        pathname: \"/community\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunity%2Fpage&page=%2Fcommunity%2Fpage&appPaths=%2Fcommunity%2Fpage&pagePath=private-next-app-dir%2Fcommunity%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Ccommunity%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Ccommunity%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/community/page.tsx */ \"(ssr)/./app/community/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDY29tbXVuaXR5JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFnRyIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvPzJiZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxcS29kZS1YR3VhcmRcXFxcYXBwXFxcXGNvbW11bml0eVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Ccommunity%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Toast.tsx */ \"(ssr)/./components/Toast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/ThemeContext.tsx */ \"(ssr)/./contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RvYXN0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDS29kZS1YR3VhcmQlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUEwSDtBQUMxSDtBQUNBLGtLQUFxSSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvPzlhMzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXEtvZGUtWEd1YXJkXFxcXGNvbXBvbmVudHNcXFxcVG9hc3QudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXEtvZGUtWEd1YXJkXFxcXGNvbnRleHRzXFxcXFRoZW1lQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CommunityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(ssr)/./components/PublicLayout.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/__barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CommunityPage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMembers: 15420,\n        activeToday: 1247,\n        totalPosts: 8934,\n        totalEvents: 156\n    });\n    const themeClasses = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useThemeClasses)();\n    const communityStats = [\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Total Members\",\n            value: stats.totalMembers.toLocaleString(),\n            change: \"+12%\",\n            color: \"text-cyber-primary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Active Today\",\n            value: stats.activeToday.toLocaleString(),\n            change: \"+8%\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Total Posts\",\n            value: stats.totalPosts.toLocaleString(),\n            change: \"+15%\",\n            color: \"text-cyber-secondary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: \"Events Hosted\",\n            value: stats.totalEvents.toLocaleString(),\n            change: \"+25%\",\n            color: \"text-cyber-accent\"\n        }\n    ];\n    const communityChannels = [\n        {\n            name: \"Discord Server\",\n            description: \"Real-time chat, voice channels, and community discussions\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Discord,\n            members: \"8.2K\",\n            link: \"#\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            name: \"Telegram Group\",\n            description: \"Quick updates, news, and instant notifications\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Telegram,\n            members: \"5.1K\",\n            link: \"#\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"GitHub Community\",\n            description: \"Open source contributions and code collaboration\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            members: \"3.8K\",\n            link: \"#\",\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, tips, and cybersecurity news\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            members: \"12.5K\",\n            link: \"#\",\n            color: \"bg-black\"\n        }\n    ];\n    const upcomingEvents = [\n        {\n            title: \"Cybersecurity Workshop\",\n            date: \"2024-01-15\",\n            time: \"19:00 WIB\",\n            type: \"Workshop\",\n            participants: 156,\n            description: \"Advanced penetration testing techniques\"\n        },\n        {\n            title: \"Bug Bounty Bootcamp\",\n            date: \"2024-01-20\",\n            time: \"14:00 WIB\",\n            type: \"Bootcamp\",\n            participants: 89,\n            description: \"From beginner to professional bug hunter\"\n        },\n        {\n            title: \"CTF Competition\",\n            date: \"2024-01-25\",\n            time: \"10:00 WIB\",\n            type: \"Competition\",\n            participants: 234,\n            description: \"Capture The Flag challenge for all levels\"\n        }\n    ];\n    const topContributors = [\n        {\n            name: \"CyberNinja\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 15420,\n            badge: \"Elite Hunter\",\n            contributions: 89\n        },\n        {\n            name: \"SecurityMaster\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 12350,\n            badge: \"Bug Hunter\",\n            contributions: 67\n        },\n        {\n            name: \"PentestPro\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 9870,\n            badge: \"Security Expert\",\n            contributions: 54\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-glow\" : \"text-blue-600\",\n                                        children: \"Join Our\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-pink\" : \"text-pink-600\",\n                                        children: \"Cyber Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-xl max-w-3xl mx-auto mb-8 ${themeClasses.textSecondary}`,\n                                children: \"Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                                children: communityStats.map((stat, index)=>{\n                                    const Icon = stat.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${themeClasses.card} text-center`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: `h-8 w-8 ${stat.color} mx-auto mb-3 ${themeClasses.isDark ? \"animate-cyber-pulse\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-2xl font-bold ${themeClasses.textPrimary} mb-1`,\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-sm ${themeClasses.textMuted} mb-1`,\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    stat.change,\n                                                    \" this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center mb-12\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                label: \"Overview\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                            },\n                            {\n                                id: \"channels\",\n                                label: \"Channels\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: \"events\",\n                                label: \"Events\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            },\n                            {\n                                id: \"leaderboard\",\n                                label: \"Top Contributors\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 ${activeTab === tab.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary\" : \"bg-blue-100 text-blue-600 border-2 border-blue-500\" : `${themeClasses.textSecondary} hover:${themeClasses.textPrimary} hover:${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\"}`}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: `text-2xl font-bold ${themeClasses.textPrimary} mb-4`,\n                                                children: \"Welcome to KodeXGuard Community\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `${themeClasses.textSecondary} mb-6`,\n                                                children: \"Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help each other grow in the field of cybersecurity.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Share security research and findings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Collaborate on bug bounty programs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Learn from industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Participate in competitions and CTFs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: `text-2xl font-bold ${themeClasses.textPrimary} mb-4`,\n                                                children: \"Community Guidelines\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-4 rounded-lg ${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `font-semibold ${themeClasses.textPrimary} mb-2`,\n                                                                children: \"\\uD83E\\uDD1D Be Respectful\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${themeClasses.textSecondary}`,\n                                                                children: \"Treat all community members with respect and professionalism\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-4 rounded-lg ${themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `font-semibold ${themeClasses.textPrimary} mb-2`,\n                                                                children: \"\\uD83D\\uDD12 Ethical Practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${themeClasses.textSecondary}`,\n                                                                children: \"Only discuss ethical hacking and responsible disclosure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-4 rounded-lg ${themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `font-semibold ${themeClasses.textPrimary} mb-2`,\n                                                                children: \"\\uD83D\\uDCDA Share Knowledge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${themeClasses.textSecondary}`,\n                                                                children: \"Help others learn and grow in cybersecurity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"channels\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: communityChannels.map((channel, index)=>{\n                                    const Icon = channel.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${themeClasses.card} hover:scale-105 transition-transform duration-300`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-3 rounded-lg ${channel.color}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: `text-xl font-bold ${themeClasses.textPrimary} mb-2`,\n                                                            children: channel.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: `${themeClasses.textSecondary} mb-4`,\n                                                            children: channel.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `text-sm ${themeClasses.textMuted}`,\n                                                                    children: [\n                                                                        channel.members,\n                                                                        \" members\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: `flex items-center space-x-2 px-4 py-2 rounded-lg ${themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\"} transition-colors duration-200`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Join\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: `text-2xl font-bold ${themeClasses.textPrimary} mb-6`,\n                                        children: \"Upcoming Events\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    upcomingEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `${themeClasses.card} hover:${themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\"} transition-colors duration-300`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-3 py-1 rounded-full text-xs font-medium ${event.type === \"Workshop\" ? \"bg-blue-100 text-blue-800\" : event.type === \"Bootcamp\" ? \"bg-green-100 text-green-800\" : \"bg-purple-100 text-purple-800\"}`,\n                                                                        children: event.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-sm ${themeClasses.textMuted}`,\n                                                                        children: [\n                                                                            event.participants,\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `text-xl font-bold ${themeClasses.textPrimary} mb-2`,\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `${themeClasses.textSecondary} mb-3`,\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `text-sm ${themeClasses.textMuted}`,\n                                                                                children: event.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Discord_ExternalLink_Github_MapPin_MessageSquare_Shield_Target_Telegram_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `text-sm ${themeClasses.textMuted}`,\n                                                                                children: event.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 md:mt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `${themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"}`,\n                                                            children: \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"leaderboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: `text-2xl font-bold ${themeClasses.textPrimary} mb-6`,\n                                        children: \"Top Contributors This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    topContributors.map((contributor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `${themeClasses.card} hover:scale-105 transition-transform duration-300`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-12 h-12 rounded-full bg-gradient-to-r ${index === 0 ? \"from-yellow-400 to-yellow-600\" : index === 1 ? \"from-gray-300 to-gray-500\" : \"from-orange-400 to-orange-600\"} flex items-center justify-center`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `text-lg font-bold ${themeClasses.textPrimary}`,\n                                                                children: contributor.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${themeClasses.textSecondary}`,\n                                                                children: contributor.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-lg font-bold ${themeClasses.textPrimary}`,\n                                                                children: [\n                                                                    contributor.points.toLocaleString(),\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-sm ${themeClasses.textMuted}`,\n                                                                children: [\n                                                                    contributor.contributions,\n                                                                    \" contributions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-center ${themeClasses.card}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: `text-3xl font-bold ${themeClasses.textPrimary} mb-4`,\n                                children: \"Ready to Join Our Community?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-lg ${themeClasses.textSecondary} mb-8 max-w-2xl mx-auto`,\n                                children: \"Connect with like-minded cybersecurity professionals and take your skills to the next level\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                        children: \"Join Discord Server\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `px-8 py-3 rounded-lg font-medium transition-colors duration-200 ${themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"}`,\n                                        children: \"Follow on Twitter\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/community/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CyberFooter.tsx":
/*!************************************!*\
  !*** ./components/CyberFooter.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CyberFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,FileText,Github,Globe,Heart,HelpCircle,Linkedin,Lock,Mail,MapPin,Phone,Shield,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CyberFooter() {\n    const currentYear = new Date().getFullYear();\n    const footerSections = [\n        {\n            title: \"Platform\",\n            links: [\n                {\n                    name: \"OSINT Investigator\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                },\n                {\n                    name: \"CVE Intelligence\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n                },\n                {\n                    name: \"Developer Tools\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                }\n            ]\n        },\n        {\n            title: \"Community\",\n            links: [\n                {\n                    name: \"Leaderboard\",\n                    href: \"/leaderboard\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                },\n                {\n                    name: \"Bug Bounty\",\n                    href: \"/bounty\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                },\n                {\n                    name: \"Discord Server\",\n                    href: \"https://discord.gg/kodexguard\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    external: true\n                },\n                {\n                    name: \"GitHub\",\n                    href: \"https://github.com/kodexguard\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    external: true\n                },\n                {\n                    name: \"Blog\",\n                    href: \"/blog\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Events\",\n                    href: \"/events\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                }\n            ]\n        },\n        {\n            title: \"Resources\",\n            links: [\n                {\n                    name: \"Documentation\",\n                    href: \"/docs\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"API Reference\",\n                    href: \"/docs/api\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                },\n                {\n                    name: \"Tutorials\",\n                    href: \"/tutorials\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Security Guide\",\n                    href: \"/security\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                },\n                {\n                    name: \"Best Practices\",\n                    href: \"/best-practices\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                },\n                {\n                    name: \"FAQ\",\n                    href: \"/faq\",\n                    icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                }\n            ]\n        },\n        {\n            title: \"Company\",\n            links: [\n                {\n                    name: \"About Us\",\n                    href: \"/about\"\n                },\n                {\n                    name: \"Careers\",\n                    href: \"/careers\"\n                },\n                {\n                    name: \"Contact\",\n                    href: \"/contact\"\n                },\n                {\n                    name: \"Privacy Policy\",\n                    href: \"/privacy\"\n                },\n                {\n                    name: \"Terms of Service\",\n                    href: \"/terms\"\n                },\n                {\n                    name: \"Security Policy\",\n                    href: \"/security-policy\"\n                }\n            ]\n        }\n    ];\n    const socialLinks = [\n        {\n            name: \"GitHub\",\n            href: \"https://github.com/kodexguard\",\n            icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Twitter\",\n            href: \"https://twitter.com/kodexguard\",\n            icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"https://linkedin.com/company/kodexguard\",\n            icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Email\",\n            href: \"mailto:<EMAIL>\",\n            icon: _barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    const stats = [\n        {\n            label: \"Active Users\",\n            value: \"50K+\"\n        },\n        {\n            label: \"Vulnerabilities Found\",\n            value: \"1M+\"\n        },\n        {\n            label: \"Security Scans\",\n            value: \"10M+\"\n        },\n        {\n            label: \"Countries\",\n            value: \"150+\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-cyber-dark border-t border-cyber-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-cyber-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-cyber-primary animate-cyber-glow group-hover:animate-cyber-pulse\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 mt-2 font-medium\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-3 group mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-10 w-10 text-cyber-primary animate-cyber-glow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-cyber-glow group-hover:animate-glitch\",\n                                                        children: \"KodeXGuard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                        children: \"Cyber Security Platform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-6 leading-relaxed\",\n                                        children: \"The ultimate cybersecurity platform for OSINT investigation, vulnerability scanning, and security research. Join thousands of security professionals worldwide.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Jakarta, Indonesia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"hover:text-cyber-primary transition-colors duration-200\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"+62 21 1234 5678\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            footerSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-bold mb-6 text-lg relative\",\n                                            children: [\n                                                section.title,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 w-8 h-0.5 bg-cyber-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: section.links.map((link)=>{\n                                                const Icon = link.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: link.href,\n                                                        target: link.external ? \"_blank\" : undefined,\n                                                        rel: link.external ? \"noopener noreferrer\" : undefined,\n                                                        className: \"flex items-center space-x-2 text-gray-400 hover:text-cyber-primary transition-colors duration-200 group\",\n                                                        children: [\n                                                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-4 w-4 group-hover:animate-cyber-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 34\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: link.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            link.external && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, link.name, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, section.title, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 pt-8 border-t border-cyber-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white mb-4\",\n                                            children: [\n                                                \"Stay Updated with \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-primary\",\n                                                    children: \"Cyber Threats\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 35\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Get the latest security news, vulnerability alerts, and platform updates delivered to your inbox.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email address\",\n                                            className: \"flex-1 px-4 py-3 bg-cyber-card border border-cyber-border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-cyber-primary transition-colors duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-cyber-primary whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Subscribe\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-cyber-border bg-cyber-card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"\\xa9 \",\n                                                currentYear,\n                                                \" KodeXGuard. All rights reserved.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Made with\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-500 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"for the cybersecurity community\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Follow us:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        socialLinks.map((social)=>{\n                                            const Icon = social.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: social.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"p-2 rounded-lg text-gray-400 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-all duration-200 group\",\n                                                title: social.name,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5 group-hover:animate-cyber-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, social.name, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-cyber-border/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"SSL Secured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"SOC 2 Compliant\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_FileText_Github_Globe_Heart_HelpCircle_Linkedin_Lock_Mail_MapPin_Phone_Shield_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 text-yellow-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"99.9% Uptime\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Platform Status: \"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500 font-medium\",\n                                                children: \"All Systems Operational\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-block w-2 h-2 bg-green-500 rounded-full ml-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n                        backgroundSize: \"50px 50px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberFooter.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CyberFooter.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CyberHeader.tsx":
/*!************************************!*\
  !*** ./components/CyberHeader.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CyberHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CyberHeader({ user }) {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { theme } = useTheme();\n    const themeClasses = useThemeClasses();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Features\",\n            href: \"#features\",\n            dropdown: [\n                {\n                    name: \"OSINT Investigator\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    description: \"Advanced intelligence gathering\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    description: \"Automated security scanning\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    description: \"Malware detection & analysis\"\n                },\n                {\n                    name: \"CVE Intelligence\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    description: \"Vulnerability database\"\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    description: \"Advanced search queries\"\n                },\n                {\n                    name: \"Developer Tools\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    description: \"Security testing tools\"\n                }\n            ]\n        },\n        {\n            name: \"Pricing\",\n            href: \"/plan\"\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Docs\",\n            href: \"/docs\"\n        },\n        {\n            name: \"Community\",\n            href: \"/community\"\n        }\n    ];\n    const handleLogin = ()=>{\n        router.push(\"/login\");\n    };\n    const handleRegister = ()=>{\n        router.push(\"/register\");\n    };\n    const handleDashboard = ()=>{\n        router.push(\"/dashboard\");\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"token\")}`\n                }\n            });\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const isActive = (href)=>{\n        if (href.startsWith(\"#\")) return false;\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? `${themeClasses.bgCard}/95 backdrop-blur-md border-b ${themeClasses.border} shadow-lg` : \"bg-transparent\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: `h-8 w-8 text-cyber-primary ${themeClasses.isDark ? \"animate-cyber-glow\" : \"\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        themeClasses.isDark && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xl font-bold ${themeClasses.isDark ? \"text-cyber-glow group-hover:animate-glitch\" : \"text-blue-600\"}`,\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs uppercase tracking-wider ${themeClasses.isDark ? \"text-cyber-secondary\" : \"text-gray-500\"}`,\n                                            children: \"Cyber Security Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setActiveDropdown(item.name),\n                                        onMouseLeave: ()=>setActiveDropdown(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `flex items-center space-x-1 ${themeClasses.textSecondary} hover:text-cyber-primary transition-colors duration-200 font-medium`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `absolute top-full left-0 mt-2 w-80 ${themeClasses.bgCard} border ${themeClasses.border} rounded-lg shadow-xl animate-fade-in-up`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 grid grid-cols-1 gap-2\",\n                                                    children: item.dropdown.map((subItem)=>{\n                                                        const Icon = subItem.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: subItem.href,\n                                                            className: `flex items-center space-x-3 p-3 rounded-lg hover:${themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-blue-50\"} transition-colors duration-200 group`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: `h-5 w-5 text-cyber-primary ${themeClasses.isDark ? \"group-hover:animate-cyber-pulse\" : \"\"}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `font-medium ${themeClasses.textPrimary} group-hover:text-cyber-primary`,\n                                                                            children: subItem.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 163,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `text-sm ${themeClasses.textMuted}`,\n                                                                            children: subItem.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 166,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, subItem.name, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 31\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: `flex items-center space-x-1 font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-cyber-primary\" : `${themeClasses.textSecondary} hover:text-cyber-primary`}`,\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 35\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {\n                                    variant: \"switch\",\n                                    size: \"md\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 px-3 py-1 bg-cyber-secondary/20 border border-cyber-secondary rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-cyber-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-cyber-accent uppercase\",\n                                                    children: user.plan\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: `flex items-center space-x-2 p-2 rounded-lg hover:${themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-gray-100\"} transition-colors duration-200`,\n                                                    children: [\n                                                        user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: user.avatar,\n                                                            alt: user.username,\n                                                            className: \"h-8 w-8 rounded-full border-2 border-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 rounded-full bg-cyber-primary/20 border-2 border-cyber-primary flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `${themeClasses.textPrimary} font-medium`,\n                                                            children: user.username\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: `h-4 w-4 ${themeClasses.textMuted}`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `absolute right-0 top-full mt-2 w-48 ${themeClasses.bgCard} border ${themeClasses.border} rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleDashboard,\n                                                                className: `w-full flex items-center space-x-2 px-3 py-2 text-left ${themeClasses.textPrimary} hover:${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"} rounded-lg transition-colors duration-200`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleLogout,\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Logout\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogin,\n                                            className: `flex items-center space-x-2 px-4 py-2 text-cyber-primary hover:${themeClasses.textPrimary} border border-cyber-primary hover:${themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-50\"} rounded-lg transition-all duration-200`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRegister,\n                                            className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Get Started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {\n                                    variant: \"button\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: `p-2 rounded-lg ${themeClasses.textSecondary} hover:text-cyber-primary hover:${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"} transition-colors duration-200`,\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `md:hidden ${themeClasses.bgCard} border-t ${themeClasses.border} animate-slide-in-right`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 space-y-4\",\n                    children: [\n                        navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `${themeClasses.textSecondary} font-medium mb-2`,\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-2\",\n                                            children: item.dropdown.map((subItem)=>{\n                                                const Icon = subItem.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.href,\n                                                    className: `flex items-center space-x-3 p-2 rounded-lg hover:${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"} transition-colors duration-200`,\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: themeClasses.textPrimary,\n                                                            children: subItem.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, subItem.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `block px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${isActive(item.href) ? `text-cyber-primary ${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"}` : `${themeClasses.textSecondary} hover:text-cyber-primary hover:${themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"}`}`,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `pt-4 border-t ${themeClasses.border} space-y-3`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleLogin();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: `w-full flex items-center justify-center space-x-2 px-4 py-2 text-cyber-primary border border-cyber-primary rounded-lg hover:${themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-50\"} transition-colors duration-200`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleRegister();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: `w-full ${themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Started\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CyberHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./components/PublicLayout.tsx":
/*!*************************************!*\
  !*** ./components/PublicLayout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CyberHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CyberHeader */ \"(ssr)/./components/CyberHeader.tsx\");\n/* harmony import */ var _CyberFooter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CyberFooter */ \"(ssr)/./components/CyberFooter.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction PublicLayout({ children, showHeader = true, showFooter = true, className = \"\" }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const userData = localStorage.getItem(\"user\");\n                if (token && userData) {\n                    const parsedUser = JSON.parse(userData);\n                    setUser(parsedUser);\n                }\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                // Clear invalid data\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cyber-primary font-medium\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-cyber-dark text-white ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none opacity-5 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `\n              linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n            `,\n                        backgroundSize: \"50px 50px\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none z-0\",\n                children: [\n                    Array.from({\n                        length: 20\n                    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-1 h-1 bg-cyber-primary rounded-full animate-matrix-rain opacity-30\",\n                            style: {\n                                left: `${Math.random() * 100}%`,\n                                animationDelay: `${Math.random() * 3}s`,\n                                animationDuration: `${3 + Math.random() * 2}s`\n                            }\n                        }, i, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-secondary to-transparent opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-32 h-32 border-l-2 border-t-2 border-cyber-primary opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-32 h-32 border-r-2 border-t-2 border-cyber-secondary opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 w-32 h-32 border-l-2 border-b-2 border-cyber-accent opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-0 w-32 h-32 border-r-2 border-b-2 border-cyber-primary opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this),\n            \")}\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    showHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CyberHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        user: user || undefined\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 24\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: showHeader ? \"pt-16\" : \"\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CyberFooter__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 24\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-50 animate-cyber-scan pointer-events-none z-20\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\PublicLayout.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/PublicLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast,default auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove after duration\n        const duration = toast.duration || 5000;\n        setTimeout(()=>{\n            removeToast(id);\n        }, duration);\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"success\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"error\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"warning\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"info\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            success,\n            error,\n            warning,\n            info\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast,\n                onRemove: ()=>removeToast(toast.id)\n            }, toast.id, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastItem({ toast, onRemove }) {\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n      max-w-sm w-full shadow-lg rounded-lg border pointer-events-auto\n      ${getBackgroundColor()}\n      animate-slide-in-right\n    `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm font-medium ${getTextColor()}`,\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${toast.title ? \"mt-1\" : \"\"} ${getTextColor()}`,\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toast.action.onClick,\n                                    className: `text-sm font-medium underline hover:no-underline ${getTextColor()}`,\n                                    children: toast.action.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRemove,\n                            className: `rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for standalone usage\nconst toast = {\n    success: (message, title)=>{\n        // This will only work if ToastProvider is available\n        console.log(\"Success:\", title || \"\", message);\n    },\n    error: (message, title)=>{\n        console.log(\"Error:\", title || \"\", message);\n    },\n    warning: (message, title)=>{\n        console.log(\"Warning:\", title || \"\", message);\n    },\n    info: (message, title)=>{\n        console.log(\"Info:\", title || \"\", message);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRjtBQUNKO0FBd0IvRSxNQUFNUyw2QkFBZVQsb0RBQWFBLENBQStCVTtBQUUxRCxTQUFTQyxjQUFjLEVBQUVDLFFBQVEsRUFBMkI7SUFDakUsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdaLCtDQUFRQSxDQUFVLEVBQUU7SUFFaEQsTUFBTWEsV0FBV1osa0RBQVdBLENBQUMsQ0FBQ2E7UUFDNUIsTUFBTUMsS0FBS0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7UUFDaEQsTUFBTUMsV0FBVztZQUFFLEdBQUdOLEtBQUs7WUFBRUM7UUFBRztRQUVoQ0gsVUFBVVMsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1EO2FBQVM7UUFFckMsNkJBQTZCO1FBQzdCLE1BQU1FLFdBQVdSLE1BQU1RLFFBQVEsSUFBSTtRQUNuQ0MsV0FBVztZQUNUQyxZQUFZVDtRQUNkLEdBQUdPO0lBQ0wsR0FBRyxFQUFFO0lBRUwsTUFBTUUsY0FBY3ZCLGtEQUFXQSxDQUFDLENBQUNjO1FBQy9CSCxVQUFVUyxDQUFBQSxPQUFRQSxLQUFLSSxNQUFNLENBQUNYLENBQUFBLFFBQVNBLE1BQU1DLEVBQUUsS0FBS0E7SUFDdEQsR0FBRyxFQUFFO0lBRUwsTUFBTVcsVUFBVXpCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNaUIsUUFBUTdCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDMUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBU0Y7WUFBU0M7UUFBTTtJQUMzQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNa0IsVUFBVTlCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNbUIsT0FBTy9CLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDekNmLFNBQVM7WUFBRWdCLE1BQU07WUFBUUY7WUFBU0M7UUFBTTtJQUMxQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixxQkFDRSw4REFBQ04sYUFBYTBCLFFBQVE7UUFBQ0MsT0FBTztZQUM1QnZCO1lBQ0FFO1lBQ0FXO1lBQ0FFO1lBQ0FJO1lBQ0FDO1lBQ0FDO1FBQ0Y7O1lBQ0d0QjswQkFDRCw4REFBQ3lCOzs7Ozs7Ozs7OztBQUdQO0FBRU8sU0FBU0M7SUFDZCxNQUFNQyxVQUFVdEMsaURBQVVBLENBQUNRO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLFNBQVNGO0lBQ1AsTUFBTSxFQUFFeEIsTUFBTSxFQUFFYSxXQUFXLEVBQUUsR0FBR1k7SUFFaEMscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7a0JBQ1o3QixPQUFPOEIsR0FBRyxDQUFDM0IsQ0FBQUEsc0JBQ1YsOERBQUM0QjtnQkFFQzVCLE9BQU9BO2dCQUNQNkIsVUFBVSxJQUFNbkIsWUFBWVYsTUFBTUMsRUFBRTtlQUYvQkQsTUFBTUMsRUFBRTs7Ozs7Ozs7OztBQU92QjtBQUVBLFNBQVMyQixVQUFVLEVBQ2pCNUIsS0FBSyxFQUNMNkIsUUFBUSxFQUlUO0lBQ0MsTUFBTUMsVUFBVTtRQUNkLE9BQVE5QixNQUFNZSxJQUFJO1lBQ2hCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMxQix3SEFBV0E7b0JBQUNxQyxXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BDLHdIQUFXQTtvQkFBQ29DLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbEMsd0hBQWFBO29CQUFDa0MsV0FBVTs7Ozs7O1lBQ2xDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNuQyx3SEFBSUE7b0JBQUNtQyxXQUFVOzs7Ozs7UUFDM0I7SUFDRjtJQUVBLE1BQU1LLHFCQUFxQjtRQUN6QixPQUFRL0IsTUFBTWUsSUFBSTtZQUNoQixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTWlCLGVBQWU7UUFDbkIsT0FBUWhDLE1BQU1lLElBQUk7WUFDaEIsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVTtRQUFJQyxXQUFXLENBQUM7O01BRWYsRUFBRUsscUJBQXFCOztJQUV6QixDQUFDO2tCQUNDLDRFQUFDTjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaSTs7Ozs7O2tDQUdILDhEQUFDTDt3QkFBSUMsV0FBVTs7NEJBQ1oxQixNQUFNYyxLQUFLLGtCQUNWLDhEQUFDbUI7Z0NBQUVQLFdBQVcsQ0FBQyxvQkFBb0IsRUFBRU0sZUFBZSxDQUFDOzBDQUNsRGhDLE1BQU1jLEtBQUs7Ozs7OzswQ0FHaEIsOERBQUNtQjtnQ0FBRVAsV0FBVyxDQUFDLFFBQVEsRUFBRTFCLE1BQU1jLEtBQUssR0FBRyxTQUFTLEdBQUcsQ0FBQyxFQUFFa0IsZUFBZSxDQUFDOzBDQUNuRWhDLE1BQU1hLE9BQU87Ozs7Ozs0QkFHZmIsTUFBTWtDLE1BQU0sa0JBQ1gsOERBQUNUO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDUztvQ0FDQ0MsU0FBU3BDLE1BQU1rQyxNQUFNLENBQUNFLE9BQU87b0NBQzdCVixXQUFXLENBQUMsaURBQWlELEVBQUVNLGVBQWUsQ0FBQzs4Q0FFOUVoQyxNQUFNa0MsTUFBTSxDQUFDRyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNM0IsOERBQUNaO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDUzs0QkFDQ0MsU0FBU1A7NEJBQ1RILFdBQVcsQ0FBQyx1QkFBdUIsRUFBRU0sZUFBZSx3RkFBd0YsQ0FBQztzQ0FFN0ksNEVBQUM1Qyx3SEFBQ0E7Z0NBQUNzQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8zQjtBQUVBLHdDQUF3QztBQUNqQyxNQUFNMUIsUUFBUTtJQUNuQlksU0FBUyxDQUFDQyxTQUFpQkM7UUFDekIsb0RBQW9EO1FBQ3BEd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FHLE9BQU8sQ0FBQ0gsU0FBaUJDO1FBQ3ZCd0IsUUFBUUMsR0FBRyxDQUFDLFVBQVV6QixTQUFTLElBQUlEO0lBQ3JDO0lBQ0FJLFNBQVMsQ0FBQ0osU0FBaUJDO1FBQ3pCd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FLLE1BQU0sQ0FBQ0wsU0FBaUJDO1FBQ3RCd0IsUUFBUUMsR0FBRyxDQUFDLFNBQVN6QixTQUFTLElBQUlEO0lBQ3BDO0FBQ0YsRUFBQztBQUVELGlFQUFlbEIsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9jb21wb25lbnRzL1RvYXN0LnRzeD85MzlhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgWCwgQ2hlY2tDaXJjbGUsIEFsZXJ0Q2lyY2xlLCBJbmZvLCBBbGVydFRyaWFuZ2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgVG9hc3Qge1xuICBpZDogc3RyaW5nXG4gIHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcicgfCAnd2FybmluZycgfCAnaW5mbydcbiAgdGl0bGU/OiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIGR1cmF0aW9uPzogbnVtYmVyXG4gIGFjdGlvbj86IHtcbiAgICBsYWJlbDogc3RyaW5nXG4gICAgb25DbGljazogKCkgPT4gdm9pZFxuICB9XG59XG5cbmludGVyZmFjZSBUb2FzdENvbnRleHRUeXBlIHtcbiAgdG9hc3RzOiBUb2FzdFtdXG4gIGFkZFRvYXN0OiAodG9hc3Q6IE9taXQ8VG9hc3QsICdpZCc+KSA9PiB2b2lkXG4gIHJlbW92ZVRvYXN0OiAoaWQ6IHN0cmluZykgPT4gdm9pZFxuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4gdm9pZFxuICBlcnJvcjogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgd2FybmluZzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgaW5mbzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbn1cblxuY29uc3QgVG9hc3RDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUb2FzdENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3RvYXN0cywgc2V0VG9hc3RzXSA9IHVzZVN0YXRlPFRvYXN0W10+KFtdKVxuXG4gIGNvbnN0IGFkZFRvYXN0ID0gdXNlQ2FsbGJhY2soKHRvYXN0OiBPbWl0PFRvYXN0LCAnaWQnPikgPT4ge1xuICAgIGNvbnN0IGlkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpXG4gICAgY29uc3QgbmV3VG9hc3QgPSB7IC4uLnRvYXN0LCBpZCB9XG4gICAgXG4gICAgc2V0VG9hc3RzKHByZXYgPT4gWy4uLnByZXYsIG5ld1RvYXN0XSlcblxuICAgIC8vIEF1dG8gcmVtb3ZlIGFmdGVyIGR1cmF0aW9uXG4gICAgY29uc3QgZHVyYXRpb24gPSB0b2FzdC5kdXJhdGlvbiB8fCA1MDAwXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICByZW1vdmVUb2FzdChpZClcbiAgICB9LCBkdXJhdGlvbilcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVtb3ZlVG9hc3QgPSB1c2VDYWxsYmFjaygoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFRvYXN0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHRvYXN0ID0+IHRvYXN0LmlkICE9PSBpZCkpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHN1Y2Nlc3MgPSB1c2VDYWxsYmFjaygobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGFkZFRvYXN0KHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlLCB0aXRsZSB9KVxuICB9LCBbYWRkVG9hc3RdKVxuXG4gIGNvbnN0IGVycm9yID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHtcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3Qgd2FybmluZyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnd2FybmluZycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3QgaW5mbyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnaW5mbycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgcmV0dXJuIChcbiAgICA8VG9hc3RDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7XG4gICAgICB0b2FzdHMsXG4gICAgICBhZGRUb2FzdCxcbiAgICAgIHJlbW92ZVRvYXN0LFxuICAgICAgc3VjY2VzcyxcbiAgICAgIGVycm9yLFxuICAgICAgd2FybmluZyxcbiAgICAgIGluZm9cbiAgICB9fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUb2FzdCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVG9hc3RDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUb2FzdCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVG9hc3RQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZnVuY3Rpb24gVG9hc3RDb250YWluZXIoKSB7XG4gIGNvbnN0IHsgdG9hc3RzLCByZW1vdmVUb2FzdCB9ID0gdXNlVG9hc3QoKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XG4gICAgICB7dG9hc3RzLm1hcCh0b2FzdCA9PiAoXG4gICAgICAgIDxUb2FzdEl0ZW1cbiAgICAgICAgICBrZXk9e3RvYXN0LmlkfVxuICAgICAgICAgIHRvYXN0PXt0b2FzdH1cbiAgICAgICAgICBvblJlbW92ZT17KCkgPT4gcmVtb3ZlVG9hc3QodG9hc3QuaWQpfVxuICAgICAgICAvPlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gVG9hc3RJdGVtKHsgXG4gIHRvYXN0LCBcbiAgb25SZW1vdmUgXG59OiB7IFxuICB0b2FzdDogVG9hc3RcbiAgb25SZW1vdmU6ICgpID0+IHZvaWQgXG59KSB7XG4gIGNvbnN0IGdldEljb24gPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gPEluZm8gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRCYWNrZ3JvdW5kQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC01MCBib3JkZXItcmVkLTIwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy01MCBib3JkZXIteWVsbG93LTIwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFRleHRDb2xvciA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHRvYXN0LnR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JlZW4tODAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ3RleHQtcmVkLTgwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ3RleHQteWVsbG93LTgwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ3RleHQtYmx1ZS04MDAnXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgbWF4LXctc20gdy1mdWxsIHNoYWRvdy1sZyByb3VuZGVkLWxnIGJvcmRlciBwb2ludGVyLWV2ZW50cy1hdXRvXG4gICAgICAke2dldEJhY2tncm91bmRDb2xvcigpfVxuICAgICAgYW5pbWF0ZS1zbGlkZS1pbi1yaWdodFxuICAgIGB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICB7Z2V0SWNvbigpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyB3LTAgZmxleC0xXCI+XG4gICAgICAgICAgICB7dG9hc3QudGl0bGUgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgICAge3RvYXN0LnRpdGxlfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RvYXN0LnRpdGxlID8gJ210LTEnIDogJyd9ICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgIHt0b2FzdC5tZXNzYWdlfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7dG9hc3QuYWN0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9hc3QuYWN0aW9uLm9uQ2xpY2t9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtIHVuZGVybGluZSBob3Zlcjpuby11bmRlcmxpbmUgJHtnZXRUZXh0Q29sb3IoKX1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHt0b2FzdC5hY3Rpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTQgZmxleC1zaHJpbmstMCBmbGV4XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVtb3ZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLW1kIGlubGluZS1mbGV4ICR7Z2V0VGV4dENvbG9yKCl9IGhvdmVyOm9wYWNpdHktNzUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctY3VycmVudGB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gVXRpbGl0eSBmdW5jdGlvbiBmb3Igc3RhbmRhbG9uZSB1c2FnZVxuZXhwb3J0IGNvbnN0IHRvYXN0ID0ge1xuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIC8vIFRoaXMgd2lsbCBvbmx5IHdvcmsgaWYgVG9hc3RQcm92aWRlciBpcyBhdmFpbGFibGVcbiAgICBjb25zb2xlLmxvZygnU3VjY2VzczonLCB0aXRsZSB8fCAnJywgbWVzc2FnZSlcbiAgfSxcbiAgZXJyb3I6IChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0Vycm9yOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICB3YXJuaW5nOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdXYXJuaW5nOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICBpbmZvOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdJbmZvOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IFRvYXN0UHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJYIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkluZm8iLCJBbGVydFRyaWFuZ2xlIiwiVG9hc3RDb250ZXh0IiwidW5kZWZpbmVkIiwiVG9hc3RQcm92aWRlciIsImNoaWxkcmVuIiwidG9hc3RzIiwic2V0VG9hc3RzIiwiYWRkVG9hc3QiLCJ0b2FzdCIsImlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwibmV3VG9hc3QiLCJwcmV2IiwiZHVyYXRpb24iLCJzZXRUaW1lb3V0IiwicmVtb3ZlVG9hc3QiLCJmaWx0ZXIiLCJzdWNjZXNzIiwibWVzc2FnZSIsInRpdGxlIiwidHlwZSIsImVycm9yIiwid2FybmluZyIsImluZm8iLCJQcm92aWRlciIsInZhbHVlIiwiVG9hc3RDb250YWluZXIiLCJ1c2VUb2FzdCIsImNvbnRleHQiLCJFcnJvciIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsIlRvYXN0SXRlbSIsIm9uUmVtb3ZlIiwiZ2V0SWNvbiIsImdldEJhY2tncm91bmRDb2xvciIsImdldFRleHRDb2xvciIsInAiLCJhY3Rpb24iLCJidXR0b24iLCJvbkNsaWNrIiwibGFiZWwiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeClasses: () => (/* binding */ useThemeClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeClasses auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setThemeState(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const root = document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"dark\", \"light\");\n        // Add current theme class\n        root.classList.add(theme);\n        // Update CSS variables based on theme\n        if (theme === \"light\") {\n            // Light theme variables\n            root.style.setProperty(\"--cyber-bg-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#f8fafc\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#1e293b\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#475569\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#64748b\");\n            root.style.setProperty(\"--cyber-border\", \"#e2e8f0\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#0ea5e9\");\n            // Keep cyber colors but adjust opacity for light mode\n            root.style.setProperty(\"--cyber-primary\", \"#0ea5e9\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ec4899\");\n            root.style.setProperty(\"--cyber-accent\", \"#f59e0b\");\n        } else {\n            // Dark theme variables (cyberpunk)\n            root.style.setProperty(\"--cyber-bg-primary\", \"#0a0a0f\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#1a1a2e\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#16213e\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#0f0f23\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#1e1e3f\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#b0b0b0\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#808080\");\n            root.style.setProperty(\"--cyber-border\", \"#333366\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-primary\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ff0080\");\n            root.style.setProperty(\"--cyber-accent\", \"#ffff00\");\n        }\n        // Save to localStorage\n        localStorage.setItem(\"theme\", theme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === \"dark\" ? \"light\" : \"dark\");\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 95,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Hook for theme-aware styling\nfunction useThemeClasses() {\n    const { theme } = useTheme();\n    return {\n        // Background classes\n        bgPrimary: theme === \"dark\" ? \"bg-cyber-dark\" : \"bg-white\",\n        bgSecondary: theme === \"dark\" ? \"bg-cyber-secondary\" : \"bg-gray-50\",\n        bgCard: theme === \"dark\" ? \"bg-cyber-card\" : \"bg-white\",\n        // Text classes\n        textPrimary: theme === \"dark\" ? \"text-white\" : \"text-gray-900\",\n        textSecondary: theme === \"dark\" ? \"text-gray-300\" : \"text-gray-600\",\n        textMuted: theme === \"dark\" ? \"text-gray-400\" : \"text-gray-500\",\n        // Border classes\n        border: theme === \"dark\" ? \"border-gray-700\" : \"border-gray-200\",\n        borderBright: theme === \"dark\" ? \"border-cyber-primary\" : \"border-blue-500\",\n        // Button classes\n        btnPrimary: theme === \"dark\" ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\",\n        btnSecondary: theme === \"dark\" ? \"btn-cyber-secondary\" : \"bg-gray-200 hover:bg-gray-300 text-gray-900\",\n        // Input classes\n        input: theme === \"dark\" ? \"input-cyber\" : \"bg-white border-gray-300 text-gray-900 focus:border-blue-500\",\n        // Card classes\n        card: theme === \"dark\" ? \"card-cyber\" : \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n        // Effects\n        glow: theme === \"dark\" ? \"animate-cyber-glow\" : \"\",\n        pulse: theme === \"dark\" ? \"animate-cyber-pulse\" : \"\",\n        // Theme identifier\n        isDark: theme === \"dark\",\n        isLight: theme === \"light\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Activity,Award,Calendar,Discord,ExternalLink,Github,MapPin,MessageSquare,Shield,Target,Telegram,Trophy,Twitter,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Activity: () => (/* reexport safe */ _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   Award: () => (/* reexport safe */ _icons_award_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   ExternalLink: () => (/* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   MapPin: () => (/* reexport safe */ _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_7__["default"]),
/* harmony export */   Target: () => (/* reexport safe */ _icons_target_js__WEBPACK_IMPORTED_MODULE_8__["default"]),
/* harmony export */   Trophy: () => (/* reexport safe */ _icons_trophy_js__WEBPACK_IMPORTED_MODULE_9__["default"]),
/* harmony export */   Twitter: () => (/* reexport safe */ _icons_twitter_js__WEBPACK_IMPORTED_MODULE_10__["default"]),
/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_11__["default"]),
/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_12__["default"])
/* harmony export */ });
/* harmony import */ var _icons_activity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/activity.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js");
/* harmony import */ var _icons_award_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/award.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js");
/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/calendar.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js");
/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/external-link.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js");
/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/github.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js");
/* harmony import */ var _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/map-pin.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js");
/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/message-square.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js");
/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/shield.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js");
/* harmony import */ var _icons_target_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/target.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js");
/* harmony import */ var _icons_trophy_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/trophy.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js");
/* harmony import */ var _icons_twitter_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/twitter.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js");
/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/users.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js");
/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/zap.js */ "(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js");















/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6662484993ed\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vYXBwL2dsb2JhbHMuY3NzPzY0YTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NjYyNDg0OTkzZWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\app\community\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(rsc)/./components/Toast.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Cybersecurity & Bug Hunting Platform\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan komunitas Bug Hunter\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} scrollbar-cyber`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   toast: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#useToast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#toast`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1),
/* harmony export */   useThemeClasses: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useTheme`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useThemeClasses`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunity%2Fpage&page=%2Fcommunity%2Fpage&appPaths=%2Fcommunity%2Fpage&pagePath=private-next-app-dir%2Fcommunity%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();