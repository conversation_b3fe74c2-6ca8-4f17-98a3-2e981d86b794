'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading, LoadingOverlay } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  Search, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Eye,
  Download,
  Filter,
  History,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Shield,
  Target
} from 'lucide-react'

interface OSINTQuery {
  id: string
  type: 'email' | 'phone' | 'nik' | 'npwp' | 'name' | 'domain' | 'imei'
  target: string
  status: 'pending' | 'completed' | 'failed'
  results: any
  createdAt: string
  sources: string[]
}

export default function OSINTPage() {
  const [activeTab, setActiveTab] = useState('search')
  const [searchType, setSearchType] = useState<OSINTQuery['type']>('email')
  const [searchTarget, setSearchTarget] = useState('')
  const [selectedSources, setSelectedSources] = useState<string[]>(['breaches', 'social', 'domains'])
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<any>(null)
  const [queryHistory, setQueryHistory] = useState<OSINTQuery[]>([])
  const [showResultModal, setShowResultModal] = useState(false)
  const [selectedResult, setSelectedResult] = useState<OSINTQuery | null>(null)
  const [stats, setStats] = useState({
    totalQueries: 0,
    successfulQueries: 0,
    dataBreaches: 0,
    socialProfiles: 0
  })

  const { success, error, warning } = useToast()

  useEffect(() => {
    loadStats()
    loadHistory()
  }, [])

  const loadStats = async () => {
    // Simulate loading stats
    setTimeout(() => {
      setStats({
        totalQueries: 1247,
        successfulQueries: 1156,
        dataBreaches: 89,
        socialProfiles: 234
      })
    }, 1000)
  }

  const loadHistory = async () => {
    // Simulate loading history
    setTimeout(() => {
      setQueryHistory([
        {
          id: '1',
          type: 'email',
          target: '<EMAIL>',
          status: 'completed',
          results: { breaches: 2, social: 1, domains: 0 },
          createdAt: '2025-01-14T10:30:00Z',
          sources: ['breaches', 'social']
        },
        {
          id: '2',
          type: 'phone',
          target: '+628123456789',
          status: 'completed',
          results: { location: 'Jakarta', carrier: 'Telkomsel', social: 1 },
          createdAt: '2025-01-14T09:15:00Z',
          sources: ['location', 'carrier', 'social']
        },
        {
          id: '3',
          type: 'domain',
          target: 'example.com',
          status: 'failed',
          results: null,
          createdAt: '2025-01-14T08:45:00Z',
          sources: ['whois', 'subdomains']
        }
      ])
    }, 1500)
  }

  const searchTypes = [
    { value: 'email', label: 'Email Address', icon: Mail, description: 'Search for email in breaches, social media, and domains' },
    { value: 'phone', label: 'Phone Number', icon: Phone, description: 'Lookup phone number location, carrier, and social profiles' },
    { value: 'nik', label: 'NIK (KTP)', icon: User, description: 'Search Indonesian ID number in leaked databases' },
    { value: 'npwp', label: 'NPWP', icon: User, description: 'Search tax ID in government and leaked databases' },
    { value: 'name', label: 'Full Name', icon: User, description: 'Search person by name across multiple sources' },
    { value: 'domain', label: 'Domain', icon: Globe, description: 'Domain WHOIS, subdomains, and security analysis' },
    { value: 'imei', label: 'IMEI', icon: Phone, description: 'Device tracking and information lookup' }
  ]

  const dataSources = [
    { id: 'breaches', name: 'Data Breaches', description: 'Check against known data breaches' },
    { id: 'social', name: 'Social Media', description: 'Search social media profiles' },
    { id: 'domains', name: 'Domain Records', description: 'WHOIS and DNS information' },
    { id: 'government', name: 'Government DBs', description: 'Public government databases' },
    { id: 'darkweb', name: 'Dark Web', description: 'Dark web marketplaces and forums' },
    { id: 'pastebins', name: 'Pastebins', description: 'Pastebin and text sharing sites' }
  ]

  const handleSearch = async () => {
    if (!searchTarget.trim()) {
      warning('Please enter a search target')
      return
    }

    if (selectedSources.length === 0) {
      warning('Please select at least one data source')
      return
    }

    setIsSearching(true)
    setSearchResults(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Mock results based on search type
      const mockResults = generateMockResults(searchType, searchTarget)
      
      setSearchResults(mockResults)
      success('Search completed successfully!')

      // Add to history
      const newQuery: OSINTQuery = {
        id: Date.now().toString(),
        type: searchType,
        target: searchTarget,
        status: 'completed',
        results: mockResults,
        createdAt: new Date().toISOString(),
        sources: selectedSources
      }
      
      setQueryHistory(prev => [newQuery, ...prev])

    } catch (err) {
      error('Search failed. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  const generateMockResults = (type: string, target: string) => {
    switch (type) {
      case 'email':
        return {
          breaches: [
            { name: 'LinkedIn 2021', date: '2021-06-01', records: 700000000 },
            { name: 'Facebook 2019', date: '2019-04-03', records: 533000000 }
          ],
          social: [
            { platform: 'Twitter', username: target.split('@')[0], verified: false },
            { platform: 'LinkedIn', username: target.split('@')[0], verified: true }
          ],
          domains: [
            { domain: target.split('@')[1], registered: '2010-01-15', expires: '2025-01-15' }
          ]
        }
      case 'phone':
        return {
          location: { country: 'Indonesia', region: 'Jakarta', city: 'Central Jakarta' },
          carrier: { name: 'Telkomsel', type: 'Mobile', country: 'ID' },
          social: [
            { platform: 'WhatsApp', active: true, lastSeen: '2025-01-14' }
          ]
        }
      case 'domain':
        return {
          whois: {
            registrar: 'GoDaddy',
            registered: '2010-01-15',
            expires: '2025-01-15',
            nameservers: ['ns1.example.com', 'ns2.example.com']
          },
          subdomains: ['www.example.com', 'mail.example.com', 'ftp.example.com'],
          security: {
            ssl: true,
            dnssec: false,
            reputation: 'clean'
          }
        }
      default:
        return { message: 'No specific results for this search type yet' }
    }
  }

  const historyColumns = [
    {
      key: 'type',
      label: 'Type',
      render: (value: string) => (
        <span className="capitalize font-medium">{value}</span>
      )
    },
    {
      key: 'target',
      label: 'Target',
      render: (value: string) => (
        <span className="font-mono text-sm">{value}</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <span className={`status-badge ${
          value === 'completed' ? 'status-success' :
          value === 'failed' ? 'status-error' : 'status-warning'
        }`}>
          {value === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
          {value === 'failed' && <AlertTriangle className="h-3 w-3 mr-1" />}
          {value === 'pending' && <Clock className="h-3 w-3 mr-1" />}
          {value}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Date',
      render: (value: string) => new Date(value).toLocaleDateString()
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: OSINTQuery) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedResult(row)
              setShowResultModal(true)
            }}
            className="text-primary-600 hover:text-primary-700"
            disabled={row.status !== 'completed'}
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => downloadResults(row)}
            className="text-green-600 hover:text-green-700"
            disabled={row.status !== 'completed'}
          >
            <Download className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  const downloadResults = (query: OSINTQuery) => {
    const data = JSON.stringify(query.results, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `osint-${query.type}-${query.target}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    success('Results downloaded successfully!')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Search className="h-6 w-6 mr-2 text-primary-600" />
              OSINT Investigator
            </h1>
            <p className="text-gray-600 mt-1">
              Investigate and gather intelligence from multiple data sources
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('search')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'search'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Target className="h-4 w-4 mr-2 inline" />
                Search
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'history'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <History className="h-4 w-4 mr-2 inline" />
                History
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Queries"
            value={stats.totalQueries}
            icon={Database}
            color="blue"
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="Successful"
            value={stats.successfulQueries}
            icon={CheckCircle}
            color="green"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Data Breaches"
            value={stats.dataBreaches}
            icon={Shield}
            color="red"
            trend={{ value: 3, isPositive: false }}
          />
          <StatsCard
            title="Social Profiles"
            value={stats.socialProfiles}
            icon={User}
            color="purple"
            trend={{ value: 15, isPositive: true }}
          />
        </div>

        {/* Main Content */}
        {activeTab === 'search' ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Search Form */}
            <div className="lg:col-span-2">
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  New Investigation
                </h3>

                {/* Search Type Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Investigation Type
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {searchTypes.map((type) => {
                      const Icon = type.icon
                      return (
                        <button
                          key={type.value}
                          onClick={() => setSearchType(type.value as OSINTQuery['type'])}
                          className={`p-3 border rounded-lg text-left transition-colors ${
                            searchType === type.value
                              ? 'border-primary-500 bg-primary-50'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className={`h-5 w-5 ${
                              searchType === type.value ? 'text-primary-600' : 'text-gray-400'
                            }`} />
                            <div>
                              <div className="font-medium text-gray-900">{type.label}</div>
                              <div className="text-xs text-gray-500">{type.description}</div>
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Search Target */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target {searchTypes.find(t => t.value === searchType)?.label}
                  </label>
                  <input
                    type="text"
                    value={searchTarget}
                    onChange={(e) => setSearchTarget(e.target.value)}
                    placeholder={`Enter ${searchTypes.find(t => t.value === searchType)?.label.toLowerCase()}`}
                    className="input-field"
                  />
                </div>

                {/* Data Sources */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Data Sources
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {dataSources.map((source) => (
                      <label
                        key={source.id}
                        className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                      >
                        <input
                          type="checkbox"
                          checked={selectedSources.includes(source.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedSources(prev => [...prev, source.id])
                            } else {
                              setSelectedSources(prev => prev.filter(s => s !== source.id))
                            }
                          }}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <div>
                          <div className="font-medium text-gray-900">{source.name}</div>
                          <div className="text-xs text-gray-500">{source.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Search Button */}
                <button
                  onClick={handleSearch}
                  disabled={isSearching || !searchTarget.trim()}
                  className="btn-primary w-full"
                >
                  {isSearching ? (
                    <div className="flex items-center justify-center space-x-2">
                      <Loading size="sm" />
                      <span>Investigating...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-2">
                      <Search className="h-4 w-4" />
                      <span>Start Investigation</span>
                    </div>
                  )}
                </button>
              </Card>
            </div>

            {/* Results */}
            <div>
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Results
                </h3>
                
                {isSearching ? (
                  <div className="text-center py-8">
                    <Loading size="lg" text="Searching..." />
                    <p className="text-sm text-gray-500 mt-2">
                      This may take a few moments
                    </p>
                  </div>
                ) : searchResults ? (
                  <div className="space-y-4">
                    {Object.entries(searchResults).map(([key, value]) => (
                      <div key={key} className="border rounded-lg p-3">
                        <h4 className="font-medium text-gray-900 capitalize mb-2">{key}</h4>
                        <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto">
                          {JSON.stringify(value, null, 2)}
                        </pre>
                      </div>
                    ))}
                    <button
                      onClick={() => downloadResults({
                        id: 'current',
                        type: searchType,
                        target: searchTarget,
                        status: 'completed',
                        results: searchResults,
                        createdAt: new Date().toISOString(),
                        sources: selectedSources
                      })}
                      className="btn-secondary w-full"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Results
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Start an investigation to see results here</p>
                  </div>
                )}
              </Card>
            </div>
          </div>
        ) : (
          /* History Tab */
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Investigation History
              </h3>
              <button className="btn-secondary">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
            </div>
            
            <DataTable
              columns={historyColumns}
              data={queryHistory}
              searchable
              pagination={{
                currentPage: 1,
                totalPages: 1,
                pageSize: 10,
                totalItems: queryHistory.length,
                onPageChange: () => {}
              }}
            />
          </Card>
        )}

        {/* Result Detail Modal */}
        <Modal
          isOpen={showResultModal}
          onClose={() => setShowResultModal(false)}
          title={`Investigation Results - ${selectedResult?.target}`}
          size="lg"
        >
          {selectedResult && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Type:</span>
                  <span className="ml-2 capitalize">{selectedResult.type}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Date:</span>
                  <span className="ml-2">{new Date(selectedResult.createdAt).toLocaleString()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Sources:</span>
                  <span className="ml-2">{selectedResult.sources.join(', ')}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Status:</span>
                  <span className={`ml-2 status-badge ${
                    selectedResult.status === 'completed' ? 'status-success' : 'status-error'
                  }`}>
                    {selectedResult.status}
                  </span>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-3">Results</h4>
                <pre className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg overflow-x-auto max-h-96">
                  {JSON.stringify(selectedResult.results, null, 2)}
                </pre>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => downloadResults(selectedResult)}
                  className="btn-secondary"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </button>
                <button
                  onClick={() => setShowResultModal(false)}
                  className="btn-primary"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  )
}
