'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card } from '@/components/Card'
import { Modal } from '@/components/Modal'
import { Check, Crown, Zap, Shield, Star } from 'lucide-react'

interface Plan {
  id: string
  name: string
  type: string
  price: number
  currency: string
  duration: string
  features: any
  limits: any
  isPopular: boolean
  description: string
}

export default function PlanPage() {
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [showSubscribeModal, setShowSubscribeModal] = useState(false)
  const [subscribing, setSubscribing] = useState(false)

  useEffect(() => {
    loadPlans()
  }, [])

  const loadPlans = async () => {
    try {
      const response = await fetch('/api/plans')
      const data = await response.json()
      
      if (data.success) {
        setPlans(data.data.plans)
      }
    } catch (error) {
      console.error('Error loading plans:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubscribe = async (paymentMethod: string) => {
    if (!selectedPlan) return

    setSubscribing(true)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/plans/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          planId: selectedPlan.id,
          paymentMethod
        })
      })

      const data = await response.json()
      
      if (data.success) {
        alert('Subscription created successfully! Please check your email for payment instructions.')
        setShowSubscribeModal(false)
      } else {
        alert(data.error || 'Failed to create subscription')
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      alert('Failed to create subscription')
    } finally {
      setSubscribing(false)
    }
  }

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'free': return Shield
      case 'student': return Star
      case 'hobby': return Zap
      case 'bughunter': return Crown
      case 'cybersecurity': return Crown
      default: return Shield
    }
  }

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'free': return 'text-gray-600'
      case 'student': return 'text-blue-600'
      case 'hobby': return 'text-green-600'
      case 'bughunter': return 'text-purple-600'
      case 'cybersecurity': return 'text-orange-600'
      default: return 'text-gray-600'
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency === 'IDR' ? 'IDR' : 'USD',
      minimumFractionDigits: 0
    }).format(price)
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Choose Your Plan</h1>
          <p className="mt-4 text-lg text-gray-600">
            Upgrade your cybersecurity capabilities with our premium plans
          </p>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {plans.map((plan) => {
            const Icon = getPlanIcon(plan.type)
            const colorClass = getPlanColor(plan.type)
            
            return (
              <Card
                key={plan.id}
                className={`relative ${plan.isPopular ? 'ring-2 ring-primary-500' : ''}`}
                hover
              >
                {plan.isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <div className={`inline-flex p-3 rounded-full bg-gray-100 ${colorClass} mb-4`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(plan.price, plan.currency)}
                    </span>
                    <span className="text-gray-600">/{plan.duration}</span>
                  </div>
                  
                  <p className="text-gray-600 mb-6">
                    {plan.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    {plan.features && Object.entries(plan.features).map(([key, value]) => (
                      <div key={key} className="flex items-center text-sm">
                        <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">
                          {key === 'dailyScans' && `${value} daily scans`}
                          {key === 'fileUploadSize' && `${value}MB file upload`}
                          {key === 'apiCallsPerDay' && `${value} API calls/day`}
                          {key === 'osintQueries' && `${value} OSINT queries`}
                          {key === 'botAccess' && value && 'Bot access'}
                          {key === 'prioritySupport' && value && 'Priority support'}
                          {key === 'advancedFeatures' && value && 'Advanced features'}
                          {key === 'customDorks' && value && 'Custom dorks'}
                          {key === 'exportReports' && value && 'Export reports'}
                        </span>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => {
                      setSelectedPlan(plan)
                      setShowSubscribeModal(true)
                    }}
                    disabled={plan.type === 'free'}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                      plan.type === 'free'
                        ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                        : plan.isPopular
                        ? 'bg-primary-600 text-white hover:bg-primary-700'
                        : 'bg-gray-900 text-white hover:bg-gray-800'
                    }`}
                  >
                    {plan.type === 'free' ? 'Current Plan' : 'Subscribe Now'}
                  </button>
                </div>
              </Card>
            )
          })}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-6">
            <Card>
              <h3 className="font-semibold text-gray-900 mb-2">
                Can I change my plan anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
              </p>
            </Card>
            
            <Card>
              <h3 className="font-semibold text-gray-900 mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600">
                We accept manual bank transfer, and various payment gateways including Tripay, Midtrans, and Xendit.
              </p>
            </Card>
            
            <Card>
              <h3 className="font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h3>
              <p className="text-gray-600">
                Yes, you can start with our free plan which includes basic features. No credit card required.
              </p>
            </Card>
          </div>
        </div>
      </div>

      {/* Subscribe Modal */}
      <Modal
        isOpen={showSubscribeModal}
        onClose={() => setShowSubscribeModal(false)}
        title={`Subscribe to ${selectedPlan?.name}`}
        size="md"
      >
        {selectedPlan && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {selectedPlan.name} Plan
              </h3>
              <div className="text-2xl font-bold text-gray-900">
                {formatPrice(selectedPlan.price, selectedPlan.currency)}
                <span className="text-sm font-normal text-gray-600">
                  /{selectedPlan.duration}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Choose Payment Method:</h4>
              
              <button
                onClick={() => handleSubscribe('manual_transfer')}
                disabled={subscribing}
                className="w-full p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left disabled:opacity-50"
              >
                <div className="font-medium text-gray-900">Manual Bank Transfer</div>
                <div className="text-sm text-gray-600">
                  Transfer to our bank account and confirm payment
                </div>
              </button>

              <button
                onClick={() => handleSubscribe('tripay')}
                disabled={subscribing}
                className="w-full p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left disabled:opacity-50"
              >
                <div className="font-medium text-gray-900">Tripay Gateway</div>
                <div className="text-sm text-gray-600">
                  Pay with various methods via Tripay
                </div>
              </button>

              <button
                onClick={() => handleSubscribe('midtrans')}
                disabled={subscribing}
                className="w-full p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left disabled:opacity-50"
              >
                <div className="font-medium text-gray-900">Midtrans</div>
                <div className="text-sm text-gray-600">
                  Credit card, bank transfer, e-wallet
                </div>
              </button>
            </div>

            {subscribing && (
              <div className="text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Creating subscription...</p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </DashboardLayout>
  )
}
