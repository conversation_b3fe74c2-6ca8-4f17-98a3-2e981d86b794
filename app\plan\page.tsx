'use client'

import { useState, useEffect } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { useToast } from '@/components/Toast'
import { Check, Crown, Zap, Shield, Star, Target, Lock, Cpu, Database, Users, ArrowRight } from 'lucide-react'

interface Plan {
  id: string
  name: string
  type: string
  price: number
  currency: string
  duration: string
  features: any
  limits: any
  isPopular: boolean
  description: string
  color: string
  gradient: string
  icon: any
}

export default function PlanPage() {
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const { success, error } = useToast()

  useEffect(() => {
    loadPlans()
  }, [])

  const loadPlans = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockPlans: Plan[] = [
        {
          id: 'free',
          name: 'Rookie',
          type: 'free',
          price: 0,
          currency: 'USD',
          duration: 'month',
          description: 'Perfect for beginners entering the cybersecurity world',
          color: 'text-gray-400',
          gradient: 'from-gray-600 to-gray-800',
          icon: Shield,
          isPopular: false,
          features: {
            osint: '10 queries/day',
            scanner: '5 scans/day',
            fileAnalyzer: '3 files/day',
            cve: 'Basic search',
            dorking: '20 dorks/day',
            tools: 'Basic tools',
            support: 'Community',
            api: false,
            priority: false
          },
          limits: {
            osintQueries: 10,
            scansPerDay: 5,
            filesPerDay: 3,
            apiCalls: 0
          }
        },
        {
          id: 'student',
          name: 'Cadet',
          type: 'student',
          price: 9,
          currency: 'USD',
          duration: 'month',
          description: 'Designed for students and learning enthusiasts',
          color: 'text-cyber-primary',
          gradient: 'from-cyan-500 to-blue-600',
          icon: Target,
          isPopular: true,
          features: {
            osint: '100 queries/day',
            scanner: '50 scans/day',
            fileAnalyzer: '25 files/day',
            cve: 'Advanced search',
            dorking: '200 dorks/day',
            tools: 'All tools',
            support: 'Email support',
            api: '1,000 calls/month',
            priority: false
          },
          limits: {
            osintQueries: 100,
            scansPerDay: 50,
            filesPerDay: 25,
            apiCalls: 1000
          }
        },
        {
          id: 'hobby',
          name: 'Hacker',
          type: 'hobby',
          price: 19,
          currency: 'USD',
          duration: 'month',
          description: 'For serious hobbyists and bug bounty hunters',
          color: 'text-cyber-secondary',
          gradient: 'from-pink-500 to-purple-600',
          icon: Zap,
          isPopular: false,
          features: {
            osint: '500 queries/day',
            scanner: '200 scans/day',
            fileAnalyzer: '100 files/day',
            cve: 'Premium features',
            dorking: '1,000 dorks/day',
            tools: 'Advanced tools',
            support: 'Priority email',
            api: '10,000 calls/month',
            priority: true
          },
          limits: {
            osintQueries: 500,
            scansPerDay: 200,
            filesPerDay: 100,
            apiCalls: 10000
          }
        },
        {
          id: 'bughunter',
          name: 'Elite',
          type: 'bughunter',
          price: 39,
          currency: 'USD',
          duration: 'month',
          description: 'Professional bug hunters and security researchers',
          color: 'text-cyber-accent',
          gradient: 'from-yellow-500 to-orange-600',
          icon: Crown,
          isPopular: false,
          features: {
            osint: '2,000 queries/day',
            scanner: '1,000 scans/day',
            fileAnalyzer: '500 files/day',
            cve: 'All features',
            dorking: '5,000 dorks/day',
            tools: 'Premium tools',
            support: 'Live chat',
            api: '50,000 calls/month',
            priority: true
          },
          limits: {
            osintQueries: 2000,
            scansPerDay: 1000,
            filesPerDay: 500,
            apiCalls: 50000
          }
        },
        {
          id: 'cybersecurity',
          name: 'Matrix',
          type: 'cybersecurity',
          price: 99,
          currency: 'USD',
          duration: 'month',
          description: 'Enterprise-grade for cybersecurity professionals',
          color: 'text-green-400',
          gradient: 'from-green-500 to-emerald-600',
          icon: Database,
          isPopular: false,
          features: {
            osint: 'Unlimited',
            scanner: 'Unlimited',
            fileAnalyzer: 'Unlimited',
            cve: 'Enterprise features',
            dorking: 'Unlimited',
            tools: 'All tools + Custom',
            support: '24/7 phone support',
            api: 'Unlimited',
            priority: true
          },
          limits: {
            osintQueries: -1,
            scansPerDay: -1,
            filesPerDay: -1,
            apiCalls: -1
          }
        }
      ]

      setPlans(mockPlans)
    } catch (err) {
      error('Failed to load plans')
    } finally {
      setLoading(false)
    }
  }

  const handleSubscribe = (plan: Plan) => {
    if (plan.type === 'free') {
      success('Free plan activated! Welcome to KodeXGuard!')
      return
    }
    
    setSelectedPlan(plan)
    // In a real app, this would redirect to payment processor
    success(`Redirecting to payment for ${plan.name} plan...`)
  }

  const getDiscountedPrice = (price: number) => {
    return billingCycle === 'yearly' ? Math.round(price * 0.8) : price
  }

  if (loading) {
    return (
      <PublicLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading cyber plans...</div>
          </div>
        </div>
      </PublicLayout>
    )
  }

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              <span className="text-cyber-glow">Choose Your</span>{' '}
              <span className="text-cyber-pink">Cyber Path</span>
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              Unlock the full potential of cybersecurity tools and join thousands of security professionals worldwide
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center space-x-4 mb-8">
              <span className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-cyber-primary' : 'text-gray-400'}`}>
                Monthly
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2"
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-cyber-primary transition-transform ${
                    billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-sm font-medium ${billingCycle === 'yearly' ? 'text-cyber-primary' : 'text-gray-400'}`}>
                Yearly
              </span>
              {billingCycle === 'yearly' && (
                <span className="bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-medium">
                  Save 20%
                </span>
              )}
            </div>
          </div>

          {/* Plans Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {plans.map((plan, index) => {
              const Icon = plan.icon
              const price = getDiscountedPrice(plan.price)
              
              return (
                <div
                  key={plan.id}
                  className={`relative card-cyber transition-all duration-300 ${
                    plan.isPopular 
                      ? 'border-cyber-primary shadow-lg shadow-cyber-primary/20 scale-105' 
                      : 'hover:border-cyber-primary/50'
                  }`}
                >
                  {/* Popular Badge */}
                  {plan.isPopular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-cyber-primary text-black px-4 py-1 rounded-full text-xs font-bold uppercase tracking-wider">
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Plan Header */}
                  <div className="text-center mb-6">
                    <div className={`inline-flex p-4 rounded-lg bg-gradient-to-r ${plan.gradient} mb-4 animate-cyber-pulse`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h3 className={`text-2xl font-bold ${plan.color} mb-2`}>
                      {plan.name}
                    </h3>
                    
                    <p className="text-gray-400 text-sm mb-4">
                      {plan.description}
                    </p>

                    {/* Price */}
                    <div className="mb-6">
                      {plan.price === 0 ? (
                        <div className="text-3xl font-bold text-white">Free</div>
                      ) : (
                        <div>
                          <div className="text-3xl font-bold text-white">
                            ${price}
                            <span className="text-lg text-gray-400">/{billingCycle === 'yearly' ? 'year' : 'month'}</span>
                          </div>
                          {billingCycle === 'yearly' && plan.price > 0 && (
                            <div className="text-sm text-gray-500 line-through">
                              ${plan.price * 12}/year
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">OSINT: {plan.features.osint}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">Scanner: {plan.features.scanner}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">File Analyzer: {plan.features.fileAnalyzer}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">CVE: {plan.features.cve}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">Dorking: {plan.features.dorking}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">Tools: {plan.features.tools}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-gray-300">Support: {plan.features.support}</span>
                    </div>
                    {plan.features.api && (
                      <div className="flex items-center space-x-3">
                        <Check className="h-4 w-4 text-green-400" />
                        <span className="text-sm text-gray-300">API: {plan.features.api}</span>
                      </div>
                    )}
                  </div>

                  {/* CTA Button */}
                  <button
                    onClick={() => handleSubscribe(plan)}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                      plan.isPopular
                        ? 'btn-cyber-primary'
                        : plan.type === 'free'
                        ? 'btn-cyber-secondary'
                        : 'border-2 border-gray-600 text-gray-300 hover:border-cyber-primary hover:text-cyber-primary'
                    }`}
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <span>{plan.type === 'free' ? 'Get Started' : 'Upgrade Now'}</span>
                      <ArrowRight className="h-4 w-4" />
                    </div>
                  </button>
                </div>
              )
            })}
          </div>

          {/* FAQ Section */}
          <div className="mt-20">
            <h2 className="text-3xl font-bold text-center text-white mb-12">
              <span className="text-cyber-glow">Frequently Asked</span>{' '}
              <span className="text-cyber-pink">Questions</span>
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="card-cyber">
                <h3 className="text-lg font-semibold text-cyber-primary mb-3">
                  Can I upgrade or downgrade anytime?
                </h3>
                <p className="text-gray-400">
                  Yes, you can change your plan at any time. Changes take effect immediately, 
                  and we'll prorate any billing differences.
                </p>
              </div>
              
              <div className="card-cyber">
                <h3 className="text-lg font-semibold text-cyber-primary mb-3">
                  Is there a free trial?
                </h3>
                <p className="text-gray-400">
                  Our Rookie plan is completely free forever. You can also try any paid plan 
                  with a 7-day free trial.
                </p>
              </div>
              
              <div className="card-cyber">
                <h3 className="text-lg font-semibold text-cyber-primary mb-3">
                  What payment methods do you accept?
                </h3>
                <p className="text-gray-400">
                  We accept all major credit cards, PayPal, bank transfers, and cryptocurrency 
                  payments for maximum flexibility.
                </p>
              </div>
              
              <div className="card-cyber">
                <h3 className="text-lg font-semibold text-cyber-primary mb-3">
                  Do you offer student discounts?
                </h3>
                <p className="text-gray-400">
                  Yes! Students get 50% off all paid plans with valid student ID verification. 
                  Contact support for details.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
