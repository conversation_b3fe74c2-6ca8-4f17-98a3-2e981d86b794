{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../src/security.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAiBH,oCAwCC;AAQD,4CAiBC;AA7ED,MAAM,UAAU,GAAG;IACjB,eAAe;IACf,UAAU;IACV,QAAQ;IACR,oBAAoB;CACrB,CAAA;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAE,GAAwB,EAAE,iBAA2B,EAAE;IACnF,MAAM,QAAQ,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAA;IACjF,qGAAqG;IACrG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAA;IACtB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;IAEpB,SAAS,QAAQ,CAAE,GAAwB;QACzC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI;YAAE,OAAO,GAAG,CAAA;QAEtD,MAAM,MAAM,GAAwB,EAAE,CAAA;QACtC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC3C,oCAAoC;YACpC,IAAI,KAAK,YAAY,GAAG,EAAE,CAAC;gBACzB,KAAK,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;YAC3D,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACvD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,qCAAqC;oBACrC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;gBACrC,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;wBAC7B,yDAAyD;wBACzD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;wBACrB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;oBACzB,CAAC;yBAAM,CAAC;wBACN,iEAAiE;wBACjE,gDAAgD;wBAChD,KAAK,GAAG,IAAI,CAAA;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,4CAA4C;YAC5C,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YACrB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,MAAM,CAAA;IACf,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAE,IAAsB,EAAE,OAAyB;;IACjF,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,KAAK;YACR,MAAK;QACP,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC,OAAO,CAAA;YACnB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YACtB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAA;YACvC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,CAAA;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YAC3B,MAAK;QACP,KAAK,SAAS;YACZ,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE,MAAA,OAAO,CAAC,cAAc,mCAAI,EAAE,CAAqB,CAAA;YAC3E,MAAK;IACT,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC"}