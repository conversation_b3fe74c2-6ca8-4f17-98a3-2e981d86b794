{"version": 3, "file": "FirefoxLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/FirefoxLauncher.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,4CAAoB;AACpB,0CAAoD;AACpD,4CAAoB;AACpB,gDAAwB;AAExB,kDAM6B;AAE7B,+CAA6C;AAC7C,iDAAyC;AAMzC,6DAA8E;AAE9E,wCAAgC;AAEhC;;GAEG;AACH,MAAa,eAAgB,SAAQ,oCAAe;IAClD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,cAAc,CACnB,iBAA2C,EAC3C,QAAkC;QAElC,OAAO;YACL,GAAG,iBAAiB;YACpB,GAAG,CAAC,QAAQ,KAAK,eAAe;gBAC9B,CAAC,CAAC;oBACE,0CAA0C;oBAC1C,yBAAyB,EAAE,CAAC;iBAC7B;gBACH,CAAC,CAAC;oBACE,wDAAwD;oBACxD,qCAAqC,EAAE,KAAK;oBAC5C,+CAA+C;oBAC/C,yEAAyE;oBACzE,+BAA+B,EAAE,CAAC;oBAClC,2EAA2E;oBAC3E,yBAAyB,EAAE,KAAK;oBAChC,+BAA+B;oBAC/B,yBAAyB,EAAE,CAAC;iBAC7B,CAAC;YACN,sEAAsE;YACtE,sEAAsE;YACtE,sEAAsE;YACtE,sBAAsB;YACtB,uDAAuD;YACvD,qCAAqC,EAAE,CAAC;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAsC,EAAE;QAExC,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,cAAc,EACd,IAAI,GAAG,KAAK,EACZ,iBAAiB,GAAG,EAAE,EACtB,aAAa,GAAG,IAAI,GACrB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC5C,gBAAgB,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,IACE,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,IAAA,kBAAM,EACJ,aAAa,KAAK,IAAI,EACtB,2EAA2E,CAC5E,CAAC;YACJ,CAAC;YACD,gBAAgB,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,WAA+B,CAAC;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAE7B,gEAAgE;QAChE,gEAAgE;QAChE,MAAM,eAAe,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvD,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3B,WAAW,GAAG,gBAAgB,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;YAED,+DAA+D;YAC/D,6BAA6B;YAC7B,iBAAiB,GAAG,KAAK,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,MAAM,IAAA,kBAAO,EAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACnD,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,IAAA,wBAAa,EAAC,kBAAiB,CAAC,OAAO,EAAE;YAC7C,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,eAAe,CAAC,cAAc,CACzC,iBAAiB,EACjB,OAAO,CAAC,QAAQ,CACjB;SACF,CAAC,CAAC;QAEH,IAAI,iBAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,cAAc,EAAE,CAAC;YACtD,IAAA,kBAAM,EACJ,cAAc,EACd,gEAAgE,CACjE,CAAC;YACF,iBAAiB,GAAG,cAAc,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,WAAW;YACX,IAAI,EAAE,gBAAgB;YACtB,cAAc,EAAE,iBAAiB;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,WAAmB,EACnB,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,IAAA,UAAE,EAAC,WAAW,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,8DAA8D;gBAC9D,8DAA8D;gBAC9D,MAAM,IAAA,iBAAM,EAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;gBAEhD,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;gBACrE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;oBACnC,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;oBACrD,MAAM,IAAA,iBAAM,EAAC,SAAS,CAAC,CAAC;oBACxB,MAAM,IAAA,iBAAM,EAAC,eAAe,EAAE,SAAS,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAEQ,cAAc;QACrB,+DAA+D;QAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,gBAAK,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAoB,CAAC,CAAC;YAC7D,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACnE,OAAO,CACL,OAAO,CAAC,QAAQ,KAAK,IAAA,gCAAqB,GAAE;oBAC5C,OAAO,CAAC,OAAO,KAAK,kBAAO,CAAC,OAAO,CACpC,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,OAAO,CAAC;YACxD,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACtC,CAAC;IAEQ,WAAW,CAAC,UAAwC,EAAE;QAC7D,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GAAG,IAAI,GACnB,GAAG,OAAO,CAAC;QAEZ,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,QAAQ,YAAE,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,KAAK,QAAQ;gBACX,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACtC,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC5C,MAAM;QACV,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF,CAAC;YACD,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;QACD,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF;AAxND,0CAwNC"}