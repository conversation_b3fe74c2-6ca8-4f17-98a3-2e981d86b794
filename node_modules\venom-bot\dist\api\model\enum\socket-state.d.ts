export declare enum SocketState {
    CONFLICT = "CONFLICT",
    CONNECTED = "CONNECTED",
    DEPRECATED_VERSION = "DEPRECATED_VERSION",
    OPENING = "OPENING",
    PAIRING = "PAIRING",
    PROXYBLOCK = "PROXYBLOCK",
    SMB_TOS_BLOCK = "SMB_TOS_BLOCK",
    TIMEOUT = "TIMEOUT",
    TOS_BLOCK = "TOS_BLOCK",
    UNLAUNCHED = "UNLAUNCHED",
    UNPAIRED = "UNPAIRED",
    UNPAIRED_IDLE = "UNPAIRED_IDLE",
    DISCONNECTED = "DISCONNECTED",
    SYNCING = "SYNCING",
    RESUMING = "RESUMING",
    WITHOUT_INTERNET = "WITHOUT INTERNET"
}
export declare enum SocketStream {
    CONNECTED = "CONNECTED",
    DISCONNECTED = "DISCONNECTED",
    RESUMING = "RESUMING",
    SYNCING = "SYNCING"
}
