import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Removes all elements in the sorted set with scores between min and max.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param min - Minimum score.
     * @param max - Maximum score.
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, min: RedisArgument | number, max: RedisArgument | number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZREMRANGEBYSCORE.d.ts.map