'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import PublicLayout from '@/components/PublicLayout'
import { useToast } from '@/components/Toast'
import { Shield, Eye, EyeOff, Mail, Lock, Zap, Terminal, ArrowRight, AlertTriangle } from 'lucide-react'

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const { success, error: showError } = useToast()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        // Store tokens and user data
        localStorage.setItem('token', data.data.tokens.accessToken)
        localStorage.setItem('refreshToken', data.data.tokens.refreshToken)
        localStorage.setItem('user', JSON.stringify(data.data.user))
        
        success('Login successful! Welcome back, cyber warrior!')
        router.push('/dashboard')
      } else {
        setError(data.error || 'Login failed')
        showError(data.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('Network error. Please try again.')
      showError('Network error. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <PublicLayout showFooter={false}>
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative">
        {/* Cyber Background Effects */}
        <div className="absolute inset-0">
          {/* Animated Grid */}
          <div className="absolute inset-0 opacity-10">
            <div 
              className="w-full h-full"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(0, 255, 255, 0.3) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(0, 255, 255, 0.3) 1px, transparent 1px)
                `,
                backgroundSize: '50px 50px'
              }}
            ></div>
          </div>
          
          {/* Floating Elements */}
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-cyber-primary rounded-full animate-matrix-rain opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            ></div>
          ))}
        </div>

        <div className="max-w-md w-full space-y-8 relative z-10">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="relative group">
                <Shield className="h-16 w-16 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-xl animate-cyber-pulse"></div>
              </div>
            </div>
            
            <h2 className="text-4xl font-bold text-white mb-2">
              <span className="text-cyber-glow">Access</span> <span className="text-cyber-pink">Granted</span>
            </h2>
            <p className="text-gray-400 text-lg">
              Enter the cybersecurity matrix
            </p>
          </div>

          {/* Login Form */}
          <div className="card-cyber">
            <form className="space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="bg-red-900/50 border border-red-500 rounded-lg p-4 flex items-center space-x-3 animate-fade-in-up">
                  <AlertTriangle className="h-5 w-5 text-red-400" />
                  <span className="text-red-400 text-sm">{error}</span>
                </div>
              )}

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="input-cyber pl-10"
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="input-cyber pl-10 pr-10"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-500 hover:text-cyber-primary transition-colors duration-200" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-500 hover:text-cyber-primary transition-colors duration-200" />
                    )}
                  </button>
                </div>
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-cyber-primary focus:ring-cyber-primary border-gray-600 rounded bg-gray-800"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-400">
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <Link
                    href="/forgot-password"
                    className="text-cyber-primary hover:text-cyber-secondary transition-colors duration-200"
                  >
                    Forgot password?
                  </Link>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loading}
                className="w-full btn-cyber-primary text-lg py-4 relative overflow-hidden group"
              >
                {loading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    <span>Accessing...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Terminal className="h-5 w-5" />
                    <span>Access System</span>
                    <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                )}
              </button>
            </form>

            {/* Divider */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-700"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-cyber-card text-gray-400">New to the matrix?</span>
                </div>
              </div>
            </div>

            {/* Register Link */}
            <div className="mt-6 text-center">
              <Link
                href="/register"
                className="btn-cyber-secondary w-full"
              >
                <Zap className="h-5 w-5 mr-2" />
                Join the Cyber Army
              </Link>
            </div>
          </div>

          {/* Demo Credentials */}
          <div className="card-cyber">
            <h3 className="text-lg font-semibold text-cyber-primary mb-4 flex items-center">
              <Terminal className="h-5 w-5 mr-2" />
              Demo Access
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Admin:</span>
                <code className="text-cyber-accent bg-gray-800 px-2 py-1 rounded"><EMAIL></code>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Password:</span>
                <code className="text-cyber-accent bg-gray-800 px-2 py-1 rounded">admin123</code>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="text-center text-xs text-gray-500">
            <p>
              Protected by advanced encryption and cybersecurity protocols.
              <br />
              Your data is secured with military-grade protection.
            </p>
          </div>
        </div>

        {/* Cyber Scan Lines */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-50 animate-cyber-scan"></div>
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyber-secondary to-transparent opacity-50 animate-cyber-scan" style={{ animationDelay: '1s' }}></div>
      </div>
    </PublicLayout>
  )
}
