'use client'

import { useState, useEffect } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { 
  Trophy, 
  Medal, 
  Star,
  Crown,
  Target,
  Zap,
  Shield,
  Bug,
  TrendingUp,
  Calendar,
  Filter,
  Award,
  Users,
  Activity,
  Search,
  Globe,
  Database,
  Eye,
  Flame,
  Sparkles,
  ChevronUp,
  ChevronDown,
  Clock,
  MapPin
} from 'lucide-react'

interface LeaderboardUser {
  id: string
  rank: number
  username: string
  fullName: string
  avatar?: string
  score: number
  level: number
  badges: Badge[]
  stats: UserStats
  country?: string
  joinedAt: string
  lastActive: string
  tier: 'legend' | 'elite' | 'expert' | 'advanced' | 'intermediate' | 'beginner'
  streak: number
  monthlyRank?: number
  weeklyRank?: number
}

interface Badge {
  id: string
  name: string
  description: string
  icon: string
  color: string
  earnedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

interface UserStats {
  vulnerabilitiesFound: number
  scansCompleted: number
  osintQueries: number
  cveReported: number
  toolsUsed: number
  daysActive: number
  pointsThisWeek: number
  pointsThisMonth: number
}

export default function LeaderboardPage() {
  const [activeTab, setActiveTab] = useState('overall')
  const [timeframe, setTimeframe] = useState('all')
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  const stats = {
    totalUsers: 15420,
    activeToday: 1247,
    topScore: 15420,
    averageScore: 2847
  }

  useEffect(() => {
    loadLeaderboard()
  }, [activeTab, timeframe])

  const loadLeaderboard = async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockUsers: LeaderboardUser[] = [
        {
          id: '1',
          rank: 1,
          username: 'CyberNinja',
          fullName: 'Alex Chen',
          avatar: '/api/placeholder/64/64',
          score: 15420,
          level: 42,
          tier: 'legend',
          streak: 89,
          monthlyRank: 1,
          weeklyRank: 2,
          badges: [
            { id: '1', name: 'Bug Hunter', description: 'Found 100+ vulnerabilities', icon: 'bug', color: 'red', earnedAt: '2024-12-01', rarity: 'legendary' },
            { id: '2', name: 'OSINT Master', description: 'Completed 1000+ OSINT queries', icon: 'search', color: 'blue', earnedAt: '2024-11-15', rarity: 'epic' }
          ],
          stats: {
            vulnerabilitiesFound: 156,
            scansCompleted: 1247,
            osintQueries: 2341,
            cveReported: 12,
            toolsUsed: 45,
            daysActive: 234,
            pointsThisWeek: 1250,
            pointsThisMonth: 4890
          },
          country: 'Singapore',
          joinedAt: '2024-01-15',
          lastActive: '2025-01-14T15:30:00Z'
        },
        {
          id: '2',
          rank: 2,
          username: 'SecurityMaster',
          fullName: 'Sarah Johnson',
          avatar: '/api/placeholder/64/64',
          score: 14890,
          level: 39,
          tier: 'elite',
          streak: 67,
          monthlyRank: 2,
          weeklyRank: 1,
          badges: [
            { id: '3', name: 'Scanner Pro', description: 'Completed 500+ scans', icon: 'shield', color: 'green', earnedAt: '2024-12-10', rarity: 'epic' }
          ],
          stats: {
            vulnerabilitiesFound: 142,
            scansCompleted: 1156,
            osintQueries: 1987,
            cveReported: 8,
            toolsUsed: 38,
            daysActive: 198,
            pointsThisWeek: 1180,
            pointsThisMonth: 4567
          },
          country: 'United States',
          joinedAt: '2024-02-01',
          lastActive: '2025-01-14T14:20:00Z'
        },
        {
          id: '3',
          rank: 3,
          username: 'PentestPro',
          fullName: 'David Kim',
          avatar: '/api/placeholder/64/64',
          score: 13567,
          level: 36,
          tier: 'elite',
          streak: 45,
          monthlyRank: 3,
          weeklyRank: 4,
          badges: [
            { id: '4', name: 'CVE Hunter', description: 'Reported 5+ CVEs', icon: 'database', color: 'purple', earnedAt: '2024-11-20', rarity: 'rare' }
          ],
          stats: {
            vulnerabilitiesFound: 134,
            scansCompleted: 987,
            osintQueries: 1654,
            cveReported: 15,
            toolsUsed: 42,
            daysActive: 187,
            pointsThisWeek: 980,
            pointsThisMonth: 3890
          },
          country: 'South Korea',
          joinedAt: '2024-03-10',
          lastActive: '2025-01-14T13:45:00Z'
        }
      ]

      // Add more mock users for demonstration
      for (let i = 4; i <= 20; i++) {
        mockUsers.push({
          id: i.toString(),
          rank: i,
          username: `User${i}`,
          fullName: `User ${i}`,
          score: Math.floor(Math.random() * 10000) + 5000,
          level: Math.floor(Math.random() * 30) + 10,
          tier: i <= 5 ? 'elite' : i <= 10 ? 'expert' : i <= 15 ? 'advanced' : 'intermediate',
          streak: Math.floor(Math.random() * 50) + 1,
          monthlyRank: i + Math.floor(Math.random() * 5) - 2,
          weeklyRank: i + Math.floor(Math.random() * 10) - 5,
          badges: [],
          stats: {
            vulnerabilitiesFound: Math.floor(Math.random() * 100) + 10,
            scansCompleted: Math.floor(Math.random() * 500) + 100,
            osintQueries: Math.floor(Math.random() * 1000) + 200,
            cveReported: Math.floor(Math.random() * 10),
            toolsUsed: Math.floor(Math.random() * 30) + 10,
            daysActive: Math.floor(Math.random() * 200) + 50,
            pointsThisWeek: Math.floor(Math.random() * 800) + 200,
            pointsThisMonth: Math.floor(Math.random() * 3000) + 1000
          },
          country: 'Global',
          joinedAt: '2024-01-01',
          lastActive: '2025-01-14T12:00:00Z'
        })
      }

      setLeaderboard(mockUsers)
    } catch (err) {
      console.error('Failed to load leaderboard:', err)
    } finally {
      setLoading(false)
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'legend': return 'text-yellow-400 bg-yellow-400/20'
      case 'elite': return 'text-purple-400 bg-purple-400/20'
      case 'expert': return 'text-blue-400 bg-blue-400/20'
      case 'advanced': return 'text-green-400 bg-green-400/20'
      case 'intermediate': return 'text-orange-400 bg-orange-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-6 w-6 text-yellow-400" />
    if (rank === 2) return <Medal className="h-6 w-6 text-gray-300" />
    if (rank === 3) return <Award className="h-6 w-6 text-orange-400" />
    return <span className="text-lg font-bold text-cyber-primary">#{rank}</span>
  }

  const filteredLeaderboard = leaderboard.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.fullName.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <PublicLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading cyber warriors...</div>
          </div>
        </div>
      </PublicLayout>
    )
  }

  return (
    <PublicLayout>
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-cyber-glow">Cyber</span>{' '}
              <span className="text-cyber-pink">Leaderboard</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Compete with the world's best cybersecurity professionals and climb the ranks
            </p>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="card-cyber text-center">
              <Users className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
              <div className="text-2xl font-bold text-white mb-1">
                {stats.totalUsers.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Total Warriors</div>
            </div>
            <div className="card-cyber text-center">
              <Activity className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
              <div className="text-2xl font-bold text-white mb-1">
                {stats.activeToday.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Active Today</div>
            </div>
            <div className="card-cyber text-center">
              <Trophy className="h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse" />
              <div className="text-2xl font-bold text-white mb-1">
                {stats.topScore.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Top Score</div>
            </div>
            <div className="card-cyber text-center">
              <TrendingUp className="h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse" />
              <div className="text-2xl font-bold text-white mb-1">
                {stats.averageScore.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Average Score</div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-8 space-y-4 md:space-y-0">
            {/* Search Bar */}
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search warriors..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg input-cyber"
              />
            </div>

            {/* Timeframe Filters */}
            <div className="flex space-x-2">
              {[
                { id: 'all', label: 'All Time' },
                { id: 'month', label: 'This Month' },
                { id: 'week', label: 'This Week' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setTimeframe(filter.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    timeframe === filter.id
                      ? 'bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary'
                      : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Top 3 Podium */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">
              <span className="text-cyber-glow">Elite</span>{' '}
              <span className="text-cyber-pink">Champions</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {filteredLeaderboard.slice(0, 3).map((user, index) => (
                <div
                  key={user.id}
                  className={`card-cyber text-center relative overflow-hidden ${
                    index === 0 ? 'md:order-2 transform md:scale-110' :
                    index === 1 ? 'md:order-1' : 'md:order-3'
                  }`}
                >
                  {/* Rank Badge */}
                  <div className="absolute top-4 right-4">
                    {getRankIcon(user.rank)}
                  </div>

                  {/* Avatar */}
                  <div className="relative mx-auto mb-4 w-20 h-20">
                    <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${
                      index === 0 ? 'from-yellow-400 to-yellow-600' :
                      index === 1 ? 'from-gray-300 to-gray-500' :
                      'from-orange-400 to-orange-600'
                    } p-1`}>
                      <div className="w-full h-full rounded-full bg-cyber-dark flex items-center justify-center text-2xl font-bold text-white">
                        {user.username.charAt(0)}
                      </div>
                    </div>
                    {user.streak > 0 && (
                      <div className="absolute -top-2 -right-2 bg-cyber-secondary text-black text-xs font-bold px-2 py-1 rounded-full">
                        🔥{user.streak}
                      </div>
                    )}
                  </div>

                  {/* User Info */}
                  <h3 className="text-xl font-bold text-white mb-1">{user.username}</h3>
                  <p className="text-gray-400 text-sm mb-3">{user.fullName}</p>

                  {/* Tier Badge */}
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wider mb-4 ${getTierColor(user.tier)}`}>
                    <Sparkles className="h-3 w-3 mr-1" />
                    {user.tier}
                  </div>

                  {/* Score */}
                  <div className="text-3xl font-bold text-cyber-primary mb-2">
                    {user.score.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400 mb-4">Level {user.level}</div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-cyber-secondary font-bold">{user.stats.vulnerabilitiesFound}</div>
                      <div className="text-gray-400">Bugs Found</div>
                    </div>
                    <div>
                      <div className="text-cyber-accent font-bold">{user.stats.cveReported}</div>
                      <div className="text-gray-400">CVEs</div>
                    </div>
                  </div>

                  {/* Badges */}
                  {user.badges.length > 0 && (
                    <div className="mt-4 flex justify-center space-x-2">
                      {user.badges.slice(0, 2).map((badge) => (
                        <div
                          key={badge.id}
                          className={`w-8 h-8 rounded-full bg-${badge.color}-500/20 border border-${badge.color}-500 flex items-center justify-center`}
                          title={badge.name}
                        >
                          <Star className={`h-4 w-4 text-${badge.color}-400`} />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Full Leaderboard */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">
                <span className="text-cyber-glow">Full</span>{' '}
                <span className="text-cyber-pink">Rankings</span>
              </h2>
              <div className="text-sm text-gray-400">
                Showing {filteredLeaderboard.length} warriors
              </div>
            </div>

            <div className="space-y-3">
              {filteredLeaderboard.slice(3).map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200 border border-transparent hover:border-cyber-primary/30"
                >
                  {/* Left Side - Rank & User */}
                  <div className="flex items-center space-x-4">
                    <div className="w-12 text-center">
                      {getRankIcon(user.rank)}
                    </div>

                    <div className="w-12 h-12 rounded-full bg-cyber-primary/20 flex items-center justify-center text-lg font-bold text-cyber-primary">
                      {user.username.charAt(0)}
                    </div>

                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-bold text-white">{user.username}</h3>
                        <div className={`px-2 py-1 rounded-full text-xs font-bold ${getTierColor(user.tier)}`}>
                          {user.tier}
                        </div>
                        {user.streak > 0 && (
                          <div className="flex items-center space-x-1 text-cyber-secondary text-xs">
                            <Flame className="h-3 w-3" />
                            <span>{user.streak}</span>
                          </div>
                        )}
                      </div>
                      <p className="text-gray-400 text-sm">Level {user.level} • {user.country}</p>
                    </div>
                  </div>

                  {/* Right Side - Stats */}
                  <div className="flex items-center space-x-8">
                    <div className="text-right">
                      <div className="text-lg font-bold text-cyber-primary">
                        {user.score.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400">Total Points</div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-bold text-cyber-secondary">
                        {user.stats.pointsThisWeek}
                      </div>
                      <div className="text-xs text-gray-400">This Week</div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-bold text-cyber-accent">
                        {user.stats.vulnerabilitiesFound}
                      </div>
                      <div className="text-xs text-gray-400">Bugs</div>
                    </div>

                    {/* Trend Indicator */}
                    <div className="w-8 text-center">
                      {user.weeklyRank && user.weeklyRank < user.rank ? (
                        <ChevronUp className="h-5 w-5 text-green-400" />
                      ) : user.weeklyRank && user.weeklyRank > user.rank ? (
                        <ChevronDown className="h-5 w-5 text-red-400" />
                      ) : (
                        <div className="w-2 h-2 bg-gray-400 rounded-full mx-auto"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  )
}
