'use client'

import { useState, useEffect } from 'react'
import PublicLayout from '@/components/PublicLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Loading } from '@/components/Loading'
import { 
  Trophy, 
  Medal, 
  Star,
  Crown,
  Target,
  Zap,
  Shield,
  Bug,
  TrendingUp,
  Calendar,
  Filter,
  Award,
  Users,
  Activity
} from 'lucide-react'

interface LeaderboardUser {
  id: string
  rank: number
  username: string
  fullName: string
  avatar?: string
  score: number
  level: number
  badges: Badge[]
  stats: UserStats
  country?: string
  joinedAt: string
  lastActive: string
}

interface Badge {
  id: string
  name: string
  description: string
  icon: string
  color: string
  earnedAt: string
}

interface UserStats {
  vulnerabilitiesFound: number
  scansCompleted: number
  osintQueries: number
  cveReported: number
  toolsUsed: number
  daysActive: number
}

export default function LeaderboardPage() {
  const [activeTab, setActiveTab] = useState('overall')
  const [timeframe, setTimeframe] = useState('all')
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([])
  const [loading, setLoading] = useState(true)
  const [currentUser, setCurrentUser] = useState<LeaderboardUser | null>(null)
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeToday: 0,
    topScore: 0,
    averageScore: 0
  })

  useEffect(() => {
    loadLeaderboard()
    loadStats()
  }, [activeTab, timeframe])

  const loadLeaderboard = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const mockUsers: LeaderboardUser[] = [
        {
          id: '1',
          rank: 1,
          username: 'cyberwarrior',
          fullName: 'Alex Chen',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
          score: 15420,
          level: 42,
          badges: [
            { id: '1', name: 'Bug Hunter', description: 'Found 100+ vulnerabilities', icon: 'bug', color: 'red', earnedAt: '2024-12-01' },
            { id: '2', name: 'OSINT Master', description: 'Completed 1000+ OSINT queries', icon: 'search', color: 'blue', earnedAt: '2024-11-15' }
          ],
          stats: {
            vulnerabilitiesFound: 156,
            scansCompleted: 1247,
            osintQueries: 2341,
            cveReported: 12,
            toolsUsed: 45,
            daysActive: 234
          },
          country: 'Singapore',
          joinedAt: '2024-01-15',
          lastActive: '2025-01-14T15:30:00Z'
        },
        {
          id: '2',
          rank: 2,
          username: 'securityninja',
          fullName: 'Sarah Johnson',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
          score: 14890,
          level: 39,
          badges: [
            { id: '3', name: 'Scanner Pro', description: 'Completed 500+ scans', icon: 'shield', color: 'green', earnedAt: '2024-12-10' }
          ],
          stats: {
            vulnerabilitiesFound: 142,
            scansCompleted: 1156,
            osintQueries: 1987,
            cveReported: 8,
            toolsUsed: 38,
            daysActive: 198
          },
          country: 'United States',
          joinedAt: '2024-02-01',
          lastActive: '2025-01-14T14:20:00Z'
        },
        {
          id: '3',
          rank: 3,
          username: 'hackermind',
          fullName: 'David Kim',
          score: 13567,
          level: 36,
          badges: [
            { id: '4', name: 'CVE Hunter', description: 'Reported 5+ CVEs', icon: 'database', color: 'purple', earnedAt: '2024-11-20' }
          ],
          stats: {
            vulnerabilitiesFound: 134,
            scansCompleted: 987,
            osintQueries: 1654,
            cveReported: 15,
            toolsUsed: 42,
            daysActive: 187
          },
          country: 'South Korea',
          joinedAt: '2024-03-10',
          lastActive: '2025-01-14T13:45:00Z'
        }
      ]

      // Add more mock users
      for (let i = 4; i <= 50; i++) {
        mockUsers.push({
          id: i.toString(),
          rank: i,
          username: `user${i}`,
          fullName: `User ${i}`,
          score: Math.floor(Math.random() * 10000) + 1000,
          level: Math.floor(Math.random() * 30) + 1,
          badges: [],
          stats: {
            vulnerabilitiesFound: Math.floor(Math.random() * 100),
            scansCompleted: Math.floor(Math.random() * 500),
            osintQueries: Math.floor(Math.random() * 1000),
            cveReported: Math.floor(Math.random() * 5),
            toolsUsed: Math.floor(Math.random() * 20),
            daysActive: Math.floor(Math.random() * 200)
          },
          joinedAt: '2024-01-01',
          lastActive: new Date().toISOString()
        })
      }

      setLeaderboard(mockUsers)
      
      // Set current user (simulate logged in user)
      setCurrentUser({
        id: 'current',
        rank: 25,
        username: 'currentuser',
        fullName: 'Current User',
        score: 5420,
        level: 18,
        badges: [
          { id: '5', name: 'Newcomer', description: 'Joined the platform', icon: 'star', color: 'yellow', earnedAt: '2024-12-01' }
        ],
        stats: {
          vulnerabilitiesFound: 23,
          scansCompleted: 156,
          osintQueries: 234,
          cveReported: 1,
          toolsUsed: 12,
          daysActive: 45
        },
        joinedAt: '2024-12-01',
        lastActive: new Date().toISOString()
      })

    } catch (error) {
      console.error('Error loading leaderboard:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    setTimeout(() => {
      setStats({
        totalUsers: 12456,
        activeToday: 1247,
        topScore: 15420,
        averageScore: 3456
      })
    }, 1000)
  }

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-5 w-5 text-yellow-500" />
    if (rank === 2) return <Medal className="h-5 w-5 text-gray-400" />
    if (rank === 3) return <Medal className="h-5 w-5 text-amber-600" />
    return <span className="text-sm font-medium text-gray-600">#{rank}</span>
  }

  const getBadgeIcon = (iconName: string) => {
    switch (iconName) {
      case 'bug': return Bug
      case 'search': return Target
      case 'shield': return Shield
      case 'database': return Trophy
      case 'star': return Star
      default: return Award
    }
  }

  const getBadgeColor = (color: string) => {
    switch (color) {
      case 'red': return 'bg-red-100 text-red-800'
      case 'blue': return 'bg-blue-100 text-blue-800'
      case 'green': return 'bg-green-100 text-green-800'
      case 'purple': return 'bg-purple-100 text-purple-800'
      case 'yellow': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const leaderboardColumns = [
    {
      key: 'rank',
      label: 'Rank',
      render: (value: number) => (
        <div className="flex items-center justify-center w-12">
          {getRankIcon(value)}
        </div>
      )
    },
    {
      key: 'user',
      label: 'User',
      render: (value: any, row: LeaderboardUser) => (
        <div className="flex items-center space-x-3">
          {row.avatar ? (
            <img
              src={row.avatar}
              alt={row.username}
              className="h-8 w-8 rounded-full"
            />
          ) : (
            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-xs font-medium text-gray-600">
                {row.username.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium text-gray-900">{row.username}</div>
            <div className="text-sm text-gray-500">{row.fullName}</div>
          </div>
        </div>
      )
    },
    {
      key: 'score',
      label: 'Score',
      render: (value: number) => (
        <span className="font-bold text-primary-600">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'level',
      label: 'Level',
      render: (value: number) => (
        <span className="px-2 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
          {value}
        </span>
      )
    },
    {
      key: 'badges',
      label: 'Badges',
      render: (value: Badge[]) => (
        <div className="flex space-x-1">
          {value.slice(0, 3).map((badge) => {
            const Icon = getBadgeIcon(badge.icon)
            return (
              <div
                key={badge.id}
                className={`p-1 rounded-full ${getBadgeColor(badge.color)}`}
                title={badge.name}
              >
                <Icon className="h-3 w-3" />
              </div>
            )
          })}
          {value.length > 3 && (
            <span className="text-xs text-gray-500">+{value.length - 3}</span>
          )}
        </div>
      )
    },
    {
      key: 'stats',
      label: 'Vulnerabilities',
      render: (value: UserStats) => (
        <span className="font-medium">{value.vulnerabilitiesFound}</span>
      )
    },
    {
      key: 'lastActive',
      label: 'Last Active',
      render: (value: string) => (
        <span className="text-sm text-gray-500">
          {new Date(value).toLocaleDateString()}
        </span>
      )
    }
  ]

  return (
    <PublicLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Trophy className="h-6 w-6 mr-2 text-primary-600" />
              Leaderboard
            </h1>
            <p className="text-gray-600 mt-1">
              Top cybersecurity researchers and bug hunters
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="input-field"
              >
                <option value="all">All Time</option>
                <option value="year">This Year</option>
                <option value="month">This Month</option>
                <option value="week">This Week</option>
              </select>
              <button className="btn-secondary">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Users"
            value={stats.totalUsers.toLocaleString()}
            icon={Users}
            color="blue"
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="Active Today"
            value={stats.activeToday.toLocaleString()}
            icon={Activity}
            color="green"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Top Score"
            value={stats.topScore.toLocaleString()}
            icon={Crown}
            color="yellow"
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="Average Score"
            value={stats.averageScore.toLocaleString()}
            icon={TrendingUp}
            color="purple"
            trend={{ value: 5, isPositive: true }}
          />
        </div>

        {/* Current User Position */}
        {currentUser && (
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Your Position
            </h3>
            <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-12 h-12 bg-primary-100 rounded-full">
                  <span className="text-lg font-bold text-primary-600">#{currentUser.rank}</span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">{currentUser.username}</div>
                  <div className="text-sm text-gray-500">Level {currentUser.level}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary-600">
                  {currentUser.score.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500">points</div>
              </div>
            </div>
          </Card>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'overall', label: 'Overall', icon: Trophy },
            { id: 'vulnerabilities', label: 'Vulnerabilities', icon: Bug },
            { id: 'scans', label: 'Scans', icon: Shield },
            { id: 'osint', label: 'OSINT', icon: Target }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Leaderboard Table */}
        <Card>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {activeTab === 'overall' ? 'Overall Rankings' :
               activeTab === 'vulnerabilities' ? 'Top Vulnerability Hunters' :
               activeTab === 'scans' ? 'Top Scanners' : 'Top OSINT Researchers'}
            </h3>
          </div>
          
          {loading ? (
            <div className="text-center py-12">
              <Loading size="lg" text="Loading leaderboard..." />
            </div>
          ) : (
            <DataTable
              columns={leaderboardColumns}
              data={leaderboard.slice(0, 50)}
              pagination={{
                currentPage: 1,
                totalPages: Math.ceil(leaderboard.length / 50),
                pageSize: 50,
                totalItems: leaderboard.length,
                onPageChange: () => {}
              }}
            />
          )}
        </Card>
      </div>
    </PublicLayout>
  )
}
