{"name": "npm-package-arg", "version": "10.1.0", "description": "Parse the things that can be arguments to `npm install`", "main": "./lib/npa.js", "directories": {"test": "test"}, "files": ["bin/", "lib/"], "dependencies": {"hosted-git-info": "^6.0.0", "proc-log": "^3.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.10.0", "tap": "^16.0.1"}, "scripts": {"test": "tap", "snap": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-package-arg.git"}, "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/npm-package-arg/issues"}, "homepage": "https://github.com/npm/npm-package-arg", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "tap": {"branches": 97, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.10.0"}}