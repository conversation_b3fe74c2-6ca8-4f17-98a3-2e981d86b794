"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./components/CyberHeader.tsx":
/*!************************************!*\
  !*** ./components/CyberHeader.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CyberHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CyberHeader(param) {\n    let { user } = param;\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Features\",\n            href: \"#features\",\n            dropdown: [\n                {\n                    name: \"OSINT Investigator\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    description: \"Advanced intelligence gathering\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    description: \"Automated security scanning\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    description: \"Malware detection & analysis\"\n                },\n                {\n                    name: \"CVE Intelligence\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    description: \"Vulnerability database\"\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    description: \"Advanced search queries\"\n                },\n                {\n                    name: \"Developer Tools\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    description: \"Security testing tools\"\n                }\n            ]\n        },\n        {\n            name: \"Pricing\",\n            href: \"/plan\"\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Docs\",\n            href: \"/docs\"\n        },\n        {\n            name: \"Community\",\n            href: \"/community\"\n        }\n    ];\n    const handleLogin = ()=>{\n        router.push(\"/login\");\n    };\n    const handleRegister = ()=>{\n        router.push(\"/register\");\n    };\n    const handleDashboard = ()=>{\n        router.push(\"/dashboard\");\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const isActive = (href)=>{\n        if (href.startsWith(\"#\")) return false;\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? \"bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border shadow-lg\" : \"bg-transparent\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-cyber-glow group-hover:animate-glitch\",\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                            children: \"Cyber Security Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setActiveDropdown(item.name),\n                                        onMouseLeave: ()=>setActiveDropdown(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 text-gray-300 hover:text-cyber-primary transition-colors duration-200 font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-80 bg-cyber-card border border-cyber-border rounded-lg shadow-xl animate-fade-in-up\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 grid grid-cols-1 gap-2\",\n                                                    children: item.dropdown.map((subItem)=>{\n                                                        const Icon = subItem.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: subItem.href,\n                                                            className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-cyber-secondary/10 transition-colors duration-200 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-cyber-primary group-hover:animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white group-hover:text-cyber-primary\",\n                                                                            children: subItem.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 158,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: subItem.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, subItem.name, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 31\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary\" : \"text-gray-300 hover:text-cyber-primary\"),\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 35\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 px-3 py-1 bg-cyber-secondary/20 border border-cyber-secondary rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyber-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-cyber-accent uppercase\",\n                                                children: user.plan\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-cyber-secondary/10 transition-colors duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-8 w-8 rounded-full border-2 border-cyber-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-cyber-primary/20 border-2 border-cyber-primary flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 top-full mt-2 w-48 bg-cyber-card border border-cyber-border rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleDashboard,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Dashboard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors duration-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Logout\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogin,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-cyber-primary hover:text-white border border-cyber-primary hover:bg-cyber-primary/20 rounded-lg transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRegister,\n                                        className: \"btn-cyber-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Get Started\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors duration-200\",\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-card border-t border-cyber-border animate-slide-in-right\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 space-y-4\",\n                    children: [\n                        navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 font-medium mb-2\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-2\",\n                                            children: item.dropdown.map((subItem)=>{\n                                                const Icon = subItem.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.href,\n                                                    className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: subItem.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, subItem.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"block px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary bg-cyber-primary/10\" : \"text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10\"),\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t border-cyber-border space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleLogin();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 text-cyber-primary border border-cyber-primary rounded-lg hover:bg-cyber-primary/20 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleRegister();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Started\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(CyberHeader, \"F3oijKM3tVCOz8jkoNovbYM3h7k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = CyberHeader;\nvar _c;\n$RefreshReg$(_c, \"CyberHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CyberHeader.tsx\n"));

/***/ })

});