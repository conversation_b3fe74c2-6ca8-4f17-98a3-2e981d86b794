import * as http from 'node:http';
import { DiagnosticResult } from './types';
import { RedactionOptions } from './Transport';
export interface ErrorOptions {
    redaction: RedactionOptions;
}
export declare class ElasticsearchClientError extends Error {
    options: ErrorOptions;
    constructor(message: string, options?: ErrorOptions);
}
export declare class TimeoutError extends ElasticsearchClientError {
    meta?: DiagnosticResult;
    constructor(message: string, meta?: DiagnosticResult, options?: ErrorOptions);
}
export declare class ConnectionError extends ElasticsearchClientError {
    meta?: DiagnosticResult;
    constructor(message: string, meta?: DiagnosticResult, options?: ErrorOptions);
}
export declare class NoLivingConnectionsError extends ElasticsearchClientError {
    meta: DiagnosticResult;
    constructor(message: string, meta: DiagnosticResult, options?: ErrorOptions);
}
export declare class SerializationError extends ElasticsearchClientError {
    data: Record<string, any>;
    constructor(message: string, data: Record<string, any>);
}
export declare class DeserializationError extends ElasticsearchClientError {
    data: string;
    constructor(message: string, data: string);
}
export declare class ConfigurationError extends ElasticsearchClientError {
    constructor(message: string);
}
export declare class ResponseError extends ElasticsearchClientError {
    meta: DiagnosticResult;
    constructor(meta: DiagnosticResult, options?: ErrorOptions);
    get body(): any | undefined;
    get statusCode(): number | undefined;
    get headers(): http.IncomingHttpHeaders | undefined;
}
export declare class RequestAbortedError extends ElasticsearchClientError {
    meta?: DiagnosticResult;
    constructor(message: string, meta?: DiagnosticResult, options?: ErrorOptions);
}
export declare class ProductNotSupportedError extends ElasticsearchClientError {
    meta?: DiagnosticResult;
    constructor(product: string, meta?: DiagnosticResult, options?: ErrorOptions);
}
