{"version": 3, "file": "puppeteer.config.js", "sourceRoot": "", "sources": ["../../src/config/puppeteer.config.ts"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe,GAAG;IACtB,WAAW,EAAE,0BAA0B;IACvC,QAAQ,EAAE,CAAC,cAAc,EAAE,0BAA0B,CAAC;IACtD,YAAY,EAAE;QACZ,aAAa;QACb,eAAe;QACf,iCAAiC;QACjC,kBAAkB;QAClB,wBAAwB;QACxB,6BAA6B;QAC7B,uCAAuC;QACvC,eAAe;QACf,sBAAsB;QACtB,wBAAwB;QACxB,kCAAkC;QAClC,0BAA0B;QAC1B,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,4BAA4B;QAC5B,sCAAsC;QACtC,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,gCAAgC;QAChC,yBAAyB;QACzB,mCAAmC;QACnC,iIAAiI;QACjI,iCAAiC;QACjC,0DAA0D;QAC1D,uCAAuC;QACvC,0CAA0C;QAC1C,oBAAoB;QACpB,0CAA0C;QAC1C,sDAAsD;QACtD,yBAAyB;QACzB,8BAA8B;QAC9B,wBAAwB;QACxB,mCAAmC;QACnC,0BAA0B;QAC1B,4BAA4B;QAC5B,kCAAkC;QAClC,gBAAgB;QAChB,4BAA4B;QAC5B,0BAA0B;QAC1B,gBAAgB;QAChB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,uCAAuC;QACvC,qBAAqB;QACrB,mBAAmB;KACpB;CACF,CAAC;AAEO,0CAAe"}