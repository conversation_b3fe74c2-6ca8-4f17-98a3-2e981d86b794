# lowercase-keys

> Lowercase the keys of an object

Check out [`map-obj`](https://github.com/sindresorhus/map-obj) if you need support for deep iteration.

## Install

```sh
npm install lowercase-keys
```

## Usage

```js
import lowercaseKeys from 'lowercase-keys';

lowercaseKeys({FOO: true, bAr: false});
//=> {foo: true, bar: false}
```

## API

### lowercaseKeys(object)

Returns a new object with the keys lowercased.

## lowercase-keys for enterprise

Available as part of the Tidelift Subscription.

The maintainers of lowercase-keys and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-lowercase-keys?utm_source=npm-lowercase-keys&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)
