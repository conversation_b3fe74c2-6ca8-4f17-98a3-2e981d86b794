{"name": "is-in-browser", "version": "2.0.0", "description": "Simple check to see if current app is running in browser", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./package.json": "./package.json"}, "scripts": {"dist.cjs": "BABEL_ENV=cjs babel -x \".ts\" src/index.ts -o dist/index.cjs", "dist.es": "BABEL_ENV=es babel -x \".ts\" src/index.ts -o dist/index.mjs", "dist.declarations": "tsc --emitDeclarationOnly src/index.ts --declaration --outDir dist", "dist": "concurrently \"npm:dist.*\"", "test.jest": "BABEL_ENV=cjs jest", "test.node": "BABEL_ENV=cjs babel-node -x \".ts\" src/node.check.ts", "test": "concurrently \"npm:test.*\"", "lint": "eslint src --ext .ts", "format": "prettier -w src", "prepublishOnly": "concurrently npm:test npm:dist"}, "keywords": [], "browserslist": ["defaults"], "license": "MIT", "devDependencies": {"@babel/cli": "^7.12.13", "@babel/core": "^7.12.13", "@babel/node": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.12.13", "@babel/preset-typescript": "^7.12.13", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^4.14.2", "babel-eslint": "^4.1.6", "concurrently": "^5.3.0", "eslint": "^7.19.0", "eslint-plugin-prettier": "^3.3.1", "jest": "^26.6.3", "prettier": "^2.2.1", "typescript": "^4.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/tuxsudo/is-in-browser.git"}, "author": "<PERSON>", "bugs": {"url": "https://github.com/tuxsudo/is-in-browser/issues"}, "homepage": "https://github.com/tuxsudo/is-in-browser#readme"}