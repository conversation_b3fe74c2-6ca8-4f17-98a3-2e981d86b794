"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommunityPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CommunityPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMembers: 15420,\n        activeToday: 1247,\n        totalPosts: 8934,\n        totalEvents: 156\n    });\n    const themeClasses = useThemeClasses();\n    const communityStats = [\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            label: \"Total Members\",\n            value: stats.totalMembers.toLocaleString(),\n            change: \"+12%\",\n            color: \"text-cyber-primary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Active Today\",\n            value: stats.activeToday.toLocaleString(),\n            change: \"+8%\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Total Posts\",\n            value: stats.totalPosts.toLocaleString(),\n            change: \"+15%\",\n            color: \"text-cyber-secondary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Events Hosted\",\n            value: stats.totalEvents.toLocaleString(),\n            change: \"+25%\",\n            color: \"text-cyber-accent\"\n        }\n    ];\n    const communityChannels = [\n        {\n            name: \"Discord Server\",\n            description: \"Real-time chat, voice channels, and community discussions\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            members: \"8.2K\",\n            link: \"#\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            name: \"Telegram Group\",\n            description: \"Quick updates, news, and instant notifications\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            members: \"5.1K\",\n            link: \"#\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"GitHub Community\",\n            description: \"Open source contributions and code collaboration\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            members: \"3.8K\",\n            link: \"#\",\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, tips, and cybersecurity news\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            members: \"12.5K\",\n            link: \"#\",\n            color: \"bg-black\"\n        }\n    ];\n    const upcomingEvents = [\n        {\n            title: \"Cybersecurity Workshop\",\n            date: \"2024-01-15\",\n            time: \"19:00 WIB\",\n            type: \"Workshop\",\n            participants: 156,\n            description: \"Advanced penetration testing techniques\"\n        },\n        {\n            title: \"Bug Bounty Bootcamp\",\n            date: \"2024-01-20\",\n            time: \"14:00 WIB\",\n            type: \"Bootcamp\",\n            participants: 89,\n            description: \"From beginner to professional bug hunter\"\n        },\n        {\n            title: \"CTF Competition\",\n            date: \"2024-01-25\",\n            time: \"10:00 WIB\",\n            type: \"Competition\",\n            participants: 234,\n            description: \"Capture The Flag challenge for all levels\"\n        }\n    ];\n    const topContributors = [\n        {\n            name: \"CyberNinja\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 15420,\n            badge: \"Elite Hunter\",\n            contributions: 89\n        },\n        {\n            name: \"SecurityMaster\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 12350,\n            badge: \"Bug Hunter\",\n            contributions: 67\n        },\n        {\n            name: \"PentestPro\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 9870,\n            badge: \"Security Expert\",\n            contributions: 54\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-glow\" : \"text-blue-600\",\n                                        children: \"Join Our\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-pink\" : \"text-pink-600\",\n                                        children: \"Cyber Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl max-w-3xl mx-auto mb-8 \".concat(themeClasses.textSecondary),\n                                children: \"Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                                children: communityStats.map((stat, index)=>{\n                                    const Icon = stat.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" text-center\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-8 w-8 \".concat(stat.color, \" mx-auto mb-3 \").concat(themeClasses.isDark ? \"animate-cyber-pulse\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm \".concat(themeClasses.textMuted, \" mb-1\"),\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    stat.change,\n                                                    \" this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center mb-12\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                label: \"Overview\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                id: \"channels\",\n                                label: \"Channels\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                id: \"events\",\n                                label: \"Events\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: \"leaderboard\",\n                                label: \"Top Contributors\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 \".concat(activeTab === tab.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary\" : \"bg-blue-100 text-blue-600 border-2 border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Welcome to KodeXGuard Community\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help each other grow in the field of cybersecurity.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Share security research and findings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Collaborate on bug bounty programs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Learn from industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Participate in competitions and CTFs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Community Guidelines\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83E\\uDD1D Be Respectful\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Treat all community members with respect and professionalism\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDD12 Ethical Practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Only discuss ethical hacking and responsible disclosure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDCDA Share Knowledge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Help others learn and grow in cybersecurity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"channels\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: communityChannels.map((channel, index)=>{\n                                    const Icon = channel.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(channel.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                            children: channel.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-4\"),\n                                                            children: channel.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                    children: [\n                                                                        channel.members,\n                                                                        \" members\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-lg \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\", \" transition-colors duration-200\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Join\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Upcoming Events\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    upcomingEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(event.type === \"Workshop\" ? \"bg-blue-100 text-blue-800\" : event.type === \"Bootcamp\" ? \"bg-green-100 text-green-800\" : \"bg-purple-100 text-purple-800\"),\n                                                                        children: event.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                        children: [\n                                                                            event.participants,\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 md:mt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                            children: \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"leaderboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Top Contributors This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this),\n                                    topContributors.map((contributor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full bg-gradient-to-r \".concat(index === 0 ? \"from-yellow-400 to-yellow-600\" : index === 1 ? \"from-gray-300 to-gray-500\" : \"from-orange-400 to-orange-600\", \" flex items-center justify-center\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: contributor.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: contributor.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: [\n                                                                    contributor.points.toLocaleString(),\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                children: [\n                                                                    contributor.contributions,\n                                                                    \" contributions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center \".concat(themeClasses.card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                children: \"Ready to Join Our Community?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg \".concat(themeClasses.textSecondary, \" mb-8 max-w-2xl mx-auto\"),\n                                children: \"Connect with like-minded cybersecurity professionals and take your skills to the next level\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                        children: \"Join Discord Server\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                        children: \"Follow on Twitter\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(CommunityPage, \"F0NgutkfAWy8nhkP5ZGTG+irf/8=\", true);\n_c = CommunityPage;\nvar _c;\n$RefreshReg$(_c, \"CommunityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21tdW5pdHkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNTO0FBbUIvQjtBQUVOLFNBQVNpQjs7SUFDdEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBQztRQUNqQ3NCLGNBQWM7UUFDZEMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLGFBQWE7SUFDZjtJQUNBLE1BQU1DLGVBQWVDO0lBRXJCLE1BQU1DLGlCQUFpQjtRQUNyQjtZQUNFQyxNQUFNM0Isc01BQUtBO1lBQ1g0QixPQUFPO1lBQ1BDLE9BQU9YLE1BQU1FLFlBQVksQ0FBQ1UsY0FBYztZQUN4Q0MsUUFBUTtZQUNSQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFTCxNQUFNYixzTUFBUUE7WUFDZGMsT0FBTztZQUNQQyxPQUFPWCxNQUFNRyxXQUFXLENBQUNTLGNBQWM7WUFDdkNDLFFBQVE7WUFDUkMsT0FBTztRQUNUO1FBQ0E7WUFDRUwsTUFBTTFCLHNNQUFhQTtZQUNuQjJCLE9BQU87WUFDUEMsT0FBT1gsTUFBTUksVUFBVSxDQUFDUSxjQUFjO1lBQ3RDQyxRQUFRO1lBQ1JDLE9BQU87UUFDVDtRQUNBO1lBQ0VMLE1BQU14QixzTUFBUUE7WUFDZHlCLE9BQU87WUFDUEMsT0FBT1gsTUFBTUssV0FBVyxDQUFDTyxjQUFjO1lBQ3ZDQyxRQUFRO1lBQ1JDLE9BQU87UUFDVDtLQUNEO0lBRUQsTUFBTUMsb0JBQW9CO1FBQ3hCO1lBQ0VDLE1BQU07WUFDTkMsYUFBYTtZQUNiUixNQUFNbkIsc01BQWFBO1lBQ25CNEIsU0FBUztZQUNUQyxNQUFNO1lBQ05MLE9BQU87UUFDVDtRQUNBO1lBQ0VFLE1BQU07WUFDTkMsYUFBYTtZQUNiUixNQUFNbEIsc01BQUlBO1lBQ1YyQixTQUFTO1lBQ1RDLE1BQU07WUFDTkwsT0FBTztRQUNUO1FBQ0E7WUFDRUUsTUFBTTtZQUNOQyxhQUFhO1lBQ2JSLE1BQU1yQixzTUFBTUE7WUFDWjhCLFNBQVM7WUFDVEMsTUFBTTtZQUNOTCxPQUFPO1FBQ1Q7UUFDQTtZQUNFRSxNQUFNO1lBQ05DLGFBQWE7WUFDYlIsTUFBTXBCLHVNQUFPQTtZQUNiNkIsU0FBUztZQUNUQyxNQUFNO1lBQ05MLE9BQU87UUFDVDtLQUNEO0lBRUQsTUFBTU0saUJBQWlCO1FBQ3JCO1lBQ0VDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsY0FBYztZQUNkUixhQUFhO1FBQ2Y7UUFDQTtZQUNFSSxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLGNBQWM7WUFDZFIsYUFBYTtRQUNmO1FBQ0E7WUFDRUksT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxjQUFjO1lBQ2RSLGFBQWE7UUFDZjtLQUNEO0lBRUQsTUFBTVMsa0JBQWtCO1FBQ3RCO1lBQ0VWLE1BQU07WUFDTlcsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsZUFBZTtRQUNqQjtRQUNBO1lBQ0VkLE1BQU07WUFDTlcsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsZUFBZTtRQUNqQjtRQUNBO1lBQ0VkLE1BQU07WUFDTlcsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsZUFBZTtRQUNqQjtLQUNEO0lBRUQscUJBQ0UsOERBQUNqRCxnRUFBWUE7a0JBQ1gsNEVBQUNrRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUdELFdBQVU7O2tEQUNaLDhEQUFDRTt3Q0FBS0YsV0FBVzFCLGFBQWE2QixNQUFNLEdBQUcsb0JBQW9CO2tEQUFpQjs7Ozs7O29DQUFnQjtrREFDNUYsOERBQUNEO3dDQUFLRixXQUFXMUIsYUFBYTZCLE1BQU0sR0FBRyxvQkFBb0I7a0RBQWlCOzs7Ozs7Ozs7Ozs7MENBRTlFLDhEQUFDQztnQ0FBRUosV0FBVyxrQ0FBNkQsT0FBM0IxQixhQUFhK0IsYUFBYTswQ0FBSTs7Ozs7OzBDQUs5RSw4REFBQ047Z0NBQUlDLFdBQVU7MENBQ1p4QixlQUFlOEIsR0FBRyxDQUFDLENBQUNDLE1BQU1DO29DQUN6QixNQUFNQyxPQUFPRixLQUFLOUIsSUFBSTtvQ0FDdEIscUJBQ0UsOERBQUNzQjt3Q0FBZ0JDLFdBQVcsR0FBcUIsT0FBbEIxQixhQUFhb0MsSUFBSSxFQUFDOzswREFDL0MsOERBQUNEO2dEQUFLVCxXQUFXLFdBQXNDMUIsT0FBM0JpQyxLQUFLekIsS0FBSyxFQUFDLGtCQUFpRSxPQUFqRFIsYUFBYTZCLE1BQU0sR0FBRyx3QkFBd0I7Ozs7OzswREFDckcsOERBQUNKO2dEQUFJQyxXQUFXLHNCQUErQyxPQUF6QjFCLGFBQWFxQyxXQUFXLEVBQUM7MERBQzVESixLQUFLNUIsS0FBSzs7Ozs7OzBEQUViLDhEQUFDb0I7Z0RBQUlDLFdBQVcsV0FBa0MsT0FBdkIxQixhQUFhc0MsU0FBUyxFQUFDOzBEQUMvQ0wsS0FBSzdCLEtBQUs7Ozs7OzswREFFYiw4REFBQ3FCO2dEQUFJQyxXQUFVOztvREFDWk8sS0FBSzFCLE1BQU07b0RBQUM7Ozs7Ozs7O3VDQVRQMkI7Ozs7O2dDQWFkOzs7Ozs7Ozs7Ozs7a0NBS0osOERBQUNUO3dCQUFJQyxXQUFVO2tDQUNaOzRCQUNDO2dDQUFFYSxJQUFJO2dDQUFZbkMsT0FBTztnQ0FBWUQsTUFBTTNCLHNNQUFLQTs0QkFBQzs0QkFDakQ7Z0NBQUUrRCxJQUFJO2dDQUFZbkMsT0FBTztnQ0FBWUQsTUFBTTFCLHNNQUFhQTs0QkFBQzs0QkFDekQ7Z0NBQUU4RCxJQUFJO2dDQUFVbkMsT0FBTztnQ0FBVUQsTUFBTXhCLHNNQUFRQTs0QkFBQzs0QkFDaEQ7Z0NBQUU0RCxJQUFJO2dDQUFlbkMsT0FBTztnQ0FBb0JELE1BQU16Qix1TUFBTUE7NEJBQUM7eUJBQzlELENBQUNzRCxHQUFHLENBQUMsQ0FBQ1E7NEJBQ0wsTUFBTUwsT0FBT0ssSUFBSXJDLElBQUk7NEJBQ3JCLHFCQUNFLDhEQUFDc0M7Z0NBRUNDLFNBQVMsSUFBTWpELGFBQWErQyxJQUFJRCxFQUFFO2dDQUNsQ2IsV0FBVyxzR0FNVixPQUxDbEMsY0FBY2dELElBQUlELEVBQUUsR0FDaEJ2QyxhQUFhNkIsTUFBTSxHQUNqQix5RUFDQSx1REFDRixHQUF1QzdCLE9BQXBDQSxhQUFhK0IsYUFBYSxFQUFDLFdBQTJDL0IsT0FBbENBLGFBQWFxQyxXQUFXLEVBQUMsV0FBcUUsT0FBNURyQyxhQUFhNkIsTUFBTSxHQUFHLHdCQUF3Qjs7a0RBRzdILDhEQUFDTTt3Q0FBS1QsV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ0U7a0RBQU1ZLElBQUlwQyxLQUFLOzs7Ozs7OytCQVhYb0MsSUFBSUQsRUFBRTs7Ozs7d0JBY2pCOzs7Ozs7a0NBSUYsOERBQUNkO3dCQUFJQyxXQUFVOzs0QkFDWmxDLGNBQWMsNEJBQ2IsOERBQUNpQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFXMUIsYUFBYW9DLElBQUk7OzBEQUMvQiw4REFBQ087Z0RBQUdqQixXQUFXLHNCQUErQyxPQUF6QjFCLGFBQWFxQyxXQUFXLEVBQUM7MERBQVE7Ozs7OzswREFHdEUsOERBQUNQO2dEQUFFSixXQUFXLEdBQThCLE9BQTNCMUIsYUFBYStCLGFBQWEsRUFBQzswREFBUTs7Ozs7OzBEQUtwRCw4REFBQ047Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN4Qyx1TUFBTUE7Z0VBQUN3QyxXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDRTtnRUFBS0YsV0FBVzFCLGFBQWFxQyxXQUFXOzBFQUFFOzs7Ozs7Ozs7Ozs7a0VBRTdDLDhEQUFDWjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN0Qyx1TUFBTUE7Z0VBQUNzQyxXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDRTtnRUFBS0YsV0FBVzFCLGFBQWFxQyxXQUFXOzBFQUFFOzs7Ozs7Ozs7Ozs7a0VBRTdDLDhEQUFDWjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN2Qyx1TUFBR0E7Z0VBQUN1QyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUNFO2dFQUFLRixXQUFXMUIsYUFBYXFDLFdBQVc7MEVBQUU7Ozs7Ozs7Ozs7OztrRUFFN0MsOERBQUNaO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3JDLHVNQUFLQTtnRUFBQ3FDLFdBQVU7Ozs7OzswRUFDakIsOERBQUNFO2dFQUFLRixXQUFXMUIsYUFBYXFDLFdBQVc7MEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLakQsOERBQUNaO3dDQUFJQyxXQUFXMUIsYUFBYW9DLElBQUk7OzBEQUMvQiw4REFBQ087Z0RBQUdqQixXQUFXLHNCQUErQyxPQUF6QjFCLGFBQWFxQyxXQUFXLEVBQUM7MERBQVE7Ozs7OzswREFHdEUsOERBQUNaO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVcsa0JBQTZFLE9BQTNEMUIsYUFBYTZCLE1BQU0sR0FBRyx3QkFBd0I7OzBFQUM5RSw4REFBQ2U7Z0VBQUdsQixXQUFXLGlCQUEwQyxPQUF6QjFCLGFBQWFxQyxXQUFXLEVBQUM7MEVBQVE7Ozs7OzswRUFDakUsOERBQUNQO2dFQUFFSixXQUFXLFdBQXNDLE9BQTNCMUIsYUFBYStCLGFBQWE7MEVBQUk7Ozs7Ozs7Ozs7OztrRUFJekQsOERBQUNOO3dEQUFJQyxXQUFXLGtCQUErRSxPQUE3RDFCLGFBQWE2QixNQUFNLEdBQUcsMEJBQTBCOzswRUFDaEYsOERBQUNlO2dFQUFHbEIsV0FBVyxpQkFBMEMsT0FBekIxQixhQUFhcUMsV0FBVyxFQUFDOzBFQUFROzs7Ozs7MEVBQ2pFLDhEQUFDUDtnRUFBRUosV0FBVyxXQUFzQyxPQUEzQjFCLGFBQWErQixhQUFhOzBFQUFJOzs7Ozs7Ozs7Ozs7a0VBSXpELDhEQUFDTjt3REFBSUMsV0FBVyxrQkFBOEUsT0FBNUQxQixhQUFhNkIsTUFBTSxHQUFHLHVCQUF1Qjs7MEVBQzdFLDhEQUFDZTtnRUFBR2xCLFdBQVcsaUJBQTBDLE9BQXpCMUIsYUFBYXFDLFdBQVcsRUFBQzswRUFBUTs7Ozs7OzBFQUNqRSw4REFBQ1A7Z0VBQUVKLFdBQVcsV0FBc0MsT0FBM0IxQixhQUFhK0IsYUFBYTswRUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQVNoRXZDLGNBQWMsNEJBQ2IsOERBQUNpQztnQ0FBSUMsV0FBVTswQ0FDWmpCLGtCQUFrQnVCLEdBQUcsQ0FBQyxDQUFDYSxTQUFTWDtvQ0FDL0IsTUFBTUMsT0FBT1UsUUFBUTFDLElBQUk7b0NBQ3pCLHFCQUNFLDhEQUFDc0I7d0NBQWdCQyxXQUFXLEdBQXFCLE9BQWxCMUIsYUFBYW9DLElBQUksRUFBQztrREFDL0MsNEVBQUNYOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVcsa0JBQWdDLE9BQWRtQixRQUFRckMsS0FBSzs4REFDN0MsNEVBQUMyQjt3REFBS1QsV0FBVTs7Ozs7Ozs7Ozs7OERBRWxCLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNpQjs0REFBR2pCLFdBQVcscUJBQThDLE9BQXpCMUIsYUFBYXFDLFdBQVcsRUFBQztzRUFDMURRLFFBQVFuQyxJQUFJOzs7Ozs7c0VBRWYsOERBQUNvQjs0REFBRUosV0FBVyxHQUE4QixPQUEzQjFCLGFBQWErQixhQUFhLEVBQUM7c0VBQ3pDYyxRQUFRbEMsV0FBVzs7Ozs7O3NFQUV0Qiw4REFBQ2M7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRTtvRUFBS0YsV0FBVyxXQUFrQyxPQUF2QjFCLGFBQWFzQyxTQUFTOzt3RUFDL0NPLFFBQVFqQyxPQUFPO3dFQUFDOzs7Ozs7OzhFQUVuQiw4REFBQzZCO29FQUFPZixXQUFXLG9EQUEySSxPQUF2RjFCLGFBQWE2QixNQUFNLEdBQUcsc0JBQXNCLDRDQUEyQzs7c0ZBQzVKLDhEQUFDRDtzRkFBSzs7Ozs7O3NGQUNOLDhEQUFDL0MsdU1BQVlBOzRFQUFDNkMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQWxCeEJROzs7OztnQ0F5QmQ7Ozs7Ozs0QkFJSDFDLGNBQWMsMEJBQ2IsOERBQUNpQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpQjt3Q0FBR2pCLFdBQVcsc0JBQStDLE9BQXpCMUIsYUFBYXFDLFdBQVcsRUFBQztrREFBUTs7Ozs7O29DQUdyRXZCLGVBQWVrQixHQUFHLENBQUMsQ0FBQ2MsT0FBT1osc0JBQzFCLDhEQUFDVDs0Q0FBZ0JDLFdBQVcsR0FBOEIxQixPQUEzQkEsYUFBYW9DLElBQUksRUFBQyxXQUEwRSxPQUFqRXBDLGFBQWE2QixNQUFNLEdBQUcseUJBQXlCLG1CQUFrQjtzREFDekgsNEVBQUNKO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNFO3dFQUFLRixXQUFXLDhDQUloQixPQUhDb0IsTUFBTTVCLElBQUksS0FBSyxhQUFhLDhCQUM1QjRCLE1BQU01QixJQUFJLEtBQUssYUFBYSxnQ0FDNUI7a0ZBRUM0QixNQUFNNUIsSUFBSTs7Ozs7O2tGQUViLDhEQUFDVTt3RUFBS0YsV0FBVyxXQUFrQyxPQUF2QjFCLGFBQWFzQyxTQUFTOzs0RUFDL0NRLE1BQU0zQixZQUFZOzRFQUFDOzs7Ozs7Ozs7Ozs7OzBFQUd4Qiw4REFBQ3lCO2dFQUFHbEIsV0FBVyxxQkFBOEMsT0FBekIxQixhQUFhcUMsV0FBVyxFQUFDOzBFQUMxRFMsTUFBTS9CLEtBQUs7Ozs7OzswRUFFZCw4REFBQ2U7Z0VBQUVKLFdBQVcsR0FBOEIsT0FBM0IxQixhQUFhK0IsYUFBYSxFQUFDOzBFQUN6Q2UsTUFBTW5DLFdBQVc7Ozs7OzswRUFFcEIsOERBQUNjO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDL0Msc01BQVFBO2dGQUFDK0MsV0FBVTs7Ozs7OzBGQUNwQiw4REFBQ0U7Z0ZBQUtGLFdBQVcsV0FBa0MsT0FBdkIxQixhQUFhc0MsU0FBUzswRkFDL0NRLE1BQU05QixJQUFJOzs7Ozs7Ozs7Ozs7a0ZBR2YsOERBQUNTO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQzlDLHVNQUFNQTtnRkFBQzhDLFdBQVU7Ozs7OzswRkFDbEIsOERBQUNFO2dGQUFLRixXQUFXLFdBQWtDLE9BQXZCMUIsYUFBYXNDLFNBQVM7MEZBQy9DUSxNQUFNN0IsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUtuQiw4REFBQ1E7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNlOzREQUFPZixXQUFXLEdBQTBKLE9BQXZKMUIsYUFBYTZCLE1BQU0sR0FBRyxzQkFBc0I7c0VBQThHOzs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FyQzVLSzs7Ozs7Ozs7Ozs7NEJBK0NmMUMsY0FBYywrQkFDYiw4REFBQ2lDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2lCO3dDQUFHakIsV0FBVyxzQkFBK0MsT0FBekIxQixhQUFhcUMsV0FBVyxFQUFDO2tEQUFROzs7Ozs7b0NBR3JFakIsZ0JBQWdCWSxHQUFHLENBQUMsQ0FBQ2UsYUFBYWIsc0JBQ2pDLDhEQUFDVDs0Q0FBZ0JDLFdBQVcsR0FBcUIsT0FBbEIxQixhQUFhb0MsSUFBSSxFQUFDO3NEQUMvQyw0RUFBQ1g7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0Q7NERBQUlDLFdBQVcsMkNBSWYsT0FIQ1EsVUFBVSxJQUFJLGtDQUNkQSxVQUFVLElBQUksOEJBQ2QsaUNBQ0Q7c0VBQ0MsNEVBQUNOO2dFQUFLRixXQUFVOztvRUFBdUI7b0VBQUVRLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUdyRCw4REFBQ1Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDa0I7Z0VBQUdsQixXQUFXLHFCQUE4QyxPQUF6QjFCLGFBQWFxQyxXQUFXOzBFQUN6RFUsWUFBWXJDLElBQUk7Ozs7OzswRUFFbkIsOERBQUNvQjtnRUFBRUosV0FBVyxXQUFzQyxPQUEzQjFCLGFBQWErQixhQUFhOzBFQUNoRGdCLFlBQVl4QixLQUFLOzs7Ozs7Ozs7Ozs7a0VBR3RCLDhEQUFDRTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFXLHFCQUE4QyxPQUF6QjFCLGFBQWFxQyxXQUFXOztvRUFDMURVLFlBQVl6QixNQUFNLENBQUNoQixjQUFjO29FQUFHOzs7Ozs7OzBFQUV2Qyw4REFBQ21CO2dFQUFJQyxXQUFXLFdBQWtDLE9BQXZCMUIsYUFBYXNDLFNBQVM7O29FQUM5Q1MsWUFBWXZCLGFBQWE7b0VBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBeEJ6QlU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQW1DbEIsOERBQUNUO3dCQUFJQyxXQUFXLGVBQWlDLE9BQWxCMUIsYUFBYW9DLElBQUk7OzBDQUM5Qyw4REFBQ087Z0NBQUdqQixXQUFXLHNCQUErQyxPQUF6QjFCLGFBQWFxQyxXQUFXLEVBQUM7MENBQVE7Ozs7OzswQ0FHdEUsOERBQUNQO2dDQUFFSixXQUFXLFdBQXNDLE9BQTNCMUIsYUFBYStCLGFBQWEsRUFBQzswQ0FBMEI7Ozs7OzswQ0FHOUUsOERBQUNOO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2U7d0NBQU9mLFdBQVcxQixhQUFhNkIsTUFBTSxHQUFHLHNCQUFzQjtrREFBNEc7Ozs7OztrREFHM0ssOERBQUNZO3dDQUFPZixXQUFXLG1FQUF3USxPQUFyTTFCLGFBQWE2QixNQUFNLEdBQUcsbUdBQW1HO2tEQUErRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVM1UztHQWhad0J0QztLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvY29tbXVuaXR5L3BhZ2UudHN4P2EzMDgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBQdWJsaWNMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL1B1YmxpY0xheW91dCdcbmltcG9ydCB7XG4gIFVzZXJzLFxuICBNZXNzYWdlU3F1YXJlLFxuICBUcm9waHksXG4gIFN0YXIsXG4gIENhbGVuZGFyLFxuICBNYXBQaW4sXG4gIEV4dGVybmFsTGluayxcbiAgR2l0aHViLFxuICBUd2l0dGVyLFxuICBNZXNzYWdlQ2lyY2xlLFxuICBTZW5kLFxuICBTaGllbGQsXG4gIFphcCxcbiAgVGFyZ2V0LFxuICBBd2FyZCxcbiAgVHJlbmRpbmdVcCxcbiAgQWN0aXZpdHlcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDb21tdW5pdHlQYWdlKCkge1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ292ZXJ2aWV3JylcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZSh7XG4gICAgdG90YWxNZW1iZXJzOiAxNTQyMCxcbiAgICBhY3RpdmVUb2RheTogMTI0NyxcbiAgICB0b3RhbFBvc3RzOiA4OTM0LFxuICAgIHRvdGFsRXZlbnRzOiAxNTZcbiAgfSlcbiAgY29uc3QgdGhlbWVDbGFzc2VzID0gdXNlVGhlbWVDbGFzc2VzKClcblxuICBjb25zdCBjb21tdW5pdHlTdGF0cyA9IFtcbiAgICB7XG4gICAgICBpY29uOiBVc2VycyxcbiAgICAgIGxhYmVsOiAnVG90YWwgTWVtYmVycycsXG4gICAgICB2YWx1ZTogc3RhdHMudG90YWxNZW1iZXJzLnRvTG9jYWxlU3RyaW5nKCksXG4gICAgICBjaGFuZ2U6ICcrMTIlJyxcbiAgICAgIGNvbG9yOiAndGV4dC1jeWJlci1wcmltYXJ5J1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogQWN0aXZpdHksXG4gICAgICBsYWJlbDogJ0FjdGl2ZSBUb2RheScsXG4gICAgICB2YWx1ZTogc3RhdHMuYWN0aXZlVG9kYXkudG9Mb2NhbGVTdHJpbmcoKSxcbiAgICAgIGNoYW5nZTogJys4JScsXG4gICAgICBjb2xvcjogJ3RleHQtZ3JlZW4tNDAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogTWVzc2FnZVNxdWFyZSxcbiAgICAgIGxhYmVsOiAnVG90YWwgUG9zdHMnLFxuICAgICAgdmFsdWU6IHN0YXRzLnRvdGFsUG9zdHMudG9Mb2NhbGVTdHJpbmcoKSxcbiAgICAgIGNoYW5nZTogJysxNSUnLFxuICAgICAgY29sb3I6ICd0ZXh0LWN5YmVyLXNlY29uZGFyeSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IENhbGVuZGFyLFxuICAgICAgbGFiZWw6ICdFdmVudHMgSG9zdGVkJyxcbiAgICAgIHZhbHVlOiBzdGF0cy50b3RhbEV2ZW50cy50b0xvY2FsZVN0cmluZygpLFxuICAgICAgY2hhbmdlOiAnKzI1JScsXG4gICAgICBjb2xvcjogJ3RleHQtY3liZXItYWNjZW50J1xuICAgIH1cbiAgXVxuXG4gIGNvbnN0IGNvbW11bml0eUNoYW5uZWxzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICdEaXNjb3JkIFNlcnZlcicsXG4gICAgICBkZXNjcmlwdGlvbjogJ1JlYWwtdGltZSBjaGF0LCB2b2ljZSBjaGFubmVscywgYW5kIGNvbW11bml0eSBkaXNjdXNzaW9ucycsXG4gICAgICBpY29uOiBNZXNzYWdlQ2lyY2xlLFxuICAgICAgbWVtYmVyczogJzguMksnLFxuICAgICAgbGluazogJyMnLFxuICAgICAgY29sb3I6ICdiZy1pbmRpZ28tNTAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ1RlbGVncmFtIEdyb3VwJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnUXVpY2sgdXBkYXRlcywgbmV3cywgYW5kIGluc3RhbnQgbm90aWZpY2F0aW9ucycsXG4gICAgICBpY29uOiBTZW5kLFxuICAgICAgbWVtYmVyczogJzUuMUsnLFxuICAgICAgbGluazogJyMnLFxuICAgICAgY29sb3I6ICdiZy1ibHVlLTUwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdHaXRIdWIgQ29tbXVuaXR5JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnT3BlbiBzb3VyY2UgY29udHJpYnV0aW9ucyBhbmQgY29kZSBjb2xsYWJvcmF0aW9uJyxcbiAgICAgIGljb246IEdpdGh1YixcbiAgICAgIG1lbWJlcnM6ICczLjhLJyxcbiAgICAgIGxpbms6ICcjJyxcbiAgICAgIGNvbG9yOiAnYmctZ3JheS04MDAnXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnVHdpdHRlci9YJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnTGF0ZXN0IHVwZGF0ZXMsIHRpcHMsIGFuZCBjeWJlcnNlY3VyaXR5IG5ld3MnLFxuICAgICAgaWNvbjogVHdpdHRlcixcbiAgICAgIG1lbWJlcnM6ICcxMi41SycsXG4gICAgICBsaW5rOiAnIycsXG4gICAgICBjb2xvcjogJ2JnLWJsYWNrJ1xuICAgIH1cbiAgXVxuXG4gIGNvbnN0IHVwY29taW5nRXZlbnRzID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAnQ3liZXJzZWN1cml0eSBXb3Jrc2hvcCcsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0xNScsXG4gICAgICB0aW1lOiAnMTk6MDAgV0lCJyxcbiAgICAgIHR5cGU6ICdXb3Jrc2hvcCcsXG4gICAgICBwYXJ0aWNpcGFudHM6IDE1NixcbiAgICAgIGRlc2NyaXB0aW9uOiAnQWR2YW5jZWQgcGVuZXRyYXRpb24gdGVzdGluZyB0ZWNobmlxdWVzJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdCdWcgQm91bnR5IEJvb3RjYW1wJyxcbiAgICAgIGRhdGU6ICcyMDI0LTAxLTIwJyxcbiAgICAgIHRpbWU6ICcxNDowMCBXSUInLFxuICAgICAgdHlwZTogJ0Jvb3RjYW1wJyxcbiAgICAgIHBhcnRpY2lwYW50czogODksXG4gICAgICBkZXNjcmlwdGlvbjogJ0Zyb20gYmVnaW5uZXIgdG8gcHJvZmVzc2lvbmFsIGJ1ZyBodW50ZXInXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ0NURiBDb21wZXRpdGlvbicsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0yNScsXG4gICAgICB0aW1lOiAnMTA6MDAgV0lCJyxcbiAgICAgIHR5cGU6ICdDb21wZXRpdGlvbicsXG4gICAgICBwYXJ0aWNpcGFudHM6IDIzNCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ2FwdHVyZSBUaGUgRmxhZyBjaGFsbGVuZ2UgZm9yIGFsbCBsZXZlbHMnXG4gICAgfVxuICBdXG5cbiAgY29uc3QgdG9wQ29udHJpYnV0b3JzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICdDeWJlck5pbmphJyxcbiAgICAgIGF2YXRhcjogJy9hcGkvcGxhY2Vob2xkZXIvNDAvNDAnLFxuICAgICAgcG9pbnRzOiAxNTQyMCxcbiAgICAgIGJhZGdlOiAnRWxpdGUgSHVudGVyJyxcbiAgICAgIGNvbnRyaWJ1dGlvbnM6IDg5XG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnU2VjdXJpdHlNYXN0ZXInLFxuICAgICAgYXZhdGFyOiAnL2FwaS9wbGFjZWhvbGRlci80MC80MCcsXG4gICAgICBwb2ludHM6IDEyMzUwLFxuICAgICAgYmFkZ2U6ICdCdWcgSHVudGVyJyxcbiAgICAgIGNvbnRyaWJ1dGlvbnM6IDY3XG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnUGVudGVzdFBybycsXG4gICAgICBhdmF0YXI6ICcvYXBpL3BsYWNlaG9sZGVyLzQwLzQwJyxcbiAgICAgIHBvaW50czogOTg3MCxcbiAgICAgIGJhZGdlOiAnU2VjdXJpdHkgRXhwZXJ0JyxcbiAgICAgIGNvbnRyaWJ1dGlvbnM6IDU0XG4gICAgfVxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8UHVibGljTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIG1iLTZcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXt0aGVtZUNsYXNzZXMuaXNEYXJrID8gXCJ0ZXh0LWN5YmVyLWdsb3dcIiA6IFwidGV4dC1ibHVlLTYwMFwifT5Kb2luIE91cjwvc3Bhbj57JyAnfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyBcInRleHQtY3liZXItcGlua1wiIDogXCJ0ZXh0LXBpbmstNjAwXCJ9PkN5YmVyIENvbW11bml0eTwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXhsIG1heC13LTN4bCBteC1hdXRvIG1iLTggJHt0aGVtZUNsYXNzZXMudGV4dFNlY29uZGFyeX1gfT5cbiAgICAgICAgICAgICAgQ29ubmVjdCB3aXRoIHRob3VzYW5kcyBvZiBjeWJlcnNlY3VyaXR5IHByb2Zlc3Npb25hbHMsIGJ1ZyBodW50ZXJzLCBhbmQgZXRoaWNhbCBoYWNrZXJzIGZyb20gYXJvdW5kIHRoZSB3b3JsZFxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogQ29tbXVuaXR5IFN0YXRzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC02IG1iLTEyXCI+XG4gICAgICAgICAgICAgIHtjb21tdW5pdHlTdGF0cy5tYXAoKHN0YXQsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IHN0YXQuaWNvblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT17YCR7dGhlbWVDbGFzc2VzLmNhcmR9IHRleHQtY2VudGVyYH0+XG4gICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT17YGgtOCB3LTggJHtzdGF0LmNvbG9yfSBteC1hdXRvIG1iLTMgJHt0aGVtZUNsYXNzZXMuaXNEYXJrID8gJ2FuaW1hdGUtY3liZXItcHVsc2UnIDogJyd9YH0gLz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmb250LWJvbGQgJHt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9IG1iLTFgfT5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RhdC52YWx1ZX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RoZW1lQ2xhc3Nlcy50ZXh0TXV0ZWR9IG1iLTFgfT5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RhdC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdGF0LmNoYW5nZX0gdGhpcyBtb250aFxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgeyBpZDogJ292ZXJ2aWV3JywgbGFiZWw6ICdPdmVydmlldycsIGljb246IFVzZXJzIH0sXG4gICAgICAgICAgICAgIHsgaWQ6ICdjaGFubmVscycsIGxhYmVsOiAnQ2hhbm5lbHMnLCBpY29uOiBNZXNzYWdlU3F1YXJlIH0sXG4gICAgICAgICAgICAgIHsgaWQ6ICdldmVudHMnLCBsYWJlbDogJ0V2ZW50cycsIGljb246IENhbGVuZGFyIH0sXG4gICAgICAgICAgICAgIHsgaWQ6ICdsZWFkZXJib2FyZCcsIGxhYmVsOiAnVG9wIENvbnRyaWJ1dG9ycycsIGljb246IFRyb3BoeSB9XG4gICAgICAgICAgICBdLm1hcCgodGFiKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IEljb24gPSB0YWIuaWNvblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNiBweS0zIG14LTIgbWItMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09IHRhYi5pZFxuICAgICAgICAgICAgICAgICAgICAgID8gdGhlbWVDbGFzc2VzLmlzRGFyayBcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWN5YmVyLXByaW1hcnkvMjAgdGV4dC1jeWJlci1wcmltYXJ5IGJvcmRlci0yIGJvcmRlci1jeWJlci1wcmltYXJ5JyBcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS02MDAgYm9yZGVyLTIgYm9yZGVyLWJsdWUtNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogYCR7dGhlbWVDbGFzc2VzLnRleHRTZWNvbmRhcnl9IGhvdmVyOiR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBob3Zlcjoke3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYmctY3liZXItcHJpbWFyeS8xMCcgOiAnYmctZ3JheS0xMDAnfWBcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e3RhYi5sYWJlbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRhYiBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTZcIj5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdvdmVydmlldycgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17dGhlbWVDbGFzc2VzLmNhcmR9PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17YHRleHQtMnhsIGZvbnQtYm9sZCAke3RoZW1lQ2xhc3Nlcy50ZXh0UHJpbWFyeX0gbWItNGB9PlxuICAgICAgICAgICAgICAgICAgICBXZWxjb21lIHRvIEtvZGVYR3VhcmQgQ29tbXVuaXR5XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgJHt0aGVtZUNsYXNzZXMudGV4dFNlY29uZGFyeX0gbWItNmB9PlxuICAgICAgICAgICAgICAgICAgICBPdXIgY29tbXVuaXR5IGlzIGEgdmlicmFudCBlY29zeXN0ZW0gb2YgY3liZXJzZWN1cml0eSBlbnRodXNpYXN0cywgcHJvZmVzc2lvbmFsIHBlbmV0cmF0aW9uIHRlc3RlcnMsIFxuICAgICAgICAgICAgICAgICAgICBidWcgYm91bnR5IGh1bnRlcnMsIGFuZCBzZWN1cml0eSByZXNlYXJjaGVycyB3aG8gc2hhcmUga25vd2xlZGdlLCBjb2xsYWJvcmF0ZSBvbiBwcm9qZWN0cywgYW5kIGhlbHAgXG4gICAgICAgICAgICAgICAgICAgIGVhY2ggb3RoZXIgZ3JvdyBpbiB0aGUgZmllbGQgb2YgY3liZXJzZWN1cml0eS5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtY3liZXItcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9PlNoYXJlIHNlY3VyaXR5IHJlc2VhcmNoIGFuZCBmaW5kaW5nczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtY3liZXItc2Vjb25kYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3RoZW1lQ2xhc3Nlcy50ZXh0UHJpbWFyeX0+Q29sbGFib3JhdGUgb24gYnVnIGJvdW50eSBwcm9ncmFtczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtY3liZXItYWNjZW50XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3RoZW1lQ2xhc3Nlcy50ZXh0UHJpbWFyeX0+TGVhcm4gZnJvbSBpbmR1c3RyeSBleHBlcnRzPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QXdhcmQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9PlBhcnRpY2lwYXRlIGluIGNvbXBldGl0aW9ucyBhbmQgQ1RGczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXt0aGVtZUNsYXNzZXMuY2FyZH0+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgdGV4dC0yeGwgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi00YH0+XG4gICAgICAgICAgICAgICAgICAgIENvbW11bml0eSBHdWlkZWxpbmVzXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTQgcm91bmRlZC1sZyAke3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYmctY3liZXItcHJpbWFyeS8xMCcgOiAnYmctYmx1ZS01MCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17YGZvbnQtc2VtaWJvbGQgJHt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9IG1iLTJgfT7wn6SdIEJlIFJlc3BlY3RmdWw8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gJHt0aGVtZUNsYXNzZXMudGV4dFNlY29uZGFyeX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRyZWF0IGFsbCBjb21tdW5pdHkgbWVtYmVycyB3aXRoIHJlc3BlY3QgYW5kIHByb2Zlc3Npb25hbGlzbVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgJHt0aGVtZUNsYXNzZXMuaXNEYXJrID8gJ2JnLWN5YmVyLXNlY29uZGFyeS8xMCcgOiAnYmctcGluay01MCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17YGZvbnQtc2VtaWJvbGQgJHt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9IG1iLTJgfT7wn5SSIEV0aGljYWwgUHJhY3RpY2VzPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtICR7dGhlbWVDbGFzc2VzLnRleHRTZWNvbmRhcnl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICBPbmx5IGRpc2N1c3MgZXRoaWNhbCBoYWNraW5nIGFuZCByZXNwb25zaWJsZSBkaXNjbG9zdXJlXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTQgcm91bmRlZC1sZyAke3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYmctY3liZXItYWNjZW50LzEwJyA6ICdiZy15ZWxsb3ctNTAnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi0yYH0+8J+TmiBTaGFyZSBLbm93bGVkZ2U8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gJHt0aGVtZUNsYXNzZXMudGV4dFNlY29uZGFyeX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIEhlbHAgb3RoZXJzIGxlYXJuIGFuZCBncm93IGluIGN5YmVyc2VjdXJpdHlcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2NoYW5uZWxzJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIHtjb21tdW5pdHlDaGFubmVscy5tYXAoKGNoYW5uZWwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gY2hhbm5lbC5pY29uXG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT17YCR7dGhlbWVDbGFzc2VzLmNhcmR9IGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBgfT5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnICR7Y2hhbm5lbC5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17YHRleHQteGwgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi0yYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NoYW5uZWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgJHt0aGVtZUNsYXNzZXMudGV4dFNlY29uZGFyeX0gbWItNGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjaGFubmVsLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RoZW1lQ2xhc3Nlcy50ZXh0TXV0ZWR9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hhbm5lbC5tZW1iZXJzfSBtZW1iZXJzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMiByb3VuZGVkLWxnICR7dGhlbWVDbGFzc2VzLmlzRGFyayA/ICdidG4tY3liZXItcHJpbWFyeScgOiAnYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSd9IHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Sm9pbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdldmVudHMnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgdGV4dC0yeGwgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi02YH0+XG4gICAgICAgICAgICAgICAgICBVcGNvbWluZyBFdmVudHNcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIHt1cGNvbWluZ0V2ZW50cy5tYXAoKGV2ZW50LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9e2Ake3RoZW1lQ2xhc3Nlcy5jYXJkfSBob3Zlcjoke3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYm9yZGVyLWN5YmVyLXByaW1hcnknIDogJ2JvcmRlci1ibHVlLTUwMCd9IHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMGB9PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgbWQ6aXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50LnR5cGUgPT09ICdXb3Jrc2hvcCcgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50LnR5cGUgPT09ICdCb290Y2FtcCcgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LnR5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RoZW1lQ2xhc3Nlcy50ZXh0TXV0ZWR9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2V2ZW50LnBhcnRpY2lwYW50c30gcGFydGljaXBhbnRzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17YHRleHQteGwgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi0yYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtldmVudC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2Ake3RoZW1lQ2xhc3Nlcy50ZXh0U2Vjb25kYXJ5fSBtYi0zYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtldmVudC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1jeWJlci1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtICR7dGhlbWVDbGFzc2VzLnRleHRNdXRlZH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtldmVudC5kYXRlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtY3liZXItc2Vjb25kYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtICR7dGhlbWVDbGFzc2VzLnRleHRNdXRlZH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtldmVudC50aW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgbWQ6bXQtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e2Ake3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYnRuLWN5YmVyLXByaW1hcnknIDogJ2JnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgcHgtNiBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVnaXN0ZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdsZWFkZXJib2FyZCcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmb250LWJvbGQgJHt0aGVtZUNsYXNzZXMudGV4dFByaW1hcnl9IG1iLTZgfT5cbiAgICAgICAgICAgICAgICAgIFRvcCBDb250cmlidXRvcnMgVGhpcyBNb250aFxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAge3RvcENvbnRyaWJ1dG9ycy5tYXAoKGNvbnRyaWJ1dG9yLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9e2Ake3RoZW1lQ2xhc3Nlcy5jYXJkfSBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwYH0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTEyIGgtMTIgcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IDAgPyAnZnJvbS15ZWxsb3ctNDAwIHRvLXllbGxvdy02MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPT09IDEgPyAnZnJvbS1ncmF5LTMwMCB0by1ncmF5LTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAnZnJvbS1vcmFuZ2UtNDAwIHRvLW9yYW5nZS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkXCI+I3tpbmRleCArIDF9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9e2B0ZXh0LWxnIGZvbnQtYm9sZCAke3RoZW1lQ2xhc3Nlcy50ZXh0UHJpbWFyeX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnRyaWJ1dG9yLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RoZW1lQ2xhc3Nlcy50ZXh0U2Vjb25kYXJ5fWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udHJpYnV0b3IuYmFkZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtbGcgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udHJpYnV0b3IucG9pbnRzLnRvTG9jYWxlU3RyaW5nKCl9IHB0c1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtc20gJHt0aGVtZUNsYXNzZXMudGV4dE11dGVkfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y29udHJpYnV0b3IuY29udHJpYnV0aW9uc30gY29udHJpYnV0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDYWxsIHRvIEFjdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtY2VudGVyICR7dGhlbWVDbGFzc2VzLmNhcmR9YH0+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgdGV4dC0zeGwgZm9udC1ib2xkICR7dGhlbWVDbGFzc2VzLnRleHRQcmltYXJ5fSBtYi00YH0+XG4gICAgICAgICAgICAgIFJlYWR5IHRvIEpvaW4gT3VyIENvbW11bml0eT9cbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LWxnICR7dGhlbWVDbGFzc2VzLnRleHRTZWNvbmRhcnl9IG1iLTggbWF4LXctMnhsIG14LWF1dG9gfT5cbiAgICAgICAgICAgICAgQ29ubmVjdCB3aXRoIGxpa2UtbWluZGVkIGN5YmVyc2VjdXJpdHkgcHJvZmVzc2lvbmFscyBhbmQgdGFrZSB5b3VyIHNraWxscyB0byB0aGUgbmV4dCBsZXZlbFxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPXt0aGVtZUNsYXNzZXMuaXNEYXJrID8gXCJidG4tY3liZXItcHJpbWFyeVwiIDogXCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHB4LTggcHktMyByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwifT5cbiAgICAgICAgICAgICAgICBKb2luIERpc2NvcmQgU2VydmVyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT17YHB4LTggcHktMyByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCAke3RoZW1lQ2xhc3Nlcy5pc0RhcmsgPyAnYm9yZGVyLTIgYm9yZGVyLWN5YmVyLXNlY29uZGFyeSB0ZXh0LWN5YmVyLXNlY29uZGFyeSBob3ZlcjpiZy1jeWJlci1zZWNvbmRhcnkgaG92ZXI6dGV4dC1ibGFjaycgOiAnYm9yZGVyLTIgYm9yZGVyLXBpbmstNTAwIHRleHQtcGluay02MDAgaG92ZXI6YmctcGluay01MDAgaG92ZXI6dGV4dC13aGl0ZSd9YH0+XG4gICAgICAgICAgICAgICAgRm9sbG93IG9uIFR3aXR0ZXJcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L1B1YmxpY0xheW91dD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiUHVibGljTGF5b3V0IiwiVXNlcnMiLCJNZXNzYWdlU3F1YXJlIiwiVHJvcGh5IiwiQ2FsZW5kYXIiLCJNYXBQaW4iLCJFeHRlcm5hbExpbmsiLCJHaXRodWIiLCJUd2l0dGVyIiwiTWVzc2FnZUNpcmNsZSIsIlNlbmQiLCJTaGllbGQiLCJaYXAiLCJUYXJnZXQiLCJBd2FyZCIsIkFjdGl2aXR5IiwiQ29tbXVuaXR5UGFnZSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInN0YXRzIiwic2V0U3RhdHMiLCJ0b3RhbE1lbWJlcnMiLCJhY3RpdmVUb2RheSIsInRvdGFsUG9zdHMiLCJ0b3RhbEV2ZW50cyIsInRoZW1lQ2xhc3NlcyIsInVzZVRoZW1lQ2xhc3NlcyIsImNvbW11bml0eVN0YXRzIiwiaWNvbiIsImxhYmVsIiwidmFsdWUiLCJ0b0xvY2FsZVN0cmluZyIsImNoYW5nZSIsImNvbG9yIiwiY29tbXVuaXR5Q2hhbm5lbHMiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJtZW1iZXJzIiwibGluayIsInVwY29taW5nRXZlbnRzIiwidGl0bGUiLCJkYXRlIiwidGltZSIsInR5cGUiLCJwYXJ0aWNpcGFudHMiLCJ0b3BDb250cmlidXRvcnMiLCJhdmF0YXIiLCJwb2ludHMiLCJiYWRnZSIsImNvbnRyaWJ1dGlvbnMiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInNwYW4iLCJpc0RhcmsiLCJwIiwidGV4dFNlY29uZGFyeSIsIm1hcCIsInN0YXQiLCJpbmRleCIsIkljb24iLCJjYXJkIiwidGV4dFByaW1hcnkiLCJ0ZXh0TXV0ZWQiLCJpZCIsInRhYiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMyIsImg0IiwiY2hhbm5lbCIsImV2ZW50IiwiY29udHJpYnV0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/community/page.tsx\n"));

/***/ })

});