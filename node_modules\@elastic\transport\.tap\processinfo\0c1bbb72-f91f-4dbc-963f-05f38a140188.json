{"date": "2025-07-01T21:18:51.499Z", "argv": ["/opt/hostedtoolcache/node/20.19.2/x64/bin/node", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/acceptance/observability.test.ts"], "execArgv": ["--import=file:///home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/ts-node-temp-fork-for-pr-2009/import.mjs", "--import=file:///home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/import.mjs", "--enable-source-maps", "--import=file:///home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/processinfo/dist/esm/import.mjs"], "NODE_OPTIONS": "\"--import=file:///home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/processinfo/dist/esm/import.mjs\"", "cwd": "/home/<USER>/work/elastic-transport-js/elastic-transport-js", "pid": 2463, "ppid": 2325, "parent": null, "uuid": "0c1bbb72-f91f-4dbc-963f-05f38a140188", "files": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/acceptance/observability.test.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/ts-node-temp-fork-for-pr-2009/import.mjs", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/ts-node-temp-fork-for-pr-2009/import-loader.mjs", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/import.mjs", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/mock-service.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/is-relative-require.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/export-line.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/munge-mocks.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/resolve-mock-entry-point.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/service-key.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/call-site-like.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/require-resolve.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-all-conditional-values.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-all-conditions.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-unique-condition-sets.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-all-exports.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-all-local-imports.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-conditional-value.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-import.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/is-windows.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-conditional-values-list.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/parse.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/errors.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-named-exports-list.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/read-pkg.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-export.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/star-glob.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/to-path.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/file-exists.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/find-dep-package.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/to-file-url.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-dependency-export.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-package-import.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/read-json.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/find-star-match.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/walk-up-path/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/has-magic.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/ignore.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/glob.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/brace-expansion/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/assert-valid-pattern.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/ast.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/escape.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/unescape.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/brace-expressions.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/pattern.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/path-scurry/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/walker.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/balanced-match/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minipass/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/processor.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/lru-cache/dist/esm/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/loader.mjs", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/hooks.mjs", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/mock-service-client.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/Transport.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/errors.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/security.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/connection/BaseConnection.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/Diagnostic.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/symbols.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/Serializer.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/connection/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/connection/HttpConnection.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/connection/UndiciConnection.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/pool/index.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/pool/BaseConnectionPool.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/pool/WeightedConnectionPool.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/pool/ClusterConnectionPool.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/lib/pool/CloudConnectionPool.js", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/index.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/buildServer.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/MockConnection.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/TestClient.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/buildCluster.ts", "/home/<USER>/work/elastic-transport-js/elastic-transport-js/test/utils/buildProxy.ts"], "sources": {"/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/import.mjs": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/import.mts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/mock-service.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/mock-service.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/is-relative-require.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/is-relative-require.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/export-line.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/export-line.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/munge-mocks.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/munge-mocks.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/resolve-mock-entry-point.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/resolve-mock-entry-point.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/dist/esm/service-key.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/mock/src/service-key.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/call-site-like.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/src/call-site-like.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/require-resolve.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/src/require-resolve.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-all-conditional-values.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/get-all-conditional-values.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-all-conditions.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/get-all-conditions.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-unique-condition-sets.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/get-unique-condition-sets.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-all-exports.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-all-exports.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-all-local-imports.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-all-local-imports.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-conditional-value.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-conditional-value.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-import.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-import.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/is-windows.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/is-windows.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-conditional-values-list.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/get-conditional-values-list.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/dist/esm/parse.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@tapjs/stack/src/parse.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/errors.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/errors.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/get-named-exports-list.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/get-named-exports-list.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/read-pkg.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/read-pkg.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-export.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-export.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/star-glob.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/star-glob.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/to-path.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/to-path.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/file-exists.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/file-exists.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/find-dep-package.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/find-dep-package.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/to-file-url.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/to-file-url.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-dependency-export.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-dependency-export.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/resolve-package-import.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/resolve-package-import.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/read-json.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/read-json.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/dist/esm/find-star-match.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/resolve-import/src/find-star-match.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/walk-up-path/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/walk-up-path/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/has-magic.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/has-magic.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/ignore.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/ignore.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/glob.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/glob.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/brace-expansion/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/brace-expansion/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/assert-valid-pattern.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/assert-valid-pattern.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/ast.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/ast.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/escape.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/escape.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/unescape.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/unescape.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/dist/esm/brace-expressions.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minimatch/src/brace-expressions.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/pattern.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/pattern.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/path-scurry/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/path-scurry/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/walker.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/walker.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/balanced-match/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/@isaacs/balanced-match/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minipass/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/minipass/src/index.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/dist/esm/processor.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/glob/src/processor.ts"], "/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/lru-cache/dist/esm/index.js": ["/home/<USER>/work/elastic-transport-js/elastic-transport-js/node_modules/lru-cache/src/index.ts"]}, "root": "0c1bbb72-f91f-4dbc-963f-05f38a140188", "externalID": "test/acceptance/observability.test.ts", "code": 0, "signal": null, "runtime": 2040.531893, "globalsAdded": ["__extends", "__assign", "__rest", "__decorate", "__param", "__esDecorate", "__runInitializers", "__prop<PERSON>ey", "__setFunctionName", "__metadata", "__awaiter", "__generator", "__exportStar", "__createBinding", "__values", "__read", "__spread", "__spreadA<PERSON>ys", "__spread<PERSON><PERSON>y", "__await", "__asyncGenerator", "__asyncDelegator", "__asyncValues", "__makeTemplateObject", "__importStar", "__importDefault", "__classPrivateFieldGet", "__classPrivateFieldSet", "__classPrivateFieldIn", "__addDisposableResource", "__disposeResources", "__rewriteRelativeImportExtension"]}