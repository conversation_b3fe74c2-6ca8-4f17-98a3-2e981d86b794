'use client'

import { useState, useEffect } from 'react'
import { 
  Shield, 
  Search, 
  FileText, 
  Bug, 
  Zap, 
  Users, 
  BarChart3, 
  Lock,
  Globe,
  Database,
  Bot,
  Crown
} from 'lucide-react'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalScans: 0,
    vulnerabilities: 0,
    users: 0,
    cveDatabase: 0
  })

  useEffect(() => {
    // Simulate loading stats
    const timer = setTimeout(() => {
      setStats({
        totalScans: 15420,
        vulnerabilities: 2847,
        users: 1205,
        cveDatabase: 8934
      })
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const features = [
    {
      icon: Search,
      title: 'OSINT Investigator',
      description: 'Investigasi mendalam dengan data NIK, NPWP, nomor HP, IMEI, dan informasi sensitif lainnya',
      color: 'text-blue-600'
    },
    {
      icon: Shield,
      title: 'Vulnerability Scanner',
      description: 'Deteksi SQLi, XSS, LFI, RCE, dan celah keamanan lainnya dengan scoring CVSS',
      color: 'text-red-600'
    },
    {
      icon: FileText,
      title: 'File Analyzer',
      description: 'Analisis file untuk mendeteksi webshell, malware, dan secret/token tersembunyi',
      color: 'text-green-600'
    },
    {
      icon: Bug,
      title: 'CVE Intelligence',
      description: 'Database CVE terbaru dengan update harian dan pencarian berdasarkan kategori',
      color: 'text-purple-600'
    },
    {
      icon: Globe,
      title: 'Google Dorking',
      description: 'Preset dork harian dan custom dork untuk pencarian informasi sensitif',
      color: 'text-orange-600'
    },
    {
      icon: Bot,
      title: 'Bot Automation',
      description: 'Integrasi WhatsApp dan Telegram untuk eksekusi scan dan notifikasi real-time',
      color: 'text-cyan-600'
    }
  ]

  const plans = [
    {
      name: 'Gratis',
      price: 'Rp 0',
      period: '/bulan',
      features: ['10 scan/hari', 'Basic OSINT', 'CVE lookup', 'Community support'],
      color: 'border-gray-200'
    },
    {
      name: 'Pelajar',
      price: 'Rp 25.000',
      period: '/bulan',
      features: ['100 scan/hari', 'Full OSINT', 'File analyzer', 'Bot access', 'Priority support'],
      color: 'border-blue-200',
      popular: true
    },
    {
      name: 'Cybersecurity',
      price: 'Rp 150.000',
      period: '/bulan',
      features: ['Unlimited scan', 'Advanced features', 'API access', 'Custom integrations', '24/7 support'],
      color: 'border-purple-200'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-primary-600" />
              <h1 className="text-2xl font-bold text-gray-900">KodeXGuard</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900">Features</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900">Pricing</a>
              <a href="#about" className="text-gray-600 hover:text-gray-900">About</a>
            </nav>
            <div className="flex space-x-4">
              <button className="text-gray-600 hover:text-gray-900">Login</button>
              <button className="btn-primary">Get Started</button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Platform Cybersecurity & 
            <span className="text-primary-600"> Bug Hunting</span> Terdepan
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Solusi lengkap untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, 
            dan komunitas Bug Hunter dengan dukungan bot automation dan API mandiri.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary text-lg px-8 py-3">
              Mulai Gratis
            </button>
            <button className="btn-secondary text-lg px-8 py-3">
              Lihat Demo
            </button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600">{stats.totalScans.toLocaleString()}</div>
              <div className="text-gray-600">Total Scans</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">{stats.vulnerabilities.toLocaleString()}</div>
              <div className="text-gray-600">Vulnerabilities Found</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{stats.users.toLocaleString()}</div>
              <div className="text-gray-600">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{stats.cveDatabase.toLocaleString()}</div>
              <div className="text-gray-600">CVE Database</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Fitur Unggulan</h3>
            <p className="text-xl text-gray-600">Platform all-in-one untuk kebutuhan cybersecurity Anda</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card hover:shadow-lg transition-shadow duration-300">
                <feature.icon className={`h-12 w-12 ${feature.color} mb-4`} />
                <h4 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h4>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Pilih Plan Anda</h3>
            <p className="text-xl text-gray-600">Mulai gratis, upgrade sesuai kebutuhan</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div key={index} className={`card ${plan.color} border-2 relative ${plan.popular ? 'ring-2 ring-primary-500' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                      <Crown className="h-4 w-4 mr-1" />
                      Popular
                    </span>
                  </div>
                )}
                <div className="text-center mb-6">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">{plan.name}</h4>
                  <div className="text-3xl font-bold text-gray-900">
                    {plan.price}
                    <span className="text-lg font-normal text-gray-600">{plan.period}</span>
                  </div>
                </div>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600">
                      <Zap className="h-4 w-4 text-green-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
                <button className={`w-full ${plan.popular ? 'btn-primary' : 'btn-secondary'}`}>
                  Pilih Plan
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Shield className="h-8 w-8 text-primary-400" />
                <h5 className="text-xl font-bold">KodeXGuard</h5>
              </div>
              <p className="text-gray-400">
                Platform cybersecurity terdepan untuk profesional dan enthusiast keamanan siber.
              </p>
            </div>
            <div>
              <h6 className="font-semibold mb-4">Platform</h6>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">OSINT Tools</a></li>
                <li><a href="#" className="hover:text-white">Vulnerability Scanner</a></li>
                <li><a href="#" className="hover:text-white">File Analyzer</a></li>
                <li><a href="#" className="hover:text-white">CVE Database</a></li>
              </ul>
            </div>
            <div>
              <h6 className="font-semibold mb-4">Community</h6>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Bug Hunters</a></li>
                <li><a href="#" className="hover:text-white">Leaderboard</a></li>
                <li><a href="#" className="hover:text-white">Discord</a></li>
                <li><a href="#" className="hover:text-white">Telegram</a></li>
              </ul>
            </div>
            <div>
              <h6 className="font-semibold mb-4">Support</h6>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Documentation</a></li>
                <li><a href="#" className="hover:text-white">API Reference</a></li>
                <li><a href="#" className="hover:text-white">Help Center</a></li>
                <li><a href="#" className="hover:text-white">Contact</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 KodeXGuard. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
