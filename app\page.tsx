'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import PublicLayout from '@/components/PublicLayout'
import { 
  Shield, 
  Search, 
  FileText, 
  Database,
  Globe,
  Cpu,
  Zap, 
  Users, 
  Trophy,
  Star,
  ArrowRight,
  CheckCircle,
  Play,
  Target,
  Eye,
  Code,
  Activity,
  Terminal,
  Crown,
  Lock,
  Wifi
} from 'lucide-react'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalScans: 0,
    vulnerabilities: 0,
    users: 0,
    cveDatabase: 0
  })

  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
    
    // Simulate loading stats with animation
    const timer = setTimeout(() => {
      setStats({
        totalScans: 15420,
        vulnerabilities: 2847,
        users: 12050,
        cveDatabase: 89340
      })
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const features = [
    {
      icon: Search,
      title: 'OSINT Investigator',
      description: 'Advanced intelligence gathering with NIK, NPWP, phone numbers, IMEI, and sensitive data investigation',
      color: 'text-cyber-primary',
      gradient: 'from-cyan-500 to-blue-500',
      href: '/osint'
    },
    {
      icon: Shield,
      title: 'Vulnerability Scanner',
      description: 'Automated security scanning for SQLi, XSS, LFI, RCE, and other critical vulnerabilities with CVSS scoring',
      color: 'text-cyber-secondary',
      gradient: 'from-pink-500 to-purple-500',
      href: '/scanner'
    },
    {
      icon: FileText,
      title: 'File Analyzer',
      description: 'Deep file analysis for webshells, malware, ransomware, and hidden threats detection',
      color: 'text-cyber-accent',
      gradient: 'from-yellow-500 to-orange-500',
      href: '/file-analyzer'
    },
    {
      icon: Database,
      title: 'CVE Intelligence',
      description: 'Comprehensive CVE database with daily updates and latest vulnerability intelligence',
      color: 'text-red-400',
      gradient: 'from-red-500 to-pink-500',
      href: '/cve'
    },
    {
      icon: Globe,
      title: 'Google Dorking',
      description: 'Extensive dork collection for sensitive information discovery and security research',
      color: 'text-green-400',
      gradient: 'from-green-500 to-emerald-500',
      href: '/dorking'
    },
    {
      icon: Cpu,
      title: 'Developer Tools',
      description: 'Essential security tools including hash generators, encoders, and payload builders',
      color: 'text-purple-400',
      gradient: 'from-purple-500 to-indigo-500',
      href: '/tools'
    }
  ]

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: '/month',
      features: ['10 scans/day', 'Basic OSINT', 'CVE lookup', 'Community support'],
      color: 'border-gray-600',
      popular: false
    },
    {
      name: 'Student',
      price: '$9',
      period: '/month',
      features: ['100 scans/day', 'Full OSINT', 'File analyzer', 'Bot access', 'Priority support'],
      color: 'border-cyber-primary',
      popular: true
    },
    {
      name: 'Cybersecurity',
      price: '$49',
      period: '/month',
      features: ['Unlimited scans', 'Advanced features', 'API access', 'Custom integrations', '24/7 support'],
      color: 'border-cyber-secondary',
      popular: false
    }
  ]

  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Cyber Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-cyber-bg-primary via-cyber-bg-secondary to-cyber-bg-tertiary"></div>
          
          {/* Animated Cyber Lines */}
          <div className="absolute inset-0">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className="absolute h-px bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-30 animate-cyber-scan"
                style={{
                  top: `${10 + i * 10}%`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${3 + i * 0.2}s`
                }}
              ></div>
            ))}
          </div>
          
          {/* Floating Particles */}
          <div className="absolute inset-0">
            {Array.from({ length: 50 }, (_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-cyber-primary rounded-full animate-matrix-rain opacity-40"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${5 + Math.random() * 3}s`
                }}
              ></div>
            ))}
          </div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            {/* Logo with Cyber Effect */}
            <div className="flex justify-center mb-8">
              <div className="relative group">
                <Shield className="h-24 w-24 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-30 blur-xl animate-cyber-pulse"></div>
                <div className="absolute -inset-4 border border-cyber-primary opacity-20 rounded-full animate-ping"></div>
              </div>
            </div>
            
            {/* Main Heading with Glitch Effect */}
            <h1 className={`text-5xl md:text-7xl lg:text-8xl font-bold mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <span className="text-cyber-glow animate-glitch">
                KodeX
              </span>
              <span className="text-cyber-pink">
                Guard
              </span>
            </h1>
            
            <div className={`transition-all duration-1000 delay-300 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <p className="text-xl md:text-3xl text-cyber-secondary mb-4 font-bold uppercase tracking-wider">
                Cybersecurity & Bug Hunting Platform
              </p>
              
              <p className="text-lg text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
                Advanced OSINT Investigation • Vulnerability Scanner • File Analyzer • CVE Intelligence • 
                Google Dorking • Developer Tools in one integrated cyberpunk platform
              </p>
            </div>
            
            {/* CTA Buttons */}
            <div className={`flex flex-col sm:flex-row gap-6 justify-center mb-16 transition-all duration-1000 delay-500 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <Link href="/register" className="btn-cyber-primary text-lg px-8 py-4">
                <Zap className="h-5 w-5 mr-2" />
                Start Hacking
              </Link>
              <Link href="/demo" className="btn-cyber-secondary text-lg px-8 py-4">
                <Play className="h-5 w-5 mr-2" />
                Watch Demo
              </Link>
            </div>
            
            {/* Cyber Stats */}
            <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto transition-all duration-1000 delay-700 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-primary mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.totalScans.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Security Scans</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-secondary mb-2 animate-cyber-glow-pink group-hover:animate-cyber-pulse">
                  {stats.vulnerabilities.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Vulnerabilities</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-cyber-accent mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.users.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">Cyber Warriors</div>
              </div>
              <div className="text-center group">
                <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2 animate-cyber-glow group-hover:animate-cyber-pulse">
                  {stats.cveDatabase.toLocaleString()}+
                </div>
                <div className="text-gray-400 uppercase tracking-wider font-medium">CVE Database</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-cyber-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-cyber-primary rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              <span className="text-cyber-glow">Cyber</span> Arsenal
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Advanced cybersecurity tools and intelligence gathering capabilities 
              designed for modern security professionals and ethical hackers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Link
                  key={feature.title}
                  href={feature.href}
                  className="group card-cyber hover:border-cyber-primary transition-all duration-300"
                >
                  <div className="relative">
                    <div className={`inline-flex p-4 rounded-lg bg-gradient-to-r ${feature.gradient} mb-6 group-hover:animate-cyber-pulse`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h3 className={`text-xl font-bold mb-4 ${feature.color} group-hover:text-white transition-colors duration-300`}>
                      {feature.title}
                    </h3>
                    
                    <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 leading-relaxed">
                      {feature.description}
                    </p>
                    
                    <div className="mt-6 flex items-center text-cyber-primary group-hover:text-white transition-colors duration-300">
                      <span className="text-sm font-medium">Explore Tool</span>
                      <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>
    </PublicLayout>
  )
}
