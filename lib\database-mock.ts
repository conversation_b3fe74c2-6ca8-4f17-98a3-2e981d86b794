// Mock database implementation for testing without MySQL
// This provides realistic data for all features to work

export interface MockUser {
  id: number
  username: string
  email: string
  full_name: string
  role: 'user' | 'admin' | 'moderator'
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak_days: number
  email_verified: boolean
  last_active: Date
  created_at: Date
}

export interface MockScan {
  id: number
  user_id: number
  target_url: string
  scan_type: string
  status: string
  vulnerabilities_found: number
  severity_critical: number
  severity_high: number
  severity_medium: number
  severity_low: number
  created_at: Date
  completed_at?: Date
}

export interface MockOSINTQuery {
  id: number
  user_id: number
  query_type: string
  query_value: string
  status: string
  results?: any
  created_at: Date
  completed_at?: Date
}

export interface MockFileAnalysis {
  id: number
  user_id: number
  filename: string
  file_size: number
  threat_detected: boolean
  threat_type?: string
  status: string
  created_at: Date
  completed_at?: Date
}

// Mock data
const mockUsers: MockUser[] = [
  {
    id: 1,
    username: 'cyberwarrior',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    role: 'user',
    plan: 'Pro',
    level: 15,
    score: 2500,
    streak_days: 7,
    email_verified: true,
    last_active: new Date(),
    created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  },
  {
    id: 2,
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'System Administrator',
    role: 'admin',
    plan: 'Elite',
    level: 100,
    score: 50000,
    streak_days: 365,
    email_verified: true,
    last_active: new Date(),
    created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
  },
  {
    id: 3,
    username: 'securityexpert',
    email: '<EMAIL>',
    full_name: 'Sarah Johnson',
    role: 'user',
    plan: 'Expert',
    level: 28,
    score: 8950,
    streak_days: 12,
    email_verified: true,
    last_active: new Date(Date.now() - 2 * 60 * 60 * 1000),
    created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
  }
]

const mockScans: MockScan[] = [
  {
    id: 1,
    user_id: 1,
    target_url: 'https://example.com',
    scan_type: 'comprehensive',
    status: 'completed',
    vulnerabilities_found: 15,
    severity_critical: 2,
    severity_high: 5,
    severity_medium: 6,
    severity_low: 2,
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
    completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    id: 2,
    user_id: 1,
    target_url: 'https://testsite.org',
    scan_type: 'advanced',
    status: 'completed',
    vulnerabilities_found: 8,
    severity_critical: 0,
    severity_high: 2,
    severity_medium: 4,
    severity_low: 2,
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
    completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000)
  },
  {
    id: 3,
    user_id: 2,
    target_url: 'https://vulnerable-app.com',
    scan_type: 'comprehensive',
    status: 'completed',
    vulnerabilities_found: 23,
    severity_critical: 5,
    severity_high: 8,
    severity_medium: 7,
    severity_low: 3,
    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    completed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)
  }
]

const mockOSINTQueries: MockOSINTQuery[] = [
  {
    id: 1,
    user_id: 1,
    query_type: 'email',
    query_value: '<EMAIL>',
    status: 'completed',
    results: { breaches: ['Adobe', 'LinkedIn'], found: true },
    created_at: new Date(Date.now() - 30 * 60 * 1000),
    completed_at: new Date(Date.now() - 25 * 60 * 1000)
  },
  {
    id: 2,
    user_id: 1,
    query_type: 'domain',
    query_value: 'example.com',
    status: 'completed',
    results: { subdomains: ['www', 'api', 'admin'], found: true },
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
    completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000)
  }
]

const mockFileAnalyses: MockFileAnalysis[] = [
  {
    id: 1,
    user_id: 1,
    filename: 'suspicious.exe',
    file_size: 2048576,
    threat_detected: true,
    threat_type: 'malware',
    status: 'completed',
    created_at: new Date(Date.now() - 45 * 60 * 1000),
    completed_at: new Date(Date.now() - 40 * 60 * 1000)
  },
  {
    id: 2,
    user_id: 1,
    filename: 'clean-file.pdf',
    file_size: 1024000,
    threat_detected: false,
    status: 'completed',
    created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),
    completed_at: new Date(Date.now() - 3 * 60 * 60 * 1000 + 2 * 60 * 1000)
  }
]

// Mock database class
export class MockDatabase {
  static async query(sql: string, params: any[] = []): Promise<any[]> {
    console.log(`Mock DB Query: ${sql}`, params)
    
    // Simulate database delay
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // Parse SQL and return appropriate mock data
    if (sql.includes('SELECT * FROM users WHERE id = ?')) {
      const userId = params[0]
      return mockUsers.filter(u => u.id === userId)
    }
    
    if (sql.includes('SELECT COUNT(*) as count FROM users')) {
      return [{ count: mockUsers.length }]
    }
    
    if (sql.includes('SELECT COUNT(*) as count FROM vulnerability_scans')) {
      return [{ count: mockScans.length }]
    }
    
    if (sql.includes('SELECT * FROM vulnerability_scans WHERE user_id = ?')) {
      const userId = params[0]
      return mockScans.filter(s => s.user_id === userId)
    }
    
    if (sql.includes('SELECT * FROM osint_queries WHERE user_id = ?')) {
      const userId = params[0]
      return mockOSINTQueries.filter(q => q.user_id === userId)
    }
    
    if (sql.includes('SELECT * FROM file_analyses WHERE user_id = ?')) {
      const userId = params[0]
      return mockFileAnalyses.filter(f => f.user_id === userId)
    }
    
    if (sql.includes('INSERT INTO')) {
      // Return mock insert result
      return { insertId: Math.floor(Math.random() * 1000) + 100 }
    }
    
    if (sql.includes('UPDATE')) {
      // Return mock update result
      return { affectedRows: 1 }
    }
    
    // Default return for other queries
    return []
  }
}

// Export mock database as default db
export const db = MockDatabase
