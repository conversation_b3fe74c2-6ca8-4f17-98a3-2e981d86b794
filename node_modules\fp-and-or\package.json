{"name": "fp-and-or", "description": "Simple `and` and `or` functional programming predicates", "version": "0.1.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/raineorshine"}, "license": "ISC", "engines": {"node": ">=10"}, "scripts": {"build": "node build.js", "lint": "eslint", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/raineorshine/fp-and-or.git"}, "devDependencies": {"eslint": "^7.14.0", "eslint-config-raine": "^0.1.2", "eslint-plugin-fp": "^2.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsdoc": "^30.7.8", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.1.0", "jest": "^26.6.3"}}