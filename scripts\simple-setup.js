const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rootkan'
}

async function simpleSetup() {
  let connection

  try {
    console.log('🚀 Setting up KodeXGuard database (Simple)...')
    
    // Connect to MySQL server
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to MySQL server')

    // Create database
    console.log('📋 Creating database...')
    await connection.query('CREATE DATABASE IF NOT EXISTS db_kodexguard')
    await connection.query('USE db_kodexguard')

    // Create essential tables
    console.log('🔨 Creating essential tables...')
    await createEssentialTables(connection)

    // Insert default data
    console.log('📊 Inserting default data...')
    await insertDefaultData(connection)

    // Create admin user
    console.log('👤 Creating admin user...')
    await createAdminUser(connection)

    // Create sample users
    console.log('👥 Creating sample users...')
    await createSampleUsers(connection)

    // Create sample data
    console.log('📈 Creating sample data...')
    await createSampleData(connection)

    console.log('🎉 Database setup completed successfully!')
    console.log('')
    console.log('📋 Setup Summary:')
    console.log('   Database: db_kodexguard')
    console.log('   Admin User: <EMAIL> / admin123456')
    console.log('   Sample Users: 5 users with realistic data')
    console.log('   Sample Data: Scans, OSINT queries, file analyses')
    console.log('')
    console.log('🔗 You can now start the application with: npm run dev')

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function createEssentialTables(connection) {
  // Users table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(50) UNIQUE NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      full_name VARCHAR(255),
      role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
      plan ENUM('Free', 'Pro', 'Expert', 'Elite') DEFAULT 'Free',
      level INT DEFAULT 1,
      score INT DEFAULT 0,
      streak_days INT DEFAULT 0,
      email_verified BOOLEAN DEFAULT FALSE,
      last_active TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `)

  // User preferences
  await connection.query(`
    CREATE TABLE IF NOT EXISTS user_preferences (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      theme ENUM('light', 'dark', 'cyberpunk') DEFAULT 'cyberpunk',
      language VARCHAR(10) DEFAULT 'en',
      notifications_email BOOLEAN DEFAULT TRUE,
      notifications_browser BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `)

  // User sessions
  await connection.query(`
    CREATE TABLE IF NOT EXISTS user_sessions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      session_token VARCHAR(500) NOT NULL,
      refresh_token VARCHAR(500),
      ip_address VARCHAR(45),
      user_agent TEXT,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Vulnerability scans
  await connection.query(`
    CREATE TABLE IF NOT EXISTS vulnerability_scans (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      target_url VARCHAR(2048) NOT NULL,
      scan_type ENUM('basic', 'advanced', 'comprehensive') NOT NULL,
      status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
      vulnerabilities_found INT DEFAULT 0,
      severity_critical INT DEFAULT 0,
      severity_high INT DEFAULT 0,
      severity_medium INT DEFAULT 0,
      severity_low INT DEFAULT 0,
      scan_results JSON,
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP NULL
    )
  `)

  // OSINT queries
  await connection.query(`
    CREATE TABLE IF NOT EXISTS osint_queries (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      query_type ENUM('email', 'domain', 'ip', 'username', 'phone') NOT NULL,
      query_value VARCHAR(500) NOT NULL,
      status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
      results JSON,
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP NULL
    )
  `)

  // File analyses
  await connection.query(`
    CREATE TABLE IF NOT EXISTS file_analyses (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      filename VARCHAR(255) NOT NULL,
      file_size BIGINT NOT NULL,
      file_type VARCHAR(100),
      analysis_type ENUM('malware', 'webshell', 'suspicious') NOT NULL,
      status ENUM('pending', 'analyzing', 'completed', 'failed') DEFAULT 'pending',
      threat_detected BOOLEAN DEFAULT FALSE,
      threat_type VARCHAR(100),
      confidence_score DECIMAL(3,2),
      analysis_results JSON,
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP NULL
    )
  `)

  // Dorking queries
  await connection.query(`
    CREATE TABLE IF NOT EXISTS dorking_queries (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      query_string TEXT NOT NULL,
      status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
      results_found INT DEFAULT 0,
      results JSON,
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP NULL
    )
  `)

  // Bot instances
  await connection.query(`
    CREATE TABLE IF NOT EXISTS bot_instances (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(100) UNIQUE NOT NULL,
      type ENUM('scanner', 'osint', 'monitor', 'crawler') NOT NULL,
      status ENUM('running', 'stopped', 'error', 'maintenance') DEFAULT 'stopped',
      tasks_completed INT DEFAULT 0,
      tasks_queued INT DEFAULT 0,
      cpu_usage DECIMAL(5,2) DEFAULT 0,
      memory_usage DECIMAL(5,2) DEFAULT 0,
      uptime_seconds BIGINT DEFAULT 0,
      last_activity TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `)

  // System logs
  await connection.query(`
    CREATE TABLE IF NOT EXISTS system_logs (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NULL,
      level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
      message TEXT NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `)

  console.log('   ✅ Essential tables created')
}

async function insertDefaultData(connection) {
  // Insert default bot instances
  await connection.query(`
    INSERT IGNORE INTO bot_instances (name, type, status, tasks_completed, tasks_queued, cpu_usage, memory_usage, uptime_seconds, last_activity) VALUES
    ('VulnScanner-01', 'scanner', 'running', 1250, 5, 45.2, 67.8, 604800, NOW()),
    ('OSINT-Collector-01', 'osint', 'running', 890, 12, 23.1, 34.5, 604800, NOW()),
    ('FileAnalyzer-01', 'monitor', 'running', 567, 3, 78.9, 89.2, 604800, NOW())
  `)

  console.log('   ✅ Default data inserted')
}

async function createAdminUser(connection) {
  const adminPassword = await bcrypt.hash('admin123456', 12)
  
  await connection.query(`
    INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) 
    VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'admin', 'Elite', 100, 50000, 365, true, NOW(), NOW(), NOW())
  `, [adminPassword])

  console.log('   ✅ Admin user created: <EMAIL> / admin123456')
}

async function createSampleUsers(connection) {
  const users = [
    { username: 'cyberwarrior', email: '<EMAIL>', full_name: 'Alex Chen', plan: 'Pro', level: 15, score: 2500, streak_days: 7 },
    { username: 'securityexpert', email: '<EMAIL>', full_name: 'Sarah Johnson', plan: 'Expert', level: 28, score: 8950, streak_days: 12 },
    { username: 'pentester', email: '<EMAIL>', full_name: 'Mike Rodriguez', plan: 'Elite', level: 42, score: 15420, streak_days: 25 },
    { username: 'hackerninja', email: '<EMAIL>', full_name: 'Emma Wilson', plan: 'Free', level: 8, score: 890, streak_days: 3 },
    { username: 'cybersleuth', email: '<EMAIL>', full_name: 'David Kim', plan: 'Pro', level: 22, score: 5670, streak_days: 15 }
  ]

  for (const user of users) {
    const passwordHash = await bcrypt.hash('password123', 12)
    
    await connection.query(`
      INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) 
      VALUES (?, ?, ?, ?, 'user', ?, ?, ?, ?, true, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), NOW(), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 24) HOUR))
    `, [user.username, user.email, passwordHash, user.full_name, user.plan, user.level, user.score, user.streak_days])
  }

  console.log('   ✅ Sample users created')
}

async function createSampleData(connection) {
  // Get user IDs
  const [users] = await connection.query('SELECT id FROM users WHERE role = "user"')
  const userIds = users.map(u => u.id)

  if (userIds.length === 0) {
    console.log('   ⚠️  No users found, skipping sample data creation')
    return
  }

  // Create sample vulnerability scans
  for (let i = 0; i < 20; i++) {
    const userId = userIds[Math.floor(Math.random() * userIds.length)]
    const targetUrl = ['https://example.com', 'https://testsite.org', 'https://vulnerable-app.com'][Math.floor(Math.random() * 3)]
    const scanType = ['basic', 'advanced', 'comprehensive'][Math.floor(Math.random() * 3)]
    const status = Math.random() > 0.2 ? 'completed' : 'failed'
    const vulnerabilitiesFound = status === 'completed' ? Math.floor(Math.random() * 15) : 0

    await connection.query(`
      INSERT INTO vulnerability_scans (user_id, target_url, scan_type, status, vulnerabilities_found, severity_critical, severity_high, severity_medium, severity_low, scan_results, created_at, completed_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})
    `, [userId, targetUrl, scanType, status, vulnerabilitiesFound, Math.floor(vulnerabilitiesFound * 0.1), Math.floor(vulnerabilitiesFound * 0.2), Math.floor(vulnerabilitiesFound * 0.4), Math.floor(vulnerabilitiesFound * 0.3), JSON.stringify({ summary: { total: vulnerabilitiesFound } })])
  }

  // Create sample OSINT queries
  for (let i = 0; i < 15; i++) {
    const userId = userIds[Math.floor(Math.random() * userIds.length)]
    const queryType = ['email', 'domain', 'ip', 'username'][Math.floor(Math.random() * 4)]
    const queryValue = ['<EMAIL>', 'example.com', '***********', 'johndoe123'][Math.floor(Math.random() * 4)]
    const status = Math.random() > 0.1 ? 'completed' : 'failed'

    await connection.query(`
      INSERT INTO osint_queries (user_id, query_type, query_value, status, results, created_at, completed_at) 
      VALUES (?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})
    `, [userId, queryType, queryValue, status, JSON.stringify({ found: Math.random() > 0.3 })])
  }

  // Create sample file analyses
  for (let i = 0; i < 10; i++) {
    const userId = userIds[Math.floor(Math.random() * userIds.length)]
    const filename = ['suspicious.exe', 'malware.dll', 'webshell.php', 'clean-file.pdf'][Math.floor(Math.random() * 4)]
    const threatDetected = Math.random() > 0.6
    const status = Math.random() > 0.1 ? 'completed' : 'failed'

    await connection.query(`
      INSERT INTO file_analyses (user_id, filename, file_size, file_type, analysis_type, status, threat_detected, threat_type, confidence_score, analysis_results, created_at, completed_at) 
      VALUES (?, ?, ?, 'application/octet-stream', 'malware', ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), ${status === 'completed' ? 'DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)' : 'NULL'})
    `, [userId, filename, Math.floor(Math.random() * 10000000) + 1000, status, threatDetected, threatDetected ? 'malware' : null, threatDetected ? 0.85 : 0.15, JSON.stringify({ threatDetected })])
  }

  console.log('   ✅ Sample data created')
}

// Run setup if called directly
if (require.main === module) {
  simpleSetup()
}

module.exports = { simpleSetup }
