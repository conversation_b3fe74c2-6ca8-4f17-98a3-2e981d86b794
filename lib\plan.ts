import { DatabaseUtils } from './database'
import { UserPlan, PlanType } from '@/types/plan'

export interface PlanFeatures {
  dailyScans: number
  monthlyScans: number
  fileUploadSize: number // in MB
  apiCallsPerDay: number
  osintQueries: number
  botAccess: boolean
  prioritySupport: boolean
  advancedFeatures: boolean
  customDorks: boolean
  exportReports: boolean
}

export interface PlanLimits {
  maxApiKeys: number
  maxFileSize: number
  maxConcurrentScans: number
  rateLimitPerMinute: number
}

export interface Plan {
  id: string
  name: string
  type: PlanType
  price: number
  currency: string
  duration: 'daily' | 'weekly' | 'monthly' | 'yearly'
  features: PlanFeatures
  limits: PlanLimits
  isActive: boolean
  isPopular: boolean
  description: string
  createdAt: Date
  updatedAt: Date
}

export class PlanService {
  // Get all active plans
  static async getActivePlans(): Promise<Plan[]> {
    try {
      const plans = await DatabaseUtils.query(
        'SELECT * FROM plans WHERE is_active = true ORDER BY price ASC',
        []
      )

      return plans.map(this.mapPlanFromDB)
    } catch (error) {
      console.error('Error getting active plans:', error)
      return []
    }
  }

  // Get plan by type
  static async getPlanByType(type: PlanType): Promise<Plan | null> {
    try {
      const plan = await DatabaseUtils.findOne(
        'SELECT * FROM plans WHERE type = ? AND is_active = true',
        [type]
      )

      return plan ? this.mapPlanFromDB(plan) : null
    } catch (error) {
      console.error('Error getting plan by type:', error)
      return null
    }
  }

  // Get plan by ID
  static async getPlanById(id: string): Promise<Plan | null> {
    try {
      const plan = await DatabaseUtils.findOne(
        'SELECT * FROM plans WHERE id = ? AND is_active = true',
        [id]
      )

      return plan ? this.mapPlanFromDB(plan) : null
    } catch (error) {
      console.error('Error getting plan by ID:', error)
      return null
    }
  }

  // Check if user can perform action based on plan limits
  static async checkPlanLimit(
    userId: string, 
    action: 'scan' | 'osint' | 'file_upload' | 'api_call',
    value?: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime?: number }> {
    try {
      // Get user's current plan
      const user = await DatabaseUtils.findOne(
        'SELECT plan FROM users WHERE id = ?',
        [userId]
      )

      if (!user) {
        return { allowed: false, remaining: 0 }
      }

      const plan = await this.getPlanByType(user.plan as PlanType)
      if (!plan) {
        return { allowed: false, remaining: 0 }
      }

      // Get current usage
      const today = new Date().toISOString().split('T')[0]
      const usage = await DatabaseUtils.findOne(`
        SELECT 
          COUNT(CASE WHEN scan_type = 'vulnerability' AND DATE(created_at) = ? THEN 1 END) as daily_scans,
          COUNT(CASE WHEN scan_type = 'osint' AND DATE(created_at) = ? THEN 1 END) as daily_osint,
          COUNT(CASE WHEN DATE(created_at) = ? THEN 1 END) as daily_api_calls
        FROM scan_results 
        WHERE user_id = ?
      `, [today, today, today, userId])

      const currentUsage = usage || { daily_scans: 0, daily_osint: 0, daily_api_calls: 0 }

      switch (action) {
        case 'scan':
          const remaining = plan.features.dailyScans - currentUsage.daily_scans
          return {
            allowed: remaining > 0,
            remaining: Math.max(0, remaining),
            resetTime: this.getNextResetTime()
          }

        case 'osint':
          const osintRemaining = plan.features.osintQueries - currentUsage.daily_osint
          return {
            allowed: osintRemaining > 0,
            remaining: Math.max(0, osintRemaining),
            resetTime: this.getNextResetTime()
          }

        case 'file_upload':
          const maxSize = plan.features.fileUploadSize * 1024 * 1024 // Convert MB to bytes
          return {
            allowed: (value || 0) <= maxSize,
            remaining: maxSize
          }

        case 'api_call':
          const apiRemaining = plan.features.apiCallsPerDay - currentUsage.daily_api_calls
          return {
            allowed: apiRemaining > 0,
            remaining: Math.max(0, apiRemaining),
            resetTime: this.getNextResetTime()
          }

        default:
          return { allowed: false, remaining: 0 }
      }
    } catch (error) {
      console.error('Error checking plan limit:', error)
      return { allowed: false, remaining: 0 }
    }
  }

  // Create subscription
  static async createSubscription(
    userId: string,
    planId: string,
    paymentMethod: string,
    amount: number
  ): Promise<{ subscriptionId: string; paymentId: string } | { error: string }> {
    try {
      const plan = await this.getPlanById(planId)
      if (!plan) {
        return { error: 'Plan not found' }
      }

      const subscriptionId = this.generateUUID()
      const paymentId = this.generateUUID()

      // Calculate dates
      const startDate = new Date()
      const endDate = this.calculateEndDate(startDate, plan.duration)

      // Create subscription
      await DatabaseUtils.insert('subscriptions', {
        id: subscriptionId,
        user_id: userId,
        plan_id: planId,
        status: 'pending',
        start_date: startDate,
        end_date: endDate,
        auto_renew: false,
        amount,
        currency: plan.currency,
        created_at: new Date(),
        updated_at: new Date()
      })

      // Create payment record
      await DatabaseUtils.insert('payments', {
        id: paymentId,
        user_id: userId,
        subscription_id: subscriptionId,
        payment_id: `PAY_${Date.now()}`,
        amount,
        currency: plan.currency,
        status: 'pending',
        payment_method: paymentMethod,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        created_at: new Date(),
        updated_at: new Date()
      })

      return { subscriptionId, paymentId }
    } catch (error) {
      console.error('Error creating subscription:', error)
      return { error: 'Failed to create subscription' }
    }
  }

  // Activate subscription (after payment)
  static async activateSubscription(subscriptionId: string): Promise<boolean> {
    try {
      // Get subscription details
      const subscription = await DatabaseUtils.findOne(
        'SELECT * FROM subscriptions WHERE id = ?',
        [subscriptionId]
      )

      if (!subscription) {
        return false
      }

      // Update subscription status
      await DatabaseUtils.update(
        'subscriptions',
        { 
          status: 'active',
          updated_at: new Date()
        },
        'id = ?',
        [subscriptionId]
      )

      // Update user plan
      await DatabaseUtils.update(
        'users',
        {
          plan: subscription.plan_id,
          plan_expiry: subscription.end_date,
          updated_at: new Date()
        },
        'id = ?',
        [subscription.user_id]
      )

      return true
    } catch (error) {
      console.error('Error activating subscription:', error)
      return false
    }
  }

  // Helper methods
  private static mapPlanFromDB(dbPlan: any): Plan {
    return {
      id: dbPlan.id,
      name: dbPlan.name,
      type: dbPlan.type as PlanType,
      price: parseFloat(dbPlan.price),
      currency: dbPlan.currency,
      duration: dbPlan.duration,
      features: JSON.parse(dbPlan.features || '{}'),
      limits: JSON.parse(dbPlan.limits || '{}'),
      isActive: dbPlan.is_active,
      isPopular: dbPlan.is_popular,
      description: dbPlan.description,
      createdAt: new Date(dbPlan.created_at),
      updatedAt: new Date(dbPlan.updated_at)
    }
  }

  private static calculateEndDate(startDate: Date, duration: string): Date {
    const endDate = new Date(startDate)
    
    switch (duration) {
      case 'daily':
        endDate.setDate(endDate.getDate() + 1)
        break
      case 'weekly':
        endDate.setDate(endDate.getDate() + 7)
        break
      case 'monthly':
        endDate.setMonth(endDate.getMonth() + 1)
        break
      case 'yearly':
        endDate.setFullYear(endDate.getFullYear() + 1)
        break
    }
    
    return endDate
  }

  private static getNextResetTime(): number {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)
    return Math.floor(tomorrow.getTime() / 1000)
  }

  private static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}
