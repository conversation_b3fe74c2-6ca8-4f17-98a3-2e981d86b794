import axios from 'axios'
import dns from 'dns'
import { promisify } from 'util'

const dnsLookup = promisify(dns.lookup)
const dnsResolve = promisify(dns.resolve)

export interface OSINTQuery {
  id: number
  type: 'email' | 'phone' | 'username' | 'domain' | 'ip'
  value: string
  sources: string[]
}

export interface OSINTResult {
  source: string
  found: boolean
  data?: any
  confidence: number
  timestamp: string
}

export class OSINTService {
  private apiKeys: Record<string, string>

  constructor() {
    this.apiKeys = {
      haveibeenpwned: process.env.HIBP_API_KEY || '',
      shodan: process.env.SHODAN_API_KEY || '',
      virustotal: process.env.VIRUSTOTAL_API_KEY || '',
      hunter: process.env.HUNTER_API_KEY || '',
      clearbit: process.env.CLEARBIT_API_KEY || ''
    }
  }

  async investigate(query: OSINTQuery): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    switch (query.type) {
      case 'email':
        results.push(...await this.investigateEmail(query.value))
        break
      case 'phone':
        results.push(...await this.investigatePhone(query.value))
        break
      case 'username':
        results.push(...await this.investigateUsername(query.value))
        break
      case 'domain':
        results.push(...await this.investigateDomain(query.value))
        break
      case 'ip':
        results.push(...await this.investigateIP(query.value))
        break
    }

    return results
  }

  private async investigateEmail(email: string): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    // Check Have I Been Pwned
    try {
      const hibpResult = await this.checkHaveIBeenPwned(email)
      results.push({
        source: 'Have I Been Pwned',
        found: hibpResult.breaches.length > 0,
        data: hibpResult,
        confidence: hibpResult.breaches.length > 0 ? 0.9 : 0.1,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('HIBP check failed:', error)
    }

    // Check email validation and deliverability
    try {
      const validationResult = await this.validateEmail(email)
      results.push({
        source: 'Email Validation',
        found: validationResult.valid,
        data: validationResult,
        confidence: validationResult.valid ? 0.8 : 0.2,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Email validation failed:', error)
    }

    // Check Hunter.io for email verification
    if (this.apiKeys.hunter) {
      try {
        const hunterResult = await this.checkHunterIO(email)
        results.push({
          source: 'Hunter.io',
          found: hunterResult.found,
          data: hunterResult,
          confidence: hunterResult.found ? 0.7 : 0.3,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error('Hunter.io check failed:', error)
      }
    }

    // Check social media presence
    try {
      const socialResult = await this.checkSocialMedia(email, 'email')
      results.push(...socialResult)
    } catch (error) {
      console.error('Social media check failed:', error)
    }

    return results
  }

  private async investigatePhone(phone: string): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    // Phone number validation and carrier lookup
    try {
      const phoneResult = await this.validatePhone(phone)
      results.push({
        source: 'Phone Validation',
        found: phoneResult.valid,
        data: phoneResult,
        confidence: phoneResult.valid ? 0.8 : 0.2,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Phone validation failed:', error)
    }

    // Check social media for phone number
    try {
      const socialResult = await this.checkSocialMedia(phone, 'phone')
      results.push(...socialResult)
    } catch (error) {
      console.error('Social media check failed:', error)
    }

    return results
  }

  private async investigateUsername(username: string): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    // Check multiple social platforms
    const platforms = [
      'github.com',
      'twitter.com',
      'instagram.com',
      'linkedin.com',
      'facebook.com',
      'reddit.com',
      'youtube.com',
      'tiktok.com',
      'pinterest.com',
      'snapchat.com'
    ]

    for (const platform of platforms) {
      try {
        const exists = await this.checkUsernameOnPlatform(username, platform)
        results.push({
          source: platform,
          found: exists,
          data: { username, platform, url: `https://${platform}/${username}` },
          confidence: exists ? 0.7 : 0.1,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error(`Username check failed for ${platform}:`, error)
      }
    }

    return results
  }

  private async investigateDomain(domain: string): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    // DNS lookup
    try {
      const dnsResult = await this.performDNSLookup(domain)
      results.push({
        source: 'DNS Lookup',
        found: dnsResult.found,
        data: dnsResult,
        confidence: dnsResult.found ? 0.9 : 0.1,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('DNS lookup failed:', error)
    }

    // WHOIS lookup
    try {
      const whoisResult = await this.performWHOISLookup(domain)
      results.push({
        source: 'WHOIS',
        found: whoisResult.found,
        data: whoisResult,
        confidence: whoisResult.found ? 0.8 : 0.2,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('WHOIS lookup failed:', error)
    }

    // VirusTotal check
    if (this.apiKeys.virustotal) {
      try {
        const vtResult = await this.checkVirusTotal(domain, 'domain')
        results.push({
          source: 'VirusTotal',
          found: vtResult.found,
          data: vtResult,
          confidence: vtResult.found ? 0.8 : 0.2,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error('VirusTotal check failed:', error)
      }
    }

    return results
  }

  private async investigateIP(ip: string): Promise<OSINTResult[]> {
    const results: OSINTResult[] = []

    // IP geolocation
    try {
      const geoResult = await this.getIPGeolocation(ip)
      results.push({
        source: 'IP Geolocation',
        found: geoResult.found,
        data: geoResult,
        confidence: geoResult.found ? 0.8 : 0.2,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('IP geolocation failed:', error)
    }

    // Shodan lookup
    if (this.apiKeys.shodan) {
      try {
        const shodanResult = await this.checkShodan(ip)
        results.push({
          source: 'Shodan',
          found: shodanResult.found,
          data: shodanResult,
          confidence: shodanResult.found ? 0.9 : 0.1,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error('Shodan check failed:', error)
      }
    }

    // VirusTotal check
    if (this.apiKeys.virustotal) {
      try {
        const vtResult = await this.checkVirusTotal(ip, 'ip')
        results.push({
          source: 'VirusTotal',
          found: vtResult.found,
          data: vtResult,
          confidence: vtResult.found ? 0.8 : 0.2,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error('VirusTotal check failed:', error)
      }
    }

    return results
  }

  // Helper methods for specific services
  private async checkHaveIBeenPwned(email: string): Promise<any> {
    try {
      if (!this.apiKeys.haveibeenpwned) {
        // Fallback to basic email validation if no API key
        return {
          breaches: [],
          email: email,
          message: 'HIBP API key not configured'
        }
      }

      const response = await axios.get(
        `https://haveibeenpwned.com/api/v3/breachedaccount/${encodeURIComponent(email)}`,
        {
          headers: {
            'hibp-api-key': this.apiKeys.haveibeenpwned,
            'User-Agent': 'KodeXGuard-OSINT'
          },
          timeout: 10000,
          validateStatus: (status) => status < 500 // Don't throw on 404
        }
      )

      if (response.status === 404) {
        return {
          breaches: [],
          email: email,
          message: 'No breaches found'
        }
      }

      return {
        breaches: response.data || [],
        email: email,
        found: response.data && response.data.length > 0
      }
    } catch (error) {
      console.error('HIBP check error:', error)
      return {
        breaches: [],
        email: email,
        error: 'Failed to check breaches'
      }
    }
  }

  private async validateEmail(email: string): Promise<any> {
    try {
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const valid = emailRegex.test(email)

      if (!valid) {
        return {
          valid: false,
          email: email,
          error: 'Invalid email format'
        }
      }

      // Extract domain for MX record check
      const domain = email.split('@')[1]
      let mxRecords: any[] = []
      let domainExists = false

      try {
        mxRecords = await dnsResolve(domain, 'MX')
        domainExists = true
      } catch (error) {
        // Try A record if MX fails
        try {
          await dnsResolve(domain, 'A')
          domainExists = true
        } catch (aError) {
          domainExists = false
        }
      }

      return {
        valid: valid && domainExists,
        email: email,
        domain: domain,
        mxRecords: mxRecords,
        deliverable: mxRecords.length > 0,
        domainExists: domainExists,
        riskLevel: this.assessEmailRisk(email, domain, mxRecords.length > 0)
      }
    } catch (error) {
      return {
        valid: false,
        email: email,
        error: 'Email validation failed'
      }
    }
  }

  private assessEmailRisk(email: string, domain: string, hasMailServer: boolean): string {
    // Common disposable email domains
    const disposableDomains = [
      '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
      'mailinator.com', 'yopmail.com', 'temp-mail.org'
    ]

    if (disposableDomains.includes(domain.toLowerCase())) {
      return 'high' // Disposable email
    }

    if (!hasMailServer) {
      return 'high' // No mail server
    }

    // Check for suspicious patterns
    if (email.includes('+') || email.includes('..')) {
      return 'medium' // Potentially suspicious
    }

    return 'low' // Looks legitimate
  }

  private async checkHunterIO(email: string): Promise<any> {
    const response = await axios.get(
      `https://api.hunter.io/v2/email-verifier?email=${encodeURIComponent(email)}&api_key=${this.apiKeys.hunter}`,
      { timeout: 10000 }
    )

    return {
      found: response.data.data.result === 'deliverable',
      result: response.data.data.result,
      score: response.data.data.score,
      email: email
    }
  }

  private async checkSocialMedia(query: string, type: 'email' | 'phone'): Promise<OSINTResult[]> {
    // This would implement social media API checks
    // For now, return empty array as most social APIs require special access
    return []
  }

  private async validatePhone(phone: string): Promise<any> {
    // Basic phone validation
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/
    const valid = phoneRegex.test(phone)
    
    return {
      valid: valid,
      phone: phone,
      formatted: phone.replace(/\D/g, ''),
      country: 'Unknown' // Would use a phone number parsing library
    }
  }

  private async checkUsernameOnPlatform(username: string, platform: string): Promise<boolean> {
    try {
      // Different URL patterns for different platforms
      let checkUrl = ''

      switch (platform) {
        case 'github.com':
          checkUrl = `https://api.github.com/users/${username}`
          break
        case 'twitter.com':
          checkUrl = `https://twitter.com/${username}`
          break
        case 'instagram.com':
          checkUrl = `https://www.instagram.com/${username}/`
          break
        case 'linkedin.com':
          checkUrl = `https://www.linkedin.com/in/${username}/`
          break
        case 'reddit.com':
          checkUrl = `https://www.reddit.com/user/${username}/about.json`
          break
        case 'youtube.com':
          checkUrl = `https://www.youtube.com/@${username}`
          break
        default:
          checkUrl = `https://${platform}/${username}`
      }

      const response = await axios.get(checkUrl, {
        timeout: 8000,
        validateStatus: (status) => status < 500,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      })

      // Special handling for different platforms
      if (platform === 'github.com') {
        return response.status === 200 && response.data && response.data.login
      }

      if (platform === 'reddit.com') {
        return response.status === 200 && response.data && !response.data.error
      }

      // For most platforms, 200 status means user exists
      return response.status === 200
    } catch (error) {
      // If we get a network error, assume user doesn't exist
      return false
    }
  }

  private async performDNSLookup(domain: string): Promise<any> {
    try {
      const [aRecords, mxRecords, txtRecords] = await Promise.allSettled([
        dnsResolve(domain, 'A'),
        dnsResolve(domain, 'MX'),
        dnsResolve(domain, 'TXT')
      ])

      return {
        found: true,
        domain: domain,
        aRecords: aRecords.status === 'fulfilled' ? aRecords.value : [],
        mxRecords: mxRecords.status === 'fulfilled' ? mxRecords.value : [],
        txtRecords: txtRecords.status === 'fulfilled' ? txtRecords.value : []
      }
    } catch (error) {
      return {
        found: false,
        domain: domain,
        error: error.message
      }
    }
  }

  private async performWHOISLookup(domain: string): Promise<any> {
    // WHOIS lookup would require a WHOIS service or library
    // For now, return basic info
    return {
      found: true,
      domain: domain,
      registrar: 'Unknown',
      createdDate: 'Unknown',
      expiryDate: 'Unknown'
    }
  }

  private async checkVirusTotal(query: string, type: 'domain' | 'ip'): Promise<any> {
    const endpoint = type === 'domain' ? 'domains' : 'ip_addresses'
    
    const response = await axios.get(
      `https://www.virustotal.com/api/v3/${endpoint}/${query}`,
      {
        headers: {
          'x-apikey': this.apiKeys.virustotal
        },
        timeout: 10000
      }
    )

    return {
      found: true,
      data: response.data,
      malicious: response.data.data.attributes.last_analysis_stats.malicious > 0,
      stats: response.data.data.attributes.last_analysis_stats
    }
  }

  private async getIPGeolocation(ip: string): Promise<any> {
    try {
      // Using a free IP geolocation service
      const response = await axios.get(`http://ip-api.com/json/${ip}`, {
        timeout: 5000
      })

      return {
        found: response.data.status === 'success',
        ip: ip,
        country: response.data.country,
        region: response.data.regionName,
        city: response.data.city,
        isp: response.data.isp,
        org: response.data.org,
        lat: response.data.lat,
        lon: response.data.lon
      }
    } catch (error) {
      return {
        found: false,
        ip: ip,
        error: error.message
      }
    }
  }

  private async checkShodan(ip: string): Promise<any> {
    const response = await axios.get(
      `https://api.shodan.io/shodan/host/${ip}?key=${this.apiKeys.shodan}`,
      { timeout: 10000 }
    )

    return {
      found: true,
      ip: ip,
      ports: response.data.ports,
      services: response.data.data.map((service: any) => ({
        port: service.port,
        service: service.product,
        version: service.version
      })),
      hostnames: response.data.hostnames,
      country: response.data.country_name,
      org: response.data.org
    }
  }
}
