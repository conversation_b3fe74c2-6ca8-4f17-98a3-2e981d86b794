"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/community/page",{

/***/ "(app-pages-browser)/./app/community/page.tsx":
/*!********************************!*\
  !*** ./app/community/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CommunityPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,ExternalLink,Github,MapPin,MessageCircle,MessageSquare,Send,Shield,Target,Trophy,Twitter,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CommunityPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMembers: 15420,\n        activeToday: 1247,\n        totalPosts: 8934,\n        totalEvents: 156\n    });\n    const communityStats = [\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            label: \"Total Members\",\n            value: stats.totalMembers.toLocaleString(),\n            change: \"+12%\",\n            color: \"text-cyber-primary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Active Today\",\n            value: stats.activeToday.toLocaleString(),\n            change: \"+8%\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Total Posts\",\n            value: stats.totalPosts.toLocaleString(),\n            change: \"+15%\",\n            color: \"text-cyber-secondary\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Events Hosted\",\n            value: stats.totalEvents.toLocaleString(),\n            change: \"+25%\",\n            color: \"text-cyber-accent\"\n        }\n    ];\n    const communityChannels = [\n        {\n            name: \"Discord Server\",\n            description: \"Real-time chat, voice channels, and community discussions\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            members: \"8.2K\",\n            link: \"#\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            name: \"Telegram Group\",\n            description: \"Quick updates, news, and instant notifications\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            members: \"5.1K\",\n            link: \"#\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"GitHub Community\",\n            description: \"Open source contributions and code collaboration\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            members: \"3.8K\",\n            link: \"#\",\n            color: \"bg-gray-800\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, tips, and cybersecurity news\",\n            icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            members: \"12.5K\",\n            link: \"#\",\n            color: \"bg-black\"\n        }\n    ];\n    const upcomingEvents = [\n        {\n            title: \"Cybersecurity Workshop\",\n            date: \"2024-01-15\",\n            time: \"19:00 WIB\",\n            type: \"Workshop\",\n            participants: 156,\n            description: \"Advanced penetration testing techniques\"\n        },\n        {\n            title: \"Bug Bounty Bootcamp\",\n            date: \"2024-01-20\",\n            time: \"14:00 WIB\",\n            type: \"Bootcamp\",\n            participants: 89,\n            description: \"From beginner to professional bug hunter\"\n        },\n        {\n            title: \"CTF Competition\",\n            date: \"2024-01-25\",\n            time: \"10:00 WIB\",\n            type: \"Competition\",\n            participants: 234,\n            description: \"Capture The Flag challenge for all levels\"\n        }\n    ];\n    const topContributors = [\n        {\n            name: \"CyberNinja\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 15420,\n            badge: \"Elite Hunter\",\n            contributions: 89\n        },\n        {\n            name: \"SecurityMaster\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 12350,\n            badge: \"Bug Hunter\",\n            contributions: 67\n        },\n        {\n            name: \"PentestPro\",\n            avatar: \"/api/placeholder/40/40\",\n            points: 9870,\n            badge: \"Security Expert\",\n            contributions: 54\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-glow\",\n                                        children: \"Join Our\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-pink\",\n                                        children: \"Cyber Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                children: \"Connect with thousands of cybersecurity professionals, bug hunters, and ethical hackers from around the world\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                                children: communityStats.map((stat, index)=>{\n                                    const Icon = stat.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-cyber text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-8 w-8 \".concat(stat.color, \" mx-auto mb-3 animate-cyber-pulse\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400 mb-1\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    stat.change,\n                                                    \" this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center mb-12\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                label: \"Overview\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                id: \"channels\",\n                                label: \"Channels\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                            },\n                            {\n                                id: \"events\",\n                                label: \"Events\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                id: \"leaderboard\",\n                                label: \"Top Contributors\",\n                                icon: _barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"flex items-center space-x-2 px-6 py-3 mx-2 mb-2 rounded-lg font-medium transition-all duration-300 \".concat(activeTab === tab.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border-2 border-cyber-primary\" : \"bg-blue-100 text-blue-600 border-2 border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Welcome to KodeXGuard Community\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Our community is a vibrant ecosystem of cybersecurity enthusiasts, professional penetration testers, bug bounty hunters, and security researchers who share knowledge, collaborate on projects, and help each other grow in the field of cybersecurity.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Share security research and findings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Collaborate on bug bounty programs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-cyber-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Learn from industry experts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: themeClasses.textPrimary,\n                                                                children: \"Participate in competitions and CTFs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                                children: \"Community Guidelines\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83E\\uDD1D Be Respectful\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Treat all community members with respect and professionalism\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDD12 Ethical Practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Only discuss ethical hacking and responsible disclosure\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"\\uD83D\\uDCDA Share Knowledge\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Help others learn and grow in cybersecurity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"channels\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: communityChannels.map((channel, index)=>{\n                                    const Icon = channel.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(channel.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                            children: channel.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-4\"),\n                                                            children: channel.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                    children: [\n                                                                        channel.members,\n                                                                        \" members\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center space-x-2 px-4 py-2 rounded-lg \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\", \" transition-colors duration-200\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Join\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Upcoming Events\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    upcomingEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row md:items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(event.type === \"Workshop\" ? \"bg-blue-100 text-blue-800\" : event.type === \"Bootcamp\" ? \"bg-green-100 text-green-800\" : \"bg-purple-100 text-purple-800\"),\n                                                                        children: event.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                        children: [\n                                                                            event.participants,\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_ExternalLink_Github_MapPin_MessageCircle_MessageSquare_Send_Shield_Target_Trophy_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                children: event.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 md:mt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                            children: \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === \"leaderboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                        children: \"Top Contributors This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    topContributors.map((contributor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-full bg-gradient-to-r \".concat(index === 0 ? \"from-yellow-400 to-yellow-600\" : index === 1 ? \"from-gray-300 to-gray-500\" : \"from-orange-400 to-orange-600\", \" flex items-center justify-center\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: contributor.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: contributor.badge\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold \".concat(themeClasses.textPrimary),\n                                                                children: [\n                                                                    contributor.points.toLocaleString(),\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                children: [\n                                                                    contributor.contributions,\n                                                                    \" contributions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center \".concat(themeClasses.card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                children: \"Ready to Join Our Community?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg \".concat(themeClasses.textSecondary, \" mb-8 max-w-2xl mx-auto\"),\n                                children: \"Connect with like-minded cybersecurity professionals and take your skills to the next level\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                        children: \"Join Discord Server\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                        children: \"Follow on Twitter\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\community\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(CommunityPage, \"evBCmepTbB2aOrEHc2m+0lAaZ10=\");\n_c = CommunityPage;\nvar _c;\n$RefreshReg$(_c, \"CommunityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/community/page.tsx\n"));

/***/ })

});