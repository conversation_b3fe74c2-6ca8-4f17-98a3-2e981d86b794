"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronRight,Code,Database,Download,FileText,Globe,Play,Search,Settings,Shield,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DocsPage() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    const categories = [\n        {\n            id: \"getting-started\",\n            name: \"Getting Started\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"Quick start guide and basic setup\"\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"Open Source Intelligence gathering\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Security scanning and assessment\"\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Malware and file analysis tools\"\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Database\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Vulnerability database and search\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Advanced search techniques\"\n        },\n        {\n            id: \"api\",\n            name: \"API Reference\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"REST API documentation\"\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Additional security utilities\"\n        }\n    ];\n    const quickStartGuides = [\n        {\n            title: \"Platform Overview\",\n            description: \"Learn about KodeXGuard features and capabilities\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            time: \"5 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"First OSINT Investigation\",\n            description: \"Step-by-step guide to your first investigation\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            time: \"15 min read\",\n            difficulty: \"Beginner\"\n        },\n        {\n            title: \"Running Vulnerability Scans\",\n            description: \"How to perform comprehensive security scans\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            time: \"20 min read\",\n            difficulty: \"Intermediate\"\n        },\n        {\n            title: \"API Integration\",\n            description: \"Integrate KodeXGuard into your workflow\",\n            icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            time: \"30 min read\",\n            difficulty: \"Advanced\"\n        }\n    ];\n    const popularDocs = [\n        {\n            title: \"OSINT Methodology\",\n            category: \"OSINT\",\n            views: \"15.2K\",\n            updated: \"2 days ago\"\n        },\n        {\n            title: \"CVE Search Techniques\",\n            category: \"CVE Database\",\n            views: \"12.8K\",\n            updated: \"1 week ago\"\n        },\n        {\n            title: \"Advanced Google Dorking\",\n            category: \"Dorking\",\n            views: \"9.5K\",\n            updated: \"3 days ago\"\n        },\n        {\n            title: \"API Authentication\",\n            category: \"API\",\n            views: \"8.1K\",\n            updated: \"5 days ago\"\n        }\n    ];\n    const tutorials = [\n        {\n            title: \"Bug Bounty Reconnaissance\",\n            description: \"Complete guide to reconnaissance for bug bounty hunting\",\n            duration: \"45 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OSINT\",\n                \"Subdomain Enumeration\",\n                \"Port Scanning\"\n            ]\n        },\n        {\n            title: \"Malware Analysis Basics\",\n            description: \"Introduction to static and dynamic malware analysis\",\n            duration: \"60 min\",\n            level: \"Advanced\",\n            topics: [\n                \"File Analysis\",\n                \"Reverse Engineering\",\n                \"Sandboxing\"\n            ]\n        },\n        {\n            title: \"Web Application Security Testing\",\n            description: \"Comprehensive web app security assessment\",\n            duration: \"90 min\",\n            level: \"Intermediate\",\n            topics: [\n                \"OWASP Top 10\",\n                \"SQL Injection\",\n                \"XSS\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-6xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-glow\" : \"text-blue-600\",\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: themeClasses.isDark ? \"text-cyber-pink\" : \"text-pink-600\",\n                                        children: \"Center\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl max-w-3xl mx-auto mb-8 \".concat(themeClasses.textSecondary),\n                                children: \"Comprehensive guides, tutorials, and API documentation to help you master cybersecurity tools and techniques\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 \".concat(themeClasses.textMuted)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search documentation...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 rounded-lg \".concat(themeClasses.input, \" text-lg\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\",\n                        children: [\n                            {\n                                label: \"Documentation Pages\",\n                                value: \"150+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            },\n                            {\n                                label: \"Video Tutorials\",\n                                value: \"45+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                            },\n                            {\n                                label: \"Code Examples\",\n                                value: \"200+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                label: \"API Endpoints\",\n                                value: \"80+\",\n                                icon: _barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                            }\n                        ].map((stat, index)=>{\n                            const Icon = stat.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(themeClasses.card, \" text-center\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 \".concat(themeClasses.isDark ? \"animate-cyber-pulse\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(themeClasses.textMuted),\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: themeClasses.card,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-4\"),\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: categories.map((category)=>{\n                                                const Icon = category.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedCategory(category.id),\n                                                    className: \"w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 \".concat(selectedCategory === category.id ? themeClasses.isDark ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary\" : \"bg-blue-100 text-blue-600 border border-blue-500\" : \"\".concat(themeClasses.textSecondary, \" hover:\").concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-gray-100\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, category.id, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Quick Start Guides\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: quickStartGuides.map((guide, index)=>{\n                                                    const Icon = guide.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-100\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-6 w-6 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: guide.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3 text-sm\"),\n                                                                            children: guide.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(guide.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : guide.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: guide.difficulty\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs \".concat(themeClasses.textMuted),\n                                                                                    children: guide.time\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Popular Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: popularDocs.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:\").concat(themeClasses.isDark ? \"border-cyber-primary\" : \"border-blue-500\", \" transition-colors duration-300 cursor-pointer\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold \".concat(themeClasses.textPrimary, \" mb-1\"),\n                                                                            children: doc.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-secondary/20 text-cyber-secondary\" : \"bg-pink-100 text-pink-600\"),\n                                                                                    children: doc.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        doc.views,\n                                                                                        \" views\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: [\n                                                                                        \"Updated \",\n                                                                                        doc.updated\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 \".concat(themeClasses.textMuted)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold \".concat(themeClasses.textPrimary, \" mb-6\"),\n                                                children: \"Video Tutorials\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-6\",\n                                                children: tutorials.map((tutorial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(themeClasses.card, \" hover:scale-105 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full md:w-48 h-32 rounded-lg \".concat(themeClasses.isDark ? \"bg-gradient-to-br from-cyber-primary/20 to-cyber-secondary/20\" : \"bg-gradient-to-br from-blue-100 to-pink-100\", \" flex items-center justify-center\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"h-12 w-12 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                            children: tutorial.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"\".concat(themeClasses.textSecondary, \" mb-3\"),\n                                                                            children: tutorial.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap items-center gap-2 mb-3\",\n                                                                            children: tutorial.topics.map((topic, topicIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(themeClasses.isDark ? \"bg-cyber-accent/20 text-cyber-accent\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                    children: topic\n                                                                                }, topicIndex, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                                    children: tutorial.duration\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(tutorial.level === \"Beginner\" ? \"bg-green-100 text-green-800\" : tutorial.level === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                    children: tutorial.level\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 355,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                                        children: \"Watch Now\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: themeClasses.card,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 \".concat(themeClasses.isDark ? \"text-cyber-primary\" : \"text-blue-600\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold \".concat(themeClasses.textPrimary),\n                                                        children: \"API Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"\".concat(themeClasses.textSecondary, \" mb-6\"),\n                                                children: \"Integrate KodeXGuard's powerful cybersecurity tools into your applications with our comprehensive REST API.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Authentication\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"API key and OAuth 2.0 authentication methods\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-pink-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"Rate Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Request limits and best practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-lg \".concat(themeClasses.isDark ? \"bg-cyber-accent/10\" : \"bg-yellow-50\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold \".concat(themeClasses.textPrimary, \" mb-2\"),\n                                                                children: \"SDKs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm \".concat(themeClasses.textSecondary),\n                                                                children: \"Python, JavaScript, and Go libraries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                                        children: \"View API Docs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(themeClasses.isDark ? \"border-2 border-cyber-secondary text-cyber-secondary hover:bg-cyber-secondary hover:text-black\" : \"border-2 border-pink-500 text-pink-600 hover:bg-pink-500 hover:text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronRight_Code_Database_Download_FileText_Globe_Play_Search_Settings_Shield_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Download SDK\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(DocsPage, \"bErKlB3njoaufpGnSTOHHJBBUlQ=\");\n_c = DocsPage;\nvar _c;\n$RefreshReg$(_c, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/docs/page.tsx\n"));

/***/ })

});