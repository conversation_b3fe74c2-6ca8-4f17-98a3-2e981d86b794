{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/HTTPRequest.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,qBAAqB,CAAC;AAIvD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAChD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACtC,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,MAAM,EAAE,yBAAyB,CAAC;IAClC,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjC,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,GAAG,UAAU,CAAC;CAC3B;AAED;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEpE;;;;GAIG;AACH,eAAO,MAAM,qCAAqC,IAAI,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,8BAAsB,WAAW;IAC/B;;OAEG;IACH,QAAQ,KAAK,EAAE,IAAI,MAAM,CAAC;IAE1B;;OAEG;IACH,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC;;OAEG;IACH,SAAS,EAAE,YAAY,GAAG,IAAI,CAAQ;IACtC;;OAEG;IACH,gBAAgB,UAAS;IACzB;;OAEG;IACH,cAAc,EAAE,WAAW,EAAE,CAAM;IAEnC;;OAEG;IACH,SAAS,CAAC,YAAY,EAAE;QACtB,OAAO,EAAE,OAAO,CAAC;QACjB,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,KAAK,CAAC,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/C,eAAe,EAAE,wBAAwB,CAAC;QAC1C,gBAAgB,EAAE,wBAAwB,CAAC;QAC3C,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;QAC7C,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;KAClD,CAUC;IAEF;;;;OAIG;IACH,QAAQ,KAAK,MAAM,IAAI,UAAU,CAAC;IAElC;;OAEG;;IAGH;;OAEG;IACH,QAAQ,CAAC,GAAG,IAAI,MAAM;IAEtB;;;;OAIG;IACH,wBAAwB,IAAI,wBAAwB;IAKpD;;;OAGG;IACH,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;IAKxD;;OAEG;IACH,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;IAKvD;;;;;;;;;;OAUG;IACH,wBAAwB,IAAI,wBAAwB;IAUpD;;;OAGG;IACH,4BAA4B,IAAI,OAAO;IAIvC;;;;;OAKG;IACH,sBAAsB,CACpB,cAAc,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,GAChD,IAAI;IAIP;;OAEG;IACH,QAAQ,CAAC,MAAM,CACb,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,GAC/C,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAEvE;;OAEG;IACH,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,wBAAwB,GAAG,OAAO,CAAC,IAAI,CAAC;IAEtE;;;OAGG;IACG,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAmB5C;;;OAGG;IACH,QAAQ,CAAC,YAAY,IAAI,YAAY;IAErC;;OAEG;IACH,QAAQ,CAAC,MAAM,IAAI,MAAM;IAEzB;;OAEG;IACH,QAAQ,CAAC,QAAQ,IAAI,MAAM,GAAG,SAAS;IAEvC;;;;;OAKG;IACH,QAAQ,CAAC,WAAW,IAAI,OAAO;IAE/B;;OAEG;IACH,QAAQ,CAAC,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAErD;;;OAGG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAE1C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,IAAI,YAAY,GAAG,IAAI;IAExC;;;OAGG;IACH,QAAQ,CAAC,KAAK,IAAI,KAAK,GAAG,IAAI;IAE9B;;OAEG;IACH,QAAQ,CAAC,mBAAmB,IAAI,OAAO;IAEvC;;OAEG;IACH,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,QAAQ,CAAC,aAAa,IAAI,WAAW,EAAE;IAEvC;;;;;;;;;;;;;;;;;;;OAmBG;IACH,QAAQ,CAAC,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAE9C;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACG,QAAQ,CACZ,SAAS,GAAE,wBAA6B,EACxC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IAkChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACG,OAAO,CACX,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA8BhB;;;;;;;;;;;;;OAaG;IACG,KAAK,CACT,SAAS,GAAE,SAAoB,EAC/B,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IAyBhB;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG;QAC7C,aAAa,EAAE,MAAM,CAAC;QACtB,MAAM,EAAE,MAAM,CAAC;KAChB;CAgBF;AAED;;GAEG;AACH,oBAAY,yBAAyB;IACnC,KAAK,UAAU;IACf,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,IAAI,SAAS;IACb,cAAc,oBAAoB;CACnC;AAED;;GAEG;AACH,MAAM,MAAM,SAAS,GACjB,SAAS,GACT,cAAc,GACd,oBAAoB,GACpB,iBAAiB,GACjB,mBAAmB,GACnB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,mBAAmB,GACnB,iBAAiB,GACjB,sBAAsB,GACtB,iBAAiB,GACjB,UAAU,GACV,QAAQ,CAAC;AAEb;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,UAAU,GAAG,OAAO,GAAG,SAAS,CAAC;AAE5D;;GAEG;AACH,wBAAgB,YAAY,CAC1B,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC,GACzC,KAAK,CAAC;IAAC,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAC,CAAC,CAgBtC;AAED;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAgEtC,CAAC;AAmBX;;GAEG;AACH,wBAAgB,WAAW,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI,CAetD"}