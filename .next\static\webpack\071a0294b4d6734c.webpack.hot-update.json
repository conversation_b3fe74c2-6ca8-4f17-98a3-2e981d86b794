{"c": ["app/layout", "webpack"], "r": ["app/leaderboard/page"], "m": ["(app-pages-browser)/./app/leaderboard/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cleaderboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}