export interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  bio?: string
  role: UserRole
  plan: UserPlan
  planExpiry?: Date
  isActive: boolean
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  lastLogin?: Date
  apiKeys: ApiKey[]
  stats: UserStats
}

export interface UserStats {
  totalScans: number
  vulnerabilitiesFound: number
  filesAnalyzed: number
  osintQueries: number
  apiCalls: number
  score: number
  rank: number
}

export interface ApiKey {
  id: string
  userId: string
  name: string
  key: string
  permissions: string[]
  isActive: boolean
  lastUsed?: Date
  usageCount: number
  rateLimit: number
  createdAt: Date
  expiresAt?: Date
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  USER = 'user'
}

export enum UserPlan {
  FREE = 'free',
  STUDENT = 'student',
  HOBBY = 'hobby',
  BUGHUNTER = 'bughunter',
  CYBERSECURITY = 'cybersecurity'
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  fullName: string
}

export interface AuthResponse {
  user: User
  token: string
  refreshToken: string
}

export interface UserProfile {
  fullName: string
  bio?: string
  avatar?: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}
