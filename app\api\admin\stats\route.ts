import { NextResponse } from 'next/server'
import { AdminService } from '@/lib/services/admin'

export async function GET() {
  try {
    // For now, we'll skip authentication since it's not fully implemented
    // In production, this would require admin authentication

    // Get comprehensive admin statistics
    const adminStats = await AdminService.getAdminStats()

    // Get users by plan
    const usersByPlan = await AdminService.getUsersByPlan()

    // Get revenue statistics
    const revenueStats = await AdminService.getRevenueStats()

    // Get recent system activities
    const recentActivities = await AdminService.getSystemActivities(20)

    // Get bot status
    const botStatus = await AdminService.getBotStatus()

    // Get chart data
    const userGrowthChart = await AdminService.getUserGrowthChart(30)
    const activityChart = await AdminService.getActivityChart(30)

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalUsers: adminStats.totalUsers,
          activeUsers: adminStats.activeUsers,
          newUsersToday: adminStats.newUsersToday,
          totalScans: adminStats.totalScans,
          scansToday: adminStats.scansToday,
          totalVulnerabilities: adminStats.totalVulnerabilities,
          criticalVulnerabilities: adminStats.criticalVulnerabilities,
          systemUptime: adminStats.systemUptime,
          serverLoad: adminStats.serverLoad
        },
        users: {
          total: adminStats.totalUsers,
          active: adminStats.activeUsers,
          inactive: adminStats.totalUsers - adminStats.activeUsers,
          byPlan: usersByPlan,
          growth: userGrowthChart
        },
        scans: {
          total: adminStats.totalScans,
          today: adminStats.scansToday,
          vulnerabilities: adminStats.totalVulnerabilities,
          critical: adminStats.criticalVulnerabilities,
          activity: activityChart
        },
        files: {
          total: adminStats.totalFileAnalyses,
          threatsDetected: adminStats.threatsDetected,
          threatRate: adminStats.totalFileAnalyses > 0 ?
            Math.round((adminStats.threatsDetected / adminStats.totalFileAnalyses) * 100) : 0
        },
        osint: {
          total: adminStats.totalOSINTQueries,
          cveSearches: adminStats.totalCVESearches,
          dorkingQueries: adminStats.totalDorkingQueries
        },
        system: {
          uptime: adminStats.systemUptime,
          serverLoad: adminStats.serverLoad,
          memoryUsage: adminStats.memoryUsage,
          diskUsage: adminStats.diskUsage
        },
        revenue: revenueStats,
        bots: botStatus,
        activities: recentActivities,
        charts: {
          userGrowth: userGrowthChart,
          activity: activityChart
        }
      }
    })

  } catch (error) {
    console.error('Admin stats error:', error)

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
