name: CI
on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
    types: [opened, reopened, synchronize]
jobs:
  test:
    name: Tests
    strategy:
      fail-fast: true
      matrix:
        node: [4, 6, 8, 10, 12, 14]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Install Node.js
        uses: actions/setup-node@v2-beta
        with:
          node-version: ${{ matrix.node }}
      - name: Install dependencies
        run: npm install
      - name: Test
        run: npm test
