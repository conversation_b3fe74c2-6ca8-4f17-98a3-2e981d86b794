{"version": 3, "file": "Serializer.js", "sourceRoot": "", "sources": ["../src/Serializer.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;AAEH,uDAA4C;AAC5C,0DAAyB;AACzB,kFAAqC;AACrC,qCAAmE;AACnE,uCAAwC;AAExC,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAA;AAMpC,MAAqB,UAAU;IAM7B,YAAa,OAA0B,EAAE;;QALzC;;;;;WAGC;QAGC,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,kCAAkC,mCAAI,KAAK,CAAA;QAChE,IAAI,CAAC,sBAAY,CAAC,GAAG;YACnB,WAAW,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;YACzE,iBAAiB,EAAE,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;SACtF,CAAA;IACH,CAAC;IAED;;OAEG;IACH,SAAS,CAAE,MAA2B;QACpC,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;QAC5B,IAAI,IAAI,CAAA;QACR,IAAI,CAAC;YACH,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC/B,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,2BAAkB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QACnD,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,WAAW,CAAe,IAAY;QACpC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC5B,IAAI,MAAM,CAAA;QACV,IAAI,CAAC;YACH,mBAAmB;YACnB,MAAM,GAAG,2BAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAY,CAAC,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,6BAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACnD,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG;IACH,WAAW,CAAE,KAA0C;QACrD,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;QAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,2BAAkB,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAA;QAC9E,CAAC;QACD,IAAI,MAAM,GAAG,EAAE,CAAA;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA,CAAC,sBAAsB;YAClD,CAAC;iBAAM,CAAC;gBACN,mBAAmB;gBACnB,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA,CAAC,sBAAsB;YAClE,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,UAAU,CAAE,MAAqC;QAC/C,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;QAC3B,IAAI,MAAM,IAAI,IAAI;YAAE,OAAO,EAAE,CAAA;QAC7B,IAAI,OAAO,MAAM,KAAK,QAAQ;YAAE,OAAO,MAAM,CAAA;QAC7C,sDAAsD;QACtD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YACnB,uDAAuD;YACvD,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC9B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,CAAC,sBAAsB;YAC3C,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACrC,CAAC;QACH,CAAC;QACD,OAAO,IAAA,4BAAS,EAAC,MAAM,CAAC,CAAA;IAC1B,CAAC;CACF;KA/EE,sBAAY;kBADM,UAAU"}