"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/CyberHeader.tsx":
/*!************************************!*\
  !*** ./components/CyberHeader.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CyberHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronDown,Crown,Database,FileText,Globe,LogIn,Menu,Search,Shield,Trophy,User,UserPlus,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CyberHeader(param) {\n    let { user } = param;\n    _s();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Features\",\n            href: \"#features\",\n            dropdown: [\n                {\n                    name: \"OSINT Investigator\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    description: \"Advanced intelligence gathering\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    description: \"Automated security scanning\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    description: \"Malware detection & analysis\"\n                },\n                {\n                    name: \"CVE Intelligence\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    description: \"Vulnerability database\"\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    description: \"Advanced search queries\"\n                },\n                {\n                    name: \"Developer Tools\",\n                    href: \"/tools\",\n                    icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    description: \"Security testing tools\"\n                }\n            ]\n        },\n        {\n            name: \"Pricing\",\n            href: \"/plan\"\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Docs\",\n            href: \"/docs\"\n        },\n        {\n            name: \"Community\",\n            href: \"/community\"\n        }\n    ];\n    const handleLogin = ()=>{\n        router.push(\"/login\");\n    };\n    const handleRegister = ()=>{\n        router.push(\"/register\");\n    };\n    const handleDashboard = ()=>{\n        router.push(\"/dashboard\");\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"refreshToken\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const isActive = (href)=>{\n        if (href.startsWith(\"#\")) return false;\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? \"\".concat(themeClasses.bgCard, \"/95 backdrop-blur-md border-b \").concat(themeClasses.border, \" shadow-lg\") : \"bg-transparent\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-primary \".concat(themeClasses.isDark ? \"animate-cyber-glow\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        themeClasses.isDark && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold \".concat(themeClasses.isDark ? \"text-cyber-glow group-hover:animate-glitch\" : \"text-blue-600\"),\n                                            children: \"KodeXGuard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs uppercase tracking-wider \".concat(themeClasses.isDark ? \"text-cyber-secondary\" : \"text-gray-500\"),\n                                            children: \"Cyber Security Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setActiveDropdown(item.name),\n                                        onMouseLeave: ()=>setActiveDropdown(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 \".concat(themeClasses.textSecondary, \" hover:text-cyber-primary transition-colors duration-200 font-medium\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-80 \".concat(themeClasses.bgCard, \" border \").concat(themeClasses.border, \" rounded-lg shadow-xl animate-fade-in-up\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 grid grid-cols-1 gap-2\",\n                                                    children: item.dropdown.map((subItem)=>{\n                                                        const Icon = subItem.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: subItem.href,\n                                                            className: \"flex items-center space-x-3 p-3 rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-blue-50\", \" transition-colors duration-200 group\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 text-cyber-primary \".concat(themeClasses.isDark ? \"group-hover:animate-cyber-pulse\" : \"\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium \".concat(themeClasses.textPrimary, \" group-hover:text-cyber-primary\"),\n                                                                            children: subItem.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm \".concat(themeClasses.textMuted),\n                                                                            children: subItem.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                            lineNumber: 164,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, subItem.name, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 31\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"flex items-center space-x-1 font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary\" : \"\".concat(themeClasses.textSecondary, \" hover:text-cyber-primary\")),\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 35\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {\n                                    variant: \"switch\",\n                                    size: \"md\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 px-3 py-1 bg-cyber-secondary/20 border border-cyber-secondary rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-cyber-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-cyber-accent uppercase\",\n                                                    children: user.plan\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center space-x-2 p-2 rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-secondary/10\" : \"bg-gray-100\", \" transition-colors duration-200\"),\n                                                    children: [\n                                                        user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: user.avatar,\n                                                            alt: user.username,\n                                                            className: \"h-8 w-8 rounded-full border-2 border-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 rounded-full bg-cyber-primary/20 border-2 border-cyber-primary flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat(themeClasses.textPrimary, \" font-medium\"),\n                                                            children: user.username\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(themeClasses.textMuted)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-2 w-48 \".concat(themeClasses.bgCard, \" border \").concat(themeClasses.border, \" rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleDashboard,\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left \".concat(themeClasses.textPrimary, \" hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\", \" rounded-lg transition-colors duration-200\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleLogout,\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors duration-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Logout\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogin,\n                                            className: \"flex items-center space-x-2 px-4 py-2 text-cyber-primary hover:\".concat(themeClasses.textPrimary, \" border border-cyber-primary hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-50\", \" rounded-lg transition-all duration-200\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRegister,\n                                            className: themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Get Started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeToggle, {\n                                    variant: \"button\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: \"p-2 rounded-lg \".concat(themeClasses.textSecondary, \" hover:text-cyber-primary hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden \".concat(themeClasses.bgCard, \" border-t \").concat(themeClasses.border, \" animate-slide-in-right\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 space-y-4\",\n                    children: [\n                        navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(themeClasses.textSecondary, \" font-medium mb-2\"),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4 space-y-2\",\n                                            children: item.dropdown.map((subItem)=>{\n                                                const Icon = subItem.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.href,\n                                                    className: \"flex items-center space-x-3 p-2 rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4 text-cyber-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: themeClasses.textPrimary,\n                                                            children: subItem.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, subItem.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"block px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(isActive(item.href) ? \"text-cyber-primary \".concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\") : \"\".concat(themeClasses.textSecondary, \" hover:text-cyber-primary hover:\").concat(themeClasses.isDark ? \"bg-cyber-primary/10\" : \"bg-blue-50\")),\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.name, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t \".concat(themeClasses.border, \" space-y-3\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleLogin();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 text-cyber-primary border border-cyber-primary rounded-lg hover:\".concat(themeClasses.isDark ? \"bg-cyber-primary/20\" : \"bg-blue-50\", \" transition-colors duration-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        handleRegister();\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    className: \"w-full \".concat(themeClasses.isDark ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronDown_Crown_Database_FileText_Globe_LogIn_Menu_Search_Shield_Trophy_User_UserPlus_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Get Started\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\CyberHeader.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(CyberHeader, \"F3oijKM3tVCOz8jkoNovbYM3h7k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = CyberHeader;\nvar _c;\n$RefreshReg$(_c, \"CyberHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CyberHeader.tsx\n"));

/***/ })

});