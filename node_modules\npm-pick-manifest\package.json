{"name": "npm-pick-manifest", "version": "8.0.2", "description": "Resolves a matching manifest from a package metadata document according to standard npm semver resolution rules.", "main": "./lib", "files": ["bin/", "lib/"], "scripts": {"coverage": "tap", "lint": "eslint \"**/*.js\"", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-pick-manifest.git"}, "keywords": ["npm", "semver", "package manager"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"npm-install-checks": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "npm-package-arg": "^10.0.0", "semver": "^7.3.5"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.18.0", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.18.0", "publish": true}}