'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { 
  Bot, 
  Play, 
  Pause, 
  Stop,
  Settings,
  Activity,
  Zap,
  Shield,
  Search,
  Globe,
  FileText,
  Database,
  Plus,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  BarChart3,
  Target,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react'

interface BotInstance {
  id: string
  name: string
  type: 'scanner' | 'osint' | 'monitor' | 'crawler'
  status: 'running' | 'stopped' | 'error' | 'maintenance'
  uptime: string
  tasksCompleted: number
  tasksQueued: number
  cpuUsage: number
  memoryUsage: number
  lastActivity: string
  config: {
    maxConcurrent: number
    timeout: number
    retryAttempts: number
  }
}

export default function AdminBotsPage() {
  const [bots, setBots] = useState<BotInstance[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedBot, setSelectedBot] = useState<string | null>(null)

  useEffect(() => {
    loadBots()
  }, [])

  const loadBots = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockBots: BotInstance[] = [
        {
          id: '1',
          name: 'Vulnerability Scanner Bot',
          type: 'scanner',
          status: 'running',
          uptime: '2d 14h 32m',
          tasksCompleted: 1247,
          tasksQueued: 23,
          cpuUsage: 45,
          memoryUsage: 67,
          lastActivity: '2 minutes ago',
          config: {
            maxConcurrent: 10,
            timeout: 30,
            retryAttempts: 3
          }
        },
        {
          id: '2',
          name: 'OSINT Collector Bot',
          type: 'osint',
          status: 'running',
          uptime: '1d 8h 15m',
          tasksCompleted: 2341,
          tasksQueued: 45,
          cpuUsage: 32,
          memoryUsage: 54,
          lastActivity: '1 minute ago',
          config: {
            maxConcurrent: 15,
            timeout: 60,
            retryAttempts: 5
          }
        },
        {
          id: '3',
          name: 'Security Monitor Bot',
          type: 'monitor',
          status: 'stopped',
          uptime: '0m',
          tasksCompleted: 567,
          tasksQueued: 0,
          cpuUsage: 0,
          memoryUsage: 0,
          lastActivity: '1 hour ago',
          config: {
            maxConcurrent: 5,
            timeout: 120,
            retryAttempts: 2
          }
        },
        {
          id: '4',
          name: 'Web Crawler Bot',
          type: 'crawler',
          status: 'error',
          uptime: '0m',
          tasksCompleted: 89,
          tasksQueued: 12,
          cpuUsage: 0,
          memoryUsage: 15,
          lastActivity: '30 minutes ago',
          config: {
            maxConcurrent: 8,
            timeout: 45,
            retryAttempts: 4
          }
        }
      ]

      setBots(mockBots)
      setLoading(false)
    } catch (error) {
      console.error('Error loading bots:', error)
      setLoading(false)
    }
  }

  const getBotTypeIcon = (type: string) => {
    switch (type) {
      case 'scanner': return Shield
      case 'osint': return Search
      case 'monitor': return Eye
      case 'crawler': return Globe
      default: return Bot
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-400/20'
      case 'stopped': return 'text-gray-400 bg-gray-400/20'
      case 'error': return 'text-red-400 bg-red-400/20'
      case 'maintenance': return 'text-yellow-400 bg-yellow-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />
      case 'stopped': return <XCircle className="h-4 w-4" />
      case 'error': return <AlertTriangle className="h-4 w-4" />
      case 'maintenance': return <Clock className="h-4 w-4" />
      default: return <XCircle className="h-4 w-4" />
    }
  }

  const handleBotAction = (botId: string, action: 'start' | 'stop' | 'restart') => {
    console.log(`${action} bot ${botId}`)
    // Implement bot control logic here
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading bot instances...</div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">Bot</span>{' '}
              <span className="text-cyber-pink">Management</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Monitor and control automated bot instances
            </p>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <button className="btn-cyber-secondary">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Status
            </button>
            <button className="btn-cyber-primary">
              <Plus className="h-4 w-4 mr-2" />
              Deploy Bot
            </button>
          </div>
        </div>

        {/* Bot Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="card-cyber text-center">
            <Bot className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {bots.length}
            </div>
            <div className="text-sm text-gray-400">Total Bots</div>
          </div>
          
          <div className="card-cyber text-center">
            <Activity className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {bots.filter(b => b.status === 'running').length}
            </div>
            <div className="text-sm text-gray-400">Running</div>
          </div>
          
          <div className="card-cyber text-center">
            <BarChart3 className="h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {bots.reduce((sum, bot) => sum + bot.tasksCompleted, 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Tasks Completed</div>
          </div>
          
          <div className="card-cyber text-center">
            <Target className="h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {bots.reduce((sum, bot) => sum + bot.tasksQueued, 0)}
            </div>
            <div className="text-sm text-gray-400">Tasks Queued</div>
          </div>
        </div>

        {/* Bot Instances */}
        <div className="card-cyber">
          <h2 className="text-2xl font-bold text-white mb-6">
            <span className="text-cyber-glow">Bot</span>{' '}
            <span className="text-cyber-pink">Instances</span>
          </h2>
          
          <div className="space-y-4">
            {bots.map((bot) => {
              const TypeIcon = getBotTypeIcon(bot.type)
              return (
                <div
                  key={bot.id}
                  className="p-6 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200 border border-cyber-border"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 rounded-lg bg-cyber-primary/20">
                        <TypeIcon className="h-6 w-6 text-cyber-primary" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{bot.name}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold ${getStatusColor(bot.status)}`}>
                            {getStatusIcon(bot.status)}
                            <span className="ml-1 capitalize">{bot.status}</span>
                          </span>
                          <span className="text-gray-400 text-sm capitalize">{bot.type}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleBotAction(bot.id, 'start')}
                        className="p-2 rounded-lg bg-green-500/20 text-green-400 hover:bg-green-500/30 transition-colors"
                        disabled={bot.status === 'running'}
                      >
                        <Play className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleBotAction(bot.id, 'stop')}
                        className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                        disabled={bot.status === 'stopped'}
                      >
                        <Stop className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleBotAction(bot.id, 'restart')}
                        className="p-2 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </button>
                      <button className="p-2 rounded-lg bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors">
                        <Settings className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{bot.uptime}</div>
                      <div className="text-xs text-gray-400">Uptime</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyber-primary">{bot.tasksCompleted}</div>
                      <div className="text-xs text-gray-400">Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyber-secondary">{bot.tasksQueued}</div>
                      <div className="text-xs text-gray-400">Queued</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-cyber-accent">{bot.cpuUsage}%</div>
                      <div className="text-xs text-gray-400">CPU</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">{bot.memoryUsage}%</div>
                      <div className="text-xs text-gray-400">Memory</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-300">{bot.lastActivity}</div>
                      <div className="text-xs text-gray-400">Last Activity</div>
                    </div>
                  </div>

                  {bot.status === 'running' && (
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-400">CPU Usage</span>
                          <span className="text-sm text-cyber-accent">{bot.cpuUsage}%</span>
                        </div>
                        <div className="w-full bg-cyber-dark rounded-full h-2">
                          <div 
                            className="bg-cyber-accent h-2 rounded-full transition-all duration-300"
                            style={{ width: `${bot.cpuUsage}%` }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-400">Memory Usage</span>
                          <span className="text-sm text-green-400">{bot.memoryUsage}%</span>
                        </div>
                        <div className="w-full bg-cyber-dark rounded-full h-2">
                          <div 
                            className="bg-green-400 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${bot.memoryUsage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
