'use client'

import { useState, useEffect, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable, StatusBadge } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading, ProgressBar } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  FileText, 
  Upload, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Bug,
  Zap,
  Download,
  Eye,
  Trash2,
  File,
  Image,
  Archive,
  Code,
  Database,
  X,
  Scan,
  History
} from 'lucide-react'

interface FileAnalysis {
  id: string
  fileName: string
  fileSize: number
  fileType: string
  uploadedAt: string
  status: 'pending' | 'scanning' | 'completed' | 'failed'
  progress: number
  results?: AnalysisResults
}

interface AnalysisResults {
  isMalicious: boolean
  riskScore: number
  threats: Threat[]
  fileInfo: FileInfo
  hashes: FileHashes
  metadata: any
}

interface Threat {
  type: 'malware' | 'webshell' | 'ransomware' | 'trojan' | 'suspicious'
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  confidence: number
}

interface FileInfo {
  size: number
  type: string
  extension: string
  encoding?: string
  language?: string
}

interface FileHashes {
  md5: string
  sha1: string
  sha256: string
}

export default function FileAnalyzerPage() {
  const [activeTab, setActiveTab] = useState('upload')
  const [uploadedFiles, setUploadedFiles] = useState<FileAnalysis[]>([])
  const [selectedFile, setSelectedFile] = useState<FileAnalysis | null>(null)
  const [showResultModal, setShowResultModal] = useState(false)
  const [stats, setStats] = useState({
    totalFiles: 0,
    maliciousFiles: 0,
    cleanFiles: 0,
    suspiciousFiles: 0
  })

  const { success, error, warning } = useToast()

  useEffect(() => {
    loadStats()
    loadFileHistory()
  }, [])

  const loadStats = async () => {
    setTimeout(() => {
      setStats({
        totalFiles: 234,
        maliciousFiles: 12,
        cleanFiles: 198,
        suspiciousFiles: 24
      })
    }, 1000)
  }

  const loadFileHistory = async () => {
    setTimeout(() => {
      setUploadedFiles([
        {
          id: '1',
          fileName: 'suspicious.php',
          fileSize: 2048,
          fileType: 'application/x-php',
          uploadedAt: '2025-01-14T10:30:00Z',
          status: 'completed',
          progress: 100,
          results: {
            isMalicious: true,
            riskScore: 85,
            threats: [
              {
                type: 'webshell',
                severity: 'critical',
                description: 'PHP webshell detected with command execution capabilities',
                confidence: 95
              }
            ],
            fileInfo: {
              size: 2048,
              type: 'PHP script',
              extension: '.php',
              language: 'PHP'
            },
            hashes: {
              md5: 'a1b2c3d4e5f6',
              sha1: 'a1b2c3d4e5f6g7h8i9j0',
              sha256: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6'
            },
            metadata: {}
          }
        },
        {
          id: '2',
          fileName: 'document.pdf',
          fileSize: 1024000,
          fileType: 'application/pdf',
          uploadedAt: '2025-01-14T09:15:00Z',
          status: 'completed',
          progress: 100,
          results: {
            isMalicious: false,
            riskScore: 5,
            threats: [],
            fileInfo: {
              size: 1024000,
              type: 'PDF document',
              extension: '.pdf'
            },
            hashes: {
              md5: 'x1y2z3a4b5c6',
              sha1: 'x1y2z3a4b5c6d7e8f9g0',
              sha256: 'x1y2z3a4b5c6d7e8f9g0h1i2j3k4l5m6'
            },
            metadata: {}
          }
        }
      ])
    }, 1500)
  }

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      // Check file size (max 10MB for free plan)
      if (file.size > 10 * 1024 * 1024) {
        error(`File ${file.name} is too large. Maximum size is 10MB.`)
        return
      }

      const newFile: FileAnalysis = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type || 'unknown',
        uploadedAt: new Date().toISOString(),
        status: 'pending',
        progress: 0
      }

      setUploadedFiles(prev => [newFile, ...prev])
      
      // Start analysis
      analyzeFile(newFile, file)
    })
  }, [error])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/*': ['.txt', '.php', '.js', '.html', '.css', '.py', '.java', '.cpp', '.c'],
      'application/*': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.rar', '.exe'],
      'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']
    },
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  const analyzeFile = async (fileAnalysis: FileAnalysis, file: File) => {
    // Update status to scanning
    setUploadedFiles(prev => 
      prev.map(f => f.id === fileAnalysis.id ? { ...f, status: 'scanning' } : f)
    )

    success(`Started analyzing ${file.name}`)

    // Simulate file analysis progress
    const progressInterval = setInterval(() => {
      setUploadedFiles(prev => 
        prev.map(f => {
          if (f.id === fileAnalysis.id && f.status === 'scanning') {
            const newProgress = Math.min(f.progress + Math.random() * 15, 100)
            
            if (newProgress >= 100) {
              clearInterval(progressInterval)
              
              // Generate mock analysis results
              const results = generateMockResults(file)
              
              success(`Analysis completed for ${file.name}`)
              
              return {
                ...f,
                status: 'completed' as const,
                progress: 100,
                results
              }
            }
            
            return { ...f, progress: newProgress }
          }
          return f
        })
      )
    }, 500)
  }

  const generateMockResults = (file: File): AnalysisResults => {
    const isSuspicious = file.name.toLowerCase().includes('suspicious') || 
                        file.name.toLowerCase().includes('malware') ||
                        file.name.toLowerCase().includes('virus')
    
    const isPhp = file.name.toLowerCase().endsWith('.php')
    const isExecutable = file.name.toLowerCase().endsWith('.exe')
    
    let threats: Threat[] = []
    let riskScore = 5
    
    if (isSuspicious || isPhp) {
      threats.push({
        type: isPhp ? 'webshell' : 'malware',
        severity: 'critical',
        description: isPhp ? 'Potential PHP webshell detected' : 'Suspicious executable detected',
        confidence: 85 + Math.random() * 15
      })
      riskScore = 80 + Math.random() * 20
    } else if (isExecutable) {
      threats.push({
        type: 'suspicious',
        severity: 'medium',
        description: 'Executable file requires careful review',
        confidence: 60 + Math.random() * 20
      })
      riskScore = 40 + Math.random() * 30
    }

    return {
      isMalicious: threats.length > 0 && riskScore > 70,
      riskScore: Math.round(riskScore),
      threats,
      fileInfo: {
        size: file.size,
        type: file.type || 'unknown',
        extension: '.' + file.name.split('.').pop(),
        language: isPhp ? 'PHP' : undefined
      },
      hashes: {
        md5: Math.random().toString(36).substr(2, 32),
        sha1: Math.random().toString(36).substr(2, 40),
        sha256: Math.random().toString(36).substr(2, 64)
      },
      metadata: {}
    }
  }

  const getFileIcon = (fileType: string, fileName: string) => {
    if (fileType.startsWith('image/')) return Image
    if (fileType.includes('zip') || fileType.includes('rar')) return Archive
    if (fileType.includes('pdf') || fileType.includes('document')) return FileText
    if (fileName.endsWith('.php') || fileName.endsWith('.js') || fileName.endsWith('.py')) return Code
    if (fileName.endsWith('.exe') || fileName.endsWith('.msi')) return Zap
    return File
  }

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-100'
    if (score >= 60) return 'text-orange-600 bg-orange-100'
    if (score >= 40) return 'text-yellow-600 bg-yellow-100'
    return 'text-green-600 bg-green-100'
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const deleteFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId))
    success('File deleted successfully')
  }

  const downloadReport = (file: FileAnalysis) => {
    if (!file.results) return

    const report = {
      fileName: file.fileName,
      uploadedAt: file.uploadedAt,
      analysis: file.results,
      summary: {
        isMalicious: file.results.isMalicious,
        riskScore: file.results.riskScore,
        threatsFound: file.results.threats.length
      }
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `analysis-${file.fileName}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    success('Analysis report downloaded!')
  }

  const fileHistoryColumns = [
    {
      key: 'fileName',
      label: 'File Name',
      render: (value: string, row: FileAnalysis) => {
        const Icon = getFileIcon(row.fileType, value)
        return (
          <div className="flex items-center space-x-3">
            <Icon className="h-5 w-5 text-gray-400" />
            <div>
              <div className="font-medium text-gray-900">{value}</div>
              <div className="text-sm text-gray-500">{formatFileSize(row.fileSize)}</div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string, row: FileAnalysis) => (
        <div className="space-y-1">
          <StatusBadge status={value} />
          {value === 'scanning' && (
            <ProgressBar progress={row.progress} className="w-20" showPercentage={false} />
          )}
        </div>
      )
    },
    {
      key: 'results',
      label: 'Risk Score',
      render: (value: AnalysisResults | undefined) => {
        if (!value) return '-'
        return (
          <span className={`px-2 py-1 rounded-full text-sm font-medium ${getRiskColor(value.riskScore)}`}>
            {value.riskScore}/100
          </span>
        )
      }
    },
    {
      key: 'results',
      label: 'Threats',
      render: (value: AnalysisResults | undefined) => {
        if (!value) return '-'
        return (
          <span className="font-medium">
            {value.threats.length}
          </span>
        )
      }
    },
    {
      key: 'uploadedAt',
      label: 'Date',
      render: (value: string) => new Date(value).toLocaleDateString()
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: FileAnalysis) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedFile(row)
              setShowResultModal(true)
            }}
            className="text-primary-600 hover:text-primary-700"
            disabled={row.status !== 'completed'}
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => downloadReport(row)}
            className="text-green-600 hover:text-green-700"
            disabled={row.status !== 'completed'}
          >
            <Download className="h-4 w-4" />
          </button>
          <button
            onClick={() => deleteFile(row.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="h-6 w-6 mr-2 text-primary-600" />
              File Analyzer
            </h1>
            <p className="text-gray-600 mt-1">
              Analyze files for malware, webshells, and security threats
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('upload')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'upload'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Upload className="h-4 w-4 mr-2 inline" />
                Upload
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'history'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <History className="h-4 w-4 mr-2 inline" />
                History
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Files"
            value={stats.totalFiles}
            icon={FileText}
            color="blue"
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="Malicious"
            value={stats.maliciousFiles}
            icon={Bug}
            color="red"
            trend={{ value: 2, isPositive: false }}
          />
          <StatsCard
            title="Clean Files"
            value={stats.cleanFiles}
            icon={CheckCircle}
            color="green"
            trend={{ value: 18, isPositive: true }}
          />
          <StatsCard
            title="Suspicious"
            value={stats.suspiciousFiles}
            icon={AlertTriangle}
            color="yellow"
            trend={{ value: 5, isPositive: false }}
          />
        </div>

        {/* Main Content */}
        {activeTab === 'upload' ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Upload Area */}
            <div className="lg:col-span-2">
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Upload Files for Analysis
                </h3>

                {/* Dropzone */}
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  {isDragActive ? (
                    <p className="text-primary-600 font-medium">Drop files here...</p>
                  ) : (
                    <div>
                      <p className="text-gray-600 font-medium mb-2">
                        Drag & drop files here, or click to select
                      </p>
                      <p className="text-sm text-gray-500">
                        Supports: PHP, JS, Python, Java, PDF, Images, Archives (Max: 10MB)
                      </p>
                    </div>
                  )}
                </div>

                {/* File Type Info */}
                <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Code className="h-4 w-4 text-blue-500" />
                    <span>Scripts</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-green-500" />
                    <span>Documents</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Image className="h-4 w-4 text-purple-500" />
                    <span>Images</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Archive className="h-4 w-4 text-orange-500" />
                    <span>Archives</span>
                  </div>
                </div>
              </Card>
            </div>

            {/* Recent Uploads */}
            <div>
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Recent Uploads
                </h3>
                
                <div className="space-y-3">
                  {uploadedFiles.slice(0, 5).map((file) => {
                    const Icon = getFileIcon(file.fileType, file.fileName)
                    return (
                      <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <Icon className="h-5 w-5 text-gray-400" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {file.fileName}
                          </p>
                          <div className="flex items-center space-x-2">
                            <StatusBadge status={file.status} />
                            {file.results && (
                              <span className={`text-xs px-2 py-1 rounded-full ${getRiskColor(file.results.riskScore)}`}>
                                Risk: {file.results.riskScore}
                              </span>
                            )}
                          </div>
                          {file.status === 'scanning' && (
                            <ProgressBar progress={file.progress} className="mt-2" showPercentage={false} />
                          )}
                        </div>
                        {file.status === 'completed' && (
                          <button
                            onClick={() => {
                              setSelectedFile(file)
                              setShowResultModal(true)
                            }}
                            className="text-primary-600 hover:text-primary-700"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    )
                  })}
                  
                  {uploadedFiles.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No files uploaded yet</p>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </div>
        ) : (
          /* History Tab */
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Analysis History
              </h3>
            </div>
            
            <DataTable
              columns={fileHistoryColumns}
              data={uploadedFiles}
              searchable
              pagination={{
                currentPage: 1,
                totalPages: 1,
                pageSize: 10,
                totalItems: uploadedFiles.length,
                onPageChange: () => {}
              }}
            />
          </Card>
        )}

        {/* Analysis Result Modal */}
        <Modal
          isOpen={showResultModal}
          onClose={() => setShowResultModal(false)}
          title={`Analysis Results - ${selectedFile?.fileName}`}
          size="lg"
        >
          {selectedFile?.results && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className={`text-2xl font-bold ${
                    selectedFile.results.isMalicious ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {selectedFile.results.isMalicious ? 'MALICIOUS' : 'CLEAN'}
                  </div>
                  <div className="text-sm text-gray-500">Overall Status</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className={`text-2xl font-bold ${getRiskColor(selectedFile.results.riskScore).split(' ')[0]}`}>
                    {selectedFile.results.riskScore}/100
                  </div>
                  <div className="text-sm text-gray-500">Risk Score</div>
                </div>
              </div>

              {/* Threats */}
              {selectedFile.results.threats.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Threats Detected</h4>
                  <div className="space-y-3">
                    {selectedFile.results.threats.map((threat, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-gray-900 capitalize">
                            {threat.type}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            threat.severity === 'critical' ? 'bg-red-100 text-red-800' :
                            threat.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                            threat.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {threat.severity}
                          </span>
                        </div>
                        <p className="text-gray-600 text-sm mb-2">{threat.description}</p>
                        <div className="text-xs text-gray-500">
                          Confidence: {Math.round(threat.confidence)}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* File Info */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">File Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Size:</span>
                    <span className="ml-2">{formatFileSize(selectedFile.results.fileInfo.size)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Type:</span>
                    <span className="ml-2">{selectedFile.results.fileInfo.type}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Extension:</span>
                    <span className="ml-2">{selectedFile.results.fileInfo.extension}</span>
                  </div>
                  {selectedFile.results.fileInfo.language && (
                    <div>
                      <span className="font-medium text-gray-700">Language:</span>
                      <span className="ml-2">{selectedFile.results.fileInfo.language}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Hashes */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">File Hashes</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-16">MD5:</span>
                    <span className="font-mono text-gray-600">{selectedFile.results.hashes.md5}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-16">SHA1:</span>
                    <span className="font-mono text-gray-600">{selectedFile.results.hashes.sha1}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-16">SHA256:</span>
                    <span className="font-mono text-gray-600">{selectedFile.results.hashes.sha256}</span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => downloadReport(selectedFile)}
                  className="btn-secondary"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Report
                </button>
                <button
                  onClick={() => setShowResultModal(false)}
                  className="btn-primary"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  )
}
