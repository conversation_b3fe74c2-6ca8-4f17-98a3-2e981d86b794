{"compilerOptions": {"target": "es2019", "module": "commonjs", "moduleResolution": "node", "declaration": true, "pretty": true, "noEmitOnError": true, "strict": true, "resolveJsonModule": true, "removeComments": false, "sourceMap": true, "newLine": "lf", "noUnusedLocals": true, "noFallthroughCasesInSwitch": true, "useDefineForClassFields": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": false, "esModuleInterop": true, "isolatedModules": true, "importHelpers": true, "outDir": "lib", "lib": ["esnext", "dom"]}, "formatCodeOptions": {"identSize": 2, "tabSize": 2}, "exclude": ["node_modules"], "include": ["./src/**/*.ts"]}