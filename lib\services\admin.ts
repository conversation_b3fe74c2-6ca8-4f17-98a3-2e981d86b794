import { db } from '@/lib/database'

export interface AdminStats {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  totalScans: number
  scansToday: number
  totalVulnerabilities: number
  criticalVulnerabilities: number
  totalFileAnalyses: number
  threatsDetected: number
  totalOSINTQueries: number
  totalCVESearches: number
  totalDorkingQueries: number
  systemUptime: string
  serverLoad: number
  memoryUsage: number
  diskUsage: number
}

export interface UsersByPlan {
  Free: number
  Pro: number
  Expert: number
  Elite: number
}

export interface RevenueStats {
  total: number
  thisMonth: number
  lastMonth: number
  growth: number
  byPlan: {
    Pro: number
    Expert: number
    Elite: number
  }
  projectedAnnual: number
}

export interface SystemActivity {
  id: string
  type: 'user_registration' | 'scan_completed' | 'threat_detected' | 'system_alert'
  description: string
  timestamp: Date
  severity: 'low' | 'medium' | 'high' | 'critical'
  userId?: number
  username?: string
}

export interface BotStatus {
  id: number
  name: string
  type: 'scanner' | 'osint' | 'monitor' | 'crawler'
  status: 'running' | 'stopped' | 'error' | 'maintenance'
  tasksCompleted: number
  tasksQueued: number
  cpuUsage: number
  memoryUsage: number
  uptime: number
  lastActivity: Date
}

export class AdminService {
  static async getAdminStats(): Promise<AdminStats> {
    try {
      // Get total users
      const totalUsersResult = await db.query('SELECT COUNT(*) as count FROM users')
      const totalUsers = totalUsersResult[0]?.count || 0

      // Get active users (last 24 hours)
      const activeUsersResult = await db.query(
        'SELECT COUNT(*) as count FROM users WHERE last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR)'
      )
      const activeUsers = activeUsersResult[0]?.count || 0

      // Get new users today
      const newUsersTodayResult = await db.query(
        'SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()'
      )
      const newUsersToday = newUsersTodayResult[0]?.count || 0

      // Get total scans
      const totalScansResult = await db.query('SELECT COUNT(*) as count FROM vulnerability_scans')
      const totalScans = totalScansResult[0]?.count || 0

      // Get scans today
      const scansTodayResult = await db.query(
        'SELECT COUNT(*) as count FROM vulnerability_scans WHERE DATE(created_at) = CURDATE()'
      )
      const scansToday = scansTodayResult[0]?.count || 0

      // Get total vulnerabilities
      const totalVulnerabilitiesResult = await db.query(
        'SELECT SUM(vulnerabilities_found) as total FROM vulnerability_scans WHERE status = "completed"'
      )
      const totalVulnerabilities = totalVulnerabilitiesResult[0]?.total || 0

      // Get critical vulnerabilities
      const criticalVulnerabilitiesResult = await db.query(
        'SELECT SUM(severity_critical) as total FROM vulnerability_scans WHERE status = "completed"'
      )
      const criticalVulnerabilities = criticalVulnerabilitiesResult[0]?.total || 0

      // Get file analyses
      const totalFileAnalysesResult = await db.query('SELECT COUNT(*) as count FROM file_analyses')
      const totalFileAnalyses = totalFileAnalysesResult[0]?.count || 0

      // Get threats detected
      const threatsDetectedResult = await db.query(
        'SELECT COUNT(*) as count FROM file_analyses WHERE threat_detected = 1'
      )
      const threatsDetected = threatsDetectedResult[0]?.count || 0

      // Get OSINT queries
      const totalOSINTResult = await db.query('SELECT COUNT(*) as count FROM osint_queries')
      const totalOSINTQueries = totalOSINTResult[0]?.count || 0

      // Get CVE searches (from logs)
      const totalCVEResult = await db.query(
        'SELECT COUNT(*) as count FROM system_logs WHERE message LIKE "%CVE search%"'
      )
      const totalCVESearches = totalCVEResult[0]?.count || 0

      // Get dorking queries
      const totalDorkingResult = await db.query('SELECT COUNT(*) as count FROM dorking_queries')
      const totalDorkingQueries = totalDorkingResult[0]?.count || 0

      // Calculate system uptime (since first user)
      const firstUserResult = await db.query(
        'SELECT MIN(created_at) as first_date FROM users'
      )
      const firstDate = firstUserResult[0]?.first_date
      const systemUptime = firstDate ? this.calculateUptime(new Date(firstDate)) : '0 days'

      // Mock system metrics (in production, these would come from system monitoring)
      const serverLoad = Math.random() * 100
      const memoryUsage = Math.random() * 100
      const diskUsage = Math.random() * 100

      return {
        totalUsers,
        activeUsers,
        newUsersToday,
        totalScans,
        scansToday,
        totalVulnerabilities,
        criticalVulnerabilities,
        totalFileAnalyses,
        threatsDetected,
        totalOSINTQueries,
        totalCVESearches,
        totalDorkingQueries,
        systemUptime,
        serverLoad,
        memoryUsage,
        diskUsage
      }
    } catch (error) {
      console.error('Error getting admin stats:', error)
      throw new Error('Failed to get admin statistics')
    }
  }

  static async getUsersByPlan(): Promise<UsersByPlan> {
    try {
      const result = await db.query(`
        SELECT plan, COUNT(*) as count
        FROM users
        GROUP BY plan
      `)

      const usersByPlan: UsersByPlan = {
        Free: 0,
        Pro: 0,
        Expert: 0,
        Elite: 0
      }

      for (const row of result) {
        if (row.plan in usersByPlan) {
          usersByPlan[row.plan as keyof UsersByPlan] = row.count
        }
      }

      return usersByPlan
    } catch (error) {
      console.error('Error getting users by plan:', error)
      return { Free: 0, Pro: 0, Expert: 0, Elite: 0 }
    }
  }

  static async getRevenueStats(): Promise<RevenueStats> {
    try {
      // Get subscription plan prices
      const plansResult = await db.query('SELECT name, price FROM subscription_plans')
      const planPrices: { [key: string]: number } = {}
      
      for (const plan of plansResult) {
        planPrices[plan.name] = plan.price
      }

      // Get users by plan
      const usersByPlan = await this.getUsersByPlan()

      // Calculate revenue
      const thisMonth = (usersByPlan.Pro * (planPrices.Pro || 29.99)) +
                       (usersByPlan.Expert * (planPrices.Expert || 79.99)) +
                       (usersByPlan.Elite * (planPrices.Elite || 199.99))

      // Mock last month (90% of this month)
      const lastMonth = thisMonth * 0.9

      const growth = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0

      const total = thisMonth * 12 // Approximate total based on current month

      return {
        total,
        thisMonth,
        lastMonth,
        growth,
        byPlan: {
          Pro: usersByPlan.Pro * (planPrices.Pro || 29.99),
          Expert: usersByPlan.Expert * (planPrices.Expert || 79.99),
          Elite: usersByPlan.Elite * (planPrices.Elite || 199.99)
        },
        projectedAnnual: thisMonth * 12
      }
    } catch (error) {
      console.error('Error getting revenue stats:', error)
      return {
        total: 0,
        thisMonth: 0,
        lastMonth: 0,
        growth: 0,
        byPlan: { Pro: 0, Expert: 0, Elite: 0 },
        projectedAnnual: 0
      }
    }
  }

  static async getSystemActivities(limit: number = 20): Promise<SystemActivity[]> {
    try {
      const activities: SystemActivity[] = []

      // Get recent user registrations
      const usersResult = await db.query(`
        SELECT id, username, created_at
        FROM users
        ORDER BY created_at DESC
        LIMIT ?
      `, [Math.ceil(limit / 4)])

      for (const user of usersResult) {
        activities.push({
          id: `user-${user.id}`,
          type: 'user_registration',
          description: `New user ${user.username} registered`,
          timestamp: new Date(user.created_at),
          severity: 'low',
          userId: user.id,
          username: user.username
        })
      }

      // Get recent completed scans
      const scansResult = await db.query(`
        SELECT vs.id, vs.target_url, vs.vulnerabilities_found, vs.completed_at, u.username, u.id as user_id
        FROM vulnerability_scans vs
        JOIN users u ON vs.user_id = u.id
        WHERE vs.status = 'completed'
        ORDER BY vs.completed_at DESC
        LIMIT ?
      `, [Math.ceil(limit / 4)])

      for (const scan of scansResult) {
        const severity = scan.vulnerabilities_found > 10 ? 'high' : 
                        scan.vulnerabilities_found > 5 ? 'medium' : 'low'
        
        activities.push({
          id: `scan-${scan.id}`,
          type: 'scan_completed',
          description: `${scan.username} completed scan of ${scan.target_url} - ${scan.vulnerabilities_found} vulnerabilities found`,
          timestamp: new Date(scan.completed_at),
          severity,
          userId: scan.user_id,
          username: scan.username
        })
      }

      // Get recent threats detected
      const threatsResult = await db.query(`
        SELECT fa.id, fa.filename, fa.completed_at, u.username, u.id as user_id
        FROM file_analyses fa
        JOIN users u ON fa.user_id = u.id
        WHERE fa.threat_detected = 1
        ORDER BY fa.completed_at DESC
        LIMIT ?
      `, [Math.ceil(limit / 4)])

      for (const threat of threatsResult) {
        activities.push({
          id: `threat-${threat.id}`,
          type: 'threat_detected',
          description: `Threat detected in ${threat.filename} by ${threat.username}`,
          timestamp: new Date(threat.completed_at),
          severity: 'critical',
          userId: threat.user_id,
          username: threat.username
        })
      }

      // Sort by timestamp and limit
      return activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, limit)

    } catch (error) {
      console.error('Error getting system activities:', error)
      return []
    }
  }

  static async getBotStatus(): Promise<BotStatus[]> {
    try {
      const result = await db.query(`
        SELECT id, name, type, status, tasks_completed, tasks_queued, 
               cpu_usage, memory_usage, uptime_seconds, last_activity
        FROM bot_instances
        ORDER BY name ASC
      `)

      return result.map((bot: any) => ({
        id: bot.id,
        name: bot.name,
        type: bot.type,
        status: bot.status,
        tasksCompleted: bot.tasks_completed,
        tasksQueued: bot.tasks_queued,
        cpuUsage: bot.cpu_usage,
        memoryUsage: bot.memory_usage,
        uptime: bot.uptime_seconds,
        lastActivity: new Date(bot.last_activity)
      }))
    } catch (error) {
      console.error('Error getting bot status:', error)
      return []
    }
  }

  static async getUserGrowthChart(days: number = 30): Promise<{ date: string; users: number }[]> {
    try {
      const result = await db.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as users
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days])

      return result.map((row: any) => ({
        date: row.date,
        users: row.users
      }))
    } catch (error) {
      console.error('Error getting user growth chart:', error)
      return []
    }
  }

  static async getActivityChart(days: number = 30): Promise<{ date: string; scans: number; threats: number }[]> {
    try {
      const result = await db.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as scans,
          SUM(vulnerabilities_found) as threats
        FROM vulnerability_scans
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND status = 'completed'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days])

      return result.map((row: any) => ({
        date: row.date,
        scans: row.scans,
        threats: row.threats || 0
      }))
    } catch (error) {
      console.error('Error getting activity chart:', error)
      return []
    }
  }

  private static calculateUptime(startDate: Date): string {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - startDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) {
      return `${diffDays} days`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} month${months > 1 ? 's' : ''}`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingMonths = Math.floor((diffDays % 365) / 30)
      return `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}`
    }
  }
}
