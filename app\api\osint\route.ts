import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'
import { OSINTService } from '@/lib/services/osint'
import { z } from 'zod'

const osintQuerySchema = z.object({
  type: z.enum(['email', 'phone', 'username', 'domain', 'ip']),
  value: z.string().min(1, 'Query value is required'),
  sources: z.array(z.string()).optional()
})

export async function POST(request: NextRequest) {
  try {
    // For demo purposes, we'll use a mock user
    // In production, uncomment the authentication below
    /*
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }
    const user = authResult.user
    */

    // Mock user for demo
    const user = { id: 1, plan: 'Pro' }
    const body = await request.json()

    // Validate input
    const validatedData = osintQuerySchema.parse(body)

    // Check user plan limits (simplified for demo)
    const planLimits = { osintQueries: 100 } // Pro plan limit
    const todayQueries = 5 // Mock current usage
    
    if (planLimits.osintQueries !== -1 && todayQueries >= planLimits.osintQueries) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Daily OSINT query limit reached. Upgrade your plan for more queries.',
          limit: planLimits.osintQueries,
          used: todayQueries
        },
        { status: 429 }
      )
    }
    
    // Create OSINT query record in database
    const result = await db.query(
      `INSERT INTO osint_queries (user_id, query_type, query_value, status, created_at)
       VALUES (?, ?, ?, 'pending', NOW())`,
      [user.id, validatedData.type, validatedData.value]
    )

    const queryId = (result as any).insertId

    // Start OSINT investigation
    const osintService = new OSINTService()

    // Run investigation in background
    osintService.investigate({
      id: queryId,
      type: validatedData.type,
      value: validatedData.value,
      sources: validatedData.sources || ['all']
    }).then(async (results) => {
      // Update query record with results
      await db.query(
        `UPDATE osint_queries SET status = 'completed', results = ?, completed_at = NOW() WHERE id = ?`,
        [JSON.stringify(results), queryId]
      )

      // Award points to user (simplified)
      await db.query(
        `UPDATE users SET score = score + 10 WHERE id = ?`,
        [user.id]
      )

    }).catch(async (error) => {
      // Update query record with error
      await db.query(
        `UPDATE osint_queries SET status = 'failed', error_message = ?, completed_at = NOW() WHERE id = ?`,
        [error.message, queryId]
      )
    })
    
    return NextResponse.json({
      success: true,
      message: 'OSINT query started',
      queryId: queryId,
      estimatedTime: '30-60 seconds'
    })
    
  } catch (error) {
    console.error('OSINT query error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid input data',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // For demo purposes, we'll use a mock user
    // In production, uncomment the authentication below
    /*
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }
    const user = authResult.user
    */

    // Mock user for demo
    const user = { id: 1 }
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const type = searchParams.get('type')

    // Get user's OSINT queries from database
    let query = 'SELECT * FROM osint_queries WHERE user_id = ?'
    const params: any[] = [user.id]

    if (status) {
      query += ' AND status = ?'
      params.push(status)
    }

    if (type) {
      query += ' AND query_type = ?'
      params.push(type)
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?'
    params.push(limit, (page - 1) * limit)

    const queries = await db.query(query, params)

    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM osint_queries WHERE user_id = ?'
    const countParams: any[] = [user.id]

    if (status) {
      countQuery += ' AND status = ?'
      countParams.push(status)
    }

    if (type) {
      countQuery += ' AND query_type = ?'
      countParams.push(type)
    }

    const countResult = await db.query(countQuery, countParams)
    const total = (countResult as any)[0]?.total || 0

    return NextResponse.json({
      success: true,
      data: queries,
      pagination: {
        page,
        limit,
        total: total,
        pages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Get OSINT queries error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
