import { URL } from 'node:url';
import { ConnectionOptions as TlsConnectionOptions } from 'node:tls';
import Diagnostic from '../Diagnostic';
import { kCaFingerprint } from '../symbols';
import { Connection, ConnectionOptions, BaseConnection } from '../connection';
import { HttpAgentOptions, UndiciAgentOptions, agentFn, ApiKeyAuth, BasicAuth, BearerAuth, nodeFilterFn, nodeSelectorFn } from '../types';
type AddConnectionOptions = string | ConnectionOptions;
export interface ConnectionPoolOptions {
    tls?: TlsConnectionOptions;
    agent?: HttpAgentOptions | UndiciAgentOptions | agentFn | false;
    proxy?: string | URL;
    auth?: BasicAuth | ApiKeyAuth | BearerAuth;
    diagnostic?: Diagnostic;
    Connection: typeof BaseConnection;
    pingTimeout?: number;
    resurrectStrategy?: 'none' | 'ping' | 'optimistic';
    caFingerprint?: string;
}
export interface GetConnectionOptions {
    filter?: nodeFilterFn;
    selector?: nodeSelectorFn;
    now: number;
    requestId: string | number;
    name: string | symbol;
    context: any;
}
export declare function defaultNodeFilter(conn: Connection): boolean;
/**
 * Manages the HTTP connections to each Elasticsearch node,
 * keeping track of which are currently dead or alive, and
 * provides the functionality for deciding which node to send
 * a request to.
 */
export default class BaseConnectionPool {
    connections: Connection[];
    size: number;
    Connection: typeof BaseConnection;
    diagnostic: Diagnostic;
    auth?: BasicAuth | ApiKeyAuth | BearerAuth;
    _agent?: HttpAgentOptions | UndiciAgentOptions | agentFn | false;
    _proxy?: string | URL;
    _tls?: TlsConnectionOptions;
    [kCaFingerprint]?: string;
    constructor(opts: ConnectionPoolOptions);
    markAlive(connection: Connection): this;
    markDead(connection: Connection): this;
    getConnection(opts: GetConnectionOptions): Connection | null;
    /**
     * Creates a new connection instance.
     */
    createConnection(opts: string | ConnectionOptions): Connection;
    /**
     * Adds a new connection to the pool.
     *
     * @param connection Connection options, or the URL of a node
     * @returns This ConnectionPool instance
     */
    addConnection(connection: AddConnectionOptions | AddConnectionOptions[]): this;
    /**
     * Removes a connection from the pool.
     *
     * @param connection The connection to remove
     * @returns This ConnectionPool instance
     */
    removeConnection(connection: Connection): this;
    /**
     * Empties the connection pool.
     *
     * @returns {ConnectionPool}
     */
    empty(): Promise<void>;
    /**
     * Update the ConnectionPool with new connections.
     *
     * @param nodes array of connections
     * @returns {ConnectionPool}
     */
    update(nodes: Array<Connection | ConnectionOptions>): this;
    /**
     * Transforms the nodes objects to a host object.
     *
     * @param {object} nodes
     * @returns {array} hosts
     */
    nodesToHost(nodes: Record<string, any>, protocol: string): ConnectionOptions[];
    /**
     * Transforms an url string to a host object
     *
     * @param {string} url
     * @returns {object} host
     */
    urlToHost(url: string): ConnectionOptions;
}
export {};
