{"version": 3, "file": "WeightedConnectionPool.js", "sourceRoot": "", "sources": ["../../src/pool/WeightedConnectionPool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,8CAA6E;AAE7E,mFAI6B;AAE7B,MAAqB,sBAAuB,SAAQ,4BAAkB;IAMpE,YAAa,IAA2B;QACtC,KAAK,CAAC,IAAI,CAAC,CAAA;QANb;;;;;WAAa;QACb;;;;;WAAiB;QACjB;;;;;WAA6B;QAC7B;;;;;WAAqB;QAInB,0BAA0B;QAC1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;QACf,0BAA0B;QAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;QAClB,+CAA+C;QAC/C,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAA;QAC9B,+BAA+B;QAC/B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;IACxB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAE,IAA0B;QACvC,MAAM,MAAM,GAAiB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sCAAiB,CAAA;QAClF,2DAA2D;QAC3D,wDAAwD;QACxD,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,OAAO,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC7B,oBAAoB;YACpB,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;YACzC,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAA;gBACpE,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAA;oBACnC,wBAAwB;oBACxB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;wBAC7B,OAAO,IAAI,CAAA;oBACb,CAAC;gBACH,CAAC;YACH,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClE,OAAO,UAAU,CAAA;YACnB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAE,UAAsB;QAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,2BAAc,CAAC,QAAQ,CAAC,KAAK;YAAE,OAAO,IAAI,CAAA;QAEvF,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,KAAK,CAAA;QACjD,UAAU,CAAC,SAAS,GAAG,CAAC,CAAA;QACxB,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;QAEhD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAA;QAEpG,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CAAE,UAAsB;QAC9B,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO,IAAI,CAAA;QAEhC,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,IAAI,CAAA;QAChD,UAAU,CAAC,SAAS,EAAE,CAAA;QACtB,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAA;QAE7F,wBAAwB;QACxB,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC;YAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;QAEjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAA;QAEpG,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,CAAC,KAAK,EAAE,CAAA;QACnB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;QAClB,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAA;QAC9B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;QACf,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;IACxB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAE,WAAkD;QACxD,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAEzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACpC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAA;QACpG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;QACf,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;QAEtB,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AA7HD,yCA6HC;AAED,SAAS,wBAAwB,CAAE,CAAS,EAAE,CAAS;IACrD,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,CAAA;IACrB,OAAO,wBAAwB,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAA;AAC3C,CAAC"}