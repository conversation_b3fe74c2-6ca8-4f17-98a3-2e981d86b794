# Database Configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="rootkan"
DB_NAME="db_kodexguard"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# Elasticsearch Configuration
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_USERNAME=""
ELASTICSEARCH_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# API Configuration
API_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Bot Configuration
WHATSAPP_SESSION_NAME="kodexguard-wa"
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
TELEGRAM_WEBHOOK_URL="https://yourdomain.com/api/bot/telegram"

# File Upload Configuration
MAX_FILE_SIZE="10485760"
UPLOAD_DIR="./uploads"

# Payment Gateway Configuration
TRIPAY_MERCHANT_CODE=""
TRIPAY_API_KEY=""
TRIPAY_PRIVATE_KEY=""
MIDTRANS_SERVER_KEY=""
MIDTRANS_CLIENT_KEY=""
XENDIT_SECRET_KEY=""

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASSWORD=""

# Security Configuration
BCRYPT_ROUNDS="12"
RATE_LIMIT_WINDOW="900000"
RATE_LIMIT_MAX="100"

# CVE API Configuration
CVE_API_KEY=""
NVD_API_KEY=""

# OSINT Configuration
OSINT_API_KEYS=""

# System Configuration
MAINTENANCE_MODE="false"
SYSTEM_VERSION="1.0.0"
