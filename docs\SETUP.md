# KodeXGuard Setup Guide

Panduan lengkap untuk menginstall dan menjalankan KodeXGuard platform cybersecurity & bug hunting.

## 📋 Prerequisites

### System Requirements
- **OS**: Windows 10/11, macOS, atau Linux
- **Node.js**: v18.0.0 atau lebih baru
- **Bun**: v1.0.0 atau lebih baru (recommended) atau npm/yarn
- **MySQL**: v8.0 atau lebih baru
- **Redis**: v6.0 atau lebih baru
- **Elasticsearch**: v8.0 atau lebih baru (optional tapi recommended)

### Hardware Requirements
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **CPU**: 2 cores minimum, 4+ cores recommended

## 🚀 Installation

### 1. Install Dependencies

#### Install Bun (Recommended)
```bash
# Windows (PowerShell)
irm bun.sh/install.ps1 | iex

# macOS/Linux
curl -fsSL https://bun.sh/install | bash
```

#### Install MySQL
**Windows:**
- Download dari [MySQL Official Website](https://dev.mysql.com/downloads/mysql/)
- Atau gunakan XAMPP/WAMP

**macOS:**
```bash
brew install mysql
brew services start mysql
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### Install Redis
**Windows:**
- Download dari [Redis for Windows](https://github.com/microsoftarchive/redis/releases)
- Atau gunakan Docker: `docker run -d -p 6379:6379 redis:alpine`

**macOS:**
```bash
brew install redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Install Elasticsearch (Optional)
**Docker (Recommended):**
```bash
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.11.0
```

### 2. Clone & Setup Project

```bash
# Clone repository
git clone https://github.com/your-repo/kodexguard.git
cd kodexguard

# Install dependencies
bun install

# Copy environment file
cp .env.example .env.local
```

### 3. Configure Environment

Edit `.env.local` file:

```env
# Database Configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="your_mysql_password"
DB_NAME="kodexguard"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# Elasticsearch Configuration (Optional)
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_USERNAME=""
ELASTICSEARCH_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# API Configuration
API_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Bot Configuration (Optional - untuk WhatsApp/Telegram)
WHATSAPP_SESSION_NAME="kodexguard-wa"
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"

# File Upload Configuration
MAX_FILE_SIZE="10485760"
UPLOAD_DIR="./uploads"

# Security Configuration
BCRYPT_ROUNDS="12"
RATE_LIMIT_WINDOW="900000"
RATE_LIMIT_MAX="100"
```

### 4. Setup Database

#### Create MySQL Database
```sql
-- Login ke MySQL
mysql -u root -p

-- Create database
CREATE DATABASE kodexguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'kodexguard'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON kodexguard.* TO 'kodexguard'@'localhost';
FLUSH PRIVILEGES;
```

#### Run Database Setup Script
```bash
# Setup database schema dan data default
bun run scripts/setup-database.ts
```

Script ini akan:
- Membuat semua tabel yang diperlukan
- Insert data default (plans, dork presets, system settings)
- Membuat user admin default
- Setup Elasticsearch indices (jika tersedia)

**Default Admin User:**
- Email: `<EMAIL>`
- Password: `admin123`
- ⚠️ **Penting**: Ganti password default setelah login pertama!

## 📋 Prerequisites

### System Requirements
- **OS**: Windows 10/11, macOS, atau Linux
- **Node.js**: v18.0.0 atau lebih baru
- **Bun**: v1.0.0 atau lebih baru (recommended) atau npm/yarn
- **MySQL**: v8.0 atau lebih baru
- **Redis**: v6.0 atau lebih baru
- **Elasticsearch**: v8.0 atau lebih baru (optional tapi recommended)

### Hardware Requirements
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **CPU**: 2 cores minimum, 4+ cores recommended

## 🚀 Installation

### 1. Install Dependencies

#### Install Bun (Recommended)
```bash
# Windows (PowerShell)
irm bun.sh/install.ps1 | iex

# macOS/Linux
curl -fsSL https://bun.sh/install | bash
```

#### Install MySQL
**Windows:**
- Download dari [MySQL Official Website](https://dev.mysql.com/downloads/mysql/)
- Atau gunakan XAMPP/WAMP

**macOS:**
```bash
brew install mysql
brew services start mysql
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### Install Redis
**Windows:**
- Download dari [Redis for Windows](https://github.com/microsoftarchive/redis/releases)
- Atau gunakan Docker: `docker run -d -p 6379:6379 redis:alpine`

**macOS:**
```bash
brew install redis
brew services start redis
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Install Elasticsearch (Optional)
**Docker (Recommended):**
```bash
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.11.0
```

### 2. Clone & Setup Project

```bash
# Clone repository
git clone https://github.com/your-repo/kodexguard.git
cd kodexguard

# Install dependencies
bun install

# Copy environment file
cp .env.example .env.local
```

### 3. Configure Environment

Edit `.env.local` file:

```env
# Database Configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="your_mysql_password"
DB_NAME="kodexguard"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# Elasticsearch Configuration (Optional)
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_USERNAME=""
ELASTICSEARCH_PASSWORD=""

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# API Configuration
API_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Bot Configuration
WHATSAPP_SESSION_NAME="kodexguard-wa"
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"

# File Upload Configuration
MAX_FILE_SIZE="10485760"
UPLOAD_DIR="./uploads"

# Security Configuration
BCRYPT_ROUNDS="12"
RATE_LIMIT_WINDOW="900000"
RATE_LIMIT_MAX="100"
```

### 4. Setup Database

#### Create MySQL Database
```sql
-- Login ke MySQL
mysql -u root -p

-- Create database
CREATE DATABASE kodexguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'kodexguard'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON kodexguard.* TO 'kodexguard'@'localhost';
FLUSH PRIVILEGES;
```

#### Run Database Setup Script
```bash
# Setup database schema dan data default
bun run scripts/setup-database.ts
```

Script ini akan:
- Membuat semua tabel yang diperlukan
- Insert data default (plans, dork presets, system settings)
- Membuat user admin default
- Setup Elasticsearch indices (jika tersedia)

**Default Admin User:**
- Email: `<EMAIL>`
- Password: `admin123`
- ⚠️ **Penting**: Ganti password default setelah login pertama!

### 5. Verify Installation

#### Test Database Connections
```bash
# Test koneksi database
bun run scripts/test-connections.ts
```

#### Start Development Server
```bash
# Start development server
bun run dev

# Atau dengan npm
npm run dev
```

Aplikasi akan berjalan di: http://localhost:3000

## 🔧 Configuration

### Bot Setup (Optional)

#### WhatsApp Bot
1. Jalankan aplikasi
2. Login sebagai Super Admin
3. Pergi ke Bot Center
4. Scan QR Code dengan WhatsApp
5. Bot akan terhubung otomatis

#### Telegram Bot
1. Buat bot baru dengan @BotFather di Telegram
2. Dapatkan Bot Token
3. Masukkan token ke `.env.local`
4. Restart aplikasi

### Payment Gateway Setup (Optional)

Edit `.env.local` untuk payment gateway:

```env
# Tripay
TRIPAY_MERCHANT_CODE="your_merchant_code"
TRIPAY_API_KEY="your_api_key"
TRIPAY_PRIVATE_KEY="your_private_key"

# Midtrans
MIDTRANS_SERVER_KEY="your_server_key"
MIDTRANS_CLIENT_KEY="your_client_key"

# Xendit
XENDIT_SECRET_KEY="your_secret_key"
```

## 🚦 Running in Production

### 1. Build Application
```bash
# Build for production
bun run build

# Start production server
bun run start
```

### 2. Process Manager (PM2)
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

### 3. Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔍 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check MySQL service
sudo systemctl status mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log

# Test connection
mysql -u root -p -e "SELECT 1"
```

#### Redis Connection Failed
```bash
# Check Redis service
sudo systemctl status redis-server

# Test Redis connection
redis-cli ping
```

#### Port Already in Use
```bash
# Find process using port 3000
lsof -i :3000

# Kill process
kill -9 <PID>
```

#### Permission Issues
```bash
# Fix file permissions
chmod -R 755 .
chown -R $USER:$USER .

# Fix uploads directory
mkdir -p uploads
chmod 755 uploads
```

### Performance Optimization

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_plan ON users(plan);
CREATE INDEX idx_scan_results_user_id ON scan_results(user_id);
CREATE INDEX idx_scan_results_created_at ON scan_results(created_at);
```

#### Redis Memory Optimization
```bash
# Edit Redis config
sudo nano /etc/redis/redis.conf

# Add these lines:
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 📚 Next Steps

1. **Login ke Admin Panel**: http://localhost:3000/admin
2. **Baca API Documentation**: http://localhost:3000/api/docs
3. **Setup Bot Integration**: Lihat [Bot Setup Guide](./BotSetup.md)
4. **Configure Payment**: Lihat [Payment Setup Guide](./PaymentSetup.md)
5. **Deploy to Production**: Lihat [Deployment Guide](./Deployment.md)

## 🆘 Support

Jika mengalami masalah:

1. **Check Logs**: `tail -f logs/app.log`
2. **GitHub Issues**: [Create Issue](https://github.com/your-repo/kodexguard/issues)
3. **Documentation**: [Full Documentation](./README.md)
4. **Community**: Join our Discord/Telegram group

---

**⚠️ Security Notice**: 
- Selalu ganti password default
- Gunakan HTTPS di production
- Update dependencies secara berkala
- Backup database secara rutin
