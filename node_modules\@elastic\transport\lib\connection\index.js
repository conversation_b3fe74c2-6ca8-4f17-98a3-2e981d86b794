"use strict";
/*
 * Copyright Elasticsearch B.V. and contributors
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UndiciConnection = exports.HttpConnection = exports.BaseConnection = void 0;
const tslib_1 = require("tslib");
const BaseConnection_1 = tslib_1.__importDefault(require("./BaseConnection"));
exports.BaseConnection = BaseConnection_1.default;
const HttpConnection_1 = tslib_1.__importDefault(require("./HttpConnection"));
exports.HttpConnection = HttpConnection_1.default;
const UndiciConnection_1 = tslib_1.__importDefault(require("./UndiciConnection"));
exports.UndiciConnection = UndiciConnection_1.default;
//# sourceMappingURL=index.js.map