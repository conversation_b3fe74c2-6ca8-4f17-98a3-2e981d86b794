import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'
import { withAuth, withCors, withErrorHandling, combineMiddlewares, AuthenticatedRequest } from '@/middlewares/auth'
import { z } from 'zod'

// Validation schema for creating API key
const createApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  permissions: z.array(z.string()).optional().default([])
})

// GET /api/user/api-keys - Get user API keys
async function getApiKeysHandler(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 400 }
      )
    }

    // Get user API keys
    const apiKeys = await AuthService.getUserApiKeys(req.userId)

    return NextResponse.json({
      success: true,
      data: {
        apiKeys: apiKeys.map(key => ({
          id: key.id,
          name: key.name,
          keyPrefix: key.key_prefix,
          permissions: JSON.parse(key.permissions || '[]'),
          isActive: key.is_active,
          usageCount: key.usage_count,
          rateLimit: key.rate_limit,
          lastUsed: key.last_used,
          createdAt: key.created_at
        }))
      }
    })

  } catch (error) {
    console.error('Get API keys error:', error)
    return NextResponse.json(
      { error: 'Failed to get API keys' },
      { status: 500 }
    )
  }
}

// POST /api/user/api-keys - Create new API key
async function createApiKeyHandler(req: AuthenticatedRequest): Promise<NextResponse> {
  try {
    if (!req.userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 400 }
      )
    }

    // Parse request body
    const body = await req.json()

    // Validate input
    const validation = createApiKeySchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      )
    }

    const { name, permissions } = validation.data

    // Check if user already has maximum number of API keys
    const existingKeys = await AuthService.getUserApiKeys(req.userId)
    const activeKeys = existingKeys.filter(key => key.is_active)
    
    if (activeKeys.length >= 10) { // Maximum 10 API keys per user
      return NextResponse.json(
        { error: 'Maximum number of API keys reached (10)' },
        { status: 400 }
      )
    }

    // Generate API key
    const result = await AuthService.generateApiKey(req.userId, name, permissions)

    if ('error' in result) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'API key created successfully',
      data: {
        apiKey: result.apiKey,
        keyId: result.keyId,
        warning: 'Please save this API key securely. You will not be able to see it again.'
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Create API key error:', error)
    return NextResponse.json(
      { error: 'Failed to create API key' },
      { status: 500 }
    )
  }
}

// Apply middlewares
export const GET = combineMiddlewares(
  withCors,
  withErrorHandling,
  withAuth
)(getApiKeysHandler)

export const POST = combineMiddlewares(
  withCors,
  withErrorHandling,
  withAuth
)(createApiKeyHandler)
