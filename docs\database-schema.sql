-- KodeXGuard Database Schema
-- MySQL 8.0+ Compatible

-- Create database
CREATE DATABASE IF NOT EXISTS kodexguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kodexguard;

-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HAR(255) NOT NULL,
    avatar VARCHAR(500),
    bio TEXT,
    role ENUM('super_admin', 'admin', 'user') DEFAULT 'user',
    plan ENUM('free', 'student', 'hobby', 'bughunter', 'cybersecurity') DEFAULT 'free',
    plan_expiry DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_plan (plan),
    INDEX idx_role (role)
);

-- User statistics table
CREATE TABLE user_stats (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    total_scans INT DEFAULT 0,
    vulnerabilities_found INT DEFAULT 0,
    files_analyzed INT DEFAULT 0,
    osint_queries INT DEFAULT 0,
    api_calls INT DEFAULT 0,
    score INT DEFAULT 0,
    rank_position INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_score (score),
    INDEX idx_rank (rank_position)
);

-- API Keys table
CREATE TABLE api_keys (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    key_prefix VARCHAR(20) NOT NULL,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_used DATETIME,
    usage_count INT DEFAULT 0,
    rate_limit INT DEFAULT 100,
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_key_hash (key_hash),
    INDEX idx_key_prefix (key_prefix)
);

-- Plans table
CREATE TABLE plans (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    type ENUM('free', 'student', 'hobby', 'bughunter', 'cybersecurity') NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'IDR',
    duration ENUM('daily', 'weekly', 'monthly', 'yearly') NOT NULL,
    features JSON,
    limits JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    plan_id VARCHAR(36) NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'pending', 'suspended') DEFAULT 'pending',
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    auto_renew BOOLEAN DEFAULT FALSE,
    payment_method JSON,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES plans(id),
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- Payments table
CREATE TABLE payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    subscription_id VARCHAR(36),
    payment_id VARCHAR(255) UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    status ENUM('pending', 'paid', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_method ENUM('manual_transfer', 'tripay', 'midtrans', 'xendit') NOT NULL,
    payment_data JSON,
    paid_at DATETIME,
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_id (payment_id),
    INDEX idx_status (status)
);

-- CVE Database table
CREATE TABLE cve_database (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    cve_id VARCHAR(20) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    severity ENUM('critical', 'high', 'medium', 'low', 'none') NOT NULL,
    cvss_score DECIMAL(3,1),
    cvss_vector VARCHAR(500),
    published_date DATETIME NOT NULL,
    last_modified DATETIME NOT NULL,
    references JSON,
    affected_products JSON,
    cwe_id VARCHAR(20),
    cwe_description TEXT,
    exploit_available BOOLEAN DEFAULT FALSE,
    patch_available BOOLEAN DEFAULT FALSE,
    tags JSON,
    source VARCHAR(100) DEFAULT 'NVD',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_cve_id (cve_id),
    INDEX idx_severity (severity),
    INDEX idx_cvss_score (cvss_score),
    INDEX idx_published_date (published_date),
    FULLTEXT idx_description (description)
);

-- Dork presets table
CREATE TABLE dork_presets (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    query TEXT NOT NULL,
    category ENUM('sensitive_files', 'login_pages', 'database_dumps', 'config_files', 'error_messages', 'directory_listing', 'vulnerable_apps', 'iot_devices') NOT NULL,
    tags JSON,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_usage_count (usage_count)
);

-- Custom dorks table
CREATE TABLE custom_dorks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    query TEXT NOT NULL,
    description TEXT,
    tags JSON,
    is_private BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_private (is_private)
);

-- Scan results table
CREATE TABLE scan_results (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    scan_type ENUM('vulnerability', 'file_analysis', 'osint', 'dorking') NOT NULL,
    target VARCHAR(500) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    results JSON,
    metadata JSON,
    started_at DATETIME,
    completed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_scan_type (scan_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- File uploads table
CREATE TABLE file_uploads (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    original_name VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(255),
    file_hash VARCHAR(64),
    scan_status ENUM('pending', 'scanning', 'completed', 'failed') DEFAULT 'pending',
    scan_results JSON,
    is_malicious BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_scan_status (scan_status),
    INDEX idx_is_malicious (is_malicious)
);

-- Bot configurations table
CREATE TABLE bot_configs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    bot_type ENUM('whatsapp', 'telegram') NOT NULL,
    name VARCHAR(100) NOT NULL,
    config JSON NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    is_connected BOOLEAN DEFAULT FALSE,
    last_activity DATETIME,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_bot_type (bot_type),
    INDEX idx_is_active (is_active)
);

-- Activity logs table
CREATE TABLE activity_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(36),
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- System settings table
CREATE TABLE system_settings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    key_name VARCHAR(100) UNIQUE NOT NULL,
    value JSON,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key_name (key_name),
    INDEX idx_is_public (is_public)
);

-- Insert default system settings
INSERT INTO system_settings (key_name, value, description, is_public) VALUES
('maintenance_mode', 'false', 'System maintenance mode toggle', true),
('system_version', '"1.0.0"', 'Current system version', true),
('max_file_size', '104857600', 'Maximum file upload size in bytes', false),
('rate_limit_window', '900000', 'Rate limit window in milliseconds', false),
('rate_limit_max', '100', 'Maximum requests per window', false);

-- Insert default plans
INSERT INTO plans (name, type, price, currency, duration, features, limits, is_active, is_popular, description) VALUES
('Gratis', 'free', 0, 'IDR', 'monthly', 
 '["Basic OSINT lookup", "CVE database access", "Community support", "Basic vulnerability scanner", "File hash checker"]',
 '{"dailyScans": 10, "monthlyScans": 300, "fileUploadSize": 5, "apiCallsPerDay": 50, "apiCallsPerMonth": 1500, "osintQueries": 5, "concurrentScans": 1, "botAccess": false, "prioritySupport": false, "customIntegrations": false, "advancedFeatures": false}',
 true, false, 'Perfect untuk pemula yang ingin mencoba fitur dasar cybersecurity'),

('Pelajar', 'student', 25000, 'IDR', 'monthly',
 '["Full OSINT investigator", "Advanced vulnerability scanner", "File analyzer", "Bot access (WhatsApp/Telegram)", "Priority support", "CVE intelligence", "Google dorking presets"]',
 '{"dailyScans": 100, "monthlyScans": 3000, "fileUploadSize": 25, "apiCallsPerDay": 500, "apiCallsPerMonth": 15000, "osintQueries": 50, "concurrentScans": 3, "botAccess": true, "prioritySupport": true, "customIntegrations": false, "advancedFeatures": false}',
 true, true, 'Ideal untuk pelajar dan mahasiswa yang belajar cybersecurity');

-- Create indexes for better performance
CREATE INDEX idx_users_plan_expiry ON users(plan_expiry);
CREATE INDEX idx_scan_results_completed_at ON scan_results(completed_at);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_cve_database_last_modified ON cve_database(last_modified);
