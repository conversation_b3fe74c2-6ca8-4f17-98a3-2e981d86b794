import { NextRequest, NextResponse } from 'next/server'
import { PlanService } from '@/lib/plan'
import { withCors, withErrorHandling, combineMiddlewares } from '@/middlewares/auth'

// GET /api/plans - Get all active plans
async function getPlansHandler(req: NextRequest): Promise<NextResponse> {
  try {
    const plans = await PlanService.getActivePlans()

    return NextResponse.json({
      success: true,
      data: {
        plans: plans.map(plan => ({
          id: plan.id,
          name: plan.name,
          type: plan.type,
          price: plan.price,
          currency: plan.currency,
          duration: plan.duration,
          features: plan.features,
          limits: plan.limits,
          isPopular: plan.isPopular,
          description: plan.description
        }))
      }
    })

  } catch (error) {
    console.error('Get plans API error:', error)
    return NextResponse.json(
      { error: 'Failed to get plans' },
      { status: 500 }
    )
  }
}

// Apply middlewares
export const GET = combineMiddlewares(
  withCors,
  withErrorHandling
)(getPlansHandler)
