'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { 
  Shield, 
  Search, 
  FileText, 
  Bug, 
  Zap, 
  Users, 
  BarChart3, 
  Activity,
  Globe,
  Database,
  Bot,
  Crown,
  TrendingUp,
  Clock,
  AlertTriangle,
  Target,
  Eye,
  Flame,
  Star,
  Award,
  Calendar,
  MapPin,
  Wifi,
  Server,
  Lock,
  Unlock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowUp,
  ArrowDown,
  Play,
  Pause,
  Settings
} from 'lucide-react'

interface DashboardStats {
  totalScans: number
  vulnerabilitiesFound: number
  filesAnalyzed: number
  osintQueries: number
  apiCalls: number
  score: number
  level: number
  rank: number
  streak: number
  pointsToday: number
  pointsThisWeek: number
  pointsThisMonth: number
}

interface RecentActivity {
  id: string
  type: 'scan' | 'osint' | 'file' | 'cve' | 'dorking'
  target: string
  time: string
  status: 'completed' | 'failed' | 'malicious' | 'clean' | 'pending'
  severity?: 'low' | 'medium' | 'high' | 'critical'
  result?: string
}

interface QuickAction {
  title: string
  description: string
  icon: any
  href: string
  badge?: string
  premium?: boolean
  color: string
  stats?: string
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalScans: 0,
    vulnerabilitiesFound: 0,
    filesAnalyzed: 0,
    osintQueries: 0,
    apiCalls: 0,
    score: 0,
    level: 0,
    rank: 0,
    streak: 0,
    pointsToday: 0,
    pointsThisWeek: 0,
    pointsThisMonth: 0
  })
  
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Fetch real dashboard data from API
      const response = await fetch('/api/dashboard/stats')
      const data = await response.json()

      if (data.success) {
        setStats({
          totalScans: data.data.stats.totalScans,
          vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,
          filesAnalyzed: data.data.stats.fileAnalyses,
          osintQueries: data.data.stats.osintQueries,
          apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,
          score: data.data.user.score,
          level: data.data.user.level,
          rank: data.data.user.rank,
          streak: data.data.user.streak,
          pointsToday: Math.floor(data.data.user.score * 0.1),
          pointsThisWeek: Math.floor(data.data.user.score * 0.3),
          pointsThisMonth: Math.floor(data.data.user.score * 0.6)
        })

        // Transform recent activities to match interface
        const transformedActivities = data.data.recentActivities.map((activity: any) => ({
          id: activity.id,
          type: activity.type === 'vulnerability_scan' ? 'scan' :
                activity.type === 'osint_query' ? 'osint' :
                activity.type === 'file_analysis' ? 'file' :
                activity.type === 'cve_search' ? 'cve' : 'dorking',
          target: activity.description.split(' ')[activity.description.split(' ').length - 1] || 'Unknown',
          time: new Date(activity.createdAt).toLocaleString(),
          status: activity.status === 'completed' ? 'completed' :
                  activity.status === 'failed' ? 'failed' : 'pending',
          severity: activity.results?.threatDetected ? 'critical' :
                   activity.results?.vulnerabilities > 5 ? 'high' :
                   activity.results?.vulnerabilities > 0 ? 'medium' : 'low',
          result: activity.results?.vulnerabilities ? `${activity.results.vulnerabilities} vulnerabilities found` :
                  activity.results?.threatDetected ? 'Threat detected' :
                  activity.results?.resultsFound ? `${activity.results.resultsFound} results found` :
                  'Completed successfully'
        }))

        setRecentActivity(transformedActivities)
      } else {
        // Fallback to mock data if API fails
        setStats({
          totalScans: 142,
          vulnerabilitiesFound: 23,
          filesAnalyzed: 89,
          osintQueries: 456,
          apiCalls: 2847,
          score: 8950,
          level: 28,
          rank: 156,
          streak: 12,
          pointsToday: 250,
          pointsThisWeek: 1450,
          pointsThisMonth: 5890
        })

        setRecentActivity([
          {
            id: '1',
            type: 'scan',
            target: 'example.com',
            time: '2 minutes ago',
            status: 'completed',
            severity: 'high',
            result: '3 vulnerabilities found'
          }
        ])
      }
      
      setLoading(false)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      setLoading(false)
    }
  }

  const quickActions: QuickAction[] = [
    {
      title: 'OSINT Lookup',
      description: 'Investigate emails, phone numbers, and personal information',
      icon: Search,
      href: '/osint',
      badge: 'Popular',
      color: 'cyber-primary',
      stats: '456 queries'
    },
    {
      title: 'Vulnerability Scan',
      description: 'Scan websites and applications for security vulnerabilities',
      icon: Shield,
      href: '/scanner',
      premium: true,
      color: 'cyber-secondary',
      stats: '142 scans'
    },
    {
      title: 'File Analysis',
      description: 'Analyze files for malware, webshells, and suspicious content',
      icon: FileText,
      href: '/file-analyzer',
      premium: true,
      color: 'cyber-accent',
      stats: '89 files'
    },
    {
      title: 'CVE Database',
      description: 'Search and explore the latest CVE vulnerabilities',
      icon: Database,
      href: '/cve',
      color: 'green-400',
      stats: '50K+ CVEs'
    },
    {
      title: 'Google Dorking',
      description: 'Use advanced search queries to find sensitive information',
      icon: Globe,
      href: '/dorking',
      color: 'blue-400',
      stats: 'Advanced'
    },
    {
      title: 'API Playground',
      description: 'Test and explore our API endpoints',
      icon: Zap,
      href: '/playground',
      color: 'purple-400',
      stats: '2.8K calls'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'scan': return Shield
      case 'osint': return Search
      case 'file': return FileText
      case 'cve': return Database
      case 'dorking': return Globe
      default: return Activity
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'failed': return 'text-red-400'
      case 'malicious': return 'text-red-400'
      case 'clean': return 'text-green-400'
      case 'pending': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-400/20'
      case 'high': return 'text-orange-400 bg-orange-400/20'
      case 'medium': return 'text-yellow-400 bg-yellow-400/20'
      case 'low': return 'text-green-400 bg-green-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading cyber dashboard...</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">Cyber</span>{' '}
              <span className="text-cyber-pink">Command</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Welcome back, Cyber Warrior! Your digital arsenal awaits.
            </p>
          </div>

          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-400">Current Time</div>
              <div className="text-lg font-mono text-cyber-primary">
                {currentTime.toLocaleTimeString()}
              </div>
            </div>
            <button className="btn-cyber-primary">
              <Zap className="h-4 w-4 mr-2" />
              Quick Scan
            </button>
          </div>
        </div>

        {/* User Profile Card */}
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black">
                CW
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Cyber Warrior</h2>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span>Level {stats.level}</span>
                  <span>•</span>
                  <span>Rank #{stats.rank}</span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Flame className="h-4 w-4 text-cyber-secondary" />
                    <span>{stats.streak} day streak</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-right">
              <div className="text-3xl font-bold text-cyber-primary">
                {stats.score.toLocaleString()}
              </div>
              <div className="text-sm text-gray-400">Total Score</div>
              <div className="flex items-center space-x-2 mt-2">
                <div className="text-xs text-green-400">+{stats.pointsToday} today</div>
                <ArrowUp className="h-3 w-3 text-green-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          <div className="card-cyber text-center">
            <Shield className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.totalScans}
            </div>
            <div className="text-sm text-gray-400">Total Scans</div>
            <div className="text-xs text-green-400 mt-1">+12 this week</div>
          </div>

          <div className="card-cyber text-center">
            <Bug className="h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.vulnerabilitiesFound}
            </div>
            <div className="text-sm text-gray-400">Vulnerabilities</div>
            <div className="text-xs text-red-400 mt-1">+3 critical</div>
          </div>

          <div className="card-cyber text-center">
            <FileText className="h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.filesAnalyzed}
            </div>
            <div className="text-sm text-gray-400">Files Analyzed</div>
            <div className="text-xs text-yellow-400 mt-1">5 malicious</div>
          </div>

          <div className="card-cyber text-center">
            <Search className="h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.osintQueries}
            </div>
            <div className="text-sm text-gray-400">OSINT Queries</div>
            <div className="text-xs text-blue-400 mt-1">+45 today</div>
          </div>

          <div className="card-cyber text-center">
            <Zap className="h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.apiCalls.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">API Calls</div>
            <div className="text-xs text-purple-400 mt-1">98% uptime</div>
          </div>

          <div className="card-cyber text-center">
            <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {stats.pointsThisWeek.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Weekly Points</div>
            <div className="text-xs text-green-400 mt-1">+15% vs last week</div>
          </div>
        </div>

        {/* System Status Alert */}
        <div className="card-cyber border-l-4 border-l-cyber-secondary">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-6 w-6 text-cyber-secondary mt-1" />
            <div className="flex-1">
              <h3 className="text-lg font-bold text-white mb-1">System Status Update</h3>
              <p className="text-gray-300 mb-3">
                New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.
              </p>
              <div className="flex items-center space-x-4">
                <button className="btn-cyber-secondary text-sm">
                  View Updates
                </button>
                <span className="text-xs text-gray-400">2 minutes ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">
            <span className="text-cyber-glow">Quick</span>{' '}
            <span className="text-cyber-pink">Actions</span>
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action, index) => {
              const Icon = action.icon
              return (
                <div
                  key={index}
                  className="card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group"
                  onClick={() => window.location.href = action.href}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg bg-${action.color}/20`}>
                      <Icon className={`h-6 w-6 text-${action.color} group-hover:animate-cyber-pulse`} />
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      {action.badge && (
                        <span className="bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold">
                          {action.badge}
                        </span>
                      )}
                      {action.premium && (
                        <span className="bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold">
                          PRO
                        </span>
                      )}
                    </div>
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-4">
                    {action.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className={`text-xs font-medium text-${action.color}`}>
                      {action.stats}
                    </span>
                    <Play className="h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors" />
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">
            <span className="text-cyber-glow">Recent</span>{' '}
            <span className="text-cyber-pink">Activity</span>
          </h2>

          <div className="card-cyber">
            <div className="space-y-4">
              {recentActivity.map((activity) => {
                const Icon = getActivityIcon(activity.type)
                return (
                  <div
                    key={activity.id}
                    className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="p-2 rounded-lg bg-cyber-primary/20">
                        <Icon className="h-5 w-5 text-cyber-primary" />
                      </div>

                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-white">{activity.target}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase ${getStatusColor(activity.status)}`}>
                            {activity.status}
                          </span>
                          {activity.severity && (
                            <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase ${getSeverityColor(activity.severity)}`}>
                              {activity.severity}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-400 text-sm">{activity.result}</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm text-gray-400">{activity.time}</div>
                      <div className="text-xs text-gray-500 capitalize">{activity.type}</div>
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="mt-6 text-center">
              <button className="text-cyber-primary hover:text-cyber-secondary transition-colors">
                View All Activity →
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
