'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { StatsCard, FeatureCard, AlertCard } from '@/components/Card'
import { 
  Shield, 
  Search, 
  FileText, 
  Bug, 
  Zap, 
  Users, 
  BarChart3, 
  Activity,
  Globe,
  Database,
  Bot,
  Crown,
  TrendingUp,
  Clock,
  AlertTriangle
} from 'lucide-react'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalScans: 0,
    vulnerabilitiesFound: 0,
    filesAnalyzed: 0,
    osintQueries: 0,
    apiCalls: 0,
    score: 0
  })
  
  const [recentActivity, setRecentActivity] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load dashboard data
    const loadDashboardData = async () => {
      try {
        const token = localStorage.getItem('token')
        
        // Simulate API calls for now
        setTimeout(() => {
          setStats({
            totalScans: 42,
            vulnerabilitiesFound: 15,
            filesAnalyzed: 28,
            osintQueries: 156,
            apiCalls: 1247,
            score: 2850
          })
          
          setRecentActivity([
            { type: 'scan', target: 'example.com', time: '2 minutes ago', status: 'completed' },
            { type: 'osint', target: '<EMAIL>', time: '15 minutes ago', status: 'completed' },
            { type: 'file', target: 'suspicious.php', time: '1 hour ago', status: 'malicious' }
          ])
          
          setLoading(false)
        }, 1000)
        
      } catch (error) {
        console.error('Error loading dashboard data:', error)
        setLoading(false)
      }
    }

    loadDashboardData()
  }, [])

  const quickActions = [
    {
      title: 'OSINT Lookup',
      description: 'Investigate emails, phone numbers, and personal information',
      icon: Search,
      href: '/osint',
      badge: 'Popular'
    },
    {
      title: 'Vulnerability Scan',
      description: 'Scan websites and applications for security vulnerabilities',
      icon: Shield,
      href: '/scanner',
      premium: true
    },
    {
      title: 'File Analysis',
      description: 'Analyze files for malware, webshells, and suspicious content',
      icon: FileText,
      href: '/file-analyzer',
      premium: true
    },
    {
      title: 'CVE Database',
      description: 'Search and explore the latest CVE vulnerabilities',
      icon: Database,
      href: '/cve'
    },
    {
      title: 'Google Dorking',
      description: 'Use advanced search queries to find sensitive information',
      icon: Globe,
      href: '/dorking'
    },
    {
      title: 'API Playground',
      description: 'Test and explore our API endpoints',
      icon: Zap,
      href: '/playground'
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back! Here's what's happening with your security research.</p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn-primary">
              <Zap className="h-4 w-4 mr-2" />
              Quick Scan
            </button>
          </div>
        </div>

        {/* System Alert */}
        <AlertCard
          type="info"
          title="New CVE Updates Available"
          message="15 new critical vulnerabilities have been added to our database. Check the CVE Intelligence section for details."
        />

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          <StatsCard
            title="Total Scans"
            value={loading ? '...' : stats.totalScans}
            icon={BarChart3}
            color="blue"
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="Vulnerabilities"
            value={loading ? '...' : stats.vulnerabilitiesFound}
            icon={Bug}
            color="red"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Files Analyzed"
            value={loading ? '...' : stats.filesAnalyzed}
            icon={FileText}
            color="green"
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="OSINT Queries"
            value={loading ? '...' : stats.osintQueries}
            icon={Search}
            color="purple"
            trend={{ value: 23, isPositive: true }}
          />
          <StatsCard
            title="API Calls"
            value={loading ? '...' : stats.apiCalls}
            icon={Zap}
            color="yellow"
            trend={{ value: 5, isPositive: false }}
          />
          <StatsCard
            title="Score"
            value={loading ? '...' : stats.score}
            icon={Crown}
            color="gray"
            trend={{ value: 18, isPositive: true }}
          />
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <FeatureCard
                key={index}
                title={action.title}
                description={action.description}
                icon={action.icon}
                href={action.href}
                badge={action.badge}
                premium={action.premium}
              />
            ))}
          </div>
        </div>

        {/* Recent Activity & System Status */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              <Activity className="h-5 w-5 text-gray-400" />
            </div>
            
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse flex space-x-3">
                    <div className="rounded-full bg-gray-300 h-8 w-8"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      activity.type === 'scan' ? 'bg-blue-100 text-blue-600' :
                      activity.type === 'osint' ? 'bg-purple-100 text-purple-600' :
                      'bg-green-100 text-green-600'
                    }`}>
                      {activity.type === 'scan' && <Shield className="h-4 w-4" />}
                      {activity.type === 'osint' && <Search className="h-4 w-4" />}
                      {activity.type === 'file' && <FileText className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.type === 'scan' && 'Vulnerability scan completed'}
                        {activity.type === 'osint' && 'OSINT lookup performed'}
                        {activity.type === 'file' && 'File analysis completed'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {activity.target} • {activity.time}
                      </p>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                      activity.status === 'malicious' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {activity.status}
                    </div>
                  </div>
                ))}
                
                <div className="pt-4 border-t border-gray-200">
                  <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    View all activity →
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* System Status */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">All systems operational</span>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Database className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-900">CVE Database</span>
                </div>
                <span className="text-sm text-green-600">Online</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Bot className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-900">Bot Services</span>
                </div>
                <span className="text-sm text-green-600">Active</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Zap className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-900">API Gateway</span>
                </div>
                <span className="text-sm text-green-600">Healthy</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Globe className="h-5 w-5 text-yellow-600" />
                  <span className="text-sm font-medium text-gray-900">OSINT Sources</span>
                </div>
                <span className="text-sm text-yellow-600">Partial</span>
              </div>
            </div>
            
            <div className="pt-4 border-t border-gray-200">
              <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                View system details →
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
