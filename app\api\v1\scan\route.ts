import { NextRequest, NextResponse } from 'next/server'
import { advancedAPI } from '@/lib/api/advanced-api'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let apiKey: any = null

  try {
    // Authenticate API request
    const authResult = await advancedAPI.authenticate(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }

    apiKey = authResult.apiKey!

    // Check rate limit
    const rateLimit = await advancedAPI.checkRateLimit(apiKey, '/api/v1/scan')
    if (!rateLimit.allowed) {
      const response = NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
      
      response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
      response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
      response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())
      
      return response
    }

    // Handle scan request
    const response = await advancedAPI.handleScanRequest(request, apiKey)
    
    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())

    return response

  } catch (error) {
    console.error('API scan error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  } finally {
    // Log API usage
    if (apiKey) {
      const responseTime = Date.now() - startTime
      const ipAddress = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       'unknown'
      const userAgent = request.headers.get('user-agent') || 'unknown'

      await advancedAPI.logAPIUsage(
        apiKey.id,
        '/api/v1/scan',
        'POST',
        200,
        responseTime,
        0,
        0,
        ipAddress,
        userAgent
      )
    }
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/v1/scan',
    method: 'POST',
    description: 'Start a network/port scanning session',
    parameters: {
      target: 'string (required) - Target IP, domain, or CIDR range',
      scanType: 'string (required) - Scan type: nmap, masscan, or zmap',
      ports: 'string (optional) - Port range, default: "1-1000"',
      options: {
        aggressive: 'boolean (optional) - Enable aggressive scanning, default: false',
        serviceDetection: 'boolean (optional) - Enable service detection, default: true',
        osDetection: 'boolean (optional) - Enable OS detection, default: false',
        timing: 'number (optional, 1-5) - Timing template, default: 3'
      }
    },
    example: {
      target: '***********',
      scanType: 'nmap',
      ports: '80,443,8080',
      options: {
        aggressive: false,
        serviceDetection: true,
        osDetection: false,
        timing: 3
      }
    },
    response: {
      success: true,
      sessionId: 'string',
      message: 'Scan session started',
      estimatedTime: 'string'
    }
  })
}
