{"name": "spawn-please", "version": "2.0.2", "description": "Promisified child_process.spawn. *Supports stdin* *Rejects on stderr*", "license": "ISC", "repository": "raineorshine/spawn-please", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/raineorshine"}, "engines": {"node": ">=14"}, "scripts": {"build": "tsc", "lint": "eslint .", "lintfix": "eslint --fix .", "test": "mocha"}, "keywords": ["promise", "promisified", "child_process", "child process", "exec", "spawn", "stdin", "stdout", "stderr"], "devDependencies": {"chai": "^4.3.7", "chai-as-promised": "^7.1.1", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-config-raine": "^0.4.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-fp": "^2.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.14", "eslint-plugin-n": "^16.0.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^4.1.0", "mocha": "^10.2.0", "nodemon": "^3.0.1", "prettier": "^3.0.2", "require-new": "^1.1.0", "typescript": "^5.1.6"}, "dependencies": {"cross-spawn": "^7.0.3"}}