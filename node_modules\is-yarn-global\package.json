{"name": "is-yarn-global", "version": "0.4.1", "description": "Check if installed by yarn globally without any `fs` calls", "repository": "**************:LitoMore/is-yarn-global.git", "author": "LitoMore (<EMAIL>)", "type": "module", "exports": "./dist/index.js", "types": "dist", "files": ["dist"], "engines": {"node": ">=12"}, "license": "MIT", "scripts": {"build": "tsc", "test": "xo"}, "devDependencies": {"@sindresorhus/tsconfig": "^1.0.2", "xo": "^0.39.1"}, "xo": {"prettier": true}}