{"version": 3, "file": "interface-state.js", "sourceRoot": "", "sources": ["../../../../src/api/model/enum/interface-state.ts"], "names": [], "mappings": ";;;AAAA,IAAY,cAiCX;AAjCD,WAAY,cAAc;IACxB;;OAEG;IACH,qCAAmB,CAAA;IACnB;;OAEG;IACH,qCAAmB,CAAA;IACnB;;OAEG;IACH,qCAAmB,CAAA;IACnB;;OAEG;IACH,qCAAmB,CAAA;IACnB;;OAEG;IACH,uCAAqB,CAAA;IACrB;;OAEG;IACH,2CAAyB,CAAA;IACzB;;OAEG;IACH,mCAAiB,CAAA;IACjB;;OAEG;IACH,uDAAqC,CAAA;AACvC,CAAC,EAjCW,cAAc,8BAAd,cAAc,QAiCzB"}