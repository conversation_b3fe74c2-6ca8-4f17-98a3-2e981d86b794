var LineByLineReader = require('../line-by-line.js'),
	lr = new LineByLineReader('example.txt', {skipEmptyLines: true }),
	row = 0;

lr.on('error', function (err) {
	throw err;
});

lr.on('open', function() {
	// Do something, like initialise progress bar etc.
});

lr.on('line', function (line) {
	console.log(++row + ": " + line);
});

lr.on('end', function () {
	console.log("Ok we're done - exiting now.");
});