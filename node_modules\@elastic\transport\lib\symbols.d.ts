export declare const kSniffEnabled: unique symbol;
export declare const kNextSniff: unique symbol;
export declare const kIsSniffing: unique symbol;
export declare const kSniffInterval: unique symbol;
export declare const kSniffOnConnectionFault: unique symbol;
export declare const kSniffEndpoint: unique symbol;
export declare const kRequestTimeout: unique symbol;
export declare const kRetryOnTimeout: unique symbol;
export declare const kCompression: unique symbol;
export declare const kMaxRetries: unique symbol;
export declare const kName: unique symbol;
export declare const kOpaqueIdPrefix: unique symbol;
export declare const kGenerateRequestId: unique symbol;
export declare const kContext: unique symbol;
export declare const kConnectionPool: unique symbol;
export declare const kSerializer: unique symbol;
export declare const kDiagnostic: unique symbol;
export declare const kHeaders: unique symbol;
export declare const kNodeFilter: unique symbol;
export declare const kNodeSelector: unique symbol;
export declare const kJsonOptions: unique symbol;
export declare const kStatus: unique symbol;
export declare const kProductCheck: unique symbol;
export declare const kCaFingerprint: unique symbol;
export declare const kMaxResponseSize: unique symbol;
export declare const kMaxCompressedResponseSize: unique symbol;
export declare const kJsonContentType: unique symbol;
export declare const kNdjsonContentType: unique symbol;
export declare const kAcceptHeader: unique symbol;
export declare const kRedaction: unique symbol;
export declare const kRetryBackoff: unique symbol;
export declare const kOtelTracer: unique symbol;
export declare const kOtelOptions: unique symbol;
