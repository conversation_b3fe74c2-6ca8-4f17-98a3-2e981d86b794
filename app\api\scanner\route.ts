import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'
import { VulnerabilityScanner } from '@/lib/services/scanner'
import { z } from 'zod'

const scanSchema = z.object({
  url: z.string().url('Invalid URL format'),
  scanType: z.enum(['basic', 'advanced', 'comprehensive']).default('basic')
})

export async function POST(request: NextRequest) {
  try {
    // For demo purposes, we'll use a mock user
    // In production, uncomment the authentication below
    /*
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }
    const user = authResult.user
    */

    // Mock user for demo
    const user = { id: 1, plan: 'Pro' }
    const body = await request.json()

    // Validate input
    const validatedData = scanSchema.parse(body)

    // Check user plan limits (simplified for demo)
    const planLimits = { vulnerabilityScans: 50 } // Pro plan limit
    const todayScans = 5 // Mock current usage
    
    if (planLimits.vulnerabilityScans !== -1 && todayScans >= planLimits.vulnerabilityScans) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Daily vulnerability scan limit reached. Upgrade your plan for more scans.',
          limit: planLimits.vulnerabilityScans,
          used: todayScans
        },
        { status: 429 }
      )
    }

    // Check if scan type is allowed for user plan
    if (validatedData.scanType === 'advanced' && !['Pro', 'Expert', 'Elite'].includes(user.plan)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Advanced scans require Pro plan or higher.' 
        },
        { status: 403 }
      )
    }

    if (validatedData.scanType === 'comprehensive' && !['Expert', 'Elite'].includes(user.plan)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Comprehensive scans require Expert plan or higher.' 
        },
        { status: 403 }
      )
    }
    
    // Create scan record in database
    const result = await db.query(
      `INSERT INTO vulnerability_scans (user_id, target_url, scan_type, status, created_at)
       VALUES (?, ?, ?, 'pending', NOW())`,
      [user.id, validatedData.url, validatedData.scanType]
    )

    const scanId = (result as any).insertId

    // Start vulnerability scan
    const scanner = new VulnerabilityScanner()

    // Run scan in background
    scanner.scan({
      id: scanId,
      url: validatedData.url,
      scanType: validatedData.scanType,
      userId: user.id
    }).then(async (results) => {
      // Update scan record with results
      await db.query(
        `UPDATE vulnerability_scans SET
         status = 'completed',
         vulnerabilities_found = ?,
         severity_critical = ?,
         severity_high = ?,
         severity_medium = ?,
         severity_low = ?,
         scan_results = ?,
         completed_at = NOW()
         WHERE id = ?`,
        [
          results.summary.total,
          results.summary.critical,
          results.summary.high,
          results.summary.medium,
          results.summary.low,
          JSON.stringify(results),
          scanId
        ]
      )

      // Award points based on scan type
      const points = validatedData.scanType === 'basic' ? 5 :
                    validatedData.scanType === 'advanced' ? 10 : 15
      await db.query(
        `UPDATE users SET score = score + ? WHERE id = ?`,
        [points, user.id]
      )

    }).catch(async (error) => {
      // Update scan record with error
      await db.query(
        `UPDATE vulnerability_scans SET status = 'failed', error_message = ?, completed_at = NOW() WHERE id = ?`,
        [error.message, scanId]
      )
    })
    
    return NextResponse.json({
      success: true,
      message: 'Vulnerability scan started',
      scanId: scanId,
      estimatedTime: validatedData.scanType === 'basic' ? '1-2 minutes' :
                     validatedData.scanType === 'advanced' ? '3-5 minutes' : '5-10 minutes'
    })
    
  } catch (error) {
    console.error('Vulnerability scan error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid input data',
          errors: error.errors 
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // For demo purposes, we'll use a mock user
    // In production, uncomment the authentication below
    /*
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }
    const user = authResult.user
    */

    // Mock user for demo
    const user = { id: 1 }
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const scanType = searchParams.get('scanType')

    // Get user's vulnerability scans from database
    let query = 'SELECT * FROM vulnerability_scans WHERE user_id = ?'
    const params: any[] = [user.id]

    if (status) {
      query += ' AND status = ?'
      params.push(status)
    }

    if (scanType) {
      query += ' AND scan_type = ?'
      params.push(scanType)
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?'
    params.push(limit, (page - 1) * limit)

    const scans = await db.query(query, params)

    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM vulnerability_scans WHERE user_id = ?'
    const countParams: any[] = [user.id]

    if (status) {
      countQuery += ' AND status = ?'
      countParams.push(status)
    }

    if (scanType) {
      countQuery += ' AND scan_type = ?'
      countParams.push(scanType)
    }

    const countResult = await db.query(countQuery, countParams)
    const total = (countResult as any)[0]?.total || 0

    return NextResponse.json({
      success: true,
      data: scans,
      pagination: {
        page,
        limit,
        total: total,
        pages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Get vulnerability scans error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
