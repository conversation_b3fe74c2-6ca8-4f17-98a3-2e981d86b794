import { spawn, exec } from 'child_process'
import { promisify } from 'util'
import { createHash } from 'crypto'
import { readFile, writeFile, unlink, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import axios from 'axios'
import { db } from '../database'

const execAsync = promisify(exec)

export interface AnalysisTarget {
  id: string
  filePath: string
  fileName: string
  fileSize: number
  analysisType: 'static' | 'dynamic' | 'sandbox' | 'comprehensive'
  options: AnalysisOptions
}

export interface AnalysisOptions {
  timeout: number // seconds
  enableNetworking: boolean
  enableFileSystem: boolean
  enableRegistry: boolean
  captureScreenshots: boolean
  captureNetwork: boolean
  yara: boolean
  clamav: boolean
  virustotal: boolean
  customRules: string[]
}

export interface StaticAnalysis {
  fileInfo: {
    md5: string
    sha1: string
    sha256: string
    size: number
    type: string
    entropy: number
  }
  peInfo?: {
    architecture: string
    subsystem: string
    timestamp: Date
    sections: Array<{
      name: string
      virtualSize: number
      rawSize: number
      entropy: number
      characteristics: string[]
    }>
    imports: Array<{
      dll: string
      functions: string[]
    }>
    exports: string[]
    resources: Array<{
      type: string
      name: string
      size: number
    }>
  }
  strings: {
    ascii: string[]
    unicode: string[]
    urls: string[]
    ips: string[]
    emails: string[]
    domains: string[]
  }
  signatures: {
    yara: Array<{
      rule: string
      description: string
      severity: 'low' | 'medium' | 'high' | 'critical'
    }>
    clamav: Array<{
      signature: string
      description: string
    }>
  }
}

export interface DynamicAnalysis {
  behavior: {
    processes: Array<{
      pid: number
      name: string
      commandLine: string
      parentPid: number
      startTime: Date
    }>
    files: {
      created: string[]
      modified: string[]
      deleted: string[]
      accessed: string[]
    }
    registry: {
      created: string[]
      modified: string[]
      deleted: string[]
    }
    network: Array<{
      protocol: string
      localAddress: string
      localPort: number
      remoteAddress: string
      remotePort: number
      direction: 'inbound' | 'outbound'
      data?: string
    }>
    mutexes: string[]
    services: Array<{
      name: string
      action: 'created' | 'started' | 'stopped' | 'deleted'
    }>
  }
  screenshots: string[] // Base64 encoded images
  memory: {
    dumps: string[]
    strings: string[]
    injections: Array<{
      process: string
      address: string
      size: number
    }>
  }
}

export interface SandboxAnalysis {
  environment: {
    os: string
    version: string
    architecture: string
    duration: number
  }
  static: StaticAnalysis
  dynamic: DynamicAnalysis
  verdict: {
    isMalicious: boolean
    confidence: number
    threatType: string[]
    family?: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    reasons: string[]
  }
}

export interface AnalysisSession {
  id: string
  target: AnalysisTarget
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout'
  startTime: Date
  endTime?: Date
  progress: number
  results?: SandboxAnalysis
  errors: string[]
  logs: string[]
}

export class MalwareAnalyzer {
  private sessions: Map<string, AnalysisSession> = new Map()
  private sandboxPath: string
  private yaraRulesPath: string

  constructor() {
    this.sandboxPath = process.env.SANDBOX_PATH || './sandbox'
    this.yaraRulesPath = process.env.YARA_RULES_PATH || './yara-rules'
    this.initializeAnalyzer()
  }

  private async initializeAnalyzer() {
    console.log('🦠 Malware Analyzer initialized')
    await this.createTables()
    await this.setupSandbox()
    await this.checkDependencies()
    await this.loadYaraRules()
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS malware_analyses (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          file_name VARCHAR(255) NOT NULL,
          file_size BIGINT NOT NULL,
          file_hash_md5 VARCHAR(32),
          file_hash_sha256 VARCHAR(64),
          analysis_type ENUM('static', 'dynamic', 'sandbox', 'comprehensive') NOT NULL,
          status ENUM('pending', 'running', 'completed', 'failed', 'timeout') DEFAULT 'pending',
          progress INT DEFAULT 0,
          is_malicious BOOLEAN DEFAULT FALSE,
          threat_type JSON,
          confidence_score DECIMAL(3,2),
          severity ENUM('low', 'medium', 'high', 'critical'),
          results JSON,
          errors JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS malware_signatures (
          id VARCHAR(36) PRIMARY KEY,
          signature_type ENUM('yara', 'clamav', 'custom') NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          rule_content TEXT NOT NULL,
          severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
          is_active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)

      console.log('✅ Malware Analyzer tables created')
    } catch (error) {
      console.error('❌ Error creating malware analyzer tables:', error)
    }
  }

  private async setupSandbox() {
    try {
      if (!existsSync(this.sandboxPath)) {
        await mkdir(this.sandboxPath, { recursive: true })
      }
      
      // Create sandbox subdirectories
      const subdirs = ['samples', 'analysis', 'logs', 'screenshots', 'memory']
      for (const subdir of subdirs) {
        const dirPath = path.join(this.sandboxPath, subdir)
        if (!existsSync(dirPath)) {
          await mkdir(dirPath, { recursive: true })
        }
      }

      console.log('✅ Sandbox environment setup completed')
    } catch (error) {
      console.error('❌ Error setting up sandbox:', error)
    }
  }

  private async checkDependencies() {
    const tools = [
      { name: 'yara', command: 'yara --version' },
      { name: 'clamav', command: 'clamscan --version' },
      { name: 'strings', command: 'strings --version' },
      { name: 'file', command: 'file --version' }
    ]
    
    for (const tool of tools) {
      try {
        await execAsync(tool.command)
        console.log(`✅ ${tool.name} is available`)
      } catch (error) {
        console.log(`⚠️ ${tool.name} is not installed or not in PATH`)
      }
    }
  }

  private async loadYaraRules() {
    try {
      if (!existsSync(this.yaraRulesPath)) {
        await mkdir(this.yaraRulesPath, { recursive: true })
        
        // Create basic YARA rules
        await this.createBasicYaraRules()
      }
      
      console.log('✅ YARA rules loaded')
    } catch (error) {
      console.error('❌ Error loading YARA rules:', error)
    }
  }

  private async createBasicYaraRules() {
    const basicRules = [
      {
        name: 'suspicious_strings.yar',
        content: `
rule SuspiciousStrings {
    meta:
        description = "Detects suspicious strings commonly found in malware"
        severity = "medium"
    strings:
        $s1 = "CreateRemoteThread" ascii
        $s2 = "VirtualAllocEx" ascii
        $s3 = "WriteProcessMemory" ascii
        $s4 = "SetWindowsHookEx" ascii
        $s5 = "keylogger" ascii nocase
        $s6 = "backdoor" ascii nocase
        $s7 = "rootkit" ascii nocase
    condition:
        any of them
}
        `
      },
      {
        name: 'crypto_miners.yar',
        content: `
rule CryptoMiner {
    meta:
        description = "Detects cryptocurrency mining malware"
        severity = "high"
    strings:
        $s1 = "stratum+tcp://" ascii
        $s2 = "xmrig" ascii nocase
        $s3 = "cryptonight" ascii nocase
        $s4 = "monero" ascii nocase
        $s5 = "bitcoin" ascii nocase
    condition:
        any of them
}
        `
      }
    ]

    for (const rule of basicRules) {
      const rulePath = path.join(this.yaraRulesPath, rule.name)
      await writeFile(rulePath, rule.content.trim())
    }
  }

  async startAnalysis(target: AnalysisTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()
    
    const session: AnalysisSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      progress: 0,
      errors: [],
      logs: []
    }

    this.sessions.set(sessionId, session)

    // Calculate file hashes
    const fileBuffer = await readFile(target.filePath)
    const md5 = createHash('md5').update(fileBuffer).digest('hex')
    const sha256 = createHash('sha256').update(fileBuffer).digest('hex')

    // Save to database
    await db.query(`
      INSERT INTO malware_analyses (id, user_id, file_name, file_size, file_hash_md5, file_hash_sha256, analysis_type, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
    `, [sessionId, userId || null, target.fileName, target.fileSize, md5, sha256, target.analysisType])

    // Start analysis
    this.executeAnalysis(sessionId)

    return sessionId
  }

  private async executeAnalysis(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🦠 Starting ${session.target.analysisType} analysis for ${session.target.fileName}`)

      let results: SandboxAnalysis

      switch (session.target.analysisType) {
        case 'static':
          results = await this.performStaticAnalysis(session)
          break
        case 'dynamic':
          results = await this.performDynamicAnalysis(session)
          break
        case 'sandbox':
          results = await this.performSandboxAnalysis(session)
          break
        case 'comprehensive':
          results = await this.performComprehensiveAnalysis(session)
          break
        default:
          throw new Error(`Unsupported analysis type: ${session.target.analysisType}`)
      }

      session.results = results
      session.status = 'completed'
      session.endTime = new Date()
      session.progress = 100

      // Save results to database
      await this.saveResults(sessionId, results)
      await this.updateSessionStatus(sessionId, 'completed')

      console.log(`🎉 Analysis ${sessionId} completed: ${results.verdict.isMalicious ? 'MALICIOUS' : 'CLEAN'}`)

    } catch (error) {
      session.status = 'failed'
      session.errors.push(`Analysis failed: ${error}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Analysis ${sessionId} failed:`, error)
    }
  }

  private async performStaticAnalysis(session: AnalysisSession): Promise<SandboxAnalysis> {
    const { filePath, fileName } = session.target
    session.progress = 10

    // File info analysis
    const fileBuffer = await readFile(filePath)
    const fileInfo = {
      md5: createHash('md5').update(fileBuffer).digest('hex'),
      sha1: createHash('sha1').update(fileBuffer).digest('hex'),
      sha256: createHash('sha256').update(fileBuffer).digest('hex'),
      size: fileBuffer.length,
      type: await this.getFileType(filePath),
      entropy: this.calculateEntropy(fileBuffer)
    }

    session.progress = 30

    // Extract strings
    const strings = await this.extractStrings(filePath)
    session.progress = 50

    // YARA scanning
    const yaraResults = await this.scanWithYara(filePath)
    session.progress = 70

    // ClamAV scanning
    const clamavResults = await this.scanWithClamAV(filePath)
    session.progress = 90

    // Generate verdict
    const verdict = this.generateVerdict(yaraResults, clamavResults, strings)
    session.progress = 100

    return {
      environment: {
        os: process.platform,
        version: process.version,
        architecture: process.arch,
        duration: Date.now() - session.startTime.getTime()
      },
      static: {
        fileInfo,
        strings,
        signatures: {
          yara: yaraResults,
          clamav: clamavResults
        }
      },
      dynamic: {
        behavior: {
          processes: [],
          files: { created: [], modified: [], deleted: [], accessed: [] },
          registry: { created: [], modified: [], deleted: [] },
          network: [],
          mutexes: [],
          services: []
        },
        screenshots: [],
        memory: { dumps: [], strings: [], injections: [] }
      },
      verdict
    }
  }

  private async performDynamicAnalysis(session: AnalysisSession): Promise<SandboxAnalysis> {
    // Dynamic analysis would require a proper sandbox environment
    // This is a simplified implementation
    const staticResults = await this.performStaticAnalysis(session)
    
    // Add mock dynamic behavior
    staticResults.dynamic = {
      behavior: {
        processes: [
          {
            pid: 1234,
            name: session.target.fileName,
            commandLine: session.target.filePath,
            parentPid: 0,
            startTime: new Date()
          }
        ],
        files: {
          created: [`C:\\temp\\${session.target.fileName}.tmp`],
          modified: [],
          deleted: [],
          accessed: ['C:\\Windows\\System32\\kernel32.dll']
        },
        registry: {
          created: ['HKEY_CURRENT_USER\\Software\\Test'],
          modified: [],
          deleted: []
        },
        network: [],
        mutexes: [`Global\\${session.target.fileName}_mutex`],
        services: []
      },
      screenshots: [],
      memory: {
        dumps: [],
        strings: [],
        injections: []
      }
    }

    return staticResults
  }

  private async performSandboxAnalysis(session: AnalysisSession): Promise<SandboxAnalysis> {
    // Full sandbox analysis combining static and dynamic
    return await this.performComprehensiveAnalysis(session)
  }

  private async performComprehensiveAnalysis(session: AnalysisSession): Promise<SandboxAnalysis> {
    // Comprehensive analysis combining all methods
    const staticResults = await this.performStaticAnalysis(session)
    const dynamicResults = await this.performDynamicAnalysis(session)
    
    // Combine results
    return {
      ...staticResults,
      dynamic: dynamicResults.dynamic,
      verdict: this.enhanceVerdict(staticResults.verdict, dynamicResults.dynamic)
    }
  }

  private async getFileType(filePath: string): Promise<string> {
    try {
      const { stdout } = await execAsync(`file "${filePath}"`)
      return stdout.trim()
    } catch (error) {
      return 'unknown'
    }
  }

  private calculateEntropy(buffer: Buffer): number {
    const frequencies: { [key: number]: number } = {}
    
    for (const byte of buffer) {
      frequencies[byte] = (frequencies[byte] || 0) + 1
    }
    
    let entropy = 0
    const length = buffer.length
    
    for (const count of Object.values(frequencies)) {
      const probability = count / length
      entropy -= probability * Math.log2(probability)
    }
    
    return entropy
  }

  private async extractStrings(filePath: string): Promise<any> {
    try {
      const { stdout } = await execAsync(`strings "${filePath}"`)
      const allStrings = stdout.split('\n').filter(s => s.length > 3)
      
      const urlRegex = /https?:\/\/[^\s]+/gi
      const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/g
      const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
      const domainRegex = /\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}\b/g
      
      return {
        ascii: allStrings.slice(0, 100), // Limit to first 100 strings
        unicode: [],
        urls: [...new Set(stdout.match(urlRegex) || [])],
        ips: [...new Set(stdout.match(ipRegex) || [])],
        emails: [...new Set(stdout.match(emailRegex) || [])],
        domains: [...new Set(stdout.match(domainRegex) || [])]
      }
    } catch (error) {
      return { ascii: [], unicode: [], urls: [], ips: [], emails: [], domains: [] }
    }
  }

  private async scanWithYara(filePath: string): Promise<any[]> {
    try {
      const rulesPath = path.join(this.yaraRulesPath, '*.yar')
      const { stdout } = await execAsync(`yara "${rulesPath}" "${filePath}"`)
      
      return stdout.split('\n')
        .filter(line => line.trim())
        .map(line => {
          const [rule, file] = line.split(' ')
          return {
            rule,
            description: `YARA rule ${rule} matched`,
            severity: 'medium' as const
          }
        })
    } catch (error) {
      return []
    }
  }

  private async scanWithClamAV(filePath: string): Promise<any[]> {
    try {
      const { stdout } = await execAsync(`clamscan "${filePath}"`)
      
      if (stdout.includes('FOUND')) {
        return [{
          signature: 'ClamAV Detection',
          description: stdout.trim()
        }]
      }
      
      return []
    } catch (error) {
      return []
    }
  }

  private generateVerdict(yaraResults: any[], clamavResults: any[], strings: any): any {
    const isMalicious = yaraResults.length > 0 || clamavResults.length > 0
    const threatTypes: string[] = []
    const reasons: string[] = []
    
    if (yaraResults.length > 0) {
      threatTypes.push('YARA Detection')
      reasons.push(`${yaraResults.length} YARA rules matched`)
    }
    
    if (clamavResults.length > 0) {
      threatTypes.push('Antivirus Detection')
      reasons.push('ClamAV signature detected')
    }
    
    // Check for suspicious strings
    const suspiciousKeywords = ['keylogger', 'backdoor', 'rootkit', 'trojan', 'virus']
    const foundKeywords = strings.ascii.filter((s: string) => 
      suspiciousKeywords.some(keyword => s.toLowerCase().includes(keyword))
    )
    
    if (foundKeywords.length > 0) {
      threatTypes.push('Suspicious Strings')
      reasons.push(`Suspicious keywords found: ${foundKeywords.slice(0, 3).join(', ')}`)
    }
    
    let confidence = 0
    if (yaraResults.length > 0) confidence += 0.4
    if (clamavResults.length > 0) confidence += 0.5
    if (foundKeywords.length > 0) confidence += 0.2
    
    return {
      isMalicious,
      confidence: Math.min(confidence, 1.0),
      threatType: threatTypes,
      severity: isMalicious ? (confidence > 0.7 ? 'high' : 'medium') : 'low',
      reasons
    }
  }

  private enhanceVerdict(staticVerdict: any, dynamicBehavior: any): any {
    // Enhance verdict with dynamic analysis results
    const enhancedReasons = [...staticVerdict.reasons]
    
    if (dynamicBehavior.behavior.files.created.length > 0) {
      enhancedReasons.push(`Created ${dynamicBehavior.behavior.files.created.length} files`)
    }
    
    if (dynamicBehavior.behavior.registry.created.length > 0) {
      enhancedReasons.push(`Modified registry`)
    }
    
    if (dynamicBehavior.behavior.network.length > 0) {
      enhancedReasons.push(`Network activity detected`)
    }
    
    return {
      ...staticVerdict,
      reasons: enhancedReasons,
      confidence: Math.min(staticVerdict.confidence + 0.1, 1.0)
    }
  }

  private async saveResults(sessionId: string, results: SandboxAnalysis) {
    try {
      await db.query(`
        UPDATE malware_analyses 
        SET is_malicious = ?, threat_type = ?, confidence_score = ?, severity = ?, results = ?, completed_at = NOW()
        WHERE id = ?
      `, [
        results.verdict.isMalicious,
        JSON.stringify(results.verdict.threatType),
        results.verdict.confidence,
        results.verdict.severity,
        JSON.stringify(results),
        sessionId
      ])
    } catch (error) {
      console.error('Error saving analysis results:', error)
    }
  }

  private async updateSessionStatus(sessionId: string, status: string) {
    try {
      const session = this.sessions.get(sessionId)
      await db.query(`
        UPDATE malware_analyses 
        SET status = ?, progress = ?, updated_at = NOW()
        WHERE id = ?
      `, [status, session?.progress || 0, sessionId])
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  async getSession(sessionId: string): Promise<AnalysisSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async getAllSessions(): Promise<AnalysisSession[]> {
    return Array.from(this.sessions.values())
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const malwareAnalyzer = new MalwareAnalyzer()
