import axios from 'axios'
import * as cheerio from 'cheerio'
import { URL } from 'url'
import { db } from '../database'

export interface CrawlTarget {
  id: string
  url: string
  depth: number
  maxPages: number
  respectRobots: boolean
  userAgent: string
  delay: number
  filters: string[]
  extractors: string[]
}

export interface CrawlResult {
  url: string
  title: string
  content: string
  links: string[]
  emails: string[]
  phones: string[]
  technologies: string[]
  headers: Record<string, string>
  statusCode: number
  responseTime: number
  timestamp: Date
  metadata: Record<string, any>
}

export interface CrawlSession {
  id: string
  target: CrawlTarget
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused'
  startTime: Date
  endTime?: Date
  pagesFound: number
  pagesCrawled: number
  errors: string[]
  results: CrawlResult[]
}

export class CrawlingEngine {
  private sessions: Map<string, CrawlSession> = new Map()
  private queue: string[] = []
  private visited: Set<string> = new Set()
  private running: boolean = false

  constructor() {
    this.initializeEngine()
  }

  private async initializeEngine() {
    console.log('🕷️ Crawling Engine initialized')
    
    // Create crawl_sessions table if not exists
    await this.createTables()
    
    // Resume any pending sessions
    await this.resumePendingSessions()
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS crawl_sessions (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          target_url VARCHAR(2048) NOT NULL,
          config JSON,
          status ENUM('pending', 'running', 'completed', 'failed', 'paused') DEFAULT 'pending',
          pages_found INT DEFAULT 0,
          pages_crawled INT DEFAULT 0,
          errors JSON,
          results JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS crawl_results (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          url VARCHAR(2048) NOT NULL,
          title TEXT,
          content LONGTEXT,
          links JSON,
          emails JSON,
          phones JSON,
          technologies JSON,
          headers JSON,
          status_code INT,
          response_time INT,
          metadata JSON,
          crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES crawl_sessions(id) ON DELETE CASCADE
        )
      `)

      console.log('✅ Crawling Engine tables created')
    } catch (error) {
      console.error('❌ Error creating crawling tables:', error)
    }
  }

  async startCrawl(target: CrawlTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()
    
    const session: CrawlSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      pagesFound: 0,
      pagesCrawled: 0,
      errors: [],
      results: []
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO crawl_sessions (id, user_id, target_url, config, status, created_at)
      VALUES (?, ?, ?, ?, 'pending', NOW())
    `, [sessionId, userId || null, target.url, JSON.stringify(target)])

    // Start crawling
    this.crawlSession(sessionId)

    return sessionId
  }

  private async crawlSession(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🕷️ Starting crawl session ${sessionId} for ${session.target.url}`)

      // Initialize crawl queue
      this.queue = [session.target.url]
      this.visited.clear()

      let currentDepth = 0
      let pagesCrawled = 0

      while (this.queue.length > 0 && pagesCrawled < session.target.maxPages && currentDepth <= session.target.depth) {
        const url = this.queue.shift()!
        
        if (this.visited.has(url)) continue
        this.visited.add(url)

        try {
          // Respect robots.txt if enabled
          if (session.target.respectRobots && !(await this.checkRobotsTxt(url))) {
            console.log(`🚫 Robots.txt disallows crawling: ${url}`)
            continue
          }

          // Add delay between requests
          if (session.target.delay > 0) {
            await this.sleep(session.target.delay)
          }

          const result = await this.crawlPage(url, session.target)
          
          if (result) {
            session.results.push(result)
            pagesCrawled++

            // Save result to database
            await this.saveResult(sessionId, result)

            // Extract new URLs for next depth level
            if (currentDepth < session.target.depth) {
              const newUrls = this.extractUrls(result.links, session.target.url)
              this.queue.push(...newUrls.filter(u => !this.visited.has(u)))
            }

            console.log(`✅ Crawled: ${url} (${pagesCrawled}/${session.target.maxPages})`)
          }

        } catch (error) {
          const errorMsg = `Error crawling ${url}: ${error}`
          session.errors.push(errorMsg)
          console.error(`❌ ${errorMsg}`)
        }
      }

      session.status = 'completed'
      session.endTime = new Date()
      session.pagesCrawled = pagesCrawled
      session.pagesFound = this.visited.size

      await this.updateSessionStatus(sessionId, 'completed')
      console.log(`🎉 Crawl session ${sessionId} completed: ${pagesCrawled} pages crawled`)

    } catch (error) {
      session.status = 'failed'
      session.errors.push(`Session failed: ${error}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Crawl session ${sessionId} failed:`, error)
    }
  }

  private async crawlPage(url: string, target: CrawlTarget): Promise<CrawlResult | null> {
    const startTime = Date.now()

    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': target.userAgent || 'KodeXGuard-Crawler/1.0',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive'
        },
        timeout: 30000,
        maxRedirects: 5,
        validateStatus: () => true // Accept all status codes
      })

      const responseTime = Date.now() - startTime
      const $ = cheerio.load(response.data)

      // Extract data
      const title = $('title').text().trim()
      const content = this.extractContent($)
      const links = this.extractLinks($, url)
      const emails = this.extractEmails(response.data)
      const phones = this.extractPhones(response.data)
      const technologies = this.detectTechnologies($, response.headers)

      const result: CrawlResult = {
        url,
        title,
        content,
        links,
        emails,
        phones,
        technologies,
        headers: response.headers as Record<string, string>,
        statusCode: response.status,
        responseTime,
        timestamp: new Date(),
        metadata: {
          contentLength: response.data.length,
          contentType: response.headers['content-type'],
          server: response.headers['server'],
          lastModified: response.headers['last-modified']
        }
      }

      return result

    } catch (error) {
      console.error(`Error crawling page ${url}:`, error)
      return null
    }
  }

  private extractContent($: cheerio.CheerioAPI): string {
    // Remove script and style elements
    $('script, style, nav, header, footer, aside').remove()
    
    // Extract main content
    const content = $('main, article, .content, #content, .post, .entry')
      .first()
      .text()
      .trim()

    return content || $('body').text().trim().substring(0, 5000)
  }

  private extractLinks($: cheerio.CheerioAPI, baseUrl: string): string[] {
    const links: string[] = []
    
    $('a[href]').each((_, element) => {
      const href = $(element).attr('href')
      if (href) {
        try {
          const absoluteUrl = new URL(href, baseUrl).toString()
          links.push(absoluteUrl)
        } catch (error) {
          // Invalid URL, skip
        }
      }
    })

    return [...new Set(links)] // Remove duplicates
  }

  private extractEmails(content: string): string[] {
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = content.match(emailRegex) || []
    return [...new Set(emails)]
  }

  private extractPhones(content: string): string[] {
    const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g
    const phones = content.match(phoneRegex) || []
    return [...new Set(phones)]
  }

  private detectTechnologies($: cheerio.CheerioAPI, headers: any): string[] {
    const technologies: string[] = []

    // Detect from meta tags
    $('meta[name="generator"]').each((_, element) => {
      const generator = $(element).attr('content')
      if (generator) technologies.push(generator)
    })

    // Detect from headers
    if (headers['server']) technologies.push(headers['server'])
    if (headers['x-powered-by']) technologies.push(headers['x-powered-by'])

    // Detect from scripts and links
    $('script[src]').each((_, element) => {
      const src = $(element).attr('src')
      if (src?.includes('jquery')) technologies.push('jQuery')
      if (src?.includes('bootstrap')) technologies.push('Bootstrap')
      if (src?.includes('react')) technologies.push('React')
      if (src?.includes('vue')) technologies.push('Vue.js')
      if (src?.includes('angular')) technologies.push('Angular')
    })

    return [...new Set(technologies)]
  }

  private extractUrls(links: string[], baseUrl: string): string[] {
    const baseDomain = new URL(baseUrl).hostname
    
    return links.filter(link => {
      try {
        const url = new URL(link)
        return url.hostname === baseDomain && 
               url.protocol.startsWith('http') &&
               !link.includes('#') &&
               !link.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|tar|gz)$/i)
      } catch {
        return false
      }
    })
  }

  private async checkRobotsTxt(url: string): Promise<boolean> {
    try {
      const robotsUrl = new URL('/robots.txt', url).toString()
      const response = await axios.get(robotsUrl, { timeout: 5000 })
      
      // Simple robots.txt parsing (basic implementation)
      const robotsTxt = response.data.toLowerCase()
      return !robotsTxt.includes('disallow: /')
    } catch {
      return true // If robots.txt not found, allow crawling
    }
  }

  private async saveResult(sessionId: string, result: CrawlResult) {
    try {
      await db.query(`
        INSERT INTO crawl_results (id, session_id, url, title, content, links, emails, phones, technologies, headers, status_code, response_time, metadata, crawled_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        this.generateId(),
        sessionId,
        result.url,
        result.title,
        result.content,
        JSON.stringify(result.links),
        JSON.stringify(result.emails),
        JSON.stringify(result.phones),
        JSON.stringify(result.technologies),
        JSON.stringify(result.headers),
        result.statusCode,
        result.responseTime,
        JSON.stringify(result.metadata)
      ])
    } catch (error) {
      console.error('Error saving crawl result:', error)
    }
  }

  private async updateSessionStatus(sessionId: string, status: string) {
    try {
      await db.query(`
        UPDATE crawl_sessions 
        SET status = ?, updated_at = NOW(), completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [status, sessionId])
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  private async resumePendingSessions() {
    try {
      const sessions = await db.query(`
        SELECT * FROM crawl_sessions 
        WHERE status IN ('pending', 'running')
        ORDER BY created_at ASC
      `)

      for (const session of sessions) {
        console.log(`🔄 Resuming crawl session ${session.id}`)
        // Resume crawling logic here
      }
    } catch (error) {
      console.error('Error resuming sessions:', error)
    }
  }

  async getSession(sessionId: string): Promise<CrawlSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async getAllSessions(): Promise<CrawlSession[]> {
    return Array.from(this.sessions.values())
  }

  async pauseSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (session && session.status === 'running') {
      session.status = 'paused'
      await this.updateSessionStatus(sessionId, 'paused')
      return true
    }
    return false
  }

  async stopSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.status = 'failed'
      session.endTime = new Date()
      await this.updateSessionStatus(sessionId, 'failed')
      return true
    }
    return false
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const crawlingEngine = new CrawlingEngine()
