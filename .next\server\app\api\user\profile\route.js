/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/profile/route";
exports.ids = ["app/api/user/profile/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/profile/route.ts */ \"(rsc)/./app/api/user/profile/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/profile/route\",\n        pathname: \"/api/user/profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/profile/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\user\\\\profile\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/profile/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/profile/route.ts":
/*!***************************************!*\
  !*** ./app/api/user/profile/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/middlewares/auth */ \"(rsc)/./middlewares/auth.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\n\n// Validation schema for profile update\nconst updateProfileSchema = zod__WEBPACK_IMPORTED_MODULE_3__.object({\n    fullName: zod__WEBPACK_IMPORTED_MODULE_3__.string().min(2).max(255).optional(),\n    bio: zod__WEBPACK_IMPORTED_MODULE_3__.string().max(1000).optional(),\n    avatar: zod__WEBPACK_IMPORTED_MODULE_3__.string().url().optional().or(zod__WEBPACK_IMPORTED_MODULE_3__.literal(\"\"))\n});\n// GET /api/user/profile - Get current user profile\nasync function getProfileHandler(req) {\n    try {\n        if (!req.userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User ID not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Get user profile\n        const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.getUserById(req.userId);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Return user profile\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: {\n                    id: user.id,\n                    username: user.username,\n                    email: user.email,\n                    fullName: user.fullName,\n                    avatar: user.avatar,\n                    bio: user.bio,\n                    role: user.role,\n                    plan: user.plan,\n                    planExpiry: user.planExpiry,\n                    emailVerified: user.emailVerified,\n                    createdAt: user.createdAt,\n                    lastLogin: user.lastLogin,\n                    stats: user.stats\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Get profile API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get profile\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/user/profile - Update user profile\nasync function updateProfileHandler(req) {\n    try {\n        if (!req.userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User ID not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Parse request body\n        const body = await req.json();\n        // Validate input\n        const validation = updateProfileSchema.safeParse(body);\n        if (!validation.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation failed\",\n                details: validation.error.errors.map((err)=>({\n                        field: err.path.join(\".\"),\n                        message: err.message\n                    }))\n            }, {\n                status: 400\n            });\n        }\n        // Update profile\n        const success = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.updateProfile(req.userId, validation.data);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update profile\"\n            }, {\n                status: 500\n            });\n        }\n        // Get updated user profile\n        const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.getUserById(req.userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Profile updated successfully\",\n            data: {\n                user: {\n                    id: user?.id,\n                    username: user?.username,\n                    email: user?.email,\n                    fullName: user?.fullName,\n                    avatar: user?.avatar,\n                    bio: user?.bio,\n                    role: user?.role,\n                    plan: user?.plan,\n                    planExpiry: user?.planExpiry,\n                    emailVerified: user?.emailVerified,\n                    createdAt: user?.createdAt,\n                    lastLogin: user?.lastLogin,\n                    stats: user?.stats\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Update profile API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update profile\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Apply middlewares\nconst GET = (0,_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.combineMiddlewares)(_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withCors, _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withErrorHandling, _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(getProfileHandler);\nconst PUT = (0,_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.combineMiddlewares)(_middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withCors, _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withErrorHandling, _middlewares_auth__WEBPACK_IMPORTED_MODULE_2__.withAuth)(updateProfileHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/profile/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.ts\");\n/* harmony import */ var _types_user__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/user */ \"(rsc)/./types/user.ts\");\n\n\n\n\n// JWT Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nconst REFRESH_TOKEN_EXPIRES_IN = \"30d\";\n// Password configuration\nconst BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || \"12\");\nclass AuthService {\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            userId: user.id,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            type: \"refresh\"\n        }, JWT_SECRET, {\n            expiresIn: REFRESH_TOKEN_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        // Calculate expiration time in seconds\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().decode(accessToken);\n        const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token) {\n        try {\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n                issuer: \"kodexguard\",\n                audience: \"kodexguard-users\"\n            });\n            return decoded;\n        } catch (error) {\n            console.error(\"Token verification failed:\", error);\n            return null;\n        }\n    }\n    // Hash password\n    static async hashPassword(password) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hash);\n    }\n    // Register new user\n    static async register(userData) {\n        try {\n            // Check if user already exists\n            const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.findOne(\"SELECT id FROM users WHERE email = ? OR username = ?\", [\n                userData.email,\n                userData.username\n            ]);\n            if (existingUser) {\n                return {\n                    error: \"User with this email or username already exists\"\n                };\n            }\n            // Hash password\n            const passwordHash = await this.hashPassword(userData.password);\n            // Generate user ID\n            const userId = this.generateUUID();\n            // Create user\n            await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.insert(\"users\", {\n                id: userId,\n                username: userData.username,\n                email: userData.email,\n                password_hash: passwordHash,\n                full_name: userData.fullName,\n                role: _types_user__WEBPACK_IMPORTED_MODULE_3__.UserRole.USER,\n                plan: _types_user__WEBPACK_IMPORTED_MODULE_3__.UserPlan.FREE,\n                is_active: true,\n                email_verified: false,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            // Create user stats\n            await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.insert(\"user_stats\", {\n                id: this.generateUUID(),\n                user_id: userId,\n                total_scans: 0,\n                vulnerabilities_found: 0,\n                files_analyzed: 0,\n                osint_queries: 0,\n                api_calls: 0,\n                score: 0,\n                rank_position: 0,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            // Get created user\n            const user = await this.getUserById(userId);\n            if (!user) {\n                return {\n                    error: \"Failed to create user\"\n                };\n            }\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            // Store refresh token in Redis\n            await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.set(`refresh_token:${userId}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                error: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const userRecord = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.findOne(\"SELECT * FROM users WHERE email = ? AND is_active = true\", [\n                email\n            ]);\n            if (!userRecord) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Verify password\n            const isValidPassword = await this.verifyPassword(password, userRecord.password_hash);\n            if (!isValidPassword) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Update last login\n            await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.update(\"users\", {\n                last_login: new Date()\n            }, \"id = ?\", [\n                userRecord.id\n            ]);\n            // Get user with stats\n            const user = await this.getUserById(userRecord.id);\n            if (!user) {\n                return {\n                    error: \"User not found\"\n                };\n            }\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            // Store refresh token in Redis\n            await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.set(`refresh_token:${user.id}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                error: \"Login failed\"\n            };\n        }\n    }\n    // Refresh access token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(refreshToken, JWT_SECRET);\n            if (decoded.type !== \"refresh\") {\n                return {\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Check if refresh token exists in Redis\n            const storedToken = await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.get(`refresh_token:${decoded.userId}`);\n            if (storedToken !== refreshToken) {\n                return {\n                    error: \"Invalid refresh token\"\n                };\n            }\n            // Get user\n            const user = await this.getUserById(decoded.userId);\n            if (!user) {\n                return {\n                    error: \"User not found\"\n                };\n            }\n            // Generate new tokens\n            const tokens = this.generateTokens(user);\n            // Update refresh token in Redis\n            await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.set(`refresh_token:${user.id}`, tokens.refreshToken, 30 * 24 * 60 * 60 // 30 days\n            );\n            return {\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Refresh token error:\", error);\n            return {\n                error: \"Invalid refresh token\"\n            };\n        }\n    }\n    // Logout user\n    static async logout(userId) {\n        try {\n            // Remove refresh token from Redis\n            await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.del(`refresh_token:${userId}`);\n            return true;\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return false;\n        }\n    }\n    // Get user by ID with stats\n    static async getUserById(userId) {\n        try {\n            const userRecord = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.findOne(`\n        SELECT \n          u.*,\n          us.total_scans,\n          us.vulnerabilities_found,\n          us.files_analyzed,\n          us.osint_queries,\n          us.api_calls,\n          us.score,\n          us.rank_position\n        FROM users u\n        LEFT JOIN user_stats us ON u.id = us.user_id\n        WHERE u.id = ? AND u.is_active = true\n      `, [\n                userId\n            ]);\n            if (!userRecord) {\n                return null;\n            }\n            // Get user API keys\n            const apiKeys = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.query(\"SELECT * FROM api_keys WHERE user_id = ? AND is_active = true\", [\n                userId\n            ]);\n            return {\n                id: userRecord.id,\n                username: userRecord.username,\n                email: userRecord.email,\n                fullName: userRecord.full_name,\n                avatar: userRecord.avatar,\n                bio: userRecord.bio,\n                role: userRecord.role,\n                plan: userRecord.plan,\n                planExpiry: userRecord.plan_expiry ? new Date(userRecord.plan_expiry) : undefined,\n                isActive: userRecord.is_active,\n                emailVerified: userRecord.email_verified,\n                createdAt: new Date(userRecord.created_at),\n                updatedAt: new Date(userRecord.updated_at),\n                lastLogin: userRecord.last_login ? new Date(userRecord.last_login) : undefined,\n                apiKeys: apiKeys || [],\n                stats: {\n                    totalScans: userRecord.total_scans || 0,\n                    vulnerabilitiesFound: userRecord.vulnerabilities_found || 0,\n                    filesAnalyzed: userRecord.files_analyzed || 0,\n                    osintQueries: userRecord.osint_queries || 0,\n                    apiCalls: userRecord.api_calls || 0,\n                    score: userRecord.score || 0,\n                    rank: userRecord.rank_position || 0\n                }\n            };\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Generate API Key\n    static async generateApiKey(userId, name, permissions = []) {\n        try {\n            // Generate API key\n            const keyId = this.generateUUID();\n            const apiKey = `kxg_${this.generateRandomString(32)}`;\n            const keyHash = await this.hashPassword(apiKey);\n            const keyPrefix = apiKey.substring(0, 12);\n            // Insert API key\n            await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.insert(\"api_keys\", {\n                id: keyId,\n                user_id: userId,\n                name,\n                key_hash: keyHash,\n                key_prefix: keyPrefix,\n                permissions: JSON.stringify(permissions),\n                is_active: true,\n                usage_count: 0,\n                rate_limit: 100,\n                created_at: new Date(),\n                updated_at: new Date()\n            });\n            return {\n                apiKey,\n                keyId\n            };\n        } catch (error) {\n            console.error(\"Generate API key error:\", error);\n            return {\n                error: \"Failed to generate API key\"\n            };\n        }\n    }\n    // Verify API Key\n    static async verifyApiKey(apiKey) {\n        try {\n            const keyPrefix = apiKey.substring(0, 12);\n            // Find API key by prefix\n            const keyRecord = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.findOne(\"SELECT * FROM api_keys WHERE key_prefix = ? AND is_active = true\", [\n                keyPrefix\n            ]);\n            if (!keyRecord) {\n                return null;\n            }\n            // Verify API key hash\n            const isValid = await this.verifyPassword(apiKey, keyRecord.key_hash);\n            if (!isValid) {\n                return null;\n            }\n            // Update usage count and last used\n            await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.update(\"api_keys\", {\n                usage_count: keyRecord.usage_count + 1,\n                last_used: new Date(),\n                updated_at: new Date()\n            }, \"id = ?\", [\n                keyRecord.id\n            ]);\n            // Get user\n            const user = await this.getUserById(keyRecord.user_id);\n            if (!user) {\n                return null;\n            }\n            return {\n                user,\n                keyId: keyRecord.id\n            };\n        } catch (error) {\n            console.error(\"Verify API key error:\", error);\n            return null;\n        }\n    }\n    // Update user profile\n    static async updateProfile(userId, profileData) {\n        try {\n            const updateData = {\n                updated_at: new Date()\n            };\n            if (profileData.fullName) updateData.full_name = profileData.fullName;\n            if (profileData.bio !== undefined) updateData.bio = profileData.bio;\n            if (profileData.avatar !== undefined) updateData.avatar = profileData.avatar;\n            return await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.update(\"users\", updateData, \"id = ?\", [\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            return false;\n        }\n    }\n    // Change password\n    static async changePassword(userId, currentPassword, newPassword) {\n        try {\n            // Get current user\n            const userRecord = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.findOne(\"SELECT password_hash FROM users WHERE id = ?\", [\n                userId\n            ]);\n            if (!userRecord) {\n                return {\n                    success: false,\n                    error: \"User not found\"\n                };\n            }\n            // Verify current password\n            const isValidPassword = await this.verifyPassword(currentPassword, userRecord.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    error: \"Current password is incorrect\"\n                };\n            }\n            // Hash new password\n            const newPasswordHash = await this.hashPassword(newPassword);\n            // Update password\n            const updated = await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.update(\"users\", {\n                password_hash: newPasswordHash,\n                updated_at: new Date()\n            }, \"id = ?\", [\n                userId\n            ]);\n            if (updated) {\n                // Invalidate all refresh tokens for this user\n                await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.del(`refresh_token:${userId}`);\n                return {\n                    success: true\n                };\n            }\n            return {\n                success: false,\n                error: \"Failed to update password\"\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                error: \"Failed to change password\"\n            };\n        }\n    }\n    // Delete API key\n    static async deleteApiKey(userId, keyId) {\n        try {\n            return await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.update(\"api_keys\", {\n                is_active: false,\n                updated_at: new Date()\n            }, \"id = ? AND user_id = ?\", [\n                keyId,\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Delete API key error:\", error);\n            return false;\n        }\n    }\n    // Get user API keys\n    static async getUserApiKeys(userId) {\n        try {\n            return await _database__WEBPACK_IMPORTED_MODULE_2__.DatabaseUtils.query(\"SELECT id, name, key_prefix, permissions, is_active, usage_count, rate_limit, last_used, created_at FROM api_keys WHERE user_id = ? ORDER BY created_at DESC\", [\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Get API keys error:\", error);\n            return [];\n        }\n    }\n    // Check rate limit\n    static async checkRateLimit(userId, keyId) {\n        try {\n            const key = keyId ? `rate_limit:api:${keyId}` : `rate_limit:user:${userId}`;\n            const window = 15 * 60 // 15 minutes\n            ;\n            const limit = 100 // requests per window\n            ;\n            const current = await _database__WEBPACK_IMPORTED_MODULE_2__.RedisUtils.incr(key, window);\n            const remaining = Math.max(0, limit - current);\n            const resetTime = Math.floor(Date.now() / 1000) + window;\n            return {\n                allowed: current <= limit,\n                remaining,\n                resetTime\n            };\n        } catch (error) {\n            console.error(\"Rate limit check error:\", error);\n            return {\n                allowed: true,\n                remaining: 100,\n                resetTime: 0\n            };\n        }\n    }\n    // Generate random string\n    static generateRandomString(length) {\n        const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n        let result = \"\";\n        for(let i = 0; i < length; i++){\n            result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n    }\n    // Generate UUID\n    static generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c == \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseUtils: () => (/* binding */ DatabaseUtils),\n/* harmony export */   RedisUtils: () => (/* binding */ RedisUtils),\n/* harmony export */   closeDatabaseConnections: () => (/* binding */ closeDatabaseConnections),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   elasticsearch: () => (/* binding */ elasticsearch),\n/* harmony export */   initRedis: () => (/* binding */ initRedis),\n/* harmony export */   initializeDatabases: () => (/* binding */ initializeDatabases),\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection),\n/* harmony export */   testElasticsearchConnection: () => (/* binding */ testElasticsearchConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @elastic/elasticsearch */ \"(rsc)/./node_modules/@elastic/elasticsearch/index.js\");\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// MySQL Database Configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"kodexguard\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    acquireTimeout: 60000,\n    timeout: 60000,\n    reconnect: true,\n    connectionLimit: 10,\n    queueLimit: 0\n};\n// Create MySQL connection pool\nconst db = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n// Test database connection\nasync function testDatabaseConnection() {\n    try {\n        const connection = await db.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ MySQL database connected successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ MySQL database connection failed:\", error);\n        return false;\n    }\n}\n// Redis Configuration\nconst redisConfig = {\n    url: process.env.REDIS_URL || \"redis://localhost:6379\",\n    password: process.env.REDIS_PASSWORD || undefined,\n    socket: {\n        reconnectStrategy: (retries)=>Math.min(retries * 50, 500)\n    }\n};\n// Create Redis client\nconst redis = (0,redis__WEBPACK_IMPORTED_MODULE_1__.createClient)(redisConfig);\n// Redis connection handlers\nredis.on(\"error\", (err)=>{\n    console.error(\"❌ Redis Client Error:\", err);\n});\nredis.on(\"connect\", ()=>{\n    console.log(\"✅ Redis connected successfully\");\n});\nredis.on(\"reconnecting\", ()=>{\n    console.log(\"\\uD83D\\uDD04 Redis reconnecting...\");\n});\nredis.on(\"ready\", ()=>{\n    console.log(\"✅ Redis ready for operations\");\n});\n// Initialize Redis connection\nasync function initRedis() {\n    try {\n        if (!redis.isOpen) {\n            await redis.connect();\n        }\n        return true;\n    } catch (error) {\n        console.error(\"❌ Redis connection failed:\", error);\n        return false;\n    }\n}\n// Elasticsearch Configuration\nconst elasticsearchConfig = {\n    node: process.env.ELASTICSEARCH_URL || \"http://localhost:9200\",\n    auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {\n        username: process.env.ELASTICSEARCH_USERNAME,\n        password: process.env.ELASTICSEARCH_PASSWORD\n    } : undefined,\n    requestTimeout: 30000,\n    pingTimeout: 3000,\n    maxRetries: 3\n};\n// Create Elasticsearch client\nconst elasticsearch = new _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.Client(elasticsearchConfig);\n// Test Elasticsearch connection\nasync function testElasticsearchConnection() {\n    try {\n        const health = await elasticsearch.cluster.health();\n        console.log(\"✅ Elasticsearch connected successfully:\", health.cluster_name);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Elasticsearch connection failed:\", error);\n        return false;\n    }\n}\n// Initialize all database connections\nasync function initializeDatabases() {\n    console.log(\"\\uD83D\\uDE80 Initializing database connections...\");\n    const results = {\n        mysql: await testDatabaseConnection(),\n        redis: await initRedis(),\n        elasticsearch: await testElasticsearchConnection()\n    };\n    const allConnected = Object.values(results).every(Boolean);\n    if (allConnected) {\n        console.log(\"✅ All databases connected successfully\");\n    } else {\n        console.warn(\"⚠️ Some database connections failed:\", results);\n    }\n    return results;\n}\n// Graceful shutdown\nasync function closeDatabaseConnections() {\n    console.log(\"\\uD83D\\uDD04 Closing database connections...\");\n    try {\n        await db.end();\n        console.log(\"✅ MySQL connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing MySQL connection:\", error);\n    }\n    try {\n        if (redis.isOpen) {\n            await redis.quit();\n        }\n        console.log(\"✅ Redis connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Redis connection:\", error);\n    }\n    try {\n        await elasticsearch.close();\n        console.log(\"✅ Elasticsearch connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Elasticsearch connection:\", error);\n    }\n}\n// Database utility functions\nclass DatabaseUtils {\n    // Execute query with error handling\n    static async query(sql, params) {\n        try {\n            const [rows] = await db.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            throw error;\n        }\n    }\n    // Get single record\n    static async findOne(sql, params) {\n        const rows = await this.query(sql, params);\n        return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;\n    }\n    // Get multiple records with pagination\n    static async findMany(sql, params, page = 1, limit = 10) {\n        const offset = (page - 1) * limit;\n        // Get total count\n        const countSql = sql.replace(/SELECT .+ FROM/, \"SELECT COUNT(*) as total FROM\");\n        const countResult = await this.findOne(countSql, params);\n        const total = countResult?.total || 0;\n        // Get paginated data\n        const dataSql = `${sql} LIMIT ${limit} OFFSET ${offset}`;\n        const data = await this.query(dataSql, params);\n        return {\n            data: Array.isArray(data) ? data : [],\n            total,\n            page,\n            limit\n        };\n    }\n    // Insert record\n    static async insert(table, data) {\n        const fields = Object.keys(data).join(\", \");\n        const placeholders = Object.keys(data).map(()=>\"?\").join(\", \");\n        const values = Object.values(data);\n        const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`;\n        const result = await this.query(sql, values);\n        return result.insertId || data.id;\n    }\n    // Update record\n    static async update(table, data, where, whereParams) {\n        const setClause = Object.keys(data).map((key)=>`${key} = ?`).join(\", \");\n        const values = [\n            ...Object.values(data),\n            ...whereParams || []\n        ];\n        const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;\n        const result = await this.query(sql, values);\n        return result.affectedRows > 0;\n    }\n    // Delete record\n    static async delete(table, where, whereParams) {\n        const sql = `DELETE FROM ${table} WHERE ${where}`;\n        const result = await this.query(sql, whereParams);\n        return result.affectedRows > 0;\n    }\n}\n// Redis utility functions\nclass RedisUtils {\n    // Set value with expiration\n    static async set(key, value, expireInSeconds) {\n        try {\n            const serializedValue = typeof value === \"string\" ? value : JSON.stringify(value);\n            if (expireInSeconds) {\n                await redis.setEx(key, expireInSeconds, serializedValue);\n            } else {\n                await redis.set(key, serializedValue);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Redis set error:\", error);\n            return false;\n        }\n    }\n    // Get value\n    static async get(key) {\n        try {\n            const value = await redis.get(key);\n            if (!value) return null;\n            try {\n                return JSON.parse(value);\n            } catch  {\n                return value;\n            }\n        } catch (error) {\n            console.error(\"Redis get error:\", error);\n            return null;\n        }\n    }\n    // Delete key\n    static async del(key) {\n        try {\n            const result = await redis.del(key);\n            return result > 0;\n        } catch (error) {\n            console.error(\"Redis delete error:\", error);\n            return false;\n        }\n    }\n    // Check if key exists\n    static async exists(key) {\n        try {\n            const result = await redis.exists(key);\n            return result === 1;\n        } catch (error) {\n            console.error(\"Redis exists error:\", error);\n            return false;\n        }\n    }\n    // Increment counter\n    static async incr(key, expireInSeconds) {\n        try {\n            const result = await redis.incr(key);\n            if (expireInSeconds && result === 1) {\n                await redis.expire(key, expireInSeconds);\n            }\n            return result;\n        } catch (error) {\n            console.error(\"Redis increment error:\", error);\n            return 0;\n        }\n    }\n}\n// Export default database instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./middlewares/auth.ts":
/*!*****************************!*\
  !*** ./middlewares/auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combineMiddlewares: () => (/* binding */ combineMiddlewares),\n/* harmony export */   withAdmin: () => (/* binding */ withAdmin),\n/* harmony export */   withApiKey: () => (/* binding */ withApiKey),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthOrApiKey: () => (/* binding */ withAuthOrApiKey),\n/* harmony export */   withCors: () => (/* binding */ withCors),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling),\n/* harmony export */   withOptionalAuth: () => (/* binding */ withOptionalAuth),\n/* harmony export */   withRole: () => (/* binding */ withRole),\n/* harmony export */   withSuperAdmin: () => (/* binding */ withSuperAdmin)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _types_user__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/user */ \"(rsc)/./types/user.ts\");\n\n\n\n// Authentication middleware\nfunction withAuth(handler) {\n    return async (req)=>{\n        try {\n            // Get token from Authorization header\n            const authHeader = req.headers.get(\"authorization\");\n            const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n            if (!token) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Verify token\n            const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n            if (!payload) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid or expired token\"\n                }, {\n                    status: 401\n                });\n            }\n            // Add user info to request\n            req.user = payload;\n            req.userId = payload.userId;\n            // Check rate limit\n            const rateLimit = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.checkRateLimit(payload.userId);\n            if (!rateLimit.allowed) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded\",\n                    resetTime: rateLimit.resetTime\n                }, {\n                    status: 429\n                });\n            }\n            // Add rate limit headers\n            const response = await handler(req);\n            response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n            response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n            return response;\n        } catch (error) {\n            console.error(\"Auth middleware error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication failed\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// API Key authentication middleware\nfunction withApiKey(handler) {\n    return async (req)=>{\n        try {\n            // Get API key from header\n            const apiKey = req.headers.get(\"x-api-key\");\n            if (!apiKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"API key required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Verify API key\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n            if (!result) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid API key\"\n                }, {\n                    status: 401\n                });\n            }\n            // Add user info to request\n            req.user = {\n                userId: result.user.id,\n                email: result.user.email,\n                role: result.user.role,\n                plan: result.user.plan\n            };\n            req.userId = result.user.id;\n            // Check rate limit for API key\n            const rateLimit = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.checkRateLimit(result.user.id, result.keyId);\n            if (!rateLimit.allowed) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"API rate limit exceeded\",\n                    resetTime: rateLimit.resetTime\n                }, {\n                    status: 429\n                });\n            }\n            // Add rate limit headers\n            const response = await handler(req);\n            response.headers.set(\"X-RateLimit-Remaining\", rateLimit.remaining.toString());\n            response.headers.set(\"X-RateLimit-Reset\", rateLimit.resetTime.toString());\n            return response;\n        } catch (error) {\n            console.error(\"API key middleware error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API authentication failed\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Role-based authorization middleware\nfunction withRole(roles) {\n    return function(handler) {\n        return async (req)=>{\n            if (!req.user) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            if (!roles.includes(req.user.role)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Insufficient permissions\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(req);\n        };\n    };\n}\n// Admin only middleware\nfunction withAdmin(handler) {\n    return withRole([\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN,\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN\n    ])(handler);\n}\n// Super admin only middleware\nfunction withSuperAdmin(handler) {\n    return withRole([\n        _types_user__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN\n    ])(handler);\n}\n// Combined auth middleware (supports both JWT and API key)\nfunction withAuthOrApiKey(handler) {\n    return async (req)=>{\n        // Try JWT first\n        const authHeader = req.headers.get(\"authorization\");\n        const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n        if (token) {\n            const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n            if (payload) {\n                req.user = payload;\n                req.userId = payload.userId;\n                return handler(req);\n            }\n        }\n        // Try API key\n        const apiKey = req.headers.get(\"x-api-key\");\n        if (apiKey) {\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n            if (result) {\n                req.user = {\n                    userId: result.user.id,\n                    email: result.user.email,\n                    role: result.user.role,\n                    plan: result.user.plan\n                };\n                req.userId = result.user.id;\n                return handler(req);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Authentication required\"\n        }, {\n            status: 401\n        });\n    };\n}\n// Optional auth middleware (doesn't require authentication but adds user info if available)\nfunction withOptionalAuth(handler) {\n    return async (req)=>{\n        try {\n            // Try JWT first\n            const authHeader = req.headers.get(\"authorization\");\n            const token = authHeader?.startsWith(\"Bearer \") ? authHeader.substring(7) : null;\n            if (token) {\n                const payload = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyToken(token);\n                if (payload) {\n                    req.user = payload;\n                    req.userId = payload.userId;\n                }\n            } else {\n                // Try API key\n                const apiKey = req.headers.get(\"x-api-key\");\n                if (apiKey) {\n                    const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.verifyApiKey(apiKey);\n                    if (result) {\n                        req.user = {\n                            userId: result.user.id,\n                            email: result.user.email,\n                            role: result.user.role,\n                            plan: result.user.plan\n                        };\n                        req.userId = result.user.id;\n                    }\n                }\n            }\n            return handler(req);\n        } catch (error) {\n            console.error(\"Optional auth middleware error:\", error);\n            return handler(req);\n        }\n    };\n}\n// CORS middleware\nfunction withCors(handler) {\n    return async (req)=>{\n        // Handle preflight requests\n        if (req.method === \"OPTIONS\") {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                status: 200,\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, X-API-Key\",\n                    \"Access-Control-Max-Age\": \"86400\"\n                }\n            });\n        }\n        const response = await handler(req);\n        // Add CORS headers to response\n        response.headers.set(\"Access-Control-Allow-Origin\", \"*\");\n        response.headers.set(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n        response.headers.set(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization, X-API-Key\");\n        return response;\n    };\n}\n// Error handling middleware\nfunction withErrorHandling(handler) {\n    return async (req)=>{\n        try {\n            return await handler(req);\n        } catch (error) {\n            console.error(\"API Error:\", error);\n            if (error instanceof Error) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Internal server error\",\n                    message:  true ? error.message : 0\n                }, {\n                    status: 500\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Internal server error\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Combine multiple middlewares\nfunction combineMiddlewares(...middlewares) {\n    return (handler)=>{\n        return middlewares.reduceRight((acc, middleware)=>middleware(acc), handler);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./middlewares/auth.ts\n");

/***/ }),

/***/ "(rsc)/./types/user.ts":
/*!***********************!*\
  !*** ./types/user.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserPlan: () => (/* binding */ UserPlan),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"USER\"] = \"user\";\n})(UserRole || (UserRole = {}));\nvar UserPlan;\n(function(UserPlan) {\n    UserPlan[\"FREE\"] = \"free\";\n    UserPlan[\"STUDENT\"] = \"student\";\n    UserPlan[\"HOBBY\"] = \"hobby\";\n    UserPlan[\"BUGHUNTER\"] = \"bughunter\";\n    UserPlan[\"CYBERSECURITY\"] = \"cybersecurity\";\n})(UserPlan || (UserPlan = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi90eXBlcy91c2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztVQTJDWUE7Ozs7R0FBQUEsYUFBQUE7O1VBTUFDOzs7Ozs7R0FBQUEsYUFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vdHlwZXMvdXNlci50cz8xYTRhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcm5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIGZ1bGxOYW1lOiBzdHJpbmdcbiAgYXZhdGFyPzogc3RyaW5nXG4gIGJpbz86IHN0cmluZ1xuICByb2xlOiBVc2VyUm9sZVxuICBwbGFuOiBVc2VyUGxhblxuICBwbGFuRXhwaXJ5PzogRGF0ZVxuICBpc0FjdGl2ZTogYm9vbGVhblxuICBlbWFpbFZlcmlmaWVkOiBib29sZWFuXG4gIGNyZWF0ZWRBdDogRGF0ZVxuICB1cGRhdGVkQXQ6IERhdGVcbiAgbGFzdExvZ2luPzogRGF0ZVxuICBhcGlLZXlzOiBBcGlLZXlbXVxuICBzdGF0czogVXNlclN0YXRzXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclN0YXRzIHtcbiAgdG90YWxTY2FuczogbnVtYmVyXG4gIHZ1bG5lcmFiaWxpdGllc0ZvdW5kOiBudW1iZXJcbiAgZmlsZXNBbmFseXplZDogbnVtYmVyXG4gIG9zaW50UXVlcmllczogbnVtYmVyXG4gIGFwaUNhbGxzOiBudW1iZXJcbiAgc2NvcmU6IG51bWJlclxuICByYW5rOiBudW1iZXJcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBcGlLZXkge1xuICBpZDogc3RyaW5nXG4gIHVzZXJJZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBrZXk6IHN0cmluZ1xuICBwZXJtaXNzaW9uczogc3RyaW5nW11cbiAgaXNBY3RpdmU6IGJvb2xlYW5cbiAgbGFzdFVzZWQ/OiBEYXRlXG4gIHVzYWdlQ291bnQ6IG51bWJlclxuICByYXRlTGltaXQ6IG51bWJlclxuICBjcmVhdGVkQXQ6IERhdGVcbiAgZXhwaXJlc0F0PzogRGF0ZVxufVxuXG5leHBvcnQgZW51bSBVc2VyUm9sZSB7XG4gIFNVUEVSX0FETUlOID0gJ3N1cGVyX2FkbWluJyxcbiAgQURNSU4gPSAnYWRtaW4nLFxuICBVU0VSID0gJ3VzZXInXG59XG5cbmV4cG9ydCBlbnVtIFVzZXJQbGFuIHtcbiAgRlJFRSA9ICdmcmVlJyxcbiAgU1RVREVOVCA9ICdzdHVkZW50JyxcbiAgSE9CQlkgPSAnaG9iYnknLFxuICBCVUdIVU5URVIgPSAnYnVnaHVudGVyJyxcbiAgQ1lCRVJTRUNVUklUWSA9ICdjeWJlcnNlY3VyaXR5J1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIExvZ2luUmVxdWVzdCB7XG4gIGVtYWlsOiBzdHJpbmdcbiAgcGFzc3dvcmQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFJlZ2lzdGVyUmVxdWVzdCB7XG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICBwYXNzd29yZDogc3RyaW5nXG4gIGZ1bGxOYW1lOiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoUmVzcG9uc2Uge1xuICB1c2VyOiBVc2VyXG4gIHRva2VuOiBzdHJpbmdcbiAgcmVmcmVzaFRva2VuOiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBVc2VyUHJvZmlsZSB7XG4gIGZ1bGxOYW1lOiBzdHJpbmdcbiAgYmlvPzogc3RyaW5nXG4gIGF2YXRhcj86IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENoYW5nZVBhc3N3b3JkUmVxdWVzdCB7XG4gIGN1cnJlbnRQYXNzd29yZDogc3RyaW5nXG4gIG5ld1Bhc3N3b3JkOiBzdHJpbmdcbn1cbiJdLCJuYW1lcyI6WyJVc2VyUm9sZSIsIlVzZXJQbGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./types/user.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@redis","vendor-chunks/apache-arrow","vendor-chunks/@elastic","vendor-chunks/undici","vendor-chunks/mysql2","vendor-chunks/semver","vendor-chunks/iconv-lite","vendor-chunks/generic-pool","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/flatbuffers","vendor-chunks/jws","vendor-chunks/debug","vendor-chunks/aws-ssl-profiles","vendor-chunks/yallist","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/tslib","vendor-chunks/secure-json-parse","vendor-chunks/lru-cache","vendor-chunks/long","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/safe-buffer","vendor-chunks/redis","vendor-chunks/named-placeholders","vendor-chunks/ms","vendor-chunks/lru.min","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/is-property","vendor-chunks/hpagent","vendor-chunks/has-flag","vendor-chunks/generate-function","vendor-chunks/denque","vendor-chunks/cluster-key-slot","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();