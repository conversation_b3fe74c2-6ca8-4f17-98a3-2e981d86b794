#!/usr/bin/env bun

import { readFileSync } from 'fs'
import { join } from 'path'
import { initializeDatabases, DatabaseUtils } from '../lib/database'
import { ElasticsearchUtils } from '../lib/elasticsearch'

async function setupDatabase() {
  console.log('🚀 Starting KodeXGuard database setup...')
  
  try {
    // Initialize database connections
    const connections = await initializeDatabases()
    
    if (!connections.mysql) {
      console.error('❌ MySQL connection failed. Please check your database configuration.')
      process.exit(1)
    }

    // Read and execute SQL schema
    console.log('📄 Reading database schema...')
    const schemaPath = join(process.cwd(), 'docs', 'database-schema.sql')
    const schema = readFileSync(schemaPath, 'utf8')
    
    // Split SQL statements and execute them
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    console.log(`📝 Executing ${statements.length} SQL statements...`)

    for (const statement of statements) {
      try {
        // Skip problematic statements
        if (statement.toLowerCase().includes('create database') ||
            statement.toLowerCase().includes('use ')) {
          continue
        }

        // Skip CREATE INDEX statements that might fail if tables don't exist yet
        if (statement.toLowerCase().startsWith('create index')) {
          try {
            await DatabaseUtils.query(statement)
          } catch (indexError: any) {
            console.warn(`⚠️ Index creation skipped: ${indexError.message}`)
          }
          continue
        }

        await DatabaseUtils.query(statement)

        // Add small delay to prevent connection issues
        await new Promise(resolve => setTimeout(resolve, 10))

      } catch (error: any) {
        // Ignore "table already exists" errors
        if (!error.message.includes('already exists') &&
            !error.message.includes('Duplicate entry')) {
          console.error('SQL Error:', error.message)
          console.error('Statement:', statement.substring(0, 100) + '...')
        }
      }
    }

    console.log('✅ MySQL schema setup completed')

    // Setup Elasticsearch indices
    if (connections.elasticsearch) {
      console.log('🔍 Setting up Elasticsearch indices...')
      await ElasticsearchUtils.initializeIndices()
    } else {
      console.warn('⚠️ Elasticsearch not available, skipping index setup')
    }

    // Insert default data
    console.log('📊 Inserting default data...')
    await insertDefaultData()

    console.log('🎉 Database setup completed successfully!')
    
  } catch (error) {
    console.error('❌ Database setup failed:', error)
    process.exit(1)
  }
}

async function insertDefaultData() {
  try {
    // Check if admin user exists
    const adminExists = await DatabaseUtils.findOne(
      'SELECT id FROM users WHERE role = ? LIMIT 1',
      ['super_admin']
    )

    if (!adminExists) {
      console.log('👤 Creating default super admin user...')
      
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('admin123', 12)
      
      const adminId = await DatabaseUtils.insert('users', {
        id: generateUUID(),
        username: 'admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'Super Administrator',
        role: 'super_admin',
        plan: 'cybersecurity',
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      })

      // Create user stats
      await DatabaseUtils.insert('user_stats', {
        id: generateUUID(),
        user_id: adminId,
        total_scans: 0,
        vulnerabilities_found: 0,
        files_analyzed: 0,
        osint_queries: 0,
        api_calls: 0,
        score: 0,
        rank_position: 1,
        created_at: new Date(),
        updated_at: new Date()
      })

      console.log('✅ Default admin user created')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: admin123')
      console.log('⚠️ Please change the default password after first login!')
    }

    // Insert default dork presets
    const dorkExists = await DatabaseUtils.findOne(
      'SELECT id FROM dork_presets LIMIT 1'
    )

    if (!dorkExists) {
      console.log('🔍 Inserting default dork presets...')
      
      const defaultDorks = [
        {
          name: 'Login Pages',
          description: 'Find login pages and admin panels',
          query: 'inurl:login OR inurl:admin OR inurl:signin OR intitle:"admin panel"',
          category: 'login_pages'
        },
        {
          name: 'Config Files',
          description: 'Find configuration files',
          query: 'filetype:conf OR filetype:config OR filetype:cfg OR filetype:ini',
          category: 'config_files'
        },
        {
          name: 'Database Files',
          description: 'Find database dumps and backups',
          query: 'filetype:sql OR filetype:db OR filetype:backup OR "database backup"',
          category: 'database_dumps'
        },
        {
          name: 'Directory Listing',
          description: 'Find open directories',
          query: 'intitle:"Index of" OR intitle:"Directory Listing"',
          category: 'directory_listing'
        },
        {
          name: 'Error Messages',
          description: 'Find error pages with sensitive information',
          query: '"Warning: mysql_connect()" OR "Warning: include(" OR "Warning: require("',
          category: 'error_messages'
        }
      ]

      for (const dork of defaultDorks) {
        await DatabaseUtils.insert('dork_presets', {
          id: generateUUID(),
          name: dork.name,
          description: dork.description,
          query: dork.query,
          category: dork.category,
          tags: JSON.stringify(['default', 'preset']),
          is_active: true,
          usage_count: 0,
          created_at: new Date(),
          updated_at: new Date()
        })
      }

      console.log('✅ Default dork presets inserted')
    }

    // Insert system settings if not exists
    const settingsExist = await DatabaseUtils.findOne(
      'SELECT id FROM system_settings LIMIT 1'
    )

    if (!settingsExist) {
      console.log('⚙️ Inserting system settings...')
      
      const settings = [
        {
          key_name: 'maintenance_mode',
          value: JSON.stringify(false),
          description: 'System maintenance mode toggle',
          is_public: true
        },
        {
          key_name: 'system_version',
          value: JSON.stringify('1.0.0'),
          description: 'Current system version',
          is_public: true
        },
        {
          key_name: 'max_file_size',
          value: JSON.stringify(104857600),
          description: 'Maximum file upload size in bytes',
          is_public: false
        }
      ]

      for (const setting of settings) {
        await DatabaseUtils.insert('system_settings', {
          id: generateUUID(),
          ...setting,
          created_at: new Date(),
          updated_at: new Date()
        })
      }

      console.log('✅ System settings inserted')
    }

  } catch (error) {
    console.error('❌ Error inserting default data:', error)
    throw error
  }
}

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// Run setup if this script is executed directly
if (import.meta.main) {
  setupDatabase()
    .then(() => {
      console.log('🏁 Setup completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error)
      process.exit(1)
    })
}

export { setupDatabase }
