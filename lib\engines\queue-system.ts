import { Kaf<PERSON>, Producer, Consumer, EachMessagePayload } from 'kafkajs'
import amqp, { Connection, Channel, Message } from 'amqplib'
import { EventEmitter } from 'events'
import { db } from '../database'

export interface QueueMessage {
  id: string
  type: string
  payload: any
  priority: 'low' | 'normal' | 'high' | 'critical'
  retries: number
  maxRetries: number
  createdAt: Date
  scheduledAt?: Date
  metadata?: Record<string, any>
}

export interface QueueConfig {
  name: string
  type: 'kafka' | 'rabbitmq' | 'memory'
  options: {
    brokers?: string[]
    groupId?: string
    exchange?: string
    routingKey?: string
    durable?: boolean
    autoAck?: boolean
    prefetch?: number
    deadLetterQueue?: string
  }
}

export interface QueueStats {
  name: string
  messagesTotal: number
  messagesProcessed: number
  messagesFailed: number
  messagesInQueue: number
  avgProcessingTime: number
  lastProcessed: Date
}

export interface JobProcessor {
  type: string
  handler: (message: QueueMessage) => Promise<void>
  concurrency?: number
  retryDelay?: number
}

export class QueueSystem extends EventEmitter {
  private kafka?: Kafka
  private kafkaProducer?: Producer
  private kafkaConsumers: Map<string, Consumer> = new Map()
  
  private rabbitmq?: Connection
  private rabbitmqChannel?: Channel
  
  private memoryQueues: Map<string, QueueMessage[]> = new Map()
  private processors: Map<string, JobProcessor> = new Map()
  private activeJobs: Map<string, Promise<void>> = new Map()
  
  private engine: 'kafka' | 'rabbitmq' | 'memory'
  private isConnected: boolean = false

  constructor() {
    super()
    this.engine = (process.env.QUEUE_ENGINE as any) || 'memory'
    this.initializeQueue()
  }

  private async initializeQueue() {
    console.log(`🚀 Queue System initializing with ${this.engine}`)
    
    try {
      if (this.engine === 'kafka') {
        await this.initializeKafka()
      } else if (this.engine === 'rabbitmq') {
        await this.initializeRabbitMQ()
      } else {
        await this.initializeMemoryQueue()
      }
      
      await this.createTables()
      await this.setupDefaultQueues()
      await this.registerDefaultProcessors()
      
      this.isConnected = true
      console.log('✅ Queue System initialized successfully')
      
    } catch (error) {
      console.error('❌ Queue System initialization failed:', error)
      // Fallback to memory queue
      await this.initializeMemoryQueue()
      this.isConnected = true
      console.log('🔄 Falling back to memory queue')
    }
  }

  private async initializeKafka() {
    this.kafka = new Kafka({
      clientId: 'kodexguard-queue',
      brokers: process.env.KAFKA_BROKERS?.split(',') || ['localhost:9092'],
      retry: {
        initialRetryTime: 100,
        retries: 8
      }
    })

    this.kafkaProducer = this.kafka.producer({
      maxInFlightRequests: 1,
      idempotent: true,
      transactionTimeout: 30000
    })

    await this.kafkaProducer.connect()
    console.log('✅ Kafka connected')
  }

  private async initializeRabbitMQ() {
    const url = process.env.RABBITMQ_URL || 'amqp://localhost:5672'
    this.rabbitmq = await amqp.connect(url)
    this.rabbitmqChannel = await this.rabbitmq.createChannel()
    
    // Set prefetch for fair dispatch
    await this.rabbitmqChannel.prefetch(1)
    
    console.log('✅ RabbitMQ connected')
  }

  private async initializeMemoryQueue() {
    // Memory queue is always available
    console.log('✅ Memory queue initialized')
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS queue_messages (
          id VARCHAR(36) PRIMARY KEY,
          queue_name VARCHAR(100) NOT NULL,
          message_type VARCHAR(100) NOT NULL,
          payload JSON NOT NULL,
          priority ENUM('low', 'normal', 'high', 'critical') DEFAULT 'normal',
          status ENUM('pending', 'processing', 'completed', 'failed', 'retrying') DEFAULT 'pending',
          retries INT DEFAULT 0,
          max_retries INT DEFAULT 3,
          error_message TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          scheduled_at TIMESTAMP NULL,
          started_at TIMESTAMP NULL,
          completed_at TIMESTAMP NULL,
          INDEX idx_queue_status (queue_name, status),
          INDEX idx_scheduled (scheduled_at),
          INDEX idx_created (created_at)
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS queue_stats (
          id VARCHAR(36) PRIMARY KEY,
          queue_name VARCHAR(100) UNIQUE NOT NULL,
          messages_total BIGINT DEFAULT 0,
          messages_processed BIGINT DEFAULT 0,
          messages_failed BIGINT DEFAULT 0,
          messages_in_queue BIGINT DEFAULT 0,
          avg_processing_time_ms INT DEFAULT 0,
          last_processed TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)

      console.log('✅ Queue System tables created')
    } catch (error) {
      console.error('❌ Error creating queue tables:', error)
    }
  }

  private async setupDefaultQueues() {
    const defaultQueues = [
      'vulnerability-scans',
      'osint-queries', 
      'malware-analysis',
      'breach-parsing',
      'search-indexing',
      'notifications',
      'reports',
      'cleanup'
    ]

    for (const queueName of defaultQueues) {
      await this.createQueue(queueName)
    }
  }

  private async registerDefaultProcessors() {
    // Register processors for different job types
    this.registerProcessor({
      type: 'vulnerability-scan',
      handler: this.processVulnerabilityScan.bind(this),
      concurrency: 3,
      retryDelay: 5000
    })

    this.registerProcessor({
      type: 'osint-query',
      handler: this.processOSINTQuery.bind(this),
      concurrency: 5,
      retryDelay: 3000
    })

    this.registerProcessor({
      type: 'malware-analysis',
      handler: this.processMalwareAnalysis.bind(this),
      concurrency: 2,
      retryDelay: 10000
    })

    this.registerProcessor({
      type: 'search-index',
      handler: this.processSearchIndexing.bind(this),
      concurrency: 10,
      retryDelay: 1000
    })

    this.registerProcessor({
      type: 'notification',
      handler: this.processNotification.bind(this),
      concurrency: 20,
      retryDelay: 2000
    })
  }

  async createQueue(name: string, config: Partial<QueueConfig> = {}): Promise<boolean> {
    try {
      if (this.engine === 'kafka' && this.kafka) {
        const admin = this.kafka.admin()
        await admin.connect()
        
        await admin.createTopics({
          topics: [{
            topic: name,
            numPartitions: config.options?.prefetch || 3,
            replicationFactor: 1
          }]
        })
        
        await admin.disconnect()
        
      } else if (this.engine === 'rabbitmq' && this.rabbitmqChannel) {
        await this.rabbitmqChannel.assertQueue(name, {
          durable: config.options?.durable !== false
        })
        
        // Create dead letter queue
        if (config.options?.deadLetterQueue) {
          await this.rabbitmqChannel.assertQueue(config.options.deadLetterQueue, {
            durable: true
          })
        }
        
      } else {
        // Memory queue
        if (!this.memoryQueues.has(name)) {
          this.memoryQueues.set(name, [])
        }
      }

      // Initialize queue stats
      await db.query(`
        INSERT INTO queue_stats (id, queue_name, created_at)
        VALUES (?, ?, NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `, [this.generateId(), name])

      console.log(`✅ Queue '${name}' created`)
      return true

    } catch (error) {
      console.error(`❌ Error creating queue '${name}':`, error)
      return false
    }
  }

  async publish(queueName: string, message: Omit<QueueMessage, 'id' | 'createdAt'>): Promise<string> {
    const messageId = this.generateId()
    const fullMessage: QueueMessage = {
      id: messageId,
      createdAt: new Date(),
      ...message
    }

    try {
      if (this.engine === 'kafka' && this.kafkaProducer) {
        await this.kafkaProducer.send({
          topic: queueName,
          messages: [{
            key: messageId,
            value: JSON.stringify(fullMessage),
            partition: this.getPartition(message.priority)
          }]
        })
        
      } else if (this.engine === 'rabbitmq' && this.rabbitmqChannel) {
        await this.rabbitmqChannel.sendToQueue(
          queueName,
          Buffer.from(JSON.stringify(fullMessage)),
          {
            persistent: true,
            priority: this.getPriorityNumber(message.priority),
            messageId: messageId
          }
        )
        
      } else {
        // Memory queue
        const queue = this.memoryQueues.get(queueName) || []
        queue.push(fullMessage)
        this.memoryQueues.set(queueName, queue)
        
        // Process immediately for memory queue
        setImmediate(() => this.processMemoryQueue(queueName))
      }

      // Log message to database
      await this.logMessage(queueName, fullMessage)
      await this.updateQueueStats(queueName, 'published')

      this.emit('message-published', { queueName, messageId, message: fullMessage })
      return messageId

    } catch (error) {
      console.error(`Error publishing message to queue '${queueName}':`, error)
      throw error
    }
  }

  async subscribe(queueName: string, processor?: JobProcessor): Promise<boolean> {
    try {
      if (this.engine === 'kafka' && this.kafka) {
        const consumer = this.kafka.consumer({
          groupId: `kodexguard-${queueName}`,
          sessionTimeout: 30000,
          heartbeatInterval: 3000
        })

        await consumer.connect()
        await consumer.subscribe({ topic: queueName })

        await consumer.run({
          eachMessage: async (payload: EachMessagePayload) => {
            await this.processKafkaMessage(payload, processor)
          }
        })

        this.kafkaConsumers.set(queueName, consumer)
        
      } else if (this.engine === 'rabbitmq' && this.rabbitmqChannel) {
        await this.rabbitmqChannel.consume(queueName, async (msg: Message | null) => {
          if (msg) {
            await this.processRabbitMQMessage(msg, processor)
          }
        }, { noAck: false })
        
      } else {
        // Memory queue processing is handled in processMemoryQueue
      }

      console.log(`✅ Subscribed to queue '${queueName}'`)
      return true

    } catch (error) {
      console.error(`❌ Error subscribing to queue '${queueName}':`, error)
      return false
    }
  }

  registerProcessor(processor: JobProcessor): void {
    this.processors.set(processor.type, processor)
    console.log(`✅ Processor registered for type '${processor.type}'`)
  }

  private async processKafkaMessage(payload: EachMessagePayload, processor?: JobProcessor) {
    try {
      const message: QueueMessage = JSON.parse(payload.message.value?.toString() || '{}')
      await this.processMessage(message, processor)
    } catch (error) {
      console.error('Error processing Kafka message:', error)
    }
  }

  private async processRabbitMQMessage(msg: Message, processor?: JobProcessor) {
    try {
      const message: QueueMessage = JSON.parse(msg.content.toString())
      await this.processMessage(message, processor)
      
      // Acknowledge message
      this.rabbitmqChannel?.ack(msg)
    } catch (error) {
      console.error('Error processing RabbitMQ message:', error)
      
      // Reject and requeue message
      this.rabbitmqChannel?.nack(msg, false, true)
    }
  }

  private async processMemoryQueue(queueName: string) {
    const queue = this.memoryQueues.get(queueName)
    if (!queue || queue.length === 0) return

    const message = queue.shift()
    if (!message) return

    try {
      await this.processMessage(message)
    } catch (error) {
      console.error('Error processing memory queue message:', error)
      
      // Retry logic for memory queue
      if (message.retries < message.maxRetries) {
        message.retries++
        queue.push(message)
      }
    }

    // Continue processing if there are more messages
    if (queue.length > 0) {
      setImmediate(() => this.processMemoryQueue(queueName))
    }
  }

  private async processMessage(message: QueueMessage, processor?: JobProcessor) {
    const startTime = Date.now()
    
    try {
      // Update message status
      await this.updateMessageStatus(message.id, 'processing')
      
      // Get processor
      const messageProcessor = processor || this.processors.get(message.type)
      if (!messageProcessor) {
        throw new Error(`No processor found for message type: ${message.type}`)
      }

      // Process message
      await messageProcessor.handler(message)
      
      // Update status and stats
      await this.updateMessageStatus(message.id, 'completed')
      await this.updateQueueStats(message.type, 'processed', Date.now() - startTime)
      
      this.emit('message-processed', { message, processingTime: Date.now() - startTime })

    } catch (error) {
      console.error(`Error processing message ${message.id}:`, error)
      
      // Handle retry logic
      if (message.retries < message.maxRetries) {
        message.retries++
        await this.updateMessageStatus(message.id, 'retrying', error.message)
        
        // Republish with delay
        setTimeout(() => {
          this.publish(message.type, {
            ...message,
            retries: message.retries
          })
        }, this.processors.get(message.type)?.retryDelay || 5000)
        
      } else {
        await this.updateMessageStatus(message.id, 'failed', error.message)
        await this.updateQueueStats(message.type, 'failed')
        
        this.emit('message-failed', { message, error })
      }
    }
  }

  // Default processors
  private async processVulnerabilityScan(message: QueueMessage): Promise<void> {
    console.log(`🔍 Processing vulnerability scan: ${message.id}`)
    // Implementation would call scannerEngine
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate processing
  }

  private async processOSINTQuery(message: QueueMessage): Promise<void> {
    console.log(`🕵️ Processing OSINT query: ${message.id}`)
    // Implementation would call OSINT services
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate processing
  }

  private async processMalwareAnalysis(message: QueueMessage): Promise<void> {
    console.log(`🦠 Processing malware analysis: ${message.id}`)
    // Implementation would call malwareAnalyzer
    await new Promise(resolve => setTimeout(resolve, 5000)) // Simulate processing
  }

  private async processSearchIndexing(message: QueueMessage): Promise<void> {
    console.log(`📚 Processing search indexing: ${message.id}`)
    // Implementation would call searchEngine
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate processing
  }

  private async processNotification(message: QueueMessage): Promise<void> {
    console.log(`📧 Processing notification: ${message.id}`)
    // Implementation would send notifications
    await new Promise(resolve => setTimeout(resolve, 200)) // Simulate processing
  }

  private async logMessage(queueName: string, message: QueueMessage) {
    try {
      await db.query(`
        INSERT INTO queue_messages (id, queue_name, message_type, payload, priority, retries, max_retries, created_at, scheduled_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        message.id,
        queueName,
        message.type,
        JSON.stringify(message.payload),
        message.priority,
        message.retries,
        message.maxRetries,
        message.createdAt,
        message.scheduledAt || null
      ])
    } catch (error) {
      console.error('Error logging message:', error)
    }
  }

  private async updateMessageStatus(messageId: string, status: string, errorMessage?: string) {
    try {
      await db.query(`
        UPDATE queue_messages 
        SET status = ?, error_message = ?, 
            started_at = ${status === 'processing' ? 'NOW()' : 'started_at'},
            completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [status, errorMessage || null, messageId])
    } catch (error) {
      console.error('Error updating message status:', error)
    }
  }

  private async updateQueueStats(queueName: string, action: 'published' | 'processed' | 'failed', processingTime?: number) {
    try {
      let updateQuery = ''
      const params: any[] = []

      switch (action) {
        case 'published':
          updateQuery = 'messages_total = messages_total + 1, messages_in_queue = messages_in_queue + 1'
          break
        case 'processed':
          updateQuery = 'messages_processed = messages_processed + 1, messages_in_queue = messages_in_queue - 1, last_processed = NOW()'
          if (processingTime) {
            updateQuery += ', avg_processing_time_ms = (avg_processing_time_ms + ?) / 2'
            params.push(processingTime)
          }
          break
        case 'failed':
          updateQuery = 'messages_failed = messages_failed + 1, messages_in_queue = messages_in_queue - 1'
          break
      }

      params.push(queueName)

      await db.query(`
        UPDATE queue_stats 
        SET ${updateQuery}, updated_at = NOW()
        WHERE queue_name = ?
      `, params)
    } catch (error) {
      console.error('Error updating queue stats:', error)
    }
  }

  async getQueueStats(queueName?: string): Promise<QueueStats[]> {
    try {
      const query = queueName 
        ? 'SELECT * FROM queue_stats WHERE queue_name = ?'
        : 'SELECT * FROM queue_stats ORDER BY queue_name'
      
      const params = queueName ? [queueName] : []
      const results = await db.query(query, params)
      
      return results.map((row: any) => ({
        name: row.queue_name,
        messagesTotal: row.messages_total,
        messagesProcessed: row.messages_processed,
        messagesFailed: row.messages_failed,
        messagesInQueue: row.messages_in_queue,
        avgProcessingTime: row.avg_processing_time_ms,
        lastProcessed: row.last_processed
      }))
    } catch (error) {
      console.error('Error getting queue stats:', error)
      return []
    }
  }

  private getPartition(priority: string): number {
    // Higher priority messages go to lower partition numbers for faster processing
    switch (priority) {
      case 'critical': return 0
      case 'high': return 1
      case 'normal': return 2
      case 'low': return 3
      default: return 2
    }
  }

  private getPriorityNumber(priority: string): number {
    switch (priority) {
      case 'critical': return 10
      case 'high': return 7
      case 'normal': return 5
      case 'low': return 1
      default: return 5
    }
  }

  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Queue System...')
    
    try {
      if (this.kafkaProducer) {
        await this.kafkaProducer.disconnect()
      }
      
      for (const consumer of this.kafkaConsumers.values()) {
        await consumer.disconnect()
      }
      
      if (this.rabbitmq) {
        await this.rabbitmq.close()
      }
      
      console.log('✅ Queue System shutdown completed')
    } catch (error) {
      console.error('❌ Error during Queue System shutdown:', error)
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const queueSystem = new QueueSystem()
