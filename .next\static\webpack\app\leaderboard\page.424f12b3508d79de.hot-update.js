"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./app/leaderboard/page.tsx":
/*!**********************************!*\
  !*** ./app/leaderboard/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PublicLayout */ \"(app-pages-browser)/./components/PublicLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Bug,Crown,Filter,Medal,Shield,Star,Target,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction LeaderboardPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overall\");\n    const [timeframe, setTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        activeToday: 0,\n        topScore: 0,\n        averageScore: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadLeaderboard();\n        loadStats();\n    }, [\n        activeTab,\n        timeframe\n    ]);\n    const loadLeaderboard = async ()=>{\n        setLoading(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            const mockUsers = [\n                {\n                    id: \"1\",\n                    rank: 1,\n                    username: \"cyberwarrior\",\n                    fullName: \"Alex Chen\",\n                    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face\",\n                    score: 15420,\n                    level: 42,\n                    badges: [\n                        {\n                            id: \"1\",\n                            name: \"Bug Hunter\",\n                            description: \"Found 100+ vulnerabilities\",\n                            icon: \"bug\",\n                            color: \"red\",\n                            earnedAt: \"2024-12-01\"\n                        },\n                        {\n                            id: \"2\",\n                            name: \"OSINT Master\",\n                            description: \"Completed 1000+ OSINT queries\",\n                            icon: \"search\",\n                            color: \"blue\",\n                            earnedAt: \"2024-11-15\"\n                        }\n                    ],\n                    stats: {\n                        vulnerabilitiesFound: 156,\n                        scansCompleted: 1247,\n                        osintQueries: 2341,\n                        cveReported: 12,\n                        toolsUsed: 45,\n                        daysActive: 234\n                    },\n                    country: \"Singapore\",\n                    joinedAt: \"2024-01-15\",\n                    lastActive: \"2025-01-14T15:30:00Z\"\n                },\n                {\n                    id: \"2\",\n                    rank: 2,\n                    username: \"securityninja\",\n                    fullName: \"Sarah Johnson\",\n                    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face\",\n                    score: 14890,\n                    level: 39,\n                    badges: [\n                        {\n                            id: \"3\",\n                            name: \"Scanner Pro\",\n                            description: \"Completed 500+ scans\",\n                            icon: \"shield\",\n                            color: \"green\",\n                            earnedAt: \"2024-12-10\"\n                        }\n                    ],\n                    stats: {\n                        vulnerabilitiesFound: 142,\n                        scansCompleted: 1156,\n                        osintQueries: 1987,\n                        cveReported: 8,\n                        toolsUsed: 38,\n                        daysActive: 198\n                    },\n                    country: \"United States\",\n                    joinedAt: \"2024-02-01\",\n                    lastActive: \"2025-01-14T14:20:00Z\"\n                },\n                {\n                    id: \"3\",\n                    rank: 3,\n                    username: \"hackermind\",\n                    fullName: \"David Kim\",\n                    score: 13567,\n                    level: 36,\n                    badges: [\n                        {\n                            id: \"4\",\n                            name: \"CVE Hunter\",\n                            description: \"Reported 5+ CVEs\",\n                            icon: \"database\",\n                            color: \"purple\",\n                            earnedAt: \"2024-11-20\"\n                        }\n                    ],\n                    stats: {\n                        vulnerabilitiesFound: 134,\n                        scansCompleted: 987,\n                        osintQueries: 1654,\n                        cveReported: 15,\n                        toolsUsed: 42,\n                        daysActive: 187\n                    },\n                    country: \"South Korea\",\n                    joinedAt: \"2024-03-10\",\n                    lastActive: \"2025-01-14T13:45:00Z\"\n                }\n            ];\n            // Add more mock users\n            for(let i = 4; i <= 50; i++){\n                mockUsers.push({\n                    id: i.toString(),\n                    rank: i,\n                    username: \"user\".concat(i),\n                    fullName: \"User \".concat(i),\n                    score: Math.floor(Math.random() * 10000) + 1000,\n                    level: Math.floor(Math.random() * 30) + 1,\n                    badges: [],\n                    stats: {\n                        vulnerabilitiesFound: Math.floor(Math.random() * 100),\n                        scansCompleted: Math.floor(Math.random() * 500),\n                        osintQueries: Math.floor(Math.random() * 1000),\n                        cveReported: Math.floor(Math.random() * 5),\n                        toolsUsed: Math.floor(Math.random() * 20),\n                        daysActive: Math.floor(Math.random() * 200)\n                    },\n                    joinedAt: \"2024-01-01\",\n                    lastActive: new Date().toISOString()\n                });\n            }\n            setLeaderboard(mockUsers);\n            // Set current user (simulate logged in user)\n            setCurrentUser({\n                id: \"current\",\n                rank: 25,\n                username: \"currentuser\",\n                fullName: \"Current User\",\n                score: 5420,\n                level: 18,\n                badges: [\n                    {\n                        id: \"5\",\n                        name: \"Newcomer\",\n                        description: \"Joined the platform\",\n                        icon: \"star\",\n                        color: \"yellow\",\n                        earnedAt: \"2024-12-01\"\n                    }\n                ],\n                stats: {\n                    vulnerabilitiesFound: 23,\n                    scansCompleted: 156,\n                    osintQueries: 234,\n                    cveReported: 1,\n                    toolsUsed: 12,\n                    daysActive: 45\n                },\n                joinedAt: \"2024-12-01\",\n                lastActive: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Error loading leaderboard:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadStats = async ()=>{\n        setTimeout(()=>{\n            setStats({\n                totalUsers: 12456,\n                activeToday: 1247,\n                topScore: 15420,\n                averageScore: 3456\n            });\n        }, 1000);\n    };\n    const getRankIcon = (rank)=>{\n        if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-5 w-5 text-yellow-500\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 28\n        }, this);\n        if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 28\n        }, this);\n        if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5 text-amber-600\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n            lineNumber: 237,\n            columnNumber: 28\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-sm font-medium text-gray-600\",\n            children: [\n                \"#\",\n                rank\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n            lineNumber: 238,\n            columnNumber: 12\n        }, this);\n    };\n    const getBadgeIcon = (iconName)=>{\n        switch(iconName){\n            case \"bug\":\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"search\":\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"shield\":\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"database\":\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            case \"star\":\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n        }\n    };\n    const getBadgeColor = (color)=>{\n        switch(color){\n            case \"red\":\n                return \"bg-red-100 text-red-800\";\n            case \"blue\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"green\":\n                return \"bg-green-100 text-green-800\";\n            case \"purple\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"yellow\":\n                return \"bg-yellow-100 text-yellow-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    const leaderboardColumns = [\n        {\n            key: \"rank\",\n            label: \"Rank\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-12\",\n                    children: getRankIcon(value)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"user\",\n            label: \"User\",\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        row.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: row.avatar,\n                            alt: row.username,\n                            className: \"h-8 w-8 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-medium text-gray-600\",\n                                children: row.username.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: row.username\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: row.fullName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            label: \"Score\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-bold text-primary-600\",\n                    children: value.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"level\",\n            label: \"Level\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"badges\",\n            label: \"Badges\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        value.slice(0, 3).map((badge)=>{\n                            const Icon = getBadgeIcon(badge.icon);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 rounded-full \".concat(getBadgeColor(badge.color)),\n                                title: badge.name,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, this)\n                            }, badge.id, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        value.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"+\",\n                                value.length - 3\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"stats\",\n            label: \"Vulnerabilities\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-medium\",\n                    children: value.vulnerabilitiesFound\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"lastActive\",\n            label: \"Last Active\",\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: new Date(value).toLocaleDateString()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PublicLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 mr-2 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Leaderboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Top cybersecurity researchers and bug hunters\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeframe,\n                                        onChange: (e)=>setTimeframe(e.target.value),\n                                        className: \"input-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"year\",\n                                                children: \"This Year\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"month\",\n                                                children: \"This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"week\",\n                                                children: \"This Week\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Filter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            title: \"Total Users\",\n                            value: stats.totalUsers.toLocaleString(),\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                            color: \"blue\",\n                            trend: {\n                                value: 12,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            title: \"Active Today\",\n                            value: stats.activeToday.toLocaleString(),\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"green\",\n                            trend: {\n                                value: 8,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            title: \"Top Score\",\n                            value: stats.topScore.toLocaleString(),\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                            color: \"yellow\",\n                            trend: {\n                                value: 15,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsCard, {\n                            title: \"Average Score\",\n                            value: stats.averageScore.toLocaleString(),\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                            color: \"purple\",\n                            trend: {\n                                value: 5,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                currentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Your Position\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 bg-primary-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-12 h-12 bg-primary-100 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-primary-600\",\n                                                children: [\n                                                    \"#\",\n                                                    currentUser.rank\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: currentUser.username\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Level \",\n                                                        currentUser.level\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-primary-600\",\n                                            children: currentUser.score.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"points\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg\",\n                    children: [\n                        {\n                            id: \"overall\",\n                            label: \"Overall\",\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                        },\n                        {\n                            id: \"vulnerabilities\",\n                            label: \"Vulnerabilities\",\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                        },\n                        {\n                            id: \"scans\",\n                            label: \"Scans\",\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        },\n                        {\n                            id: \"osint\",\n                            label: \"OSINT\",\n                            icon: _barrel_optimize_names_Activity_Award_Bug_Crown_Filter_Medal_Shield_Star_Target_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                        }\n                    ].map((tab)=>{\n                        const Icon = tab.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors \".concat(activeTab === tab.id ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: activeTab === \"overall\" ? \"Overall Rankings\" : activeTab === \"vulnerabilities\" ? \"Top Vulnerability Hunters\" : activeTab === \"scans\" ? \"Top Scanners\" : \"Top OSINT Researchers\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n                                size: \"lg\",\n                                text: \"Loading leaderboard...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DataTable, {\n                            columns: leaderboardColumns,\n                            data: leaderboard.slice(0, 50),\n                            pagination: {\n                                currentPage: 1,\n                                totalPages: Math.ceil(leaderboard.length / 50),\n                                pageSize: 50,\n                                totalItems: leaderboard.length,\n                                onPageChange: ()=>{}\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n            lineNumber: 361,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\leaderboard\\\\page.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"fCqH0GM8f0ctlmnnvyAFFiamkpE=\");\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/leaderboard/page.tsx\n"));

/***/ })

});