import { spawn, exec } from 'child_process'
import { promisify } from 'util'
import { db } from '../database'
import axios from 'axios'

const execAsync = promisify(exec)

export interface ScanTarget {
  id: string
  target: string // IP, domain, or CIDR range
  scanType: 'nmap' | 'masscan' | 'zmap' | 'custom'
  ports: string // Port range (e.g., "1-1000", "80,443,8080")
  options: ScanOptions
}

export interface ScanOptions {
  aggressive: boolean
  serviceDetection: boolean
  osDetection: boolean
  scriptScan: boolean
  timing: 1 | 2 | 3 | 4 | 5 // T1-T5
  maxRetries: number
  timeout: number
  userAgent?: string
  sourcePort?: number
  decoy?: string[]
}

export interface PortResult {
  port: number
  protocol: 'tcp' | 'udp'
  state: 'open' | 'closed' | 'filtered' | 'unfiltered'
  service?: string
  version?: string
  banner?: string
  cpe?: string[]
  scripts?: Record<string, any>
}

export interface HostResult {
  ip: string
  hostname?: string
  status: 'up' | 'down' | 'unknown'
  os?: {
    name: string
    accuracy: number
    cpe: string[]
  }
  ports: PortResult[]
  latency: number
  lastSeen: Date
  geolocation?: {
    country: string
    city: string
    latitude: number
    longitude: number
    isp: string
  }
}

export interface ScanSession {
  id: string
  target: ScanTarget
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  progress: number
  hostsFound: number
  hostsScanned: number
  portsFound: number
  results: HostResult[]
  errors: string[]
  command?: string
}

export class ScannerEngine {
  private sessions: Map<string, ScanSession> = new Map()
  private runningProcesses: Map<string, any> = new Map()

  constructor() {
    this.initializeEngine()
  }

  private async initializeEngine() {
    console.log('🔍 Scanner Engine initialized')
    await this.createTables()
    await this.checkDependencies()
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS scan_sessions (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          target VARCHAR(500) NOT NULL,
          scan_type ENUM('nmap', 'masscan', 'zmap', 'custom') NOT NULL,
          config JSON,
          status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
          progress INT DEFAULT 0,
          hosts_found INT DEFAULT 0,
          hosts_scanned INT DEFAULT 0,
          ports_found INT DEFAULT 0,
          results JSON,
          errors JSON,
          command TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS scan_results (
          id VARCHAR(36) PRIMARY KEY,
          session_id VARCHAR(36),
          ip VARCHAR(45) NOT NULL,
          hostname VARCHAR(255),
          status ENUM('up', 'down', 'unknown') DEFAULT 'unknown',
          os_name VARCHAR(255),
          os_accuracy INT,
          ports JSON,
          latency DECIMAL(8,3),
          geolocation JSON,
          last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (session_id) REFERENCES scan_sessions(id) ON DELETE CASCADE,
          INDEX idx_ip (ip),
          INDEX idx_session (session_id)
        )
      `)

      console.log('✅ Scanner Engine tables created')
    } catch (error) {
      console.error('❌ Error creating scanner tables:', error)
    }
  }

  private async checkDependencies() {
    const tools = ['nmap', 'masscan']
    
    for (const tool of tools) {
      try {
        await execAsync(`${tool} --version`)
        console.log(`✅ ${tool} is available`)
      } catch (error) {
        console.log(`⚠️ ${tool} is not installed or not in PATH`)
      }
    }
  }

  async startScan(target: ScanTarget, userId?: number): Promise<string> {
    const sessionId = this.generateId()
    
    const session: ScanSession = {
      id: sessionId,
      target,
      status: 'pending',
      startTime: new Date(),
      progress: 0,
      hostsFound: 0,
      hostsScanned: 0,
      portsFound: 0,
      results: [],
      errors: []
    }

    this.sessions.set(sessionId, session)

    // Save to database
    await db.query(`
      INSERT INTO scan_sessions (id, user_id, target, scan_type, config, status, created_at)
      VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    `, [sessionId, userId || null, target.target, target.scanType, JSON.stringify(target)])

    // Start scanning
    this.executeScan(sessionId)

    return sessionId
  }

  private async executeScan(sessionId: string) {
    const session = this.sessions.get(sessionId)
    if (!session) return

    try {
      session.status = 'running'
      await this.updateSessionStatus(sessionId, 'running')

      console.log(`🔍 Starting ${session.target.scanType} scan for ${session.target.target}`)

      let results: HostResult[] = []

      switch (session.target.scanType) {
        case 'nmap':
          results = await this.executeNmapScan(session)
          break
        case 'masscan':
          results = await this.executeMasscanScan(session)
          break
        case 'zmap':
          results = await this.executeZmapScan(session)
          break
        default:
          throw new Error(`Unsupported scan type: ${session.target.scanType}`)
      }

      session.results = results
      session.hostsFound = results.length
      session.hostsScanned = results.filter(h => h.status === 'up').length
      session.portsFound = results.reduce((total, host) => total + host.ports.length, 0)
      session.status = 'completed'
      session.endTime = new Date()
      session.progress = 100

      // Save results to database
      await this.saveResults(sessionId, results)
      await this.updateSessionStatus(sessionId, 'completed')

      console.log(`🎉 Scan ${sessionId} completed: ${session.hostsFound} hosts, ${session.portsFound} ports`)

    } catch (error) {
      session.status = 'failed'
      session.errors.push(`Scan failed: ${error}`)
      await this.updateSessionStatus(sessionId, 'failed')
      console.error(`❌ Scan ${sessionId} failed:`, error)
    }
  }

  private async executeNmapScan(session: ScanSession): Promise<HostResult[]> {
    const { target, ports, options } = session.target
    
    // Build nmap command
    let command = ['nmap']
    
    // Add timing
    command.push(`-T${options.timing}`)
    
    // Add scan options
    if (options.serviceDetection) command.push('-sV')
    if (options.osDetection) command.push('-O')
    if (options.scriptScan) command.push('-sC')
    if (options.aggressive) command.push('-A')
    
    // Add port specification
    if (ports && ports !== 'all') {
      command.push('-p', ports)
    }
    
    // Add output format
    command.push('-oX', '-') // XML output to stdout
    
    // Add target
    command.push(target)
    
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('nmap', command.slice(1))
      let xmlOutput = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        xmlOutput += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
        console.log(`Nmap stderr: ${data}`)
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          try {
            const results = this.parseNmapXML(xmlOutput)
            resolve(results)
          } catch (error) {
            reject(new Error(`Failed to parse nmap output: ${error}`))
          }
        } else {
          reject(new Error(`Nmap exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start nmap: ${error}`))
      })
    })
  }

  private async executeMasscanScan(session: ScanSession): Promise<HostResult[]> {
    const { target, ports, options } = session.target
    
    // Build masscan command
    let command = ['masscan']
    
    // Add rate limiting
    command.push('--rate', '1000')
    
    // Add port specification
    if (ports && ports !== 'all') {
      command.push('-p', ports)
    } else {
      command.push('-p', '1-65535')
    }
    
    // Add output format
    command.push('-oJ', '-') // JSON output to stdout
    
    // Add target
    command.push(target)
    
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('masscan', command.slice(1))
      let jsonOutput = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        jsonOutput += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          try {
            const results = this.parseMasscanJSON(jsonOutput)
            resolve(results)
          } catch (error) {
            reject(new Error(`Failed to parse masscan output: ${error}`))
          }
        } else {
          reject(new Error(`Masscan exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start masscan: ${error}`))
      })
    })
  }

  private async executeZmapScan(session: ScanSession): Promise<HostResult[]> {
    // ZMap implementation for single port scanning
    const { target, ports } = session.target
    const port = ports.split(',')[0] || '80' // Use first port for zmap
    
    let command = ['zmap', '-p', port, target]
    session.command = command.join(' ')

    return new Promise((resolve, reject) => {
      const process = spawn('zmap', command.slice(1))
      let output = ''
      let errorOutput = ''

      this.runningProcesses.set(session.id, process)

      process.stdout.on('data', (data) => {
        output += data.toString()
      })

      process.stderr.on('data', (data) => {
        errorOutput += data.toString()
      })

      process.on('close', (code) => {
        this.runningProcesses.delete(session.id)
        
        if (code === 0) {
          const results = this.parseZmapOutput(output, parseInt(port))
          resolve(results)
        } else {
          reject(new Error(`ZMap exited with code ${code}: ${errorOutput}`))
        }
      })

      process.on('error', (error) => {
        this.runningProcesses.delete(session.id)
        reject(new Error(`Failed to start zmap: ${error}`))
      })
    })
  }

  private parseNmapXML(xmlOutput: string): HostResult[] {
    // Basic XML parsing for nmap output
    // In production, use a proper XML parser like xml2js
    const results: HostResult[] = []
    
    // This is a simplified parser - implement proper XML parsing
    const hostRegex = /<host[^>]*>[\s\S]*?<\/host>/g
    const hosts = xmlOutput.match(hostRegex) || []
    
    for (const hostXml of hosts) {
      const ipMatch = hostXml.match(/<address addr="([^"]+)" addrtype="ipv4"/)
      const hostnameMatch = hostXml.match(/<hostname name="([^"]+)"/)
      const stateMatch = hostXml.match(/<status state="([^"]+)"/)
      
      if (ipMatch) {
        const host: HostResult = {
          ip: ipMatch[1],
          hostname: hostnameMatch?.[1],
          status: stateMatch?.[1] === 'up' ? 'up' : 'down',
          ports: [],
          latency: 0,
          lastSeen: new Date()
        }
        
        // Parse ports (simplified)
        const portRegex = /<port protocol="([^"]+)" portid="([^"]+)"[^>]*>[\s\S]*?<\/port>/g
        let portMatch
        while ((portMatch = portRegex.exec(hostXml)) !== null) {
          const stateMatch = portMatch[0].match(/<state state="([^"]+)"/)
          const serviceMatch = portMatch[0].match(/<service name="([^"]+)"/)
          
          host.ports.push({
            port: parseInt(portMatch[2]),
            protocol: portMatch[1] as 'tcp' | 'udp',
            state: stateMatch?.[1] as any || 'unknown',
            service: serviceMatch?.[1]
          })
        }
        
        results.push(host)
      }
    }
    
    return results
  }

  private parseMasscanJSON(jsonOutput: string): HostResult[] {
    const results: HostResult[] = []
    const lines = jsonOutput.trim().split('\n')
    
    for (const line of lines) {
      if (line.trim() && line !== '}') {
        try {
          const data = JSON.parse(line.replace(/,$/, ''))
          
          if (data.ip && data.ports) {
            const host: HostResult = {
              ip: data.ip,
              status: 'up',
              ports: data.ports.map((p: any) => ({
                port: p.port,
                protocol: p.proto,
                state: 'open'
              })),
              latency: 0,
              lastSeen: new Date()
            }
            
            results.push(host)
          }
        } catch (error) {
          // Skip invalid JSON lines
        }
      }
    }
    
    return results
  }

  private parseZmapOutput(output: string, port: number): HostResult[] {
    const results: HostResult[] = []
    const ips = output.trim().split('\n').filter(ip => ip.trim())
    
    for (const ip of ips) {
      if (ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        results.push({
          ip: ip.trim(),
          status: 'up',
          ports: [{
            port,
            protocol: 'tcp',
            state: 'open'
          }],
          latency: 0,
          lastSeen: new Date()
        })
      }
    }
    
    return results
  }

  private async saveResults(sessionId: string, results: HostResult[]) {
    for (const host of results) {
      try {
        await db.query(`
          INSERT INTO scan_results (id, session_id, ip, hostname, status, ports, latency, last_seen)
          VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          this.generateId(),
          sessionId,
          host.ip,
          host.hostname || null,
          host.status,
          JSON.stringify(host.ports),
          host.latency
        ])
      } catch (error) {
        console.error('Error saving scan result:', error)
      }
    }
  }

  private async updateSessionStatus(sessionId: string, status: string) {
    try {
      const session = this.sessions.get(sessionId)
      await db.query(`
        UPDATE scan_sessions 
        SET status = ?, progress = ?, hosts_found = ?, hosts_scanned = ?, ports_found = ?, 
            updated_at = NOW(), completed_at = ${status === 'completed' ? 'NOW()' : 'NULL'}
        WHERE id = ?
      `, [
        status, 
        session?.progress || 0,
        session?.hostsFound || 0,
        session?.hostsScanned || 0,
        session?.portsFound || 0,
        sessionId
      ])
    } catch (error) {
      console.error('Error updating session status:', error)
    }
  }

  async getSession(sessionId: string): Promise<ScanSession | null> {
    return this.sessions.get(sessionId) || null
  }

  async cancelScan(sessionId: string): Promise<boolean> {
    const process = this.runningProcesses.get(sessionId)
    if (process) {
      process.kill('SIGTERM')
      this.runningProcesses.delete(sessionId)
      
      const session = this.sessions.get(sessionId)
      if (session) {
        session.status = 'cancelled'
        await this.updateSessionStatus(sessionId, 'cancelled')
      }
      
      return true
    }
    return false
  }

  // Enhanced geolocation lookup
  async enrichWithGeolocation(ip: string): Promise<any> {
    try {
      const response = await axios.get(`http://ip-api.com/json/${ip}`)
      return {
        country: response.data.country,
        city: response.data.city,
        latitude: response.data.lat,
        longitude: response.data.lon,
        isp: response.data.isp
      }
    } catch (error) {
      return null
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const scannerEngine = new ScannerEngine()
