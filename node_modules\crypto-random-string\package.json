{"name": "crypto-random-string", "version": "4.0.0", "description": "Generate a cryptographically strong random string", "license": "MIT", "repository": "sindresorhus/crypto-random-string", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["random", "string", "text", "id", "identifier", "slug", "salt", "pin", "crypto", "strong", "secure", "hex", "secret", "protect"], "dependencies": {"type-fest": "^1.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}