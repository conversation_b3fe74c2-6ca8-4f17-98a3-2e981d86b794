'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable, StatusBadge } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading, ProgressBar } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  Shield, 
  Target, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Bug,
  Zap,
  Globe,
  Database,
  Eye,
  Download,
  Play,
  Pause,
  Square,
  Settings,
  Filter,
  History
} from 'lucide-react'

interface VulnerabilityScan {
  id: string
  target: string
  scanType: 'quick' | 'full' | 'custom'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  vulnerabilities: Vulnerability[]
  startedAt: string
  completedAt?: string
  duration?: number
}

interface Vulnerability {
  id: string
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
  type: 'sqli' | 'xss' | 'lfi' | 'rce' | 'csrf' | 'xxe' | 'idor' | 'other'
  cvss: number
  cve?: string
  url: string
  parameter?: string
  payload?: string
  evidence: string
  recommendation: string
}

export default function ScannerPage() {
  const [activeTab, setActiveTab] = useState('scan')
  const [scanTarget, setScanTarget] = useState('')
  const [scanType, setScanType] = useState<'quick' | 'full' | 'custom'>('quick')
  const [scanOptions, setScanOptions] = useState({
    checkSQLi: true,
    checkXSS: true,
    checkLFI: true,
    checkRCE: true,
    checkCSRF: false,
    checkXXE: false,
    checkIDOR: false,
    deepScan: false,
    followRedirects: true,
    checkSSL: true
  })
  const [currentScan, setCurrentScan] = useState<VulnerabilityScan | null>(null)
  const [scanHistory, setScanHistory] = useState<VulnerabilityScan[]>([])
  const [showVulnModal, setShowVulnModal] = useState(false)
  const [selectedVuln, setSelectedVuln] = useState<Vulnerability | null>(null)
  const [stats, setStats] = useState({
    totalScans: 0,
    vulnerabilitiesFound: 0,
    criticalVulns: 0,
    averageScore: 0
  })

  const { success, error, warning } = useToast()

  useEffect(() => {
    loadStats()
    loadScanHistory()
  }, [])

  const loadStats = async () => {
    setTimeout(() => {
      setStats({
        totalScans: 156,
        vulnerabilitiesFound: 89,
        criticalVulns: 12,
        averageScore: 7.2
      })
    }, 1000)
  }

  const loadScanHistory = async () => {
    setTimeout(() => {
      setScanHistory([
        {
          id: '1',
          target: 'https://example.com',
          scanType: 'full',
          status: 'completed',
          progress: 100,
          vulnerabilities: [
            {
              id: 'v1',
              title: 'SQL Injection in login form',
              description: 'The login form is vulnerable to SQL injection attacks',
              severity: 'critical',
              type: 'sqli',
              cvss: 9.8,
              cve: 'CVE-2023-1234',
              url: 'https://example.com/login',
              parameter: 'username',
              payload: "' OR 1=1 --",
              evidence: 'MySQL error message revealed',
              recommendation: 'Use parameterized queries and input validation'
            }
          ],
          startedAt: '2025-01-14T10:00:00Z',
          completedAt: '2025-01-14T10:15:00Z',
          duration: 15
        },
        {
          id: '2',
          target: 'https://test.com',
          scanType: 'quick',
          status: 'completed',
          progress: 100,
          vulnerabilities: [],
          startedAt: '2025-01-14T09:30:00Z',
          completedAt: '2025-01-14T09:35:00Z',
          duration: 5
        }
      ])
    }, 1500)
  }

  const startScan = async () => {
    if (!scanTarget.trim()) {
      warning('Please enter a target URL')
      return
    }

    if (!scanTarget.startsWith('http://') && !scanTarget.startsWith('https://')) {
      warning('Please enter a valid URL starting with http:// or https://')
      return
    }

    const newScan: VulnerabilityScan = {
      id: Date.now().toString(),
      target: scanTarget,
      scanType,
      status: 'running',
      progress: 0,
      vulnerabilities: [],
      startedAt: new Date().toISOString()
    }

    setCurrentScan(newScan)
    setScanHistory(prev => [newScan, ...prev])
    success('Vulnerability scan started!')

    // Simulate scan progress
    const progressInterval = setInterval(() => {
      setCurrentScan(prev => {
        if (!prev || prev.status !== 'running') {
          clearInterval(progressInterval)
          return prev
        }

        const newProgress = Math.min(prev.progress + Math.random() * 10, 100)
        
        if (newProgress >= 100) {
          clearInterval(progressInterval)
          
          // Generate mock vulnerabilities
          const mockVulns = generateMockVulnerabilities(scanTarget)
          
          const completedScan = {
            ...prev,
            status: 'completed' as const,
            progress: 100,
            vulnerabilities: mockVulns,
            completedAt: new Date().toISOString(),
            duration: Math.floor((Date.now() - new Date(prev.startedAt).getTime()) / 1000 / 60)
          }

          setScanHistory(prevHistory => 
            prevHistory.map(scan => 
              scan.id === prev.id ? completedScan : scan
            )
          )

          success(`Scan completed! Found ${mockVulns.length} vulnerabilities`)
          
          return completedScan
        }

        return { ...prev, progress: newProgress }
      })
    }, 1000)
  }

  const generateMockVulnerabilities = (target: string): Vulnerability[] => {
    const vulnTypes = ['sqli', 'xss', 'lfi', 'rce', 'csrf'] as const
    const severities = ['critical', 'high', 'medium', 'low'] as const
    
    return Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, i) => ({
      id: `v${i + 1}`,
      title: `${vulnTypes[i % vulnTypes.length].toUpperCase()} Vulnerability`,
      description: `Potential ${vulnTypes[i % vulnTypes.length]} vulnerability detected`,
      severity: severities[Math.floor(Math.random() * severities.length)],
      type: vulnTypes[i % vulnTypes.length],
      cvss: Math.round((Math.random() * 10) * 10) / 10,
      url: `${target}/page${i + 1}`,
      parameter: `param${i + 1}`,
      payload: 'test_payload',
      evidence: 'Vulnerability evidence found',
      recommendation: 'Apply security patches and input validation'
    }))
  }

  const stopScan = () => {
    if (currentScan && currentScan.status === 'running') {
      setCurrentScan(prev => prev ? { ...prev, status: 'cancelled' } : null)
      setScanHistory(prev => 
        prev.map(scan => 
          scan.id === currentScan.id ? { ...scan, status: 'cancelled' } : scan
        )
      )
      warning('Scan cancelled')
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      case 'info': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const scanHistoryColumns = [
    {
      key: 'target',
      label: 'Target',
      render: (value: string) => (
        <span className="font-mono text-sm">{value}</span>
      )
    },
    {
      key: 'scanType',
      label: 'Type',
      render: (value: string) => (
        <span className="capitalize font-medium">{value}</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => <StatusBadge status={value} />
    },
    {
      key: 'vulnerabilities',
      label: 'Vulnerabilities',
      render: (value: Vulnerability[]) => (
        <span className="font-medium">{value.length}</span>
      )
    },
    {
      key: 'duration',
      label: 'Duration',
      render: (value: number) => value ? `${value}m` : '-'
    },
    {
      key: 'startedAt',
      label: 'Date',
      render: (value: string) => new Date(value).toLocaleDateString()
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: VulnerabilityScan) => (
        <div className="flex space-x-2">
          <button
            onClick={() => viewScanDetails(row)}
            className="text-primary-600 hover:text-primary-700"
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={() => downloadScanReport(row)}
            className="text-green-600 hover:text-green-700"
            disabled={row.status !== 'completed'}
          >
            <Download className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ]

  const viewScanDetails = (scan: VulnerabilityScan) => {
    // Implementation for viewing scan details
    console.log('View scan details:', scan)
  }

  const downloadScanReport = (scan: VulnerabilityScan) => {
    const report = {
      scan: {
        id: scan.id,
        target: scan.target,
        type: scan.scanType,
        startedAt: scan.startedAt,
        completedAt: scan.completedAt,
        duration: scan.duration
      },
      vulnerabilities: scan.vulnerabilities,
      summary: {
        total: scan.vulnerabilities.length,
        critical: scan.vulnerabilities.filter(v => v.severity === 'critical').length,
        high: scan.vulnerabilities.filter(v => v.severity === 'high').length,
        medium: scan.vulnerabilities.filter(v => v.severity === 'medium').length,
        low: scan.vulnerabilities.filter(v => v.severity === 'low').length
      }
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `vulnerability-scan-${scan.id}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    success('Scan report downloaded!')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Shield className="h-6 w-6 mr-2 text-primary-600" />
              Vulnerability Scanner
            </h1>
            <p className="text-gray-600 mt-1">
              Scan web applications for security vulnerabilities
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('scan')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'scan'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Target className="h-4 w-4 mr-2 inline" />
                New Scan
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'history'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <History className="h-4 w-4 mr-2 inline" />
                Scan History
              </button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Scans"
            value={stats.totalScans}
            icon={Target}
            color="blue"
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Vulnerabilities"
            value={stats.vulnerabilitiesFound}
            icon={Bug}
            color="red"
            trend={{ value: 12, isPositive: false }}
          />
          <StatsCard
            title="Critical Issues"
            value={stats.criticalVulns}
            icon={AlertTriangle}
            color="red"
            trend={{ value: 3, isPositive: false }}
          />
          <StatsCard
            title="Avg CVSS Score"
            value={stats.averageScore.toFixed(1)}
            icon={Zap}
            color="yellow"
            trend={{ value: 5, isPositive: false }}
          />
        </div>

        {/* Main Content */}
        {activeTab === 'scan' ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Scan Configuration */}
            <div className="lg:col-span-2">
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Configure Scan
                </h3>

                {/* Target URL */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target URL
                  </label>
                  <input
                    type="url"
                    value={scanTarget}
                    onChange={(e) => setScanTarget(e.target.value)}
                    placeholder="https://example.com"
                    className="input-field"
                    disabled={currentScan?.status === 'running'}
                  />
                </div>

                {/* Scan Type */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Scan Type
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {[
                      { value: 'quick', label: 'Quick Scan', desc: '5-10 minutes', icon: Zap },
                      { value: 'full', label: 'Full Scan', desc: '30-60 minutes', icon: Shield },
                      { value: 'custom', label: 'Custom Scan', desc: 'Configure options', icon: Settings }
                    ].map((type) => {
                      const Icon = type.icon
                      return (
                        <button
                          key={type.value}
                          onClick={() => setScanType(type.value as any)}
                          disabled={currentScan?.status === 'running'}
                          className={`p-3 border rounded-lg text-left transition-colors ${
                            scanType === type.value
                              ? 'border-primary-500 bg-primary-50'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className={`h-5 w-5 ${
                              scanType === type.value ? 'text-primary-600' : 'text-gray-400'
                            }`} />
                            <div>
                              <div className="font-medium text-gray-900">{type.label}</div>
                              <div className="text-xs text-gray-500">{type.desc}</div>
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Scan Options */}
                {scanType === 'custom' && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Scan Options
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {Object.entries(scanOptions).map(([key, value]) => (
                        <label
                          key={key}
                          className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                        >
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setScanOptions(prev => ({
                              ...prev,
                              [key]: e.target.checked
                            }))}
                            disabled={currentScan?.status === 'running'}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <span className="font-medium text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {/* Scan Controls */}
                <div className="flex space-x-3">
                  {!currentScan || currentScan.status !== 'running' ? (
                    <button
                      onClick={startScan}
                      disabled={!scanTarget.trim()}
                      className="btn-primary flex-1"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Scan
                    </button>
                  ) : (
                    <button
                      onClick={stopScan}
                      className="btn-danger flex-1"
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </button>
                  )}
                </div>
              </Card>
            </div>

            {/* Current Scan Status */}
            <div>
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Scan Status
                </h3>
                
                {currentScan ? (
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">Target</span>
                        <StatusBadge status={currentScan.status} />
                      </div>
                      <p className="text-sm text-gray-600 font-mono break-all">
                        {currentScan.target}
                      </p>
                    </div>

                    {currentScan.status === 'running' && (
                      <div>
                        <ProgressBar
                          progress={currentScan.progress}
                          showPercentage
                          color="primary"
                        />
                        <p className="text-sm text-gray-500 mt-2">
                          Scanning in progress...
                        </p>
                      </div>
                    )}

                    {currentScan.vulnerabilities.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          Vulnerabilities Found: {currentScan.vulnerabilities.length}
                        </h4>
                        <div className="space-y-2">
                          {currentScan.vulnerabilities.slice(0, 3).map((vuln) => (
                            <div
                              key={vuln.id}
                              className="p-2 border rounded cursor-pointer hover:bg-gray-50"
                              onClick={() => {
                                setSelectedVuln(vuln)
                                setShowVulnModal(true)
                              }}
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{vuln.title}</span>
                                <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(vuln.severity)}`}>
                                  {vuln.severity}
                                </span>
                              </div>
                            </div>
                          ))}
                          {currentScan.vulnerabilities.length > 3 && (
                            <p className="text-sm text-gray-500">
                              +{currentScan.vulnerabilities.length - 3} more vulnerabilities
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {currentScan.status === 'completed' && (
                      <button
                        onClick={() => downloadScanReport(currentScan)}
                        className="btn-secondary w-full"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download Report
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No active scan</p>
                    <p className="text-sm">Start a scan to see status here</p>
                  </div>
                )}
              </Card>
            </div>
          </div>
        ) : (
          /* History Tab */
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Scan History
              </h3>
              <button className="btn-secondary">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </button>
            </div>
            
            <DataTable
              columns={scanHistoryColumns}
              data={scanHistory}
              searchable
              pagination={{
                currentPage: 1,
                totalPages: 1,
                pageSize: 10,
                totalItems: scanHistory.length,
                onPageChange: () => {}
              }}
            />
          </Card>
        )}

        {/* Vulnerability Detail Modal */}
        <Modal
          isOpen={showVulnModal}
          onClose={() => setShowVulnModal(false)}
          title="Vulnerability Details"
          size="lg"
        >
          {selectedVuln && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-gray-900">
                  {selectedVuln.title}
                </h4>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSeverityColor(selectedVuln.severity)}`}>
                  {selectedVuln.severity.toUpperCase()}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Type:</span>
                  <span className="ml-2 uppercase">{selectedVuln.type}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">CVSS Score:</span>
                  <span className="ml-2">{selectedVuln.cvss}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">URL:</span>
                  <span className="ml-2 font-mono text-xs break-all">{selectedVuln.url}</span>
                </div>
                {selectedVuln.cve && (
                  <div>
                    <span className="font-medium text-gray-700">CVE:</span>
                    <span className="ml-2">{selectedVuln.cve}</span>
                  </div>
                )}
              </div>

              <div>
                <h5 className="font-medium text-gray-900 mb-2">Description</h5>
                <p className="text-gray-600">{selectedVuln.description}</p>
              </div>

              {selectedVuln.payload && (
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">Payload</h5>
                  <pre className="text-sm bg-gray-100 p-3 rounded font-mono">
                    {selectedVuln.payload}
                  </pre>
                </div>
              )}

              <div>
                <h5 className="font-medium text-gray-900 mb-2">Evidence</h5>
                <p className="text-gray-600">{selectedVuln.evidence}</p>
              </div>

              <div>
                <h5 className="font-medium text-gray-900 mb-2">Recommendation</h5>
                <p className="text-gray-600">{selectedVuln.recommendation}</p>
              </div>

              <div className="flex justify-end pt-4 border-t">
                <button
                  onClick={() => setShowVulnModal(false)}
                  className="btn-primary"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  )
}
