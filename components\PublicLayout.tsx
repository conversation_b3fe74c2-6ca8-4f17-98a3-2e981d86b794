'use client'

import { <PERSON>actN<PERSON>, useEffect, useState } from 'react'
import { useTheme, useThemeClasses } from '@/contexts/ThemeContext'
import CyberHeader from './CyberHeader'
import CyberFooter from './CyberFooter'

interface PublicLayoutProps {
  children: ReactNode
  showHeader?: boolean
  showFooter?: boolean
  className?: string
}

interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  role: string
  plan: string
}

export default function PublicLayout({
  children,
  showHeader = true,
  showFooter = true,
  className = ''
}: PublicLayoutProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const { theme } = useTheme()
  const themeClasses = useThemeClasses()

  useEffect(() => {
    // Check if user is logged in
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token')
        const userData = localStorage.getItem('user')
        
        if (token && userData) {
          const parsedUser = JSON.parse(userData)
          setUser(parsedUser)
        }
      } catch (error) {
        console.error('Auth check error:', error)
        // Clear invalid data
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  if (loading) {
    return (
      <div className={`min-h-screen ${themeClasses.bgPrimary} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <div className="text-cyber-primary font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen ${themeClasses.bgPrimary} ${themeClasses.textPrimary} ${className}`}>
      {/* Cyber Grid Background - Only in dark mode */}
      {themeClasses.isDark && (
        <div className="fixed inset-0 pointer-events-none opacity-5 z-0">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }}
          ></div>
        </div>
      )}

      {/* Animated Background Elements - Only in dark mode */}
      {themeClasses.isDark && (
        <div className="fixed inset-0 pointer-events-none z-0">
          {/* Floating Particles */}
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-cyber-primary rounded-full animate-matrix-rain opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            ></div>
          ))}

          {/* Cyber Lines */}
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-20"></div>
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-secondary to-transparent opacity-20"></div>

          {/* Corner Accents */}
          <div className="absolute top-0 left-0 w-32 h-32 border-l-2 border-t-2 border-cyber-primary opacity-30"></div>
          <div className="absolute top-0 right-0 w-32 h-32 border-r-2 border-t-2 border-cyber-secondary opacity-30"></div>
          <div className="absolute bottom-0 left-0 w-32 h-32 border-l-2 border-b-2 border-cyber-accent opacity-30"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 border-r-2 border-b-2 border-cyber-primary opacity-30"></div>
        </div>
      )}

      {/* Light mode subtle background */}
      {themeClasses.isLight && (
        <div className="fixed inset-0 pointer-events-none opacity-30 z-0">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px)
              `,
              backgroundSize: '100px 100px'
            }}
          ></div>
        </div>
      )}

      {/* Content */}
      <div className="relative z-10">
        {showHeader && <CyberHeader user={user || undefined} />}
        
        <main className={showHeader ? 'pt-16' : ''}>
          {children}
        </main>
        
        {showFooter && <CyberFooter />}
      </div>

      {/* Cyber Scan Line Effect - Only in dark mode */}
      {themeClasses.isDark && (
        <div className="fixed top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyber-primary to-transparent opacity-50 animate-cyber-scan pointer-events-none z-20"></div>
      )}
    </div>
  )
}
