globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Toast.tsx":{"*":{"id":"(ssr)/./components/Toast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/ThemeContext.tsx":{"*":{"id":"(ssr)/./contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/osint/page.tsx":{"*":{"id":"(ssr)/./app/osint/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/users/page.tsx":{"*":{"id":"(ssr)/./app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/settings/page.tsx":{"*":{"id":"(ssr)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/plan/page.tsx":{"*":{"id":"(ssr)/./app/plan/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Users\\Downloads\\Kode-XGuard\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\components\\Toast.tsx":{"id":"(app-pages-browser)/./components/Toast.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\contexts\\ThemeContext.tsx":{"id":"(app-pages-browser)/./contexts/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\osint\\page.tsx":{"id":"(app-pages-browser)/./app/osint/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\admin\\users\\page.tsx":{"id":"(app-pages-browser)/./app/admin/users/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\admin\\settings\\page.tsx":{"id":"(app-pages-browser)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\plan\\page.tsx":{"id":"(app-pages-browser)/./app/plan/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Users\\Downloads\\Kode-XGuard\\":[],"D:\\Users\\Downloads\\Kode-XGuard\\app\\page":[],"D:\\Users\\Downloads\\Kode-XGuard\\app\\layout":["static/css/app/layout.css"],"D:\\Users\\Downloads\\Kode-XGuard\\app\\dashboard\\page":[]}}