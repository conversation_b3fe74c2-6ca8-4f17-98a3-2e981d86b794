export interface CVE {
  id: string
  cveId: string
  description: string
  severity: CVESeverity
  cvssScore: number
  cvssVector: string
  publishedDate: Date
  lastModified: Date
  references: CVEReference[]
  affectedProducts: AffectedProduct[]
  cweId?: string
  cweDescription?: string
  exploitAvailable: boolean
  patchAvailable: boolean
  tags: string[]
  source: string
  createdAt: Date
  updatedAt: Date
}

export enum CVESeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  NONE = 'none'
}

export interface CVEReference {
  url: string
  source: string
  tags: string[]
}

export interface AffectedProduct {
  vendor: string
  product: string
  version: string
  versionType: string
}

export interface CVESearchRequest {
  query?: string
  severity?: CVESeverity[]
  dateFrom?: Date
  dateTo?: Date
  hasExploit?: boolean
  hasPatch?: boolean
  vendor?: string
  product?: string
  page?: number
  limit?: number
  sortBy?: CVESortBy
  sortOrder?: SortOrder
}

export enum CVESortBy {
  PUBLISHED_DATE = 'publishedDate',
  CVSS_SCORE = 'cvssScore',
  LAST_MODIFIED = 'lastModified'
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

export interface CVESearchResponse {
  cves: CVE[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface CVEStats {
  total: number
  bySeverity: Record<CVESeverity, number>
  recentCount: number
  withExploits: number
  withPatches: number
}

export interface DorkPreset {
  id: string
  name: string
  description: string
  query: string
  category: DorkCategory
  tags: string[]
  isActive: boolean
  usageCount: number
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export enum DorkCategory {
  SENSITIVE_FILES = 'sensitive_files',
  LOGIN_PAGES = 'login_pages',
  DATABASE_DUMPS = 'database_dumps',
  CONFIG_FILES = 'config_files',
  ERROR_MESSAGES = 'error_messages',
  DIRECTORY_LISTING = 'directory_listing',
  VULNERABLE_APPS = 'vulnerable_apps',
  IOT_DEVICES = 'iot_devices'
}

export interface CustomDork {
  id: string
  userId: string
  name: string
  query: string
  description?: string
  tags: string[]
  isPrivate: boolean
  usageCount: number
  createdAt: Date
  updatedAt: Date
}
