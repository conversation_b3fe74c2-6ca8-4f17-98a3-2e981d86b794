import { NextRequest, NextResponse } from 'next/server'
import { advancedAPI } from '@/lib/api/advanced-api'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let apiKey: any = null

  try {
    // Authenticate API request
    const authResult = await advancedAPI.authenticate(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }

    apiKey = authResult.apiKey!

    // Check rate limit
    const rateLimit = await advancedAPI.checkRateLimit(apiKey, '/api/v1/crawl')
    if (!rateLimit.allowed) {
      const response = NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
      
      response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
      response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
      response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())
      
      return response
    }

    // Handle crawl request
    const response = await advancedAPI.handleCrawlRequest(request, apiKey)
    
    // Add rate limit headers
    response.headers.set('X-RateLimit-Limit', apiKey.rateLimit.requests.toString())
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
    response.headers.set('X-RateLimit-Reset', rateLimit.resetTime.toISOString())

    return response

  } catch (error) {
    console.error('API crawl error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  } finally {
    // Log API usage
    if (apiKey) {
      const responseTime = Date.now() - startTime
      const ipAddress = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       'unknown'
      const userAgent = request.headers.get('user-agent') || 'unknown'

      await advancedAPI.logAPIUsage(
        apiKey.id,
        '/api/v1/crawl',
        'POST',
        200, // This would be the actual response status
        responseTime,
        0, // Request size would be calculated
        0, // Response size would be calculated
        ipAddress,
        userAgent
      )
    }
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/v1/crawl',
    method: 'POST',
    description: 'Start a web crawling session',
    parameters: {
      url: 'string (required) - Target URL to crawl',
      depth: 'number (optional, 1-5) - Crawl depth, default: 2',
      maxPages: 'number (optional, 1-1000) - Maximum pages to crawl, default: 100',
      respectRobots: 'boolean (optional) - Respect robots.txt, default: true',
      delay: 'number (optional, 0-10000) - Delay between requests in ms, default: 1000'
    },
    example: {
      url: 'https://example.com',
      depth: 2,
      maxPages: 50,
      respectRobots: true,
      delay: 1000
    },
    response: {
      success: true,
      sessionId: 'string',
      message: 'Crawl session started',
      estimatedTime: 'string'
    }
  })
}
