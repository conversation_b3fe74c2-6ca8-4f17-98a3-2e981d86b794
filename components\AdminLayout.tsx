'use client'

import { useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { 
  Shield, 
  User, 
  Settings, 
  LogOut, 
  Menu, 
  X, 
  Bell,
  Search,
  Crown,
  Activity,
  BarChart3,
  Database,
  Bot,
  Users,
  Server,
  Lock,
  Eye,
  FileText,
  TrendingUp,
  Calendar,
  Clock,
  Flame,
  Star,
  ChevronDown,
  ChevronRight,
  Home,
  Cpu,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface AdminLayoutProps {
  children: ReactNode
}

interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  role: string
  plan: string
}

interface NavItem {
  name: string
  href: string
  icon: any
  badge?: string
  children?: NavItem[]
}

interface SystemAlert {
  id: string
  type: 'info' | 'warning' | 'error'
  message: string
  timestamp: string
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>(['System'])
  const [currentTime, setCurrentTime] = useState(new Date())
  const [systemStatus, setSystemStatus] = useState('online')
  const [alerts, setAlerts] = useState<SystemAlert[]>([])
  
  const router = useRouter()
  const pathname = usePathname()

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Load user and check auth
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token')
        const storedUser = localStorage.getItem('user')

        if (!token || !storedUser) {
          router.push('/login')
          return
        }

        // Mock admin user data
        const userData = {
          id: '1',
          username: 'AdminUser',
          email: '<EMAIL>',
          fullName: 'System Administrator',
          role: 'admin',
          plan: 'Elite'
        }
        
        setUser(userData)
        
        // Mock system alerts
        setAlerts([
          {
            id: '1',
            type: 'warning',
            message: 'High CPU usage detected on server-02',
            timestamp: '2 minutes ago'
          },
          {
            id: '2',
            type: 'info',
            message: 'Database backup completed successfully',
            timestamp: '15 minutes ago'
          }
        ])
        
        setLoading(false)
      } catch (error) {
        console.error('Auth check error:', error)
        router.push('/login')
      }
    }

    checkAuth()
  }, [router])

  const handleLogout = async () => {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    router.push('/login')
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    if (href === '/admin/dashboard') {
      return pathname === '/admin/dashboard'
    }
    return pathname.startsWith(href)
  }

  const adminNavItems: NavItem[] = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: BarChart3 },
    { name: 'Users', href: '/admin/users', icon: Users, badge: '15.4K' },
    { name: 'Bots', href: '/admin/bots', icon: Bot, badge: '4' },
    { name: 'Plans', href: '/admin/plans', icon: Crown, badge: 'New' },
    {
      name: 'System',
      href: '#',
      icon: Server,
      children: [
        { name: 'Monitoring', href: '/admin/monitoring', icon: Activity },
        { name: 'Logs', href: '/admin/logs', icon: FileText },
        { name: 'Security', href: '/admin/security', icon: Lock },
        { name: 'Database', href: '/admin/database', icon: Database }
      ]
    },
    { name: 'Settings', href: '/admin/settings', icon: Settings }
  ]

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <XCircle className="h-4 w-4 text-red-400" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'info': return <CheckCircle className="h-4 w-4 text-blue-400" />
      default: return <CheckCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error': return 'bg-red-500/10 border-red-500/30'
      case 'warning': return 'bg-yellow-500/10 border-yellow-500/30'
      case 'info': return 'bg-blue-500/10 border-blue-500/30'
      default: return 'bg-gray-500/10 border-gray-500/30'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cyber-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
          <div className="text-cyber-primary font-medium">Loading admin console...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-cyber-dark">
      {/* Admin Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border">
        <div className="flex items-center justify-between h-16 px-4">
          {/* Left Side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden"
            >
              {isSidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
            
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="h-8 w-8 text-cyber-primary animate-cyber-glow" />
                <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-cyber-glow">KodeXGuard</h1>
                <p className="text-xs text-cyber-secondary uppercase tracking-wider">
                  Admin Console
                </p>
              </div>
            </div>
          </div>

          {/* Center - System Status */}
          <div className="hidden md:flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${systemStatus === 'online' ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
              <span className="text-sm text-gray-300">System Online</span>
            </div>
            <div className="text-sm text-gray-400">
              <span className="text-cyber-primary">15,420</span> users online
            </div>
            <div className="text-sm text-gray-400">
              <span className="text-cyber-secondary">99.8%</span> uptime
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Time */}
            <div className="hidden sm:block text-xs text-gray-400 font-mono">
              {currentTime.toLocaleTimeString()}
            </div>

            {/* Alerts */}
            <div className="relative">
              <button className="p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative">
                <Bell className="h-5 w-5" />
                {alerts.length > 0 && (
                  <div className="absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full animate-pulse"></div>
                )}
              </button>
            </div>

            {/* Admin User Menu */}
            <div className="relative">
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center text-sm font-bold text-black">
                  A
                </div>
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-white">{user.username}</div>
                  <div className="text-xs text-yellow-400">Administrator</div>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-400" />
              </button>

              {/* Admin Dropdown */}
              {isProfileOpen && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50">
                  <div className="p-4 border-b border-cyber-border">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center text-lg font-bold text-black">
                        A
                      </div>
                      <div>
                        <div className="font-medium text-white">{user.fullName}</div>
                        <div className="text-sm text-gray-400">{user.email}</div>
                        <div className="text-xs text-yellow-400 font-medium">System Administrator</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-2">
                    <button
                      onClick={() => router.push('/admin/settings')}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors"
                    >
                      <Settings className="h-4 w-4 text-cyber-primary" />
                      <span>Admin Settings</span>
                    </button>
                    <button
                      onClick={() => router.push('/dashboard')}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors"
                    >
                      <User className="h-4 w-4 text-cyber-primary" />
                      <span>User Dashboard</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* System Alerts Bar */}
        {alerts.length > 0 && (
          <div className="border-t border-cyber-border bg-cyber-card/50">
            <div className="px-4 py-2">
              <div className="flex items-center space-x-4 overflow-x-auto">
                {alerts.slice(0, 3).map((alert) => (
                  <div
                    key={alert.id}
                    className={`flex items-center space-x-2 px-3 py-1 rounded-lg border ${getAlertColor(alert.type)} whitespace-nowrap`}
                  >
                    {getAlertIcon(alert.type)}
                    <span className="text-sm text-white">{alert.message}</span>
                    <span className="text-xs text-gray-400">{alert.timestamp}</span>
                  </div>
                ))}
                {alerts.length > 3 && (
                  <button className="text-xs text-cyber-primary hover:text-cyber-secondary transition-colors">
                    +{alerts.length - 3} more alerts
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Admin Sidebar */}
      <aside className={`fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 ${alerts.length > 0 ? 'top-24' : 'top-16'}`}>
        <div className="flex flex-col h-full">
          {/* Admin Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {adminNavItems.map((item) => {
              const Icon = item.icon
              const hasChildren = item.children && item.children.length > 0
              const isExpanded = expandedItems.includes(item.name)
              const itemIsActive = hasChildren ? item.children?.some(child => isActive(child.href)) : isActive(item.href)

              return (
                <div key={item.name}>
                  {hasChildren ? (
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                        itemIsActive
                          ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                          : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-5 w-5" />
                        <span>{item.name}</span>
                      </div>
                      <ChevronRight className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`} />
                    </button>
                  ) : (
                    <button
                      onClick={() => router.push(item.href)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${
                        itemIsActive
                          ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                          : 'text-gray-300 hover:text-white hover:bg-cyber-primary/10'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{item.name}</span>
                      {item.badge && (
                        <span className="ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold">
                          {item.badge}
                        </span>
                      )}
                    </button>
                  )}

                  {/* Admin Submenu */}
                  {hasChildren && isExpanded && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.children?.map((child) => {
                        const ChildIcon = child.icon
                        return (
                          <button
                            key={child.name}
                            onClick={() => router.push(child.href)}
                            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${
                              isActive(child.href)
                                ? 'bg-cyber-primary/20 text-cyber-primary'
                                : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                            }`}
                          >
                            <ChildIcon className="h-4 w-4" />
                            <span>{child.name}</span>
                          </button>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            })}
          </nav>

          {/* Admin Sidebar Footer */}
          <div className="p-4 border-t border-cyber-border">
            <div className="space-y-3">
              <div className="text-center">
                <div className="text-xs text-gray-400 mb-2">Admin Console</div>
                <div className="flex items-center justify-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${systemStatus === 'online' ? 'bg-green-400' : 'bg-red-400'} animate-pulse`}></div>
                  <span className="text-xs text-gray-300 capitalize">{systemStatus}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-center">
                  <div className="text-cyber-primary font-bold">15.4K</div>
                  <div className="text-gray-400">Users</div>
                </div>
                <div className="text-center">
                  <div className="text-cyber-secondary font-bold">99.8%</div>
                  <div className="text-gray-400">Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Admin Main Content */}
      <main className={`transition-all duration-300 ${alerts.length > 0 ? 'pt-24' : 'pt-16'} ${isSidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>
        <div className="min-h-[calc(100vh-4rem)]">
          <div className="p-6">
            {children}
          </div>
        </div>

        {/* Admin Footer */}
        <footer className="bg-cyber-card border-t border-cyber-border">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Admin Brand */}
              <div className="col-span-1 md:col-span-2">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="relative">
                    <Shield className="h-6 w-6 text-cyber-primary animate-cyber-glow" />
                    <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-cyber-glow">KodeXGuard Admin</h3>
                    <p className="text-xs text-cyber-secondary uppercase tracking-wider">
                      System Administration Console
                    </p>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mb-4">
                  Comprehensive admin console for managing users, monitoring system health,
                  and configuring platform settings.
                </p>
              </div>

              {/* System Status */}
              <div>
                <h4 className="text-sm font-bold text-white mb-4 uppercase tracking-wider">System Status</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span className="text-gray-400">Database:</span>
                    <span className="text-green-400">Online</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">API:</span>
                    <span className="text-green-400">Healthy</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Storage:</span>
                    <span className="text-yellow-400">78%</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Memory:</span>
                    <span className="text-green-400">67%</span>
                  </li>
                </ul>
              </div>

              {/* Quick Stats */}
              <div>
                <h4 className="text-sm font-bold text-white mb-4 uppercase tracking-wider">Quick Stats</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span className="text-gray-400">Active Users:</span>
                    <span className="text-cyber-primary">1,247</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Total Scans:</span>
                    <span className="text-cyber-secondary">89.4K</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Revenue:</span>
                    <span className="text-cyber-accent">$165K</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-400">Alerts:</span>
                    <span className="text-red-400">{alerts.length}</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-cyber-border">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="text-sm text-gray-400">
                  © 2024 KodeXGuard Admin Console. All rights reserved.
                </div>
                <div className="flex items-center space-x-4 mt-4 md:mt-0">
                  <span className="text-xs text-gray-500">
                    Admin Session: {currentTime.toLocaleTimeString()}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Wifi className="h-4 w-4 text-green-400" />
                    <span className="text-xs text-green-400">Secure Connection</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  )
}
