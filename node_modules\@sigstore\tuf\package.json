{"name": "@sigstore/tuf", "version": "1.0.3", "description": "Client for the Sigstore TUF repository", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "shx rm -rf dist *.tsbuildinfo", "build": "tsc --build", "test": "jest"}, "files": ["dist", "store"], "author": "<EMAIL>", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/sigstore/sigstore-js.git"}, "bugs": {"url": "https://github.com/sigstore/sigstore-js/issues"}, "homepage": "https://github.com/sigstore/sigstore-js/tree/main/packages/tuf#readme", "publishConfig": {"provenance": true}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@tufjs/repo-mock": "^1.1.0", "@types/make-fetch-happen": "^10.0.0"}, "dependencies": {"@sigstore/protobuf-specs": "^0.2.0", "tuf-js": "^1.1.7"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}