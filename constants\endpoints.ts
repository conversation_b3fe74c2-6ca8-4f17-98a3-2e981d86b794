// API Endpoints Configuration
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    VERIFY_EMAIL: '/api/auth/verify-email',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
  },

  // User Management
  USERS: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    CHANGE_PASSWORD: '/api/users/change-password',
    UPLOAD_AVATAR: '/api/users/avatar',
    STATS: '/api/users/stats',
    ACTIVITY: '/api/users/activity',
  },

  // API Keys
  API_KEYS: {
    LIST: '/api/api-keys',
    CREATE: '/api/api-keys',
    UPDATE: '/api/api-keys',
    DELETE: '/api/api-keys',
    REGENERATE: '/api/api-keys/regenerate',
  },

  // OSINT
  OSINT: {
    SEARCH: '/api/osint/search',
    PHONE: '/api/osint/phone',
    EMAIL: '/api/osint/email',
    DOMAIN: '/api/osint/domain',
    IP: '/api/osint/ip',
    NIK: '/api/osint/nik',
    NPWP: '/api/osint/npwp',
    IMEI: '/api/osint/imei',
    HISTORY: '/api/osint/history',
  },

  // Vulnerability Scanner
  SCANNER: {
    SCAN: '/api/scanner/scan',
    RESULTS: '/api/scanner/results',
    HISTORY: '/api/scanner/history',
    REPORT: '/api/scanner/report',
    STOP: '/api/scanner/stop',
  },

  // File Analyzer
  FILE_ANALYZER: {
    UPLOAD: '/api/file-analyzer/upload',
    ANALYZE: '/api/file-analyzer/analyze',
    RESULTS: '/api/file-analyzer/results',
    DOWNLOAD: '/api/file-analyzer/download',
    HISTORY: '/api/file-analyzer/history',
  },

  // CVE Intelligence
  CVE: {
    SEARCH: '/api/cve/search',
    DETAILS: '/api/cve/details',
    LATEST: '/api/cve/latest',
    STATS: '/api/cve/stats',
    EXPORT: '/api/cve/export',
  },

  // Google Dorking
  DORKING: {
    PRESETS: '/api/dorking/presets',
    CUSTOM: '/api/dorking/custom',
    SEARCH: '/api/dorking/search',
    HISTORY: '/api/dorking/history',
  },

  // Tools
  TOOLS: {
    HASH: '/api/tools/hash',
    ENCODE: '/api/tools/encode',
    DECODE: '/api/tools/decode',
    PAYLOAD: '/api/tools/payload',
  },

  // Bot Management
  BOTS: {
    WHATSAPP: {
      CONNECT: '/api/bots/whatsapp/connect',
      DISCONNECT: '/api/bots/whatsapp/disconnect',
      STATUS: '/api/bots/whatsapp/status',
      QR: '/api/bots/whatsapp/qr',
    },
    TELEGRAM: {
      CONNECT: '/api/bots/telegram/connect',
      DISCONNECT: '/api/bots/telegram/disconnect',
      STATUS: '/api/bots/telegram/status',
      WEBHOOK: '/api/bots/telegram/webhook',
    },
  },

  // Plans & Subscriptions
  PLANS: {
    LIST: '/api/plans',
    DETAILS: '/api/plans',
    SUBSCRIBE: '/api/plans/subscribe',
    CANCEL: '/api/plans/cancel',
    UPGRADE: '/api/plans/upgrade',
  },

  // Payments
  PAYMENTS: {
    CREATE: '/api/payments/create',
    VERIFY: '/api/payments/verify',
    HISTORY: '/api/payments/history',
    WEBHOOK: '/api/payments/webhook',
  },

  // Leaderboard
  LEADERBOARD: {
    TOP_HUNTERS: '/api/leaderboard/hunters',
    TOP_SCANNERS: '/api/leaderboard/scanners',
    MONTHLY: '/api/leaderboard/monthly',
    YEARLY: '/api/leaderboard/yearly',
  },

  // Admin
  ADMIN: {
    DASHBOARD: '/api/admin/dashboard',
    USERS: '/api/admin/users',
    SYSTEM: '/api/admin/system',
    MAINTENANCE: '/api/admin/maintenance',
    BACKUP: '/api/admin/backup',
    AUDIT: '/api/admin/audit',
    BOTS: '/api/admin/bots',
    PLANS: '/api/admin/plans',
    PAYMENTS: '/api/admin/payments',
  },
}

// Rate Limiting Configuration
export const RATE_LIMITS = {
  // Per minute limits
  AUTH: 5,
  OSINT: 10,
  SCANNER: 5,
  FILE_ANALYZER: 3,
  CVE: 20,
  DORKING: 10,
  TOOLS: 30,
  API_GENERAL: 100,
}

// File Upload Limits
export const UPLOAD_LIMITS = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  ALLOWED_TYPES: [
    'application/pdf',
    'application/zip',
    'application/x-zip-compressed',
    'text/plain',
    'text/html',
    'text/css',
    'text/javascript',
    'application/javascript',
    'application/json',
    'application/xml',
    'text/xml',
    'application/php',
    'text/x-php',
    'application/x-httpd-php',
    'application/x-sh',
    'text/x-shellscript',
    'application/x-python-code',
    'text/x-python',
    'application/java-archive',
    'application/x-java-archive',
    'application/vnd.android.package-archive',
  ],
  SCAN_TIMEOUT: 300000, // 5 minutes
}

// External API Endpoints (for integrations)
export const EXTERNAL_APIS = {
  CVE_MITRE: 'https://cve.mitre.org/cgi-bin/cvename.cgi?name=',
  NVD: 'https://nvd.nist.gov/vuln/detail/',
  EXPLOIT_DB: 'https://www.exploit-db.com/exploits/',
  SHODAN: 'https://api.shodan.io',
  VIRUSTOTAL: 'https://www.virustotal.com/vtapi/v2',
}
