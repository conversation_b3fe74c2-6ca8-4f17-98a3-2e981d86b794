import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { db } from './database'
import { NextRequest } from 'next/server'

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: 'user' | 'admin' | 'moderator'
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak_days: number
  email_verified: boolean
  last_active: Date
  created_at: Date
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface TokenPayload {
  userId: number
  email: string
  role: string
  plan: string
  iat?: number
  exp?: number
}

// Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h'
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d'
const BCRYPT_ROUNDS = 12

export class AuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, BCRYPT_ROUNDS)
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash)
  }

  // Generate JWT tokens
  static generateTokens(user: User): AuthTokens {
    const payload: TokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      plan: user.plan
    }

    const accessToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'kodexguard',
      audience: 'kodexguard-users'
    })

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      JWT_SECRET,
      {
        expiresIn: REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'kodexguard',
        audience: 'kodexguard-users'
      }
    )

    return {
      accessToken,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds
    }
  }

  // Register new user
  static async register(userData: {
    username: string
    email: string
    password: string
    fullName?: string
  }): Promise<{ user: User; tokens: AuthTokens } | { error: string }> {
    try {
      // Check if user already exists
      const existingUsers = await db.query(
        'SELECT id FROM users WHERE email = ? OR username = ?',
        [userData.email, userData.username]
      )

      if (existingUsers.length > 0) {
        return { error: 'User already exists with this email or username' }
      }

      // Hash password
      const passwordHash = await this.hashPassword(userData.password)

      // Create user
      const result = await db.query(
        `INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) 
         VALUES (?, ?, ?, ?, 'user', 'Free', 1, 0, 0, false, NOW(), NOW(), NOW())`,
        [userData.username, userData.email, passwordHash, userData.fullName || '']
      )

      const userId = (result as any).insertId

      // Get created user
      const users = await db.query('SELECT * FROM users WHERE id = ?', [userId])
      const user = users[0] as User

      // Generate tokens
      const tokens = this.generateTokens(user)

      return { user, tokens }
    } catch (error) {
      console.error('Registration error:', error)
      return { error: 'Registration failed' }
    }
  }

  // Login user
  static async login(
    email: string,
    password: string
  ): Promise<{ user: User; tokens: AuthTokens } | { error: string }> {
    try {
      // Find user by email
      const users = await db.query(
        'SELECT * FROM users WHERE email = ?',
        [email]
      )

      if (users.length === 0) {
        return { error: 'Invalid email or password' }
      }

      const userRecord = users[0] as User & { password_hash: string }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, userRecord.password_hash)
      if (!isValidPassword) {
        return { error: 'Invalid email or password' }
      }

      // Update last active
      await db.query('UPDATE users SET last_active = NOW() WHERE id = ?', [userRecord.id])

      // Remove password from user object
      const user: User = {
        id: userRecord.id,
        username: userRecord.username,
        email: userRecord.email,
        full_name: userRecord.full_name,
        role: userRecord.role,
        plan: userRecord.plan,
        level: userRecord.level,
        score: userRecord.score,
        streak_days: userRecord.streak_days,
        email_verified: userRecord.email_verified,
        last_active: userRecord.last_active,
        created_at: userRecord.created_at
      }

      // Generate tokens
      const tokens = this.generateTokens(user)

      return { user, tokens }
    } catch (error) {
      console.error('Login error:', error)
      return { error: 'Login failed' }
    }
  }

  // Authenticate request
  static async authenticateRequest(request: NextRequest): Promise<{ success: boolean; user?: User; message?: string }> {
    try {
      // Get token from Authorization header or cookies
      let token = request.headers.get('authorization')?.replace('Bearer ', '')
      
      if (!token) {
        // Try to get from cookies
        const cookies = request.headers.get('cookie')
        if (cookies) {
          const tokenMatch = cookies.match(/accessToken=([^;]+)/)
          token = tokenMatch ? tokenMatch[1] : null
        }
      }

      if (!token) {
        return { success: false, message: 'No token provided' }
      }

      // Verify token
      const decoded = jwt.verify(token, JWT_SECRET) as TokenPayload
      if (!decoded) {
        return { success: false, message: 'Invalid token' }
      }

      // Get user from database
      const users = await db.query('SELECT * FROM users WHERE id = ?', [decoded.userId])
      if (users.length === 0) {
        return { success: false, message: 'User not found' }
      }

      const user = users[0] as User

      return { success: true, user }
    } catch (error) {
      console.error('Authentication error:', error)
      return { success: false, message: 'Authentication failed' }
    }
  }

  // Get user by ID
  static async getUserById(userId: number): Promise<User | null> {
    try {
      const users = await db.query('SELECT * FROM users WHERE id = ?', [userId])
      return users.length > 0 ? users[0] as User : null
    } catch (error) {
      console.error('Get user error:', error)
      return null
    }
  }

  // Update user profile
  static async updateProfile(userId: number, profileData: {
    fullName?: string
    bio?: string
    avatar?: string
  }): Promise<boolean> {
    try {
      const updateFields: string[] = []
      const updateValues: any[] = []

      if (profileData.fullName !== undefined) {
        updateFields.push('full_name = ?')
        updateValues.push(profileData.fullName)
      }

      if (updateFields.length === 0) {
        return true // Nothing to update
      }

      updateFields.push('updated_at = NOW()')
      updateValues.push(userId)

      const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`
      await db.query(sql, updateValues)

      return true
    } catch (error) {
      console.error('Update profile error:', error)
      return false
    }
  }

  // Change password
  static async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get current user
      const users = await db.query('SELECT password_hash FROM users WHERE id = ?', [userId])
      if (users.length === 0) {
        return { success: false, error: 'User not found' }
      }

      const userRecord = users[0] as { password_hash: string }

      // Verify current password
      const isValid = await this.verifyPassword(currentPassword, userRecord.password_hash)
      if (!isValid) {
        return { success: false, error: 'Current password is incorrect' }
      }

      // Hash new password
      const newPasswordHash = await this.hashPassword(newPassword)

      // Update password
      await db.query(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
        [newPasswordHash, userId]
      )

      return { success: true }
    } catch (error) {
      console.error('Change password error:', error)
      return { success: false, error: 'Failed to change password' }
    }
  }

  // Verify JWT token
  static verifyToken(token: string): TokenPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as TokenPayload
    } catch (error) {
      return null
    }
  }

  // Generate random string
  static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}
