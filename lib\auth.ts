import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { db } from './database'
import { NextRequest } from 'next/server'

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: 'user' | 'admin' | 'moderator'
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  level: number
  score: number
  streak_days: number
  email_verified: boolean
  last_active: Date
  created_at: Date
}

export type UserRole = 'user' | 'admin' | 'moderator'
export type UserPlan = 'Free' | 'Pro' | 'Expert' | 'Elite'

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'
const REFRESH_TOKEN_EXPIRES_IN = '30d'

// Password configuration
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')

export interface TokenPayload {
  userId: number
  email: string
  role: UserRole
  plan: UserPlan
  iat?: number
  exp?: number
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export class AuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, BCRYPT_ROUNDS)
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash)
  }

  // Generate JWT tokens
  static generateTokens(user: User): AuthTokens {
    const payload: TokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      plan: user.plan
    }

    const accessToken = jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'kodexguard',
      audience: 'kodexguard-users'
    })

    const refreshToken = jwt.sign(
      { userId: user.id, type: 'refresh' },
      JWT_SECRET,
      {
        expiresIn: REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'kodexguard',
        audience: 'kodexguard-users'
      }
    )

    // Calculate expiration time in seconds
    const decoded = jwt.decode(accessToken) as any
    const expiresIn = decoded.exp - Math.floor(Date.now() / 1000)

    return {
      accessToken,
      refreshToken,
      expiresIn
    }
  }

  // Verify JWT token
  static verifyToken(token: string): TokenPayload | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET, {
        issuer: 'kodexguard',
        audience: 'kodexguard-users'
      }) as TokenPayload

      return decoded
    } catch (error) {
      console.error('Token verification failed:', error)
      return null
    }
  }

  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, BCRYPT_ROUNDS)
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  // Register new user
  static async register(userData: {
    username: string
    email: string
    password: string
    fullName: string
  }): Promise<{ user: User; tokens: AuthTokens } | { error: string }> {
    try {
      // Check if user already exists
      const existingUser = await DatabaseUtils.findOne(
        'SELECT id FROM users WHERE email = ? OR username = ?',
        [userData.email, userData.username]
      )

      if (existingUser) {
        return { error: 'User with this email or username already exists' }
      }

      // Hash password
      const passwordHash = await this.hashPassword(userData.password)

      // Generate user ID
      const userId = this.generateUUID()

      // Create user
      await DatabaseUtils.insert('users', {
        id: userId,
        username: userData.username,
        email: userData.email,
        password_hash: passwordHash,
        full_name: userData.fullName,
        role: UserRole.USER,
        plan: UserPlan.FREE,
        is_active: true,
        email_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      })

      // Create user stats
      await DatabaseUtils.insert('user_stats', {
        id: this.generateUUID(),
        user_id: userId,
        total_scans: 0,
        vulnerabilities_found: 0,
        files_analyzed: 0,
        osint_queries: 0,
        api_calls: 0,
        score: 0,
        rank_position: 0,
        created_at: new Date(),
        updated_at: new Date()
      })

      // Get created user
      const user = await this.getUserById(userId)
      if (!user) {
        return { error: 'Failed to create user' }
      }

      // Generate tokens
      const tokens = this.generateTokens(user)

      // Store refresh token in Redis
      await RedisUtils.set(
        `refresh_token:${userId}`,
        tokens.refreshToken,
        30 * 24 * 60 * 60 // 30 days
      )

      return { user, tokens }
    } catch (error) {
      console.error('Registration error:', error)
      return { error: 'Registration failed' }
    }
  }

  // Login user
  static async login(
    email: string,
    password: string
  ): Promise<{ user: User; tokens: AuthTokens } | { error: string }> {
    try {
      // Find user by email
      const userRecord = await DatabaseUtils.findOne(
        'SELECT * FROM users WHERE email = ? AND is_active = true',
        [email]
      )

      if (!userRecord) {
        return { error: 'Invalid email or password' }
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, userRecord.password_hash)
      if (!isValidPassword) {
        return { error: 'Invalid email or password' }
      }

      // Update last login
      await DatabaseUtils.update(
        'users',
        { last_login: new Date() },
        'id = ?',
        [userRecord.id]
      )

      // Get user with stats
      const user = await this.getUserById(userRecord.id)
      if (!user) {
        return { error: 'User not found' }
      }

      // Generate tokens
      const tokens = this.generateTokens(user)

      // Store refresh token in Redis
      await RedisUtils.set(
        `refresh_token:${user.id}`,
        tokens.refreshToken,
        30 * 24 * 60 * 60 // 30 days
      )

      return { user, tokens }
    } catch (error) {
      console.error('Login error:', error)
      return { error: 'Login failed' }
    }
  }

  // Refresh access token
  static async refreshToken(refreshToken: string): Promise<{ tokens: AuthTokens } | { error: string }> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, JWT_SECRET) as any
      
      if (decoded.type !== 'refresh') {
        return { error: 'Invalid refresh token' }
      }

      // Check if refresh token exists in Redis
      const storedToken = await RedisUtils.get(`refresh_token:${decoded.userId}`)
      if (storedToken !== refreshToken) {
        return { error: 'Invalid refresh token' }
      }

      // Get user
      const user = await this.getUserById(decoded.userId)
      if (!user) {
        return { error: 'User not found' }
      }

      // Generate new tokens
      const tokens = this.generateTokens(user)

      // Update refresh token in Redis
      await RedisUtils.set(
        `refresh_token:${user.id}`,
        tokens.refreshToken,
        30 * 24 * 60 * 60 // 30 days
      )

      return { tokens }
    } catch (error) {
      console.error('Refresh token error:', error)
      return { error: 'Invalid refresh token' }
    }
  }

  // Logout user
  static async logout(userId: string): Promise<boolean> {
    try {
      // Remove refresh token from Redis
      await RedisUtils.del(`refresh_token:${userId}`)
      return true
    } catch (error) {
      console.error('Logout error:', error)
      return false
    }
  }

  // Get user by ID with stats
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userRecord = await DatabaseUtils.findOne(`
        SELECT 
          u.*,
          us.total_scans,
          us.vulnerabilities_found,
          us.files_analyzed,
          us.osint_queries,
          us.api_calls,
          us.score,
          us.rank_position
        FROM users u
        LEFT JOIN user_stats us ON u.id = us.user_id
        WHERE u.id = ? AND u.is_active = true
      `, [userId])

      if (!userRecord) {
        return null
      }

      // Get user API keys
      const apiKeys = await DatabaseUtils.query(
        'SELECT * FROM api_keys WHERE user_id = ? AND is_active = true',
        [userId]
      )

      return {
        id: userRecord.id,
        username: userRecord.username,
        email: userRecord.email,
        fullName: userRecord.full_name,
        avatar: userRecord.avatar,
        bio: userRecord.bio,
        role: userRecord.role as UserRole,
        plan: userRecord.plan as UserPlan,
        planExpiry: userRecord.plan_expiry ? new Date(userRecord.plan_expiry) : undefined,
        isActive: userRecord.is_active,
        emailVerified: userRecord.email_verified,
        createdAt: new Date(userRecord.created_at),
        updatedAt: new Date(userRecord.updated_at),
        lastLogin: userRecord.last_login ? new Date(userRecord.last_login) : undefined,
        apiKeys: apiKeys || [],
        stats: {
          totalScans: userRecord.total_scans || 0,
          vulnerabilitiesFound: userRecord.vulnerabilities_found || 0,
          filesAnalyzed: userRecord.files_analyzed || 0,
          osintQueries: userRecord.osint_queries || 0,
          apiCalls: userRecord.api_calls || 0,
          score: userRecord.score || 0,
          rank: userRecord.rank_position || 0
        }
      }
    } catch (error) {
      console.error('Get user error:', error)
      return null
    }
  }

  // Generate API Key
  static async generateApiKey(userId: string, name: string, permissions: string[] = []): Promise<{ apiKey: string; keyId: string } | { error: string }> {
    try {
      // Generate API key
      const keyId = this.generateUUID()
      const apiKey = `kxg_${this.generateRandomString(32)}`
      const keyHash = await this.hashPassword(apiKey)
      const keyPrefix = apiKey.substring(0, 12)

      // Insert API key
      await DatabaseUtils.insert('api_keys', {
        id: keyId,
        user_id: userId,
        name,
        key_hash: keyHash,
        key_prefix: keyPrefix,
        permissions: JSON.stringify(permissions),
        is_active: true,
        usage_count: 0,
        rate_limit: 100,
        created_at: new Date(),
        updated_at: new Date()
      })

      return { apiKey, keyId }
    } catch (error) {
      console.error('Generate API key error:', error)
      return { error: 'Failed to generate API key' }
    }
  }

  // Verify API Key
  static async verifyApiKey(apiKey: string): Promise<{ user: User; keyId: string } | null> {
    try {
      const keyPrefix = apiKey.substring(0, 12)

      // Find API key by prefix
      const keyRecord = await DatabaseUtils.findOne(
        'SELECT * FROM api_keys WHERE key_prefix = ? AND is_active = true',
        [keyPrefix]
      )

      if (!keyRecord) {
        return null
      }

      // Verify API key hash
      const isValid = await this.verifyPassword(apiKey, keyRecord.key_hash)
      if (!isValid) {
        return null
      }

      // Update usage count and last used
      await DatabaseUtils.update(
        'api_keys',
        {
          usage_count: keyRecord.usage_count + 1,
          last_used: new Date(),
          updated_at: new Date()
        },
        'id = ?',
        [keyRecord.id]
      )

      // Get user
      const user = await this.getUserById(keyRecord.user_id)
      if (!user) {
        return null
      }

      return { user, keyId: keyRecord.id }
    } catch (error) {
      console.error('Verify API key error:', error)
      return null
    }
  }

  // Update user profile
  static async updateProfile(userId: string, profileData: {
    fullName?: string
    bio?: string
    avatar?: string
  }): Promise<boolean> {
    try {
      const updateData: any = {
        updated_at: new Date()
      }

      if (profileData.fullName) updateData.full_name = profileData.fullName
      if (profileData.bio !== undefined) updateData.bio = profileData.bio
      if (profileData.avatar !== undefined) updateData.avatar = profileData.avatar

      return await DatabaseUtils.update('users', updateData, 'id = ?', [userId])
    } catch (error) {
      console.error('Update profile error:', error)
      return false
    }
  }

  // Change password
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get current user
      const userRecord = await DatabaseUtils.findOne(
        'SELECT password_hash FROM users WHERE id = ?',
        [userId]
      )

      if (!userRecord) {
        return { success: false, error: 'User not found' }
      }

      // Verify current password
      const isValidPassword = await this.verifyPassword(currentPassword, userRecord.password_hash)
      if (!isValidPassword) {
        return { success: false, error: 'Current password is incorrect' }
      }

      // Hash new password
      const newPasswordHash = await this.hashPassword(newPassword)

      // Update password
      const updated = await DatabaseUtils.update(
        'users',
        { password_hash: newPasswordHash, updated_at: new Date() },
        'id = ?',
        [userId]
      )

      if (updated) {
        // Invalidate all refresh tokens for this user
        await RedisUtils.del(`refresh_token:${userId}`)
        return { success: true }
      }

      return { success: false, error: 'Failed to update password' }
    } catch (error) {
      console.error('Change password error:', error)
      return { success: false, error: 'Failed to change password' }
    }
  }

  // Delete API key
  static async deleteApiKey(userId: string, keyId: string): Promise<boolean> {
    try {
      return await DatabaseUtils.update(
        'api_keys',
        { is_active: false, updated_at: new Date() },
        'id = ? AND user_id = ?',
        [keyId, userId]
      )
    } catch (error) {
      console.error('Delete API key error:', error)
      return false
    }
  }

  // Get user API keys
  static async getUserApiKeys(userId: string): Promise<any[]> {
    try {
      return await DatabaseUtils.query(
        'SELECT id, name, key_prefix, permissions, is_active, usage_count, rate_limit, last_used, created_at FROM api_keys WHERE user_id = ? ORDER BY created_at DESC',
        [userId]
      )
    } catch (error) {
      console.error('Get API keys error:', error)
      return []
    }
  }

  // Check rate limit
  static async checkRateLimit(userId: string, keyId?: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    try {
      const key = keyId ? `rate_limit:api:${keyId}` : `rate_limit:user:${userId}`
      const window = 15 * 60 // 15 minutes
      const limit = 100 // requests per window

      const current = await RedisUtils.incr(key, window)
      const remaining = Math.max(0, limit - current)
      const resetTime = Math.floor(Date.now() / 1000) + window

      return {
        allowed: current <= limit,
        remaining,
        resetTime
      }
    } catch (error) {
      console.error('Rate limit check error:', error)
      return { allowed: true, remaining: 100, resetTime: 0 }
    }
  }

  // Generate random string
  static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // Generate UUID
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}
