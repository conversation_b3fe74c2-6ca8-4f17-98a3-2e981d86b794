'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/DashboardLayout'
import { Shield, ArrowRight } from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to admin dashboard after a short delay
    const timer = setTimeout(() => {
      router.push('/admin/dashboard')
    }, 2000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <DashboardLayout>
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <Shield className="h-24 w-24 text-cyber-primary mx-auto animate-cyber-glow" />
            <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
          </div>
          
          <h1 className="text-4xl font-bold mb-4">
            <span className="text-cyber-glow">Admin</span>{' '}
            <span className="text-cyber-pink">Access</span>
          </h1>
          
          <p className="text-gray-300 text-lg mb-8">
            Redirecting to admin dashboard...
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-cyber-primary">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-cyber-primary border-t-transparent"></div>
            <span>Loading admin console</span>
            <ArrowRight className="h-5 w-5 animate-pulse" />
          </div>
          
          <div className="mt-8">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="btn-cyber-primary"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
