import { Client } from '@elastic/elasticsearch'
import { MeiliSearch } from 'meilisearch'
import { db } from '../database'

export interface SearchDocument {
  id: string
  type: 'vulnerability' | 'breach' | 'malware' | 'host' | 'domain' | 'ip' | 'email' | 'cve'
  title: string
  content: string
  metadata: Record<string, any>
  tags: string[]
  severity?: 'low' | 'medium' | 'high' | 'critical'
  timestamp: Date
  source: string
  indexed_at: Date
}

export interface SearchQuery {
  query: string
  filters?: {
    type?: string[]
    severity?: string[]
    dateRange?: {
      from: Date
      to: Date
    }
    tags?: string[]
    source?: string[]
  }
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  pagination?: {
    page: number
    size: number
  }
  highlight?: boolean
  facets?: string[]
}

export interface SearchResult {
  id: string
  type: string
  title: string
  content: string
  metadata: Record<string, any>
  score: number
  highlights?: Record<string, string[]>
  timestamp: Date
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  took: number
  facets?: Record<string, Record<string, number>>
  suggestions?: string[]
}

export interface IndexStats {
  totalDocuments: number
  indexSize: string
  lastUpdated: Date
  documentsPerType: Record<string, number>
}

export class SearchEngine {
  private elasticsearch?: Client
  private meilisearch?: MeiliSearch
  private engine: 'elasticsearch' | 'meilisearch'
  private indices: Map<string, string> = new Map()

  constructor() {
    this.engine = (process.env.SEARCH_ENGINE as any) || 'meilisearch'
    this.initializeEngine()
  }

  private async initializeEngine() {
    console.log(`🔍 Search Engine initializing with ${this.engine}`)
    
    try {
      if (this.engine === 'elasticsearch') {
        await this.initializeElasticsearch()
      } else {
        await this.initializeMeilisearch()
      }
      
      await this.createTables()
      await this.setupIndices()
      
      console.log('✅ Search Engine initialized successfully')
    } catch (error) {
      console.error('❌ Search Engine initialization failed:', error)
      // Fallback to database search
      console.log('🔄 Falling back to database search')
    }
  }

  private async initializeElasticsearch() {
    this.elasticsearch = new Client({
      node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
      auth: process.env.ELASTICSEARCH_AUTH ? {
        username: process.env.ELASTICSEARCH_USER || 'elastic',
        password: process.env.ELASTICSEARCH_PASSWORD || 'changeme'
      } : undefined
    })

    // Test connection
    await this.elasticsearch.ping()
    console.log('✅ Elasticsearch connected')
  }

  private async initializeMeilisearch() {
    this.meilisearch = new MeiliSearch({
      host: process.env.MEILISEARCH_URL || 'http://localhost:7700',
      apiKey: process.env.MEILISEARCH_API_KEY
    })

    // Test connection
    await this.meilisearch.health()
    console.log('✅ Meilisearch connected')
  }

  private async createTables() {
    try {
      await db.query(`
        CREATE TABLE IF NOT EXISTS search_indices (
          id VARCHAR(36) PRIMARY KEY,
          name VARCHAR(100) UNIQUE NOT NULL,
          type VARCHAR(50) NOT NULL,
          settings JSON,
          mappings JSON,
          document_count BIGINT DEFAULT 0,
          size_bytes BIGINT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `)

      await db.query(`
        CREATE TABLE IF NOT EXISTS search_queries (
          id VARCHAR(36) PRIMARY KEY,
          user_id INT,
          query_text TEXT NOT NULL,
          filters JSON,
          results_count INT DEFAULT 0,
          response_time_ms INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_created_at (created_at)
        )
      `)

      console.log('✅ Search Engine tables created')
    } catch (error) {
      console.error('❌ Error creating search tables:', error)
    }
  }

  private async setupIndices() {
    const indices = [
      {
        name: 'vulnerabilities',
        type: 'vulnerability',
        settings: {
          searchableAttributes: ['title', 'content', 'cve_id', 'description'],
          filterableAttributes: ['severity', 'type', 'source', 'timestamp'],
          sortableAttributes: ['timestamp', 'severity', 'cvss_score']
        }
      },
      {
        name: 'breaches',
        type: 'breach',
        settings: {
          searchableAttributes: ['name', 'domain', 'description', 'email'],
          filterableAttributes: ['breach_date', 'data_classes', 'is_verified'],
          sortableAttributes: ['breach_date', 'pwn_count']
        }
      },
      {
        name: 'malware',
        type: 'malware',
        settings: {
          searchableAttributes: ['file_name', 'threat_type', 'family', 'description'],
          filterableAttributes: ['threat_detected', 'severity', 'confidence_score'],
          sortableAttributes: ['timestamp', 'confidence_score']
        }
      },
      {
        name: 'hosts',
        type: 'host',
        settings: {
          searchableAttributes: ['ip', 'hostname', 'os', 'services'],
          filterableAttributes: ['status', 'country', 'ports', 'last_seen'],
          sortableAttributes: ['last_seen', 'port_count']
        }
      },
      {
        name: 'cves',
        type: 'cve',
        settings: {
          searchableAttributes: ['cve_id', 'description', 'affected_products'],
          filterableAttributes: ['severity', 'cvss_score', 'published_date', 'exploit_available'],
          sortableAttributes: ['published_date', 'cvss_score']
        }
      }
    ]

    for (const index of indices) {
      await this.createIndex(index.name, index.type, index.settings)
      this.indices.set(index.type, index.name)
    }
  }

  async createIndex(name: string, type: string, settings: any = {}): Promise<boolean> {
    try {
      if (this.engine === 'elasticsearch' && this.elasticsearch) {
        const exists = await this.elasticsearch.indices.exists({ index: name })
        
        if (!exists) {
          await this.elasticsearch.indices.create({
            index: name,
            body: {
              settings: {
                number_of_shards: 1,
                number_of_replicas: 0,
                analysis: {
                  analyzer: {
                    cybersecurity_analyzer: {
                      type: 'custom',
                      tokenizer: 'standard',
                      filter: ['lowercase', 'stop', 'snowball']
                    }
                  }
                }
              },
              mappings: {
                properties: {
                  title: { type: 'text', analyzer: 'cybersecurity_analyzer' },
                  content: { type: 'text', analyzer: 'cybersecurity_analyzer' },
                  type: { type: 'keyword' },
                  severity: { type: 'keyword' },
                  tags: { type: 'keyword' },
                  timestamp: { type: 'date' },
                  metadata: { type: 'object', enabled: false }
                }
              }
            }
          })
        }
      } else if (this.engine === 'meilisearch' && this.meilisearch) {
        const index = this.meilisearch.index(name)
        
        try {
          await index.getSettings()
        } catch (error) {
          // Index doesn't exist, create it
          await this.meilisearch.createIndex(name, { primaryKey: 'id' })
        }
        
        // Update settings
        await index.updateSettings(settings)
      }

      // Save index info to database
      await db.query(`
        INSERT INTO search_indices (id, name, type, settings, created_at)
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        settings = VALUES(settings),
        updated_at = NOW()
      `, [this.generateId(), name, type, JSON.stringify(settings)])

      console.log(`✅ Index '${name}' created/updated`)
      return true

    } catch (error) {
      console.error(`❌ Error creating index '${name}':`, error)
      return false
    }
  }

  async indexDocument(document: SearchDocument): Promise<boolean> {
    try {
      const indexName = this.indices.get(document.type) || 'default'
      
      if (this.engine === 'elasticsearch' && this.elasticsearch) {
        await this.elasticsearch.index({
          index: indexName,
          id: document.id,
          body: {
            ...document,
            indexed_at: new Date()
          }
        })
      } else if (this.engine === 'meilisearch' && this.meilisearch) {
        const index = this.meilisearch.index(indexName)
        await index.addDocuments([{
          ...document,
          indexed_at: new Date()
        }])
      }

      return true
    } catch (error) {
      console.error('Error indexing document:', error)
      return false
    }
  }

  async indexDocuments(documents: SearchDocument[]): Promise<number> {
    let successCount = 0
    
    // Group documents by type
    const documentsByType = new Map<string, SearchDocument[]>()
    
    for (const doc of documents) {
      if (!documentsByType.has(doc.type)) {
        documentsByType.set(doc.type, [])
      }
      documentsByType.get(doc.type)!.push(doc)
    }

    // Index documents by type
    for (const [type, docs] of documentsByType) {
      try {
        const indexName = this.indices.get(type) || 'default'
        
        if (this.engine === 'elasticsearch' && this.elasticsearch) {
          const body = docs.flatMap(doc => [
            { index: { _index: indexName, _id: doc.id } },
            { ...doc, indexed_at: new Date() }
          ])
          
          const response = await this.elasticsearch.bulk({ body })
          successCount += docs.length - (response.body.errors ? response.body.items.filter((item: any) => item.index.error).length : 0)
          
        } else if (this.engine === 'meilisearch' && this.meilisearch) {
          const index = this.meilisearch.index(indexName)
          await index.addDocuments(docs.map(doc => ({
            ...doc,
            indexed_at: new Date()
          })))
          successCount += docs.length
        }
      } catch (error) {
        console.error(`Error indexing documents for type ${type}:`, error)
      }
    }

    return successCount
  }

  async search(query: SearchQuery, userId?: number): Promise<SearchResponse> {
    const startTime = Date.now()
    
    try {
      let response: SearchResponse

      if (this.engine === 'elasticsearch' && this.elasticsearch) {
        response = await this.searchElasticsearch(query)
      } else if (this.engine === 'meilisearch' && this.meilisearch) {
        response = await this.searchMeilisearch(query)
      } else {
        // Fallback to database search
        response = await this.searchDatabase(query)
      }

      const responseTime = Date.now() - startTime
      response.took = responseTime

      // Log search query
      await this.logSearchQuery(query, response.total, responseTime, userId)

      return response
    } catch (error) {
      console.error('Search error:', error)
      
      // Fallback to database search
      const response = await this.searchDatabase(query)
      response.took = Date.now() - startTime
      return response
    }
  }

  private async searchElasticsearch(query: SearchQuery): Promise<SearchResponse> {
    if (!this.elasticsearch) throw new Error('Elasticsearch not initialized')

    const searchBody: any = {
      query: {
        bool: {
          must: [
            {
              multi_match: {
                query: query.query,
                fields: ['title^2', 'content', 'metadata.*'],
                type: 'best_fields',
                fuzziness: 'AUTO'
              }
            }
          ],
          filter: []
        }
      },
      highlight: query.highlight ? {
        fields: {
          title: {},
          content: {}
        }
      } : undefined,
      sort: query.sort ? [
        { [query.sort.field]: { order: query.sort.order } }
      ] : [
        { _score: { order: 'desc' } },
        { timestamp: { order: 'desc' } }
      ],
      from: ((query.pagination?.page || 1) - 1) * (query.pagination?.size || 20),
      size: query.pagination?.size || 20
    }

    // Add filters
    if (query.filters) {
      if (query.filters.type) {
        searchBody.query.bool.filter.push({ terms: { type: query.filters.type } })
      }
      if (query.filters.severity) {
        searchBody.query.bool.filter.push({ terms: { severity: query.filters.severity } })
      }
      if (query.filters.tags) {
        searchBody.query.bool.filter.push({ terms: { tags: query.filters.tags } })
      }
      if (query.filters.dateRange) {
        searchBody.query.bool.filter.push({
          range: {
            timestamp: {
              gte: query.filters.dateRange.from,
              lte: query.filters.dateRange.to
            }
          }
        })
      }
    }

    // Add aggregations for facets
    if (query.facets) {
      searchBody.aggs = {}
      for (const facet of query.facets) {
        searchBody.aggs[facet] = {
          terms: { field: facet, size: 10 }
        }
      }
    }

    const response = await this.elasticsearch.search({
      index: Array.from(this.indices.values()),
      body: searchBody
    })

    return this.formatElasticsearchResponse(response.body)
  }

  private async searchMeilisearch(query: SearchQuery): Promise<SearchResponse> {
    if (!this.meilisearch) throw new Error('Meilisearch not initialized')

    const searchParams: any = {
      q: query.query,
      limit: query.pagination?.size || 20,
      offset: ((query.pagination?.page || 1) - 1) * (query.pagination?.size || 20),
      attributesToHighlight: query.highlight ? ['title', 'content'] : undefined,
      sort: query.sort ? [`${query.sort.field}:${query.sort.order}`] : undefined,
      facets: query.facets
    }

    // Add filters
    if (query.filters) {
      const filters: string[] = []
      
      if (query.filters.type) {
        filters.push(`type IN [${query.filters.type.map(t => `"${t}"`).join(', ')}]`)
      }
      if (query.filters.severity) {
        filters.push(`severity IN [${query.filters.severity.map(s => `"${s}"`).join(', ')}]`)
      }
      if (query.filters.tags) {
        filters.push(`tags IN [${query.filters.tags.map(t => `"${t}"`).join(', ')}]`)
      }
      if (query.filters.dateRange) {
        filters.push(`timestamp >= ${query.filters.dateRange.from.getTime()} AND timestamp <= ${query.filters.dateRange.to.getTime()}`)
      }
      
      if (filters.length > 0) {
        searchParams.filter = filters.join(' AND ')
      }
    }

    // Search across all indices
    const results: SearchResult[] = []
    let totalHits = 0
    const facets: Record<string, Record<string, number>> = {}

    for (const indexName of this.indices.values()) {
      try {
        const index = this.meilisearch.index(indexName)
        const response = await index.search(query.query, searchParams)
        
        results.push(...response.hits.map(hit => this.formatMeilisearchHit(hit)))
        totalHits += response.estimatedTotalHits || 0
        
        if (response.facetDistribution) {
          Object.assign(facets, response.facetDistribution)
        }
      } catch (error) {
        console.error(`Error searching index ${indexName}:`, error)
      }
    }

    // Sort results by score
    results.sort((a, b) => (b.score || 0) - (a.score || 0))

    return {
      results: results.slice(0, query.pagination?.size || 20),
      total: totalHits,
      took: 0, // Will be set by caller
      facets: Object.keys(facets).length > 0 ? facets : undefined
    }
  }

  private async searchDatabase(query: SearchQuery): Promise<SearchResponse> {
    // Fallback database search implementation
    const results: SearchResult[] = []
    
    // Search vulnerabilities
    const vulnResults = await db.query(`
      SELECT id, target_url as title, scan_results as content, 'vulnerability' as type, created_at as timestamp
      FROM vulnerability_scans 
      WHERE target_url LIKE ? OR scan_results LIKE ?
      LIMIT 10
    `, [`%${query.query}%`, `%${query.query}%`])

    results.push(...vulnResults.map((row: any) => ({
      id: row.id,
      type: row.type,
      title: row.title,
      content: typeof row.content === 'string' ? row.content : JSON.stringify(row.content),
      metadata: {},
      score: 1.0,
      timestamp: row.timestamp
    })))

    return {
      results,
      total: results.length,
      took: 0
    }
  }

  private formatElasticsearchResponse(response: any): SearchResponse {
    return {
      results: response.hits.hits.map((hit: any) => ({
        id: hit._id,
        type: hit._source.type,
        title: hit._source.title,
        content: hit._source.content,
        metadata: hit._source.metadata,
        score: hit._score,
        highlights: hit.highlight,
        timestamp: new Date(hit._source.timestamp)
      })),
      total: response.hits.total.value || response.hits.total,
      took: response.took,
      facets: response.aggregations ? this.formatElasticsearchFacets(response.aggregations) : undefined
    }
  }

  private formatMeilisearchHit(hit: any): SearchResult {
    return {
      id: hit.id,
      type: hit.type,
      title: hit.title,
      content: hit.content,
      metadata: hit.metadata || {},
      score: hit._rankingScore || 1.0,
      highlights: hit._formatted ? {
        title: hit._formatted.title ? [hit._formatted.title] : undefined,
        content: hit._formatted.content ? [hit._formatted.content] : undefined
      } : undefined,
      timestamp: new Date(hit.timestamp)
    }
  }

  private formatElasticsearchFacets(aggregations: any): Record<string, Record<string, number>> {
    const facets: Record<string, Record<string, number>> = {}
    
    for (const [key, agg] of Object.entries(aggregations)) {
      if ((agg as any).buckets) {
        facets[key] = {}
        for (const bucket of (agg as any).buckets) {
          facets[key][bucket.key] = bucket.doc_count
        }
      }
    }
    
    return facets
  }

  private async logSearchQuery(query: SearchQuery, resultsCount: number, responseTime: number, userId?: number) {
    try {
      await db.query(`
        INSERT INTO search_queries (id, user_id, query_text, filters, results_count, response_time_ms, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        this.generateId(),
        userId || null,
        query.query,
        JSON.stringify(query.filters || {}),
        resultsCount,
        responseTime
      ])
    } catch (error) {
      console.error('Error logging search query:', error)
    }
  }

  async deleteDocument(id: string, type: string): Promise<boolean> {
    try {
      const indexName = this.indices.get(type) || 'default'
      
      if (this.engine === 'elasticsearch' && this.elasticsearch) {
        await this.elasticsearch.delete({
          index: indexName,
          id: id
        })
      } else if (this.engine === 'meilisearch' && this.meilisearch) {
        const index = this.meilisearch.index(indexName)
        await index.deleteDocument(id)
      }

      return true
    } catch (error) {
      console.error('Error deleting document:', error)
      return false
    }
  }

  async getIndexStats(): Promise<IndexStats> {
    try {
      const stats: IndexStats = {
        totalDocuments: 0,
        indexSize: '0 MB',
        lastUpdated: new Date(),
        documentsPerType: {}
      }

      if (this.engine === 'elasticsearch' && this.elasticsearch) {
        const response = await this.elasticsearch.indices.stats({
          index: Array.from(this.indices.values())
        })
        
        stats.totalDocuments = Object.values(response.body.indices).reduce((total: number, index: any) => {
          return total + (index.total?.docs?.count || 0)
        }, 0)
        
        const totalSize = Object.values(response.body.indices).reduce((total: number, index: any) => {
          return total + (index.total?.store?.size_in_bytes || 0)
        }, 0)
        
        stats.indexSize = `${(totalSize / 1024 / 1024).toFixed(2)} MB`
        
      } else if (this.engine === 'meilisearch' && this.meilisearch) {
        for (const [type, indexName] of this.indices) {
          try {
            const index = this.meilisearch.index(indexName)
            const indexStats = await index.getStats()
            stats.totalDocuments += indexStats.numberOfDocuments
            stats.documentsPerType[type] = indexStats.numberOfDocuments
          } catch (error) {
            console.error(`Error getting stats for index ${indexName}:`, error)
          }
        }
      }

      return stats
    } catch (error) {
      console.error('Error getting index stats:', error)
      return {
        totalDocuments: 0,
        indexSize: '0 MB',
        lastUpdated: new Date(),
        documentsPerType: {}
      }
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }
}

// Export singleton instance
export const searchEngine = new SearchEngine()
