import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'
import { withAuth, withCors, withErrorHandling, combineMiddlewares, AuthenticatedRequest } from '@/middlewares/auth'

interface RouteParams {
  params: {
    keyId: string
  }
}

// DELETE /api/user/api-keys/[keyId] - Delete API key
async function deleteApiKeyHandler(
  req: AuthenticatedRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    if (!req.userId) {
      return NextResponse.json(
        { error: 'User ID not found' },
        { status: 400 }
      )
    }

    const { keyId } = params

    if (!keyId) {
      return NextResponse.json(
        { error: 'API key ID is required' },
        { status: 400 }
      )
    }

    // Delete API key
    const success = await AuthService.deleteApiKey(req.userId, keyId)

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete API key or key not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'API key deleted successfully'
    })

  } catch (error) {
    console.error('Delete API key error:', error)
    return NextResponse.json(
      { error: 'Failed to delete API key' },
      { status: 500 }
    )
  }
}

// Apply middlewares
export const DELETE = combineMiddlewares(
  withCors,
  withErrorHandling,
  withAuth
)(deleteApiKeyHandler)
