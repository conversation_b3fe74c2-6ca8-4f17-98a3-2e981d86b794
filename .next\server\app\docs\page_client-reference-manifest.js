globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/docs/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/Toast.tsx":{"*":{"id":"(ssr)/./components/Toast.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/ThemeContext.tsx":{"*":{"id":"(ssr)/./contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/community/page.tsx":{"*":{"id":"(ssr)/./app/community/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/docs/page.tsx":{"*":{"id":"(ssr)/./app/docs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/plan/page.tsx":{"*":{"id":"(ssr)/./app/plan/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/leaderboard/page.tsx":{"*":{"id":"(ssr)/./app/leaderboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Users\\Downloads\\Kode-XGuard\\components\\Toast.tsx":{"id":"(app-pages-browser)/./components/Toast.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\contexts\\ThemeContext.tsx":{"id":"(app-pages-browser)/./contexts/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\community\\page.tsx":{"id":"(app-pages-browser)/./app/community/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\docs\\page.tsx":{"id":"(app-pages-browser)/./app/docs/page.tsx","name":"*","chunks":["app/docs/page","static/chunks/app/docs/page.js"],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\plan\\page.tsx":{"id":"(app-pages-browser)/./app/plan/page.tsx","name":"*","chunks":[],"async":false},"D:\\Users\\Downloads\\Kode-XGuard\\app\\leaderboard\\page.tsx":{"id":"(app-pages-browser)/./app/leaderboard/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Users\\Downloads\\Kode-XGuard\\":[],"D:\\Users\\Downloads\\Kode-XGuard\\app\\layout":["static/css/app/layout.css"],"D:\\Users\\Downloads\\Kode-XGuard\\app\\page":[],"D:\\Users\\Downloads\\Kode-XGuard\\app\\docs\\page":[]}}