{"name": "@elastic/transport", "version": "8.10.0", "description": "Transport classes and utilities shared among Node.js Elastic client libraries", "main": "./index.js", "types": "index.d.ts", "exports": {".": "./index.js", "./package.json": "./package.json", "./connection/BaseConnection": "./lib/connection/BaseConnection.js", "./lib/connection/BaseConnection": "./lib/connection/BaseConnection.js", "./pool/BaseConnectionPool": "./lib/pool/BaseConnectionPool.js", "./lib/pool/BaseConnectionPool": "./lib/pool/BaseConnectionPool.js", "./pool/CloudConnectionPool": "./lib/pool/CloudConnectionPool.js", "./lib/pool/CloudConnectionPool": "./lib/pool/CloudConnectionPool.js", "./pool/ClusterConnectionPool": "./lib/pool/ClusterConnectionPool.js", "./lib/pool/ClusterConnectionPool": "./lib/pool/ClusterConnectionPool.js", "./Diagnostic": "./lib/Diagnostic.js", "./lib/Diagnostic": "./lib/Diagnostic.js", "./errors": "./lib/errors.js", "./lib/errors": "./lib/errors.js", "./connection/HttpConnection": "./lib/connection/HttpConnection.js", "./lib/connection/HttpConnection": "./lib/connection/HttpConnection.js", "./connection": "./lib/connection/index.js", "./lib/connection": "./lib/connection/index.js", "./pool": "./lib/pool/index.js", "./lib/pool": "./lib/pool/index.js", "./security": "./lib/security.js", "./lib/security": "./lib/security.js", "./Serializer": "./lib/Serializer.js", "./lib/Serializer": "./lib/Serializer.js", "./symbols": "./lib/symbols.js", "./lib/symbols": "./lib/symbols.js", "./Transport": "./lib/Transport.js", "./lib/Transport": "./lib/Transport.js", "./types": "./lib/types.js", "./lib/types": "./lib/types.js", "./connection/UndiciConnection": "./lib/connection/UndiciConnection.js", "./lib/connection/UndiciConnection": "./lib/connection/UndiciConnection.js", "./pool/WeightedConnectionPool": "./lib/pool/WeightedConnectionPool.js", "./lib/pool/WeightedConnectionPool": "./lib/pool/WeightedConnectionPool.js"}, "scripts": {"test": "npm run build && npm run lint && tap test/{unit,acceptance}/{*,**/*}.test.ts", "test:unit": "npm run build && tap test/unit/{*,**/*}.test.ts --disable-coverage", "test:acceptance": "npm run build && tap test/acceptance/*.test.ts --disable-coverage", "test:coverage-100": "npm run build && tap test/{unit,acceptance}/{*,**/*}.test.ts --show-full-coverage", "test:coverage-report": "npm test && tap report --coverage-report=lcov", "test:coverage-ui": "npm run build && tap test/{unit,acceptance}/{*,**/*}.test.ts --coverage-report=html", "lint": "ts-standard src", "lint:fix": "ts-standard --fix src", "license-checker": "license-checker --production --onlyAllow='MIT;Apache-2.0;Apache1.1;ISC;BSD-3-Clause;BSD-2-Clause;0BSD'", "license-header": "./scripts/check-spdx", "prebuild": "npm run clean-build && npm run lint", "build": "tsc", "clean-build": "rimraf ./lib && mkdir lib", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/elastic/elastic-transport-js.git"}, "keywords": [], "author": {"name": "Elastic Client Library Maintainers", "company": "Elastic BV"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/elastic/elastic-transport-js/issues"}, "homepage": "https://github.com/elastic/elastic-transport-js#readme", "engines": {"node": ">=18"}, "devDependencies": {"@opentelemetry/sdk-trace-base": "1.30.1", "@sinonjs/fake-timers": "14.0.0", "@tapjs/clock": "3.0.0", "@types/debug": "4.1.12", "@types/ms": "0.7.34", "@types/node": "22.10.7", "@types/sinonjs__fake-timers": "8.1.5", "@types/stoppable": "1.1.3", "into-stream": "6.0.0", "license-checker": "25.0.1", "node-abort-controller": "3.1.1", "proxy": "2.2.0", "rimraf": "6.0.1", "stoppable": "1.1.0", "tap": "21.0.1", "ts-node": "10.9.2", "ts-standard": "12.0.2", "typescript": "5.7.3", "workq": "3.0.0"}, "dependencies": {"@opentelemetry/api": "1.x", "@opentelemetry/core": "2.x", "debug": "^4.4.1", "hpagent": "^1.2.0", "ms": "^2.1.3", "secure-json-parse": "^3.0.1", "tslib": "^2.8.1", "undici": "^6.21.1"}, "tap": {"allow-incomplete-coverage": true, "plugin": ["@tapjs/clock", "@tapjs/before"]}}