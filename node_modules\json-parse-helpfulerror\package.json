{"name": "json-parse-<PERSON><PERSON><PERSON>r", "version": "1.0.3", "description": "A drop-in replacement for JSON.parse that uses `jju` to give helpful errors", "main": "index.js", "scripts": {"test": "lab -c", "lint": "jslint --edition=latest --terse *.js"}, "repository": {"type": "git", "url": "https://github.com/smikes/json-parse-helpfulerror.git"}, "keywords": ["json", "parse", "line", "doublequote", "error"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/smikes/json-parse-helpfulerror/issues"}, "homepage": "https://github.com/smikes/json-parse-helpfulerror", "devDependencies": {"code": "^1.2.1", "jslint": "^0.7.1", "lab": "^5.1.1"}, "dependencies": {"jju": "^1.1.0"}}