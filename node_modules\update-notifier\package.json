{"name": "update-notifier", "version": "6.0.2", "description": "Update notifications for your CLI app", "license": "BSD-2-<PERSON><PERSON>", "repository": "yeoman/update-notifier", "funding": "https://github.com/yeoman/update-notifier?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "update-notifier.js", "check.js"], "keywords": ["npm", "update", "updater", "notify", "notifier", "check", "checker", "cli", "module", "package", "version"], "dependencies": {"boxen": "^7.0.0", "chalk": "^5.0.1", "configstore": "^6.0.0", "has-yarn": "^3.0.0", "import-lazy": "^4.0.0", "is-ci": "^3.0.1", "is-installed-globally": "^0.4.0", "is-npm": "^6.0.0", "is-yarn-global": "^0.4.0", "latest-version": "^7.0.0", "pupa": "^3.1.0", "semver": "^7.3.7", "semver-diff": "^4.0.0", "xdg-basedir": "^5.1.0"}, "devDependencies": {"ava": "^4.3.0", "clear-module": "^4.1.2", "fixture-stdout": "^0.2.1", "mock-require": "^3.0.3", "strip-ansi": "^7.0.1", "xo": "^0.50.0"}, "ava": {"timeout": "20s", "serial": true}}