(function() {

  module.exports = {
    SingularToPlural: {
      "search": "searches",
      "switch": "switches",
      "fix": "fixes",
      "box": "boxes",
      "process": "processes",
      "address": "addresses",
      "case": "cases",
      "stack": "stacks",
      "wish": "wishes",
      "fish": "fish",
      "jeans": "jeans",
      "funky jeans": "funky jeans",
      "my money": "my money",
      "category": "categories",
      "query": "queries",
      "ability": "abilities",
      "agency": "agencies",
      "movie": "movies",
      "archive": "archives",
      "index": "indices",
      "wife": "wives",
      "safe": "saves",
      "half": "halves",
      "move": "moves",
      "salesperson": "salespeople",
      "person": "people",
      "spokesman": "spokesmen",
      "man": "men",
      "woman": "women",
      "basis": "bases",
      "diagnosis": "diagnoses",
      "diagnosis_a": "diagnosis_as",
      "datum": "data",
      "medium": "media",
      "stadium": "stadia",
      "analysis": "analyses",
      "node_child": "node_children",
      "child": "children",
      "experience": "experiences",
      "day": "days",
      "comment": "comments",
      "foobar": "foobars",
      "newsletter": "newsletters",
      "old_news": "old_news",
      "news": "news",
      "series": "series",
      "species": "species",
      "quiz": "quizzes",
      "perspective": "perspectives",
      "ox": "oxen",
      "photo": "photos",
      "buffalo": "buffaloes",
      "tomato": "tomatoes",
      "dwarf": "dwarves",
      "elf": "elves",
      "information": "information",
      "equipment": "equipment",
      "bus": "buses",
      "status": "statuses",
      "status_code": "status_codes",
      "mouse": "mice",
      "louse": "lice",
      "house": "houses",
      "octopus": "octopi",
      "virus": "viri",
      "alias": "aliases",
      "portfolio": "portfolios",
      "vertex": "vertices",
      "matrix": "matrices",
      "matrix_fu": "matrix_fus",
      "axis": "axes",
      "testis": "testes",
      "crisis": "crises",
      "rice": "rice",
      "shoe": "shoes",
      "horse": "horses",
      "prize": "prizes",
      "edge": "edges",
      "cow": "kine",
      "database": "databases",
      "safe": "safes",
      "belief": "beliefs",
      "gaffe": "gaffes",
      "cafe": "cafes",
      "caffe": "caffes",
      "life": "lives",
      "wife": "wives",
      "save": "saves",
      "fife": "fifes",
      "carafe": "carafes",
      "giraffe": "giraffes",
      "elf": "elves",
      "calf": "calves",
      "bookshelf": "bookshelves",
      "wolf": "wolves",
      "half": "halves",
      "meatloaf": "meatloaves",
      "loaf": "loaves",
      "oaf": "oafs",
      "jefe": "jefes",
      "afterlife": "afterlives",
    },
    CamelToUnderscore: {
      "Product": "product",
      "SpecialGuest": "special_guest",
      "ApplicationController": "application_controller",
      "Area51Controller": "area51_controller"
    },
    UnderscoreToLowerCamel: {
      "product": "product",
      "Widget": "widget",
      "special_guest": "specialGuest",
      "application_controller": "applicationController",
      "area51_controller": "area51Controller"
    },
    CamelToUnderscoreWithoutReverse: {
      "HTMLTidy": "html_tidy",
      "HTMLTidyGenerator": "html_tidy_generator",
      "FreeBSD": "free_bsd",
      "HTML": "html"
    },
    CamelWithModuleToUnderscoreWithSlash: {
      "Admin.Product": "admin/product",
      "Users.Commission.Department": "users/commission/department",
      "UsersSection.CommissionDepartment": "users_section/commission_department"
    },
    ClassNameToForeignKeyWithUnderscore: {
      "Person": "person_id",
      "MyApplication.Billing.Account": "account_id"
    },
    ClassNameToForeignKeyWithoutUnderscore: {
      "Person": "personid",
      "MyApplication.Billing.Account": "accountid"
    },
    ClassNameToTableName: {
      "PrimarySpokesman": "primary_spokesmen",
      "NodeChild": "node_children"
    },
    UnderscoreToHuman: {
      "employee_salary": "Employee salary",
      "employee_id": "Employee",
      "underground": "Underground"
    },
    MixtureToTitleCase: {
      'bullet_record': 'Bullet Record',
      'BulletRecord': 'Bullet Record',
      'bullet web service': 'Bullet Web Service',
      'Bullet Web Service': 'Bullet Web Service',
      'Bullet web service': 'Bullet Web Service',
      'bulletwebservice': 'Bulletwebservice',
      'Bulletwebservice': 'Bulletwebservice',
      "pavan's code": "Pavan's Code",
      "Pavan's code": "Pavan's Code",
      "pavan's Code": "Pavan's Code"
    },
    OrdinalNumbers: {
      "-1": "-1st",
      "-2": "-2nd",
      "-3": "-3rd",
      "-4": "-4th",
      "-5": "-5th",
      "-6": "-6th",
      "-7": "-7th",
      "-8": "-8th",
      "-9": "-9th",
      "-10": "-10th",
      "-11": "-11th",
      "-12": "-12th",
      "-13": "-13th",
      "-14": "-14th",
      "-20": "-20th",
      "-21": "-21st",
      "-22": "-22nd",
      "-23": "-23rd",
      "-24": "-24th",
      "-100": "-100th",
      "-101": "-101st",
      "-102": "-102nd",
      "-103": "-103rd",
      "-104": "-104th",
      "-110": "-110th",
      "-111": "-111th",
      "-112": "-112th",
      "-113": "-113th",
      "-1000": "-1000th",
      "-1001": "-1001st",
      "0": "0th",
      "1": "1st",
      "2": "2nd",
      "3": "3rd",
      "4": "4th",
      "5": "5th",
      "6": "6th",
      "7": "7th",
      "8": "8th",
      "9": "9th",
      "10": "10th",
      "11": "11th",
      "12": "12th",
      "13": "13th",
      "14": "14th",
      "20": "20th",
      "21": "21st",
      "22": "22nd",
      "23": "23rd",
      "24": "24th",
      "100": "100th",
      "101": "101st",
      "102": "102nd",
      "103": "103rd",
      "104": "104th",
      "110": "110th",
      "111": "111th",
      "112": "112th",
      "113": "113th",
      "1000": "1000th",
      "1001": "1001st"
    },
    UnderscoresToDashes: {
      "street": "street",
      "street_address": "street-address",
      "person_street_address": "person-street-address"
    }
  };

}).call(this);
