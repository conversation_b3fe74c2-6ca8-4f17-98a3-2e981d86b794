{"name": "fast-memoize", "version": "2.5.2", "description": "Fastest memoization lib that supports N arguments", "main": "src/index.js", "typings": "typings/fast-memoize.d.ts", "scripts": {"benchmark": "node benchmark", "benchmark:cache": "node benchmark/cache", "benchmark:combination": "node benchmark/combination.js", "benchmark:recursive-all": "node benchmark/recursive-all.js", "benchmark:recursive-solo": "node --trace-opt benchmark/recursive-solo.js", "benchmark:serializer": "node benchmark/serializer", "benchmark:solo": "node benchmark/solo.js", "benchmark:strategy": "node benchmark/strategy", "benchmark:trace": "node --trace-inlining --trace-opt --trace-deopt benchmark/trace.js", "benchmark:v8-optimization-analysis": "node --allow_natives_syntax --expose_debug_as=VirtualMachine benchmark/v8-optimization-analysis.js", "benchmark:compare": "./benchmark/compare-commits/index.sh", "lint": "standard --fix \"src/**/*.js\" \"test/**/*.js\" \"benchmark/**/*.js\"", "preversion": "npm run test:all", "test": "jest", "test:all": "npm run lint && npm run test:typescript && npm run test", "test:coverage": "covert test/*.js", "test:typescript": "cd ./typings/tests && npm install-test"}, "files": ["README.md", "typings/fast-memoize.d.ts", "src/"], "repository": {"type": "git", "url": "git+https://github.com/caiogondim/fast-memoize.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://caiogondim.com)", "license": "MIT", "bugs": {"url": "https://github.com/caiogondim/fast-memoize/issues"}, "homepage": "https://github.com/caiogondim/fast-memoize#readme", "devDependencies": {"benchmark": "^2.0.0", "cli-table2": "^0.2.0", "codecov": "^2.0.2", "covert": "^1.1.0", "iMemoized": "0.0.10", "jest": "20.0.4", "lodash": "^4.0.0", "logdown": "^1.2.5", "lru-cache": "^4.0.0", "lru-memoize": "^1.0.2", "memoizee": "^0.4.1", "nano-memoize": "^0.1.0", "ora": "^0.3.0", "ramda": "^0.22.1", "standard": "^10.0.2", "underscore": "^1.8.3"}, "jest": {"roots": ["test/"], "testRegex": "test/.*\\.js$", "collectCoverage": true, "coverageDirectory": "./coverage/"}, "dependencies": {}}