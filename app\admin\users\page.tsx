'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { 
  Users, 
  Search, 
  Filter,
  Plus,
  Edit,
  Trash2,
  Crown,
  Shield,
  Star,
  Activity,
  Clock,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  TrendingDown,
  Eye,
  EyeOff,
  Ban,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  plan: 'Free' | 'Pro' | 'Expert' | 'Elite'
  status: 'active' | 'inactive' | 'banned' | 'pending'
  joinedAt: string
  lastActive: string
  totalScans: number
  vulnerabilitiesFound: number
  score: number
  level: number
  country: string
  ipAddress: string
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterPlan, setFilterPlan] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockUsers: User[] = [
        {
          id: '1',
          username: 'CyberNinja',
          email: '<EMAIL>',
          fullName: 'Alex Chen',
          plan: 'Elite',
          status: 'active',
          joinedAt: '2024-01-15',
          lastActive: '2 minutes ago',
          totalScans: 156,
          vulnerabilitiesFound: 23,
          score: 8950,
          level: 28,
          country: 'Singapore',
          ipAddress: '*************'
        },
        {
          id: '2',
          username: 'SecurityMaster',
          email: '<EMAIL>',
          fullName: 'Sarah Johnson',
          plan: 'Pro',
          status: 'active',
          joinedAt: '2024-02-01',
          lastActive: '15 minutes ago',
          totalScans: 89,
          vulnerabilitiesFound: 12,
          score: 5670,
          level: 22,
          country: 'United States',
          ipAddress: '*************'
        },
        {
          id: '3',
          username: 'PentestPro',
          email: '<EMAIL>',
          fullName: 'David Kim',
          plan: 'Expert',
          status: 'inactive',
          joinedAt: '2024-03-10',
          lastActive: '2 hours ago',
          totalScans: 67,
          vulnerabilitiesFound: 8,
          score: 4230,
          level: 19,
          country: 'South Korea',
          ipAddress: '*************'
        },
        {
          id: '4',
          username: 'HackerMind',
          email: '<EMAIL>',
          fullName: 'Maria Rodriguez',
          plan: 'Free',
          status: 'banned',
          joinedAt: '2024-04-05',
          lastActive: '1 day ago',
          totalScans: 12,
          vulnerabilitiesFound: 2,
          score: 890,
          level: 8,
          country: 'Spain',
          ipAddress: '*************'
        }
      ]

      // Add more mock users
      for (let i = 5; i <= 20; i++) {
        mockUsers.push({
          id: i.toString(),
          username: `User${i}`,
          email: `user${i}@example.com`,
          fullName: `User ${i}`,
          plan: ['Free', 'Pro', 'Expert', 'Elite'][Math.floor(Math.random() * 4)] as any,
          status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)] as any,
          joinedAt: '2024-01-01',
          lastActive: `${Math.floor(Math.random() * 24)} hours ago`,
          totalScans: Math.floor(Math.random() * 100) + 10,
          vulnerabilitiesFound: Math.floor(Math.random() * 20),
          score: Math.floor(Math.random() * 5000) + 1000,
          level: Math.floor(Math.random() * 30) + 5,
          country: 'Global',
          ipAddress: `192.168.1.${100 + i}`
        })
      }

      setUsers(mockUsers)
      setLoading(false)
    } catch (error) {
      console.error('Error loading users:', error)
      setLoading(false)
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Elite': return 'text-yellow-400 bg-yellow-400/20'
      case 'Expert': return 'text-purple-400 bg-purple-400/20'
      case 'Pro': return 'text-blue-400 bg-blue-400/20'
      case 'Free': return 'text-gray-400 bg-gray-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400'
      case 'inactive': return 'text-yellow-400'
      case 'banned': return 'text-red-400'
      case 'pending': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />
      case 'inactive': return <Clock className="h-4 w-4" />
      case 'banned': return <Ban className="h-4 w-4" />
      case 'pending': return <AlertCircle className="h-4 w-4" />
      default: return <XCircle className="h-4 w-4" />
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.fullName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesPlan = filterPlan === 'all' || user.plan === filterPlan
    const matchesStatus = filterStatus === 'all' || user.status === filterStatus
    
    return matchesSearch && matchesPlan && matchesStatus
  })

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = () => {
    setSelectedUsers(
      selectedUsers.length === filteredUsers.length 
        ? [] 
        : filteredUsers.map(user => user.id)
    )
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading users...</div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">User</span>{' '}
              <span className="text-cyber-pink">Management</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Manage and monitor all platform users
            </p>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <button className="btn-cyber-secondary">
              <Filter className="h-4 w-4 mr-2" />
              Export Data
            </button>
            <button className="btn-cyber-primary">
              <Plus className="h-4 w-4 mr-2" />
              Add User
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="card-cyber text-center">
            <Users className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {users.length}
            </div>
            <div className="text-sm text-gray-400">Total Users</div>
          </div>

          <div className="card-cyber text-center">
            <Activity className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {users.filter(u => u.status === 'active').length}
            </div>
            <div className="text-sm text-gray-400">Active Users</div>
          </div>

          <div className="card-cyber text-center">
            <Crown className="h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              {users.filter(u => u.plan !== 'Free').length}
            </div>
            <div className="text-sm text-gray-400">Premium Users</div>
          </div>

          <div className="card-cyber text-center">
            <TrendingUp className="h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse" />
            <div className="text-2xl font-bold text-white mb-1">
              +12%
            </div>
            <div className="text-sm text-gray-400">Growth Rate</div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="card-cyber">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative w-full lg:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg input-cyber"
              />
            </div>

            {/* Filters */}
            <div className="flex space-x-4">
              <select
                value={filterPlan}
                onChange={(e) => setFilterPlan(e.target.value)}
                className="px-4 py-2 rounded-lg input-cyber"
              >
                <option value="all">All Plans</option>
                <option value="Free">Free</option>
                <option value="Pro">Pro</option>
                <option value="Expert">Expert</option>
                <option value="Elite">Elite</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 rounded-lg input-cyber"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="banned">Banned</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="card-cyber">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">
              <span className="text-cyber-glow">Users</span>{' '}
              <span className="text-cyber-pink">({filteredUsers.length})</span>
            </h2>

            {selectedUsers.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">{selectedUsers.length} selected</span>
                <button className="btn-cyber-secondary text-sm">
                  Bulk Actions
                </button>
              </div>
            )}
          </div>

          {/* Table Header */}
          <div className="hidden lg:grid lg:grid-cols-12 gap-4 p-4 bg-cyber-secondary/5 rounded-lg mb-4">
            <div className="col-span-1">
              <input
                type="checkbox"
                checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                onChange={handleSelectAll}
                className="rounded border-cyber-border bg-cyber-dark text-cyber-primary focus:ring-cyber-primary"
              />
            </div>
            <div className="col-span-3 text-sm font-medium text-gray-400">User</div>
            <div className="col-span-2 text-sm font-medium text-gray-400">Plan & Status</div>
            <div className="col-span-2 text-sm font-medium text-gray-400">Activity</div>
            <div className="col-span-2 text-sm font-medium text-gray-400">Stats</div>
            <div className="col-span-2 text-sm font-medium text-gray-400">Actions</div>
          </div>

          {/* Table Body */}
          <div className="space-y-3">
            {filteredUsers.map((user) => (
              <div
                key={user.id}
                className="grid grid-cols-1 lg:grid-cols-12 gap-4 p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200"
              >
                {/* Checkbox */}
                <div className="hidden lg:block col-span-1">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id)}
                    onChange={() => handleSelectUser(user.id)}
                    className="rounded border-cyber-border bg-cyber-dark text-cyber-primary focus:ring-cyber-primary"
                  />
                </div>

                {/* User Info */}
                <div className="col-span-1 lg:col-span-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-cyber-primary/20 flex items-center justify-center text-sm font-bold text-cyber-primary">
                      {user.username.charAt(0)}
                    </div>
                    <div>
                      <h4 className="font-medium text-white">{user.username}</h4>
                      <p className="text-gray-400 text-sm">{user.email}</p>
                      <p className="text-gray-500 text-xs">{user.fullName}</p>
                    </div>
                  </div>
                </div>

                {/* Plan & Status */}
                <div className="col-span-1 lg:col-span-2">
                  <div className="space-y-2">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold ${getPlanColor(user.plan)}`}>
                      {user.plan}
                    </div>
                    <div className={`flex items-center space-x-1 text-sm ${getStatusColor(user.status)}`}>
                      {getStatusIcon(user.status)}
                      <span className="capitalize">{user.status}</span>
                    </div>
                  </div>
                </div>

                {/* Activity */}
                <div className="col-span-1 lg:col-span-2">
                  <div className="text-sm">
                    <div className="text-white">Joined: {user.joinedAt}</div>
                    <div className="text-gray-400">Last: {user.lastActive}</div>
                    <div className="text-gray-500 text-xs">{user.country}</div>
                  </div>
                </div>

                {/* Stats */}
                <div className="col-span-1 lg:col-span-2">
                  <div className="text-sm space-y-1">
                    <div className="text-cyber-primary font-medium">Level {user.level}</div>
                    <div className="text-gray-400">{user.totalScans} scans</div>
                    <div className="text-gray-400">{user.vulnerabilitiesFound} bugs</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="col-span-1 lg:col-span-2">
                  <div className="flex space-x-2">
                    <button className="p-2 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-2 rounded-lg bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {filteredUsers.length} of {users.length} users
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 rounded bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                Previous
              </button>
              <button className="px-3 py-1 rounded bg-cyber-primary text-black hover:bg-cyber-primary/80 transition-colors">
                1
              </button>
              <button className="px-3 py-1 rounded bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                2
              </button>
              <button className="px-3 py-1 rounded bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
