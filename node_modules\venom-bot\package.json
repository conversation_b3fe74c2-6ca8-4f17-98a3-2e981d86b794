{"name": "venom-bot", "version": "5.3.0", "description": "Venom is a high-performance system developed with JavaScript to create a bot for WhatsApp, support for creating any interaction, such as customer service, media sending, sentence recognition based on artificial intelligence and all types of design architecture for WhatsApp. ", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build:counter": "cd src/lib/counter/ && gulp", "build:docs": "typedoc && git add docs/*", "build:jsQR": "cd src/lib/jsQR/ && gulp", "build:middleware": "cd src/lib/middleware/ && webpack", "build:venom": "tsc", "build:wapi": "cd src/lib/wapi/ && webpack", "build": "npm run fix:imports && npm run build:wapi && npm run build:middleware && npm run build:counter && npm run build:venom", "build:test": "npm run fix:imports && npm run build:wapi && npm run build:middleware && npm run build:counter && npm run build:venom && npm test", "changelog:last": "conventional-changelog -p angular -r 2", "changelog:preview": "conventional-changelog -p angular -u", "changelog:update": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean": "shx rm -rf session dist", "commit": "cz", "fix:imports": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\" && npm run lint:fix", "generate-api-docs": "typedoc", "lint:js": "npx eslint -c .eslintrc.cjs --ext .js src", "lint:ts": "npx eslint -c .eslintrc.cjs --ext .ts src", "lint": "npm run lint:ts && npm run lint:js", "lint:fix": "npx eslint src --fix", "prepare": "npm run clean && npm run build", "release": "release-it", "start": "npm run build:venom & tsc app.ts && node app.js", "test": "node ./test/index.js", "test:app": "node ./app.js", "watch": "concurrently \"tsc -w\" \"nodemon dist/index.js\""}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "pretty-quick --staged"}}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "repository": {"type": "git", "url": "git+https://github.com/orkestral/venom.git"}, "keywords": ["whatsapp", "javascript", "bot", "chat bot", "bot", "typescript", "automation", "puppeteer"], "author": "Orkestral", "license": "MIT", "bugs": {"url": "https://github.com/orkestral/venom/issues"}, "publishConfig": {"access": "public"}, "files": [".env", "dist/*", "dist", "package.json", "CHANGELOG.md", "LICENSE", "README.md", "SECURITY.md"], "homepage": "https://github.com/orkestral/venom#readme", "devDependencies": {"@commitlint/cli": "^19.2.2", "@commitlint/config-angular": "^19.2.2", "@types/atob": "^2.1.4", "@types/is-root": "^2.1.2", "@types/mime-types": "^2.1.0", "@types/node": "^20.10.8", "@types/puppeteer": "^5.4.7", "@types/qrcode": "^1.5.1", "@types/qrcode-terminal": "^0.12.2", "@types/sharp": "^0.30.5", "@types/unzipper": "^0.10.9", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.62.0", "concurrently": "^7.6.0", "conventional-changelog-cli": "^2.2.2", "copy-webpack-plugin": "^10.2.4", "eslint": "^8.39.0", "eslint-config-eslint": "^9.0.0", "eslint-config-prettier": "^6.14.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "gulp": "^4.0.2", "husky": "^8.0.1", "nodemon": "^3.0.1", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "release-it": "^16.1.0", "shx": "^0.3.4", "ts-loader": "^9.5.1", "typedoc": "^0.25.12", "typedoc-default-themes": "^0.12.10", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.0.1"}, "dependencies": {"async-mutex": "^0.5.0", "axios": "^1.4.0", "boxen": "^5.1.1", "chalk": "^4.0.0", "cheerio": "^1.0.0-rc.12", "chrome-launcher": "0.15.2", "chrome-version": "^1.0.1", "dotenv": "^16.0.1", "esm": "^3.2.25", "futoin-hkdf": "^1.5.3", "i": "^0.3.7", "latest-lib": "^0.2.1", "mime-types": "^2.1.35", "npm-check-updates": "^16.14.12", "ora": "^7.0.1", "os": "^0.1.2", "puppeteer": "^21.6.1", "puppeteer-core": "^22.6.5", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "qrcode": "^1.5.1", "qrcode-terminal": "^0.12.0", "sanitize-filename": "^1.6.3", "sharp": "^0.33.1", "spinnies": "^0.5.1", "unzipper": "^0.11.4", "ws": "^8.8.0", "yoo-hoo": "^1.2.0"}}