{"version": 3, "file": "BrowsingContext.d.ts", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/BrowsingContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAExE,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE1D,OAAO,EAAkB,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAExE,OAAO,KAAK,EAAC,uBAAuB,EAAC,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,KAAK,EAAC,oBAAoB,EAAC,MAAM,YAAY,CAAC;AACrD,OAAO,EAAC,WAAW,EAAC,MAAM,YAAY,CAAC;AACvC,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAE3C;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,IAAI,CACpC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EACnC,UAAU,CACX,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG,IAAI,CACzC,IAAI,CAAC,eAAe,CAAC,2BAA2B,EAChD,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,IAAI,CAC9B,IAAI,CAAC,eAAe,CAAC,gBAAgB,EACrC,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,IAAI,CAC7B,IAAI,CAAC,eAAe,CAAC,eAAe,EACpC,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,IAAI,CACxC,IAAI,CAAC,eAAe,CAAC,0BAA0B,EAC/C,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAC1C,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG,IAAI,CAClC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EACjC,WAAW,CACZ,CAAC;AAEF;;GAEG;AACH,qBAAa,eAAgB,SAAQ,YAAY,CAAC;IAChD,2CAA2C;IAC3C,MAAM,EAAE;QACN,iDAAiD;QACjD,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,wDAAwD;IACxD,eAAe,EAAE;QACf,gDAAgD;QAChD,eAAe,EAAE,eAAe,CAAC;KAClC,CAAC;IACF,4CAA4C;IAC5C,UAAU,EAAE;QACV,oCAAoC;QACpC,UAAU,EAAE,UAAU,CAAC;KACxB,CAAC;IACF,0CAA0C;IAC1C,OAAO,EAAE;QACP,iCAAiC;QACjC,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;IACF,6CAA6C;IAC7C,GAAG,EAAE;QACH,8BAA8B;QAC9B,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;KACvB,CAAC;IACF,2CAA2C;IAC3C,UAAU,EAAE;QACV,kCAAkC;QAClC,UAAU,EAAE,UAAU,CAAC;KACxB,CAAC;IACF,0DAA0D;IAC1D,gBAAgB,EAAE,IAAI,CAAC;IACvB,8CAA8C;IAC9C,IAAI,EAAE,IAAI,CAAC;IACX,qDAAqD;IACrD,MAAM,EAAE;QACN,6CAA6C;QAC7C,KAAK,EAAE,oBAAoB,CAAC;KAC7B,CAAC;CACH,CAAC;;IACA,MAAM,CAAC,IAAI,CACT,WAAW,EAAE,WAAW,EACxB,MAAM,EAAE,eAAe,GAAG,SAAS,EACnC,EAAE,EAAE,MAAM,EACV,GAAG,EAAE,MAAM,EACX,cAAc,EAAE,MAAM,GAAG,IAAI,GAC5B,eAAe;IAmBlB,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;IACnC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,MAAM,EAAE,eAAe,GAAG,SAAS,CAAC;IAC7C,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC;IAClC,QAAQ,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvC,OAAO;IAmJP,IAAI,QAAQ,IAAI,QAAQ,CAAC,eAAe,CAAC,CAExC;IACD,IAAI,MAAM,IAAI,OAAO,CAEpB;IACD,IAAI,QAAQ,IAAI,OAAO,CAEtB;IACD,IAAI,MAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,CAOlC;IACD,IAAI,GAAG,IAAI,eAAe,CAMzB;IACD,IAAI,GAAG,IAAI,MAAM,CAEhB;IAWD,OAAO,CAAC,OAAO;IAST,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAUzB,iBAAiB,CACrB,OAAO,GAAE,wBAA6B,GACrC,OAAO,CAAC,MAAM,CAAC;IAcZ,KAAK,CAAC,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB5C,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAW7C,QAAQ,CACZ,GAAG,EAAE,MAAM,EACX,IAAI,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,GACzC,OAAO,CAAC,IAAI,CAAC;IAYV,MAAM,CAAC,OAAO,GAAE,aAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAWlD,gBAAgB,CAAC,aAAa,EAAE,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAYpE,KAAK,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;IAclD,gBAAgB,CAAC,OAAO,GAAE,uBAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IAWtE,WAAW,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC;IAW5D,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAWlE,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;IAUrC,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,WAAW;IAQzC,gBAAgB,CACpB,mBAAmB,EAAE,MAAM,EAC3B,OAAO,GAAE,uBAA4B,GACpC,OAAO,CAAC,MAAM,CAAC;IAcZ,YAAY,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;IAe3D,mBAAmB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAQlD,UAAU,CACd,OAAO,GAAE,iBAAsB,GAC9B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IAiB3B,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAc5D,QAAQ,CACZ,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,EACpC,KAAK,EAAE,MAAM,EAAE,GACd,OAAO,CAAC,IAAI,CAAC;IAYV,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAQvD,eAAe,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAInE,CAAC,aAAa,CAAC,IAAI,IAAI;IAajB,YAAY,CAChB,GAAG,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,GAC5C,OAAO,CAAC,IAAI,CAAC;IAkBV,WAAW,CACf,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EACrC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAC1E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;CAS1C"}