{"version": 3, "file": "UndiciConnection.js", "sourceRoot": "", "sources": ["../../src/connection/UndiciConnection.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,qEAAqE;AAErE,0DAAyB;AACzB,sEAAgC;AAGhC,2EAUyB;AACzB,mCAAyD;AACzD,sCAKkB;AAElB,wCAA2C;AAE3C,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAA;AACpC,MAAM,kBAAkB,GAAG,kBAAkB,CAAA;AAC7C,MAAM,iBAAiB,GAAG,qBAAM,CAAC,SAAS,CAAC,UAAU,CAAA;AACrD,MAAM,iBAAiB,GAAG,qBAAM,CAAC,SAAS,CAAC,iBAAiB,CAAA;AAE5D;;GAEG;AACH,MAAqB,UAAW,SAAQ,wBAAc;IAGpD,YAAa,IAAuB;;QAClC,KAAK,CAAC,IAAI,CAAC,CAAA;QAHb;;;;;WAAU;QAKR,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,2BAAkB,CAAC,4CAA4C,CAAC,CAAA;QAC5E,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YACxE,MAAM,IAAI,2BAAkB,CAAC,mEAAmE,CAAC,CAAA;QACnG,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,2BAAkB,CAAC,0CAA0C,CAAC,CAAA;QAC1E,CAAC;QAED,MAAM,aAAa,GAAiB;YAClC,gBAAgB,EAAE,KAAK;YACvB,mBAAmB,EAAE,KAAK;YAC1B,yBAAyB,EAAE,IAAI;YAC/B,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,KAAK;YACpB,WAAW,EAAE,GAAG;YAChB,cAAc,EAAE,IAAI,CAAC,OAAO;YAC5B,WAAW,EAAE,IAAI,CAAC,OAAO;YACzB,GAAG,IAAI,CAAC,KAAK;SACd,CAAA;QAED,IAAI,IAAI,CAAC,wBAAc,CAAC,KAAK,IAAI,EAAE,CAAC;YAClC,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAc,CAAC,CAAA;YAC1C,MAAM,SAAS,GAAG,IAAA,uBAAc,EAAC,CAAC,MAAA,IAAI,CAAC,GAAG,mCAAI,EAAE,CAAgC,CAAC,CAAA;YACjF,aAAa,CAAC,OAAO,GAAG,UAAU,IAA4B,EAAE,EAA2B;gBACzF,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;oBAC9B,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;wBAChB,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;oBACtB,CAAC;oBACD,IAAI,aAAa,KAAK,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;wBACxD,MAAM,iBAAiB,GAAG,IAAA,qCAAoB,EAAC,MAAM,CAAC,CAAA;wBACtD,0BAA0B;wBAC1B,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;4BAC9B,MAAM,CAAC,OAAO,EAAE,CAAA;4BAChB,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,kCAAkC,CAAC,EAAE,IAAI,CAAC,CAAA;wBAChE,CAAC;wBAED,qFAAqF;wBACrF,uFAAuF;wBACvF,wFAAwF;wBACxF,kEAAkE;wBAClE,wEAAwE;wBACxE,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC;4BAC5E,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;wBACzB,CAAC;wBAED,+BAA+B;wBAC/B,0BAA0B;wBAC1B,IAAI,CAAC,IAAA,qCAAoB,EAAC,aAAa,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;4BAC3E,MAAM,CAAC,OAAO,EAAE,CAAA;4BAChB,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,wFAAwF,CAAC,EAAE,IAAI,CAAC,CAAA;wBACtH,CAAC;oBACH,CAAC;oBACD,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;gBACzB,CAAC,CAAC,CAAA;YACJ,CAAC,CAAA;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;YAC7B,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,GAAkC,CAAA;QACjE,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,aAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAA;IAC1D,CAAC;IAID,KAAK,CAAC,OAAO,CAAE,MAA+B,EAAE,OAAY;;QAC1D,MAAM,eAAe,GAAG,MAAA,OAAO,CAAC,eAAe,mCAAI,iBAAiB,CAAA;QACpE,MAAM,yBAAyB,GAAG,MAAA,OAAO,CAAC,yBAAyB,mCAAI,iBAAiB,CAAA;QACxF,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YAC7G,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;YACxD,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAA,OAAO,CAAC,MAAM,mCAAI,IAAI,eAAe,EAAE,CAAC,MAAM;SACvD,CAAA;QAED,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAClC,aAAa,CAAC,IAAI,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,CAAA;QAC/C,CAAC;QAED,gDAAgD;QAChD,uDAAuD;QACvD,0DAA0D;QAC1D,sDAAsD;QACtD,6CAA6C;QAC7C,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,SAAS,CAAA;QACb,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAChE,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC1B,QAAQ,GAAG,IAAI,CAAA;gBACf,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YACxD,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;QACrB,CAAC;QAED,mDAAmD;QACnD,IAAI,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,SAAS,CAAC,6BAA6B,aAAa,CAAC,IAAI,EAAE,CAAC,CAAA;QACxE,CAAC;QAED,KAAK,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAA;QACvC,IAAI,QAAQ,CAAA;QACZ,IAAI,CAAC;YACH,mBAAmB;YACnB,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAA4B,CAAA;YAC9E,IAAI,SAAS,IAAI,IAAI;gBAAE,YAAY,CAAC,SAAS,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,SAAS,IAAI,IAAI;gBAAE,YAAY,CAAC,SAAS,CAAC,CAAA;YAC9C,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;gBACjB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,YAAY,CAAC,SAAS;oBACzB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,qBAAY,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,4BAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAA;gBACvG,KAAK,yBAAyB;oBAC5B,MAAM,IAAI,qBAAY,CAAC,mBAAmB,CAAC,CAAA;gBAC7C,KAAK,gBAAgB;oBACnB,MAAM,IAAI,wBAAe,CAAC,GAAG,GAAG,CAAC,OAAO,aAAa,MAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,YAAY,mCAAI,SAAS,IAAI,MAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,SAAS,mCAAI,SAAS,aAAa,MAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,aAAa,mCAAI,SAAS,IAAI,MAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,mCAAI,SAAS,EAAE,CAAC,CAAA,CAAC,sBAAsB;gBACtP;oBACE,MAAM,IAAI,wBAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;aACpB,CAAA;QACH,CAAC;QAED,0DAA0D;QAC1D,MAAM,eAAe,GAAG,CAAC,MAAA,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,mCAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;QAClF,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC,sBAAsB;QACnH,MAAM,YAAY,GAAG,IAAA,yBAAQ,EAAC,MAAA,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC,CAAA;QAErE,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAAE,CAAC;YACrD,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAA;YAChE,IAAI,YAAY,IAAI,aAAa,GAAG,yBAAyB,EAAE,CAAC,CAAC,sBAAsB;gBACrF,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;gBACvB,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,aAAa,gDAAgD,yBAAyB,GAAG,CAAC,CAAA;YACjJ,CAAC;iBAAM,IAAI,aAAa,GAAG,eAAe,EAAE,CAAC;gBAC3C,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;gBACvB,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,aAAa,gDAAgD,eAAe,GAAG,CAAC,CAAA;YACvI,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;QACtD,IAAI,CAAC;YACH,IAAI,YAAY,IAAI,YAAY,EAAE,CAAC,CAAC,sBAAsB;gBACxD,IAAI,aAAa,GAAG,CAAC,CAAA;gBACrB,MAAM,OAAO,GAAa,EAAE,CAAA;gBAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACxC,aAAa,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;oBACzC,IAAI,aAAa,GAAG,yBAAyB,EAAE,CAAC;wBAC9C,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;wBACvB,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,aAAa,gDAAgD,yBAAyB,GAAG,CAAC,CAAA;oBACjJ,CAAC;oBACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACrB,CAAC;gBACD,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;iBAC7B,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,OAAO,GAAG,EAAE,CAAA;gBAChB,IAAI,aAAa,GAAG,CAAC,CAAA;gBACrB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;gBACjC,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACxC,aAAa,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;oBACzC,IAAI,aAAa,GAAG,eAAe,EAAE,CAAC;wBACpC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;wBACvB,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,aAAa,gDAAgD,eAAe,GAAG,CAAC,CAAA;oBACvI,CAAC;oBACD,OAAO,IAAI,KAAe,CAAA;gBAC5B,CAAC;gBACD,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,IAAI,EAAE,OAAO;iBACd,CAAA;YACH,CAAC;QACH,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,GAAG,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBACvC,MAAM,GAAG,CAAA;YACX,CAAC;YACD,MAAM,IAAI,wBAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACpC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAA;IACzB,CAAC;CACF;AA1MD,6BA0MC;AAED,0BAA0B;AAC1B,SAAS,oBAAoB,CAAE,IAAyB;IACtD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IACxC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IAC7C,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IACzC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IAC7C,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IACzC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;QAAE,OAAO,KAAK,CAAA;IACpC,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,WAAW,CAAE,IAA4B,EAAE,MAAiC;IACnF,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAA;AACtD,CAAC"}