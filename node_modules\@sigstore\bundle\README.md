# @sigstore/bundle &middot; [![npm version](https://img.shields.io/npm/v/@sigstore/bundle.svg?style=flat)](https://www.npmjs.com/package/@sigstore/tuf) [![CI Status](https://github.com/sigstore/sigstore-js/workflows/CI/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/ci.yml) [![Smoke Test Status](https://github.com/sigstore/sigstore-js/workflows/smoke-test/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/smoke-test.yml)

A JavaScript library for working with the Sigstore bundle format.

## Features

- TypeScript types for the different Sigstore bundle versions.
- Bundle validation functions.
- Support for serializing/deserializing bundles to/from JSON.

## Prerequisites

- Node.js version >= 14.17.0

## Installation

```
npm install @sigstore/bundle
```
