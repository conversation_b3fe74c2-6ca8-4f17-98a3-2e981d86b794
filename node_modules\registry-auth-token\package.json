{"name": "registry-auth-token", "version": "5.1.0", "description": "Get the auth token set for an npm registry (if any)", "main": "index.js", "scripts": {"test": "mocha --require test/global-hooks.js", "posttest": "standard", "coverage": "istanbul cover _mocha"}, "repository": {"type": "git", "url": "git+ssh://**************/rexxars/registry-auth-token.git"}, "engines": {"node": ">=14"}, "keywords": ["npm", "conf", "config", "npmconf", "registry", "auth", "token", "authtoken"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rexxars/registry-auth-token/issues"}, "homepage": "https://github.com/rexxars/registry-auth-token#readme", "dependencies": {"@pnpm/npm-conf": "^2.1.0"}, "devDependencies": {"istanbul": "^0.4.2", "mocha": "^10.0.0", "require-uncached": "^1.0.2", "standard": "^12.0.1"}, "standard": {"ignore": ["coverage/**"]}}