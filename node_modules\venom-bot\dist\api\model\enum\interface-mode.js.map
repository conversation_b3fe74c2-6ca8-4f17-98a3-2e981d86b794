{"version": 3, "file": "interface-mode.js", "sourceRoot": "", "sources": ["../../../../src/api/model/enum/interface-mode.ts"], "names": [], "mappings": ";;;AAAA,IAAY,aAyCX;AAzCD,WAAY,aAAa;IACvB;;OAEG;IACH,0BAAS,CAAA;IACT;;OAEG;IACH,8BAAa,CAAA;IACb;;OAEG;IACH,0CAAyB,CAAA;IACzB;;OAEG;IACH,oCAAmB,CAAA;IACnB;;OAEG;IACH,oCAAmB,CAAA;IACnB;;OAEG;IACH,sCAAqB,CAAA;IACrB;;OAEG;IACH,0CAAyB,CAAA;IACzB;;OAEG;IACH,wCAAuB,CAAA;IACvB;;OAEG;IACH,gDAA+B,CAAA;IAC/B;;OAEG;IACH,0DAAyC,CAAA;AAC3C,CAAC,EAzCW,aAAa,6BAAb,aAAa,QAyCxB"}